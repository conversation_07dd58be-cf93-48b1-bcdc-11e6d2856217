
using Infrastructure.Constants;
using Infrastructure.Events.Employee;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Employee.UpdateEmployeeNumberUnderOffice
{
    public class SyncNumberOfEmployeesOfOfficeService
    {
        private readonly ILogger<SyncNumberOfEmployeesOfOfficeService> _logger;
        private readonly IRepository _repository;

        public SyncNumberOfEmployeesOfOfficeService(
            ILogger<SyncNumberOfEmployeesOfOfficeService> logger,
            IRepository repository)
        {
            _logger = logger;
            _repository = repository;
        }

        public async Task UpdateNumberOfEmployeesOfOffice(SyncNumberOfEmployeesEvent employeeSyncEvent)
        {
            try
            {
                _logger.LogInformation("Inside UpdateNumberOfEmployeesOfOffice");
                await ProcessEmployeeCounts(employeeSyncEvent.OfficeId);
                _logger.LogInformation("Finished UpdateNumberOfEmployeesOfOffice");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Occured while Updating Employees");
            }
        }

        private async Task ProcessEmployeeCounts(string officeId)
        {
            _logger.LogInformation("Inside ProcessEmployeeCounts - START");
            var officeInfo = await GetOfficeById(officeId);
            var propertiesToBeUpdated = PrepareDataToUpdateOffice(officeInfo.NumberOfEmployee + 1);
            await UpdateBuroOffice(propertiesToBeUpdated, officeInfo.ItemId);
            _logger.LogInformation("Inside ProcessEmployeeCounts - END");
        }

        private Dictionary<string, object> PrepareDataToUpdateOffice(int countOfEmployees)
        {
            _logger.LogInformation("SyncNumberOfOfficeService -> PrepareDataToUpdateOffice -> START");
            var updatedProperties = new Dictionary<string, object>
            {
                {nameof(BuroOffice.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroOffice.LastUpdatedBy), BuroConstants.SuperAdmin },
                {nameof(BuroOffice.NumberOfEmployee), countOfEmployees}
            };
            _logger.LogInformation("SyncNumberOfOfficeService -> PrepareDataToUpdateOffice -> END");
            return updatedProperties;
        }

        private async Task UpdateBuroOffice(Dictionary<string, object> properties, string officeId)
        {
            await _repository.UpdateAsync<BuroOffice>(x => x.ItemId == officeId, properties);
        }

        private async Task<BuroOffice> GetOfficeById(string officeId)
        {
            return await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == officeId);
        }
    }
}
