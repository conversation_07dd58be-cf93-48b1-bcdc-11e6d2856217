using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Extensions;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Employee.StaffingApprovalPermission
{
    public class StaffingApprovalPermissionService
    {
        private readonly ILogger<StaffingApprovalPermissionService> _logger;
        private readonly IRepository _repository;
        private readonly INotificationServiceClient _notificationServiceClient;
        public StaffingApprovalPermissionService(
            IRepository repository,
            INotificationServiceClient notificationServiceClient,
            ILogger<StaffingApprovalPermissionService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _notificationServiceClient = notificationServiceClient;
        }

        public async Task UpdateBuroApprovalForStaffing(string staffingRequestId)
        {
            try
            {
                var staffingRequestDetails = await GetStaffingRequestById(staffingRequestId);
                await ProcessStaffingForApprovals(staffingRequestDetails);
                _logger.LogInformation("Inside StaffingApprovalPermissionService:  Approvals created for staffing Id: {staffingId}", staffingRequestId);
            }
            catch (Exception ex)
            {
                _logger.LogInformation("Error Occured while Fetching Staffing Request, StaffingApprovalPermissionService");
            }
        }

        private async Task<BuroStaffingRequest> GetStaffingRequestById(string itemId)
        {
            return await _repository.GetItemAsync<BuroStaffingRequest>(r => r.ItemId == itemId);
        }

        private async Task ProcessStaffingForApprovals(BuroStaffingRequest staffingRequest)
        {
            List<BuroEmployee> approvalActionEmployees = new List<BuroEmployee>();

            if (string.IsNullOrEmpty(staffingRequest.MovingFromOfficeId))
            {
                approvalActionEmployees = await ProcessApprovalIdsBasedOnFirstTimeApprovalMatrixAsync(staffingRequest);
            }
            else
            {
                approvalActionEmployees = await ProcessApprovalIdsBasedOnApprovalMatrixAsync(staffingRequest.MovingFromOfficeId, staffingRequest.MovingToOfficeId);
            }

            var requestorInfo = await GetEmployeeInfoById(staffingRequest.TransferRequestedByEmployeeId);
            var requiredEmployeesForApproval = approvalActionEmployees.Select(x => x.ItemId).ToList();

            await UpdateStaffingRequestEntity(staffingRequest.ItemId, requiredEmployeesForApproval);
            CreateApprovalBasedOnPermissions(
                approvalActionEmployees,
                requestorInfo,
                EnumApprovalCategoryType.EmployeeTransferIn,
                BuroEntityNameConstants.BuroStaffingRequest,
                staffingRequest);
        }

        public async Task<List<BuroEmployee>> ProcessApprovalIdsBasedOnFirstTimeApprovalMatrixAsync(BuroStaffingRequest staffingRequest)
        {
            var employee = await _repository.GetItemAsync<BuroEmployee>(x => x.ItemId == staffingRequest.EmployeeItemId);
            var destinationOfficeForFrsitTime = await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == staffingRequest.MovingToOfficeId);
            string officeId = await GetOfficeNeededForApprovalForFirstTime(destinationOfficeForFrsitTime, employee);

            return string.IsNullOrEmpty(officeId) ? new List<BuroEmployee>() : GetEmployeesNeededForApproval(new List<string>() { officeId });
        }

        private async Task<List<BuroEmployee>> ProcessApprovalIdsBasedOnApprovalMatrixAsync(string sourceOfficeId, string destinationOfficeId)
        {
            var (sourceOffice, destinationOffice) = await GetRespectedOfficeDetails(sourceOfficeId, destinationOfficeId);
            var offices = await GetAllOfficesNeededForApproval(sourceOffice, destinationOffice);

            return GetEmployeesNeededForApproval(offices.ToList());
        }

        private async Task<(BuroOffice, BuroOffice)> GetRespectedOfficeDetails(string sourceOfficeId, string destinationOfficeId)
        {
            var sourceOffice = await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == sourceOfficeId);
            var destinationOffice = await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == destinationOfficeId);

            return (sourceOffice, destinationOffice);
        }

        public async Task<string> GetOfficeNeededForApprovalForFirstTime(BuroOffice destinationOffice, BuroEmployee transferEmployee)
        {
            // Employee Designation == Brnachmanager. Area Manager, Top Manager --> move to open step
            var managerDesignation = new List<string>
            {
                BuroEmployeeDesignationConstant.BuroDivisionalManager,
                BuroEmployeeDesignationConstant.BuroZonalManager,
                BuroEmployeeDesignationConstant.BuroAreaManager,
                BuroEmployeeDesignationConstant.BuroBranchManager,
                BuroEmployeeDesignationConstant.BuroHeadOfficeITAdmin
            };

            string officeItemId = destinationOffice.ItemId;
            if (managerDesignation.Contains(transferEmployee.DesignationTitle))
            {
                if (destinationOffice.OfficeType == EnumOfficeType.BranchOffice)
                {
                    officeItemId = destinationOffice.AreaOfficeItemId;
                }

                if (destinationOffice.OfficeType == EnumOfficeType.AreaOffice)
                {
                    officeItemId = destinationOffice.ZoneOfficeItemId;
                }

                if (destinationOffice.OfficeType == EnumOfficeType.ZoneOffice)
                {
                    officeItemId = destinationOffice.DivisionOfficeItemId;
                }

                if (destinationOffice.OfficeType == EnumOfficeType.DivisionOffice)
                {
                    var headoffice = await _repository.GetItemAsync<BuroOffice>(x => x.OfficeType == EnumOfficeType.HeadOffice);
                    officeItemId = headoffice.ItemId;
                }

                if (destinationOffice.OfficeType == EnumOfficeType.HeadOffice)
                {
                    return ""; // For Moving to HeadOFfice Need no permission
                }

            }

            // or else return that branch's branch manager
            return officeItemId;
        }

        public static async Task<List<string>> GetAllOfficesNeededForApproval(BuroOffice sourceOffice, BuroOffice destinationOffice)
        {
            var offices = new List<string>();
            bool isParentSame = false;

            if (sourceOffice == null || destinationOffice == null)
            {
                return offices;
            }
            //Headoffice check
            else if (sourceOffice.OfficeType == EnumOfficeType.HeadOffice || destinationOffice.OfficeType == EnumOfficeType.HeadOffice)
            {
                if (sourceOffice.OfficeType != EnumOfficeType.HeadOffice)
                {
                    offices.AddRange(new[] {
                        sourceOffice.DivisionOfficeItemId,
                        sourceOffice.ZoneOfficeItemId,
                        sourceOffice.AreaOfficeItemId,
                        sourceOffice.BranchOfficeItemId }.Where(item => item != null));
                }
                else
                {
                    offices.AddRange(new[] { sourceOffice.ItemId });
                }

                if (destinationOffice.OfficeType != EnumOfficeType.HeadOffice)
                {
                    offices.AddRange(new[] {
                        destinationOffice.DivisionOfficeItemId,
                        destinationOffice.ZoneOfficeItemId,
                        destinationOffice.AreaOfficeItemId,
                        destinationOffice.BranchOfficeItemId }.Where(item => item != null));
                }
                else
                {
                    offices.AddRange(new[] { destinationOffice.ItemId }.Where(item => !offices.Contains(item)));
                }

                return offices;
            }
            else
            {
                offices.AddRange(new[] { sourceOffice.ItemId, destinationOffice.ItemId });
            }

            if (sourceOffice.AreaOfficeItemId != destinationOffice.AreaOfficeItemId)
            {
                if (!String.IsNullOrEmpty(sourceOffice.AreaOfficeItemId) && !offices.Contains(sourceOffice.AreaOfficeItemId))
                {
                    offices.Add(sourceOffice.AreaOfficeItemId);
                }
                if (!String.IsNullOrEmpty(destinationOffice.AreaOfficeItemId) && !offices.Contains(destinationOffice.AreaOfficeItemId))
                {
                    offices.Add(destinationOffice.AreaOfficeItemId);
                }
            }
            else if (sourceOffice.AreaOfficeItemId == destinationOffice.AreaOfficeItemId)
            {
                isParentSame = true;
                if (!String.IsNullOrEmpty(sourceOffice.AreaOfficeItemId) && !offices.Contains(sourceOffice.AreaOfficeItemId))
                {
                    offices.Add(sourceOffice.AreaOfficeItemId);
                }
            }

            if (sourceOffice.ZoneOfficeItemId != destinationOffice.ZoneOfficeItemId)
            {
                if (!String.IsNullOrEmpty(sourceOffice.ZoneOfficeItemId) && !offices.Contains(sourceOffice.ZoneOfficeItemId))
                {
                    offices.Add(sourceOffice.ZoneOfficeItemId);
                }
                if (!String.IsNullOrEmpty(destinationOffice.ZoneOfficeItemId) && !offices.Contains(destinationOffice.ZoneOfficeItemId))
                {
                    offices.Add(destinationOffice.ZoneOfficeItemId);
                }
            }
            else if ((sourceOffice.ZoneOfficeItemId == destinationOffice.ZoneOfficeItemId) && !isParentSame)
            {
                isParentSame = true;
                if (!String.IsNullOrEmpty(sourceOffice.ZoneOfficeItemId) && !offices.Contains(sourceOffice.ZoneOfficeItemId))
                {
                    offices.Add(sourceOffice.ZoneOfficeItemId);
                }
            }

            if (sourceOffice.DivisionOfficeItemId != destinationOffice.DivisionOfficeItemId)
            {
                if (!String.IsNullOrEmpty(sourceOffice.DivisionOfficeItemId) && !offices.Contains(sourceOffice.DivisionOfficeItemId))
                {
                    offices.Add(sourceOffice.DivisionOfficeItemId);
                }
                if (!String.IsNullOrEmpty(destinationOffice.DivisionOfficeItemId) && !offices.Contains(destinationOffice.DivisionOfficeItemId))
                {
                    offices.Add(destinationOffice.DivisionOfficeItemId);
                }
            }
            else if ((sourceOffice.DivisionOfficeItemId == destinationOffice.DivisionOfficeItemId) && !isParentSame)
            {
                isParentSame = true;
                if (!String.IsNullOrEmpty(sourceOffice.DivisionOfficeItemId) && !offices.Contains(sourceOffice.DivisionOfficeItemId))
                {
                    offices.Add(sourceOffice.DivisionOfficeItemId);
                }
            }

            return offices;
        }

        private List<BuroEmployee> GetEmployeesNeededForApproval(List<string> requiredOfficeIds)
        {
            var designationPermissions = new List<string>
            {
                BuroEmployeeDesignationConstant.BuroDivisionalManager,
                BuroEmployeeDesignationConstant.BuroZonalManager,
                BuroEmployeeDesignationConstant.BuroAreaManager,
                BuroEmployeeDesignationConstant.BuroBranchManager,
                BuroEmployeeDesignationConstant.BuroHeadOfficeITAdmin
            };

            var avoidSendingApprovalTypeList = new List<EnumEmployeeEmploymentStatus>
            {
                EnumEmployeeEmploymentStatus.Terminated,
                EnumEmployeeEmploymentStatus.Suspended,
                EnumEmployeeEmploymentStatus.OnLeave,
                EnumEmployeeEmploymentStatus.Retired
            };

            return _repository.GetItems<BuroEmployee>(x => requiredOfficeIds.Contains(x.CurrentOfficeItemId)
                    && designationPermissions.Contains(x.DesignationTitle)
                    && !avoidSendingApprovalTypeList.Contains(x.EmploymentStatus)
                   ).ToList();
        }

        private async Task UpdateStaffingRequestEntity(string staffingRequestId, List<string> employeeIds)
        {
            _logger.LogInformation("StaffingApprovalPermissionService -> UpdateStaffingRequestEntity -> START");
            var updatedProperties = new Dictionary<string, object>
                                {
                                    {nameof(BuroStaffingRequest.LastUpdateDate), DateTime.UtcNow},
                                    {nameof(BuroStaffingRequest.LastUpdatedBy), BuroConstants.SuperAdmin },
                                    {nameof(BuroStaffingRequest.ApprovalRequiredFromEmployeeIds), employeeIds },
                                };

            if (employeeIds.Count == 0)
            {
                _logger.LogInformation("StaffingApprovalPermissionService -> UpdateStaffingRequestEntity -> employeeIds.Count == 0");
                updatedProperties.Add(nameof(BuroStaffingRequest.Status), EnumStaffingRequestStatus.Completed);
            }

            await _repository.UpdateAsync<BuroStaffingRequest>(x => x.ItemId == staffingRequestId, updatedProperties);
            _logger.LogInformation("StaffingApprovalPermissionService -> UpdateStaffingRequestEntity -> END");
        }

        private void CreateApprovalBasedOnPermissions(
            List<BuroEmployee> employees,
            BuroEmployee requestorInfo,
            EnumApprovalCategoryType category,
            string entityName,
            BuroStaffingRequest staffingRequest
            )
        {
            foreach (var employee in employees)
            {
                var newApproval = PrepareBuroApproval(staffingRequest.ItemId, entityName, requestorInfo, employee, category);

                _repository.Save(newApproval);
                if (newApproval != null)
                {
                    NotifyEmployeesForApproval(requestorInfo, employee, staffingRequest, newApproval.ItemId);
                }
            }

        }

        public static BuroApproval PrepareBuroApproval(
            string relatedEntityItemId,
            string relatedEnityName,
            BuroEmployee requestorEmployeeInfo,
            BuroEmployee actionedEmployeeInfo,
            EnumApprovalCategoryType category
            )
        {
            var buroApproval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                CreatedBy = BuroConstants.SuperAdmin,
                LastUpdatedBy = BuroConstants.SuperAdmin,
                RolesAllowedToRead = new[] { UserRoles.Admin },
                IdsAllowedToRead = new[] { BuroConstants.SuperAdmin },
                IdsAllowedToDelete = new[] { BuroConstants.SuperAdmin },
                IdsAllowedToUpdate = new[] { BuroConstants.SuperAdmin },
                IdsAllowedToWrite = new[] { BuroConstants.SuperAdmin },
                RelatedEntityItemId = relatedEntityItemId,
                RelatedEntityName = relatedEnityName,
                RequestedFromPersonItemId = requestorEmployeeInfo.PersonItemId,
                RequestedFromEmployeeItemId = requestorEmployeeInfo.ItemId,
                RequestedFromEmployeeName = requestorEmployeeInfo.EmployeeName,
                RequestedFromEmployeePIN = requestorEmployeeInfo.EmployeePin,
                ActionedByEmployeeItemId = actionedEmployeeInfo.ItemId,
                ActionedByPersonItemId = actionedEmployeeInfo.PersonItemId,
                ActionedByEmployeeName = actionedEmployeeInfo.EmployeeName,
                ActionedByEmployeePIN = actionedEmployeeInfo.EmployeePin,
                ActionedByEmployeeDesignation = actionedEmployeeInfo.DesignationTitle,
                Status = EnumApprovalStatusType.Pending,
                ActionDate = null,
                MetaData = null,
                Category = category,
                Remarks = null

            };
            buroApproval.AddEntityBasicInfo();

            return buroApproval;
        }


        private async Task<BuroEmployee> GetEmployeeInfoById(string employeeId)
        {
            return await _repository.GetItemAsync<BuroEmployee>(x => x.ItemId == employeeId);
        }

        private async Task NotifyEmployeesForApproval(
            BuroEmployee requestorDetails,
            BuroEmployee approverDetails,
            BuroStaffingRequest staffingRequestDetails,
            string approvalId)
        {
            try
            {
                var payload = new
                {
                    ApprovalItemId = approvalId,
                    StaffngRequestId = staffingRequestDetails.ItemId,
                    RequestorByEmployeeId = requestorDetails.ItemId,
                    RequestorByEmployeeName = requestorDetails.EmployeeName,
                    StaffingType = staffingRequestDetails.TransferType,
                    RequestedForEmployeeId = staffingRequestDetails.ItemId,
                    SourceOfficeName = staffingRequestDetails.MovingFromOfficeName,
                    SourceOfficeId = staffingRequestDetails.MovingFromOfficeId,
                    DestinationOfficeName = staffingRequestDetails.MovingToOfficeName,
                    DestinationOfficeId = staffingRequestDetails.MovingToOfficeId
                };

                var notification = new NotifierPayloadWithResponse
                {
                    UserIds = new List<Guid> { Guid.Parse(approverDetails.UserItemId) },
                    NotificationType = NotificationReceiverTypes.UserSpecificReceiverType,
                    ResponseKey = BuroNotificationKeys.EmployeeStaffingRequestCreated,
                    DenormalizedPayload = System.Text.Json.JsonSerializer.Serialize(payload)
                };

                await _notificationServiceClient.NotifyAsync(notification);
                _logger.LogInformation("Notification sent for for Approval Creation");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification for Approval Creation");

            }
        }

    }
}
