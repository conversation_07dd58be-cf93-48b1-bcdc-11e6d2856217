using EventHandlers.Employee.StaffingApprovalPermission;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Events;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Employee.CenterApprovalPermission
{
    public class CenterApprovalPermissionService
    {
        private readonly ILogger<CenterApprovalPermissionService> _logger;
        private readonly IRepository _repository;
        private readonly INotificationServiceClient _notificationServiceClient;
        public CenterApprovalPermissionService(
            IRepository repository,
            INotificationServiceClient notificationServiceClient,
            ILogger<CenterApprovalPermissionService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _notificationServiceClient = notificationServiceClient;
        }

        public async Task CreateApprovalForCenter(CenterApprovalPermissionEvent command)
        {
            try
            {
                _logger.LogInformation("Inside CenterApprovalPermissionService : {CenterId}", command.BuroCenterItemId);
                var centerDetails = await GetCenterDetailsById(command.BuroCenterItemId);
                await ProcessCenterApprovalRequest(centerDetails, command.ProcessOperationType);
                _logger.LogInformation("Finished CenterApprovalPermissionService:  Approvals created for centerId : {CenterId}", command.BuroCenterItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in CenterApprovalPermissionService.");
            }
        }

        private async Task ProcessCenterApprovalRequest(BuroCenter buroCenter, EnumOperationTypes operationType)
        {
            _logger.LogInformation("Inside ProcessCenterApprovalRequest");
            var approverInfo = await GetEmployeeInfoByOfficeId(buroCenter.BranchId);
            BuroEmployee requestorInfo = null;
            BuroEmployee projectOrganizerInfo = null;

            if (operationType == EnumOperationTypes.ADD)
            {
                requestorInfo = await GetEmployeeInfoByUserId(buroCenter.CreatedBy);
                projectOrganizerInfo = string.IsNullOrWhiteSpace(buroCenter.ProgramOrganizerEmployeeItemId)
                    ? null : await GetEmployeeInfoByItemId(buroCenter.ProgramOrganizerEmployeeItemId);
            }
            else if (operationType == EnumOperationTypes.EDIT)
            {
                var requestorUserId = buroCenter.PendingChanges.ContainsKey(BuroCenterConstants.LastUpdatedByProperty)
                    ? (string)buroCenter.PendingChanges[BuroCenterConstants.LastUpdatedByProperty] : null;
                var programOrganizerEmployeeId = buroCenter.PendingChanges.ContainsKey(BuroCenterConstants.ProgramOrganizerIdProperty)
                    ? (string)buroCenter.PendingChanges[BuroCenterConstants.ProgramOrganizerIdProperty] : null;
                requestorInfo = await GetEmployeeInfoByUserId(requestorUserId);
                projectOrganizerInfo = operationType == EnumOperationTypes.EDIT
                    ? await GetEmployeeInfoByItemId(programOrganizerEmployeeId) : null;
                buroCenter = UpdateExistingCenterWithNewData(buroCenter);
            }
            else if (operationType == EnumOperationTypes.REMOVE)
            {
                var requestorUserId = buroCenter.PendingChanges.ContainsKey(BuroCenterConstants.LastUpdatedByProperty)
                    ? (string)buroCenter.PendingChanges[BuroCenterConstants.LastUpdatedByProperty] : null;
                requestorInfo = await GetEmployeeInfoByUserId(requestorUserId);
                projectOrganizerInfo = string.IsNullOrWhiteSpace(buroCenter.ProgramOrganizerEmployeeItemId)
                    ? null : await GetEmployeeInfoByItemId(buroCenter.ProgramOrganizerEmployeeItemId);
            }

            var newBuroApproval = StaffingApprovalPermissionService
                .PrepareBuroApproval(
                    buroCenter.ItemId,
                    BuroConstants.BuroCenterEntity,
                    requestorInfo,
                    approverInfo,
                    EnumApprovalCategoryType.CreationOfCenterByBA);

            await CreateApprovalForCenter(newBuroApproval);

            await NotifyEmployeeAboutCenterApproval(
                    requestorInfo,
                    projectOrganizerInfo,
                    approverInfo.UserItemId,
                    buroCenter,
                    newBuroApproval.ItemId,
                    operationType);

            _logger.LogInformation("Finished ProcessCenterApprovalRequest");
        }

        private async Task<BuroCenter> GetCenterDetailsById(string centerId)
        {
            return await _repository.GetItemAsync<BuroCenter>(c => c.ItemId == centerId);
        }

        private async Task<BuroEmployee> GetEmployeeInfoByOfficeId(string officeId)
        {
            var avoidSendingApprovalTypeList = new List<EnumEmployeeEmploymentStatus>
            {
                EnumEmployeeEmploymentStatus.Terminated,
                EnumEmployeeEmploymentStatus.Suspended,
                EnumEmployeeEmploymentStatus.OnLeave,
                EnumEmployeeEmploymentStatus.Retired
            };

            return await _repository.GetItemAsync<BuroEmployee>(
                    e => e.CurrentOfficeItemId == officeId
                        && e.DesignationTitle == BuroEmployeeDesignationConstant.BuroBranchManager
                        && !avoidSendingApprovalTypeList.Contains(e.EmploymentStatus));
        }

        private async Task<BuroEmployee> GetEmployeeInfoByUserId(string userId)
        {
            return await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == userId);
        }

        private async Task<BuroEmployee> GetEmployeeInfoByItemId(string employeeId)
        {
            return await _repository.GetItemAsync<BuroEmployee>(e => e.ItemId == employeeId);
        }

        private async Task CreateApprovalForCenter(BuroApproval approval)
        {
            _logger.LogInformation("Inside CreateApprovalForCenter");
            await _repository.SaveAsync(approval);
            _logger.LogInformation("Finished CreateApprovalForCenter");

        }

        private async Task NotifyEmployeeAboutCenterApproval(
            BuroEmployee requestorInfo,
            BuroEmployee? projectOrganizerInfo,
            string approverUserId,
            BuroCenter centerDetails,
            string approvalId,
            EnumOperationTypes operationType)
        {
            try
            {
                var payload = new
                {
                    ApprovalItemId = approvalId,
                    CenterId = centerDetails.ItemId,
                    CenterName = centerDetails.Name,
                    RequestedByEmployeeId = requestorInfo.ItemId,
                    RequestedByEmployeeName = requestorInfo.EmployeeName,
                    RequestedByEmployeePin = requestorInfo.EmployeePin,
                    CenterType = centerDetails.Type,
                    CenterDay = centerDetails.CenterDay,
                    CenterTime = centerDetails.CenterTime,
                    DivisionName = centerDetails.DivisionName,
                    DistrictName = centerDetails.DistrictName,
                    UpazilaName = centerDetails.UpazilaName,
                    PostOfficeName = centerDetails.PostOfficeName,
                    PostCode = centerDetails.PostCode,
                    WardName = centerDetails.WardName,
                    OperationType = operationType,
                    ProjectOrganizerId = projectOrganizerInfo?.ItemId,
                    ProjectOrganizerName = projectOrganizerInfo?.EmployeeName,
                    ProjectOrganizerPin = projectOrganizerInfo?.EmployeePin
                };

                var notification = new NotifierPayloadWithResponse
                {
                    UserIds = new List<Guid> { Guid.Parse(approverUserId) },
                    NotificationType = NotificationReceiverTypes.UserSpecificReceiverType,
                    ResponseKey = BuroNotificationKeys.BuroCenterCreationApprovalCreated,
                    DenormalizedPayload = System.Text.Json.JsonSerializer.Serialize(payload)
                };

                await _notificationServiceClient.NotifyAsync(notification);
                _logger.LogInformation("Notification sent for for Approval Creation");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification for Approval Creation");

            }
        }

        private BuroCenter UpdateExistingCenterWithNewData(BuroCenter buroCenter)
        {
            if (buroCenter.PendingChanges == null || buroCenter.PendingChanges.Count == 0)
                return buroCenter;

            var buroCenterType = typeof(BuroCenter);

            foreach (var change in buroCenter.PendingChanges)
            {
                var property = buroCenterType.GetProperty(change.Key);

                if (property != null && property.CanWrite)
                {
                    property.SetValue(buroCenter, change.Value);
                }
            }
            return buroCenter;
        }
    }
}
