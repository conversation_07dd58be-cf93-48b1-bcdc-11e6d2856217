using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Employee.StaffingRequestApprovalStatusUpdate
{
    public class StaffingRequestApprovalStatusUpdateService
    {
        private readonly ILogger<StaffingRequestApprovalStatusUpdateService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;
        public StaffingRequestApprovalStatusUpdateService(IRepository repository, ILogger<StaffingRequestApprovalStatusUpdateService> logger, ISecurityContextProvider securityContextProvider)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _securityContextProvider = securityContextProvider ?? throw new ArgumentNullException(nameof(securityContextProvider));
        }

        public async Task<BuroApproval> GetBuroApproval(string itemId)
        {
            return await _repository.GetItemAsync<BuroApproval>(r => r.ItemId == itemId);
        }

        public List<BuroApproval> GetBuroApprovalListByStaffingRequest(string staffingRequestItemId)
        {
            return _repository.GetItems<BuroApproval>(x => x.RelatedEntityItemId == staffingRequestItemId).ToList();
        }

        public async Task<BuroEmployee> GetEmployeeByUserId(string userId)
        {
            return await _repository.GetItemAsync<BuroEmployee>(r => r.UserItemId == userId);
        }

        public async Task<BuroEmployee> GetEmployee(string itemId)
        {
            return await _repository.GetItemAsync<BuroEmployee>(r => r.ItemId == itemId);
        }

        public async Task<BuroStaffingRequest> GetStaffingRequest(string itemId)
        {
            return await _repository.GetItemAsync<BuroStaffingRequest>(r => r.ItemId == itemId);
        }

        private Dictionary<string, object> GetStaffingRequestPropertyForStatusUpdate(EnumStaffingRequestStatus status)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroStaffingRequest.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroStaffingRequest.LastUpdatedBy), securityContext.UserId},
                {nameof(BuroStaffingRequest.Status), status},
            };

            return properties;
        }

        public async Task UpdateStaffingRequestStatus(string itemId, EnumStaffingRequestStatus status)
        {
            this._logger.LogInformation("StaffingRequestApprovalStatusUpdateService->UpdateStaffingRequestStatus-START");
            var updatedProperties = GetStaffingRequestPropertyForStatusUpdate(status);
            await _repository.UpdateAsync<BuroStaffingRequest>(x => x.ItemId == itemId, updatedProperties);
        }

        public async Task UpdateUserInfoForActivationOfEmployee(string userItemId)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(User.LastUpdateDate), DateTime.UtcNow},
                {nameof(User.LastUpdatedBy), BuroConstants.SuperAdmin},
                {nameof(User.Active), true}
            };
            await _repository.UpdateAsync<User>(x => x.ItemId == userItemId, properties);
        }
    }
}
