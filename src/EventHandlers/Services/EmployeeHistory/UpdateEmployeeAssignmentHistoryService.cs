using Infrastructure.Events.EmployeeHistory;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.EmployeeHistory;

public class UpdateEmployeeAssignmentHistoryService
{
    private readonly IRepository _repository;

    public UpdateEmployeeAssignmentHistoryService(IRepository repository)
    {
        _repository = repository;
    }

    public async Task UpdateEmployeeAssignmentHistory(UpdateEmployeeAssignmentHistoryEvent command)
    {
        var updatedObject = CreateUpdatedObject(command);

        await _repository.UpdateAsync<BuroCenterAssignmentHistory>(
            item => item.CenterItemId == command.CenterItemId && item.EmployeeItemId == command.EmployeeItemId,
            updatedObject);
    }

    private Dictionary<string, object> CreateUpdatedObject(UpdateEmployeeAssignmentHistoryEvent command)
    {
        return new Dictionary<string, object>
        {
            { nameof(BuroCenterAssignmentHistory.EndDate), command.EndDate },
            { nameof(BuroCenterAssignmentHistory.LastUpdateDate), DateTime.UtcNow }
        };
    }

}