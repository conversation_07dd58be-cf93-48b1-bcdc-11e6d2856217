using Infrastructure.Constants;
using Infrastructure.Events.EmployeeHistory;
using Infrastructure.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.EmployeeHistory;

public class CreateEmployeeAssignmentHistoryService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateEmployeeAssignmentHistoryService(IRepository repository, ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task<CommandResponse> CreateEmployeeAssignmentHistory(CreateEmployeeAssignmentHistoryEvent command)
    {
        var commandResponse = new CommandResponse();
        try
        {
            var context = _securityContextProvider.GetSecurityContext();
            var employeeAssignmentHistory = PopulateEmployeeAssignmentHistory(command, context.UserId);
            employeeAssignmentHistory.AddEntityBasicInfo();

            await _repository.SaveAsync(employeeAssignmentHistory);
        }
        catch (Exception e)
        {
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return commandResponse;
    }

    private BuroCenterAssignmentHistory PopulateEmployeeAssignmentHistory(CreateEmployeeAssignmentHistoryEvent command, string userId)
    {
        return new BuroCenterAssignmentHistory
        {
            ItemId = Guid.NewGuid().ToString(),
            UserItemId = command.UserItemId,
            EmployeeItemId = command.EmployeeItemId,
            CenterTitle = command.CenterTitle,
            CenterItemId = command.CenterItemId,
            OfficeItemId = command.OfficeItemId,
            OfficeTitle = command.OfficeTitle,
            OfficeCode = command.OfficeCode,
            MemberCount = command.MemberCount,
            StartDate = command.StartDate,
            EndDate = command.EndDate,
            CreatedBy = userId,
            CreateDate = DateTime.UtcNow
        };
    }


}