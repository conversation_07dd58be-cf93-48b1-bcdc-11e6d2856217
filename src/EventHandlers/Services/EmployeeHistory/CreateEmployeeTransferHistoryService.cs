using Infrastructure.Constants;
using Infrastructure.Events.EmployeeHistory;
using Infrastructure.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.EmployeeHistory;

public class CreateEmployeeTransferHistoryService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateEmployeeTransferHistoryService(IRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task<CommandResponse> CreateEmployeeTransferHistory(CreateEmployeeTransferHistoryEvent command)
    {
        var commandResponse = new CommandResponse();
        try
        {
            var context = _securityContextProvider.GetSecurityContext();
            var employeeTransitionHistory = CreateEmployeeTransferHistory(command, context.UserId);
            employeeTransitionHistory.AddEntityBasicInfo();
            await _repository.SaveAsync(employeeTransitionHistory);
        }
        catch (Exception e)
        {
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return commandResponse;
    }

    private BuroEmployeeTransferHistory CreateEmployeeTransferHistory(CreateEmployeeTransferHistoryEvent command, string userId) =>
        new BuroEmployeeTransferHistory
        {
            ItemId = Guid.NewGuid().ToString(),
            UserItemId = command.UserItemId,
            EmployeeItemId = command.EmployeeItemId,
            DesignationItemId = command.DesignationItemId,
            DesignationTitle = command.DesignationTitle,
            DesignationCode = command.DesignationCode,
            OfficeItemId = command.OfficeItemId,
            OfficeTitle = command.OfficeTitle,
            OfficeCode = command.OfficeCode,
            MemberCount = command.MemberCount,
            StartDate = command.StartDate,
            EndDate = command.EndDate,
            TransferHistoryType = command.TransferHistoryType,
            CreatedBy = userId
        };
}