using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Events.Collection;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Collection
{
    public class AdjustCashInHandBalanceEventService : IAdjustCashInHandBalanceEventService
    {
        private readonly ILogger<AdjustCashInHandBalanceEventService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;

        public AdjustCashInHandBalanceEventService(
            ILogger<AdjustCashInHandBalanceEventService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
        }

        public async Task AdjustCashInHandBalanceAsync(AdjustCashInHandBalanceEvent command)
        {
            _logger.LogInformation("Starting adjusting amount {Amount} for branch item id: {BranchItemId}", command.Amount, command.BranchOfficeItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();

            await UpdateBranchBalanceAsync(command, securityContext);
            await SaveBranchBalanceHistoryAsync(command, securityContext);

            _logger.LogInformation("Successfully adjusted amount {Amount} for branch item id: {BranchItemId}", command.Amount, command.BranchOfficeItemId);
        }

        private async Task SaveBranchBalanceHistoryAsync(AdjustCashInHandBalanceEvent command, SecurityContext securityContext)
        {
            var employee = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId);

            var history = new BuroBranchBalanceHistory
            {
                BranchBalanceItemId = command.BranchOfficeItemId,
                Amount = command.Amount,
                IsCashInHand = true,
                IsReverted = false,
                ActionByEmployeeItemId = employee.ItemId,
                ActionByEmployeePin = employee.EmployeePin,
                ActionByEmployeeName = employee.EmployeeName,
                ActionByEmployeeDesginationItemId = employee.DesignationItemId,
                ActionByEmployeeDesginationTitle = employee.DesignationTitle,
                ActionByEmployeeOfficeItemId = employee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = employee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = employee.CurrentOfficeCode
            };

            await _finMongoDbRepository.InsertOneAsync(history);
        }

        private async Task UpdateBranchBalanceAsync(AdjustCashInHandBalanceEvent command, SecurityContext securityContext)
        {
            var (FinalCashInHandAmount, FinalTotalAmount) = await GetAdjustedAmountAsync(command);

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroBranchBalance.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroBranchBalance.LastUpdatedBy), securityContext.UserId },
                {nameof(BuroBranchBalance.CashInHandAmount), FinalCashInHandAmount },
                {nameof(BuroBranchBalance.TotalBalance), FinalTotalAmount },
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroBranchBalance>(b => b.BranchOfficeItemId == command.BranchOfficeItemId, properties);
        }

        private async Task<(double FinalCashInHandAmount, double FinalTotalAmount)> GetAdjustedAmountAsync(AdjustCashInHandBalanceEvent command)
        {
            var branchBalance = await _finMongoDbRepository.FindOneAsync<BuroBranchBalance>(b => b.BranchOfficeItemId == command.BranchOfficeItemId);

            var finalCashInHandAmount = command.ShouldIncreaseAmount
                ? branchBalance.CashInHandAmount + command.Amount
                : branchBalance.CashInHandAmount - command.Amount;

            var finalTotalAmount = finalCashInHandAmount + branchBalance.CashInBankAmount;

            return (finalCashInHandAmount, finalTotalAmount);
        }
    }
}
