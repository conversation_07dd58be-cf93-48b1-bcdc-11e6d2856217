using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Collection;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Collection
{
    public class CollectionFundTransferPostProcessingEventService : ICollectionFundTransferPostProcessingEventService
    {
        private readonly ILogger<CollectionFundTransferPostProcessingEventService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;
        private readonly IServiceClient _serviceClient;

        public CollectionFundTransferPostProcessingEventService(
            ILogger<CollectionFundTransferPostProcessingEventService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            IInfrastructureSharedService sharedService,
            ISecurityContextProvider securityContextProvider,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _sharedService = sharedService;
            _securityContextProvider = securityContextProvider;
            _serviceClient = serviceClient;
        }

        public async Task CollectionFundTransferPostProcessingAsync(CollectionFundTransferPostProcessingEvent command)
        {
            _logger.LogInformation("Starting CollectionFundTransferPostProcessingAsync for transfer Id: {CollectionFundTransferItemId}", command.CollectionFundTransferItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();
            var currentEmployee = await GetCurrentEmployeeInformationAsync();
            var collectionFundTransfer = await GetCollectionFundTransferAsync(command);

            switch (command.Status)
            {
                case EnumCollectionFundTransferStatus.Submitted:
                    await HandleCollectionTransferSubmissionAsync(command, securityContext, currentEmployee, collectionFundTransfer);
                    break;
                case EnumCollectionFundTransferStatus.Reverted:
                    await HandleCollectionTransferRevertAsync(command, securityContext, collectionFundTransfer);
                    break;
                default:
                    break;
            }

            await TrackEmployeePerformanceDataAsync(command, securityContext, currentEmployee, collectionFundTransfer);

            _logger.LogInformation("Successfully processed transfer with Id: {TransactionItemId}", command.CollectionFundTransferItemId);
        }

        private async Task TrackEmployeePerformanceDataAsync(
            CollectionFundTransferPostProcessingEvent command,
            SecurityContext securityContext,
            BuroActionByEmployeeInformation currentEmployee,
            BuroCollectionFundTransfer collectionFundTransfer)
        {
            _logger.LogInformation("Starting TrackEmployeePerformanceDataAsync");

            var existingPerformanceData = await _finMongoDbRepository.FindOneAsync<BuroEmployeePerformanceData>(p => p.EmployeeItemId == currentEmployee.EmployeeItemId);

            var performanceData = existingPerformanceData ?? new BuroEmployeePerformanceData();

            var properties = new Dictionary<string, object>();

            if (command.Status == EnumCollectionFundTransferStatus.Submitted)
            {
                if (string.IsNullOrWhiteSpace(collectionFundTransfer.RevertApprovalItemId))
                {
                    performanceData.NumberOfCollectionTransferSubmission += 1;

                    properties.Add(nameof(BuroEmployeePerformanceData.NumberOfCollectionTransferSubmission), performanceData.NumberOfCollectionTransferSubmission);
                }
                else
                {
                    performanceData.NumberOfCollectionTransferReSubmission += 1;

                    properties.Add(nameof(BuroEmployeePerformanceData.NumberOfCollectionTransferReSubmission), performanceData.NumberOfCollectionTransferReSubmission);
                }
            }

            if (command.Status == EnumCollectionFundTransferStatus.Reverted)
            {
                performanceData.NumberOfCollectionTransferRevertPerformed += 1;

                properties.Add(nameof(BuroEmployeePerformanceData.NumberOfCollectionTransferRevertPerformed), performanceData.NumberOfCollectionTransferRevertPerformed);
            }

            if (existingPerformanceData == null)
            {
                performanceData.ItemId = Guid.NewGuid().ToString();
                performanceData.EmployeeItemId = currentEmployee.EmployeeItemId;

                await _finMongoDbRepository.InsertOneAsync(performanceData);
            }
            else
            {
                properties.Add(nameof(BuroEmployeePerformanceData.LastUpdateDate), DateTime.UtcNow);
                properties.Add(nameof(BuroEmployeePerformanceData.LastUpdatedBy), securityContext.UserId);

                await _finMongoDbRepository.UpdateOneAsync<BuroEmployeePerformanceData>(p => p.EmployeeItemId == currentEmployee.EmployeeItemId, properties);
            }
        }

        private async Task<BuroCollectionFundTransfer> GetCollectionFundTransferAsync(CollectionFundTransferPostProcessingEvent command)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroCollectionFundTransfer>(t => t.ItemId == command.CollectionFundTransferItemId);
        }

        private async Task HandleCollectionTransferRevertAsync(
            CollectionFundTransferPostProcessingEvent command,
            SecurityContext securityContext,
            BuroCollectionFundTransfer collectionFundTransfer)
        {
            // NOSONAR Create A task for BM

            _logger.LogInformation("Starting HandleCollectionTransferRevertAsync");
            var programOrganizer = await GetEmployeeByItemIdAsync(collectionFundTransfer.ProgramOrganizerEmployeeInformation.EmployeeItemId);
            var branchManager = await GetEmployeeByItemIdAsync(collectionFundTransfer.BranchManagerEmployeeInformation.EmployeeItemId);
            var payments = await GetAssociatedPaymentsAsync(collectionFundTransfer.ItemId);

            var approvalItemId = await CreateApprovalAsync(command, programOrganizer, branchManager, securityContext.UserId, collectionFundTransfer, payments);

            await SendNotificationToBranchManagerAsync(command, securityContext, programOrganizer, branchManager, approvalItemId);
            await SaveRevertApprovalItemIdAsync(command, securityContext, approvalItemId);
        }

        private async Task<BuroEmployee> GetEmployeeByItemIdAsync(string employeeItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.ItemId == employeeItemId);
        }

        private async Task<List<BuroMemberAccountPayment>> GetAssociatedPaymentsAsync(string collectionFundTransferItemId)
        {
            var collectionMovements = await _finMongoDbRepository.FindAsync<BuroCollectionMovementPerTransfer>(m => m.CollectionFundTransferItemId == collectionFundTransferItemId);
            var paymentItemIds = collectionMovements.Select(m => m.MemberSchedulePaymentItemId).ToList();
            var payments = await _finMongoDbRepository.FindAsync<BuroMemberAccountPayment>(p => paymentItemIds.Contains(p.ItemId));
            return payments;
        }

        private async Task SendNotificationToBranchManagerAsync(
            CollectionFundTransferPostProcessingEvent command,
            SecurityContext securityContext,
            BuroEmployee programOrganizer,
            BuroEmployee branchManager,
            string approvalItemId)
        {
            var notificationPayload = new
            {
                command.CollectionFundTransferItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                ApprovalItemId = approvalItemId,
                RequestedByEmployeeName = programOrganizer.EmployeeName,
                RequestedByEmployeePin = programOrganizer.EmployeePin,
                RequestedByEmployeeDesignation = programOrganizer.DesignationTitle,
                RequestedByEmployeeOfficeName = programOrganizer.CurrentOfficeTitle,
                RequestedByEmployeeOfficeCode = programOrganizer.CurrentOfficeCode
            };

            await _sharedService.NotifyUserAsync(
                BuroNotificationKeys.CollectionFundTransferRevertBranchManagerInvited,
                notificationPayload,
                command.CollectionFundTransferItemId,
                branchManager.UserItemId);
        }

        private async Task SaveRevertApprovalItemIdAsync(CollectionFundTransferPostProcessingEvent command, SecurityContext securityContext, string approvalItemId)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCollectionFundTransfer.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroCollectionFundTransfer.LastUpdatedBy), securityContext.UserId },
                {nameof(BuroCollectionFundTransfer.RevertApprovalItemId), approvalItemId },
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroCollectionFundTransfer>(t => t.ItemId == command.CollectionFundTransferItemId, properties);
        }

        private async Task<string> CreateApprovalAsync(
            CollectionFundTransferPostProcessingEvent command,
            BuroEmployee initiator,
            BuroEmployee approver,
            string currentUserId,
            BuroCollectionFundTransfer collectionFundTransfer,
            List<BuroMemberAccountPayment> payments)
        {
            _logger.LogInformation("Starting CreateApprovalAsync");

            var numberOfCenters = payments.Select(p => p.CenterItemId).Distinct().Count().ToString();
            var centerNames = string.Join(", ", payments.Select(p => p.CenterName).Distinct().ToList());

            var approval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsACollectionFundTransferRevertApproval },
                CreatedBy = currentUserId,
                LastUpdatedBy = currentUserId,
                RelatedEntityItemId = command.CollectionFundTransferItemId,
                RelatedEntityName = nameof(BuroCollectionFundTransfer),
                RequestedFromPersonItemId = initiator.PersonItemId,
                RequestedFromEmployeeItemId = initiator.ItemId,
                RequestedFromEmployeePIN = initiator.EmployeePin,
                RequestedFromEmployeeName = initiator.EmployeeName,
                ActionedByPersonItemId = approver.PersonItemId,
                ActionedByEmployeeItemId = approver.ItemId,
                ActionedByEmployeePIN = approver.EmployeePin,
                ActionedByEmployeeName = approver.EmployeeName,
                Status = EnumApprovalStatusType.Pending,
                Category = EnumApprovalCategoryType.CollectionFundTransferRevertApproval,
                ActionedByEmployeeDesignation = approver.DesignationTitle,
                MetaData = new List<MetaData>
                {
                    new() { Key = "CollectionFundTransferItemId", Value = collectionFundTransfer.ItemId },
                    new() { Key = "ReturnedAmount", Value = collectionFundTransfer.CollectedAmount.ToString() },
                    new() { Key = "PorgramOrganizerName", Value = initiator.EmployeeName },
                    new() { Key = "PorgramOrganizerPin", Value = initiator.EmployeePin },
                    new() { Key = "PorgramOrganizerBranchName", Value = initiator.CurrentOfficeTitle },
                    new() { Key = "PorgramOrganizerBranchCode", Value = initiator.CurrentOfficeCode },
                    new() { Key = "NumberOfCenters", Value = numberOfCenters },
                    new() { Key = "CenterNames", Value = centerNames },
                },
                IdsAllowedToRead = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToUpdate = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToDelete = new[] { currentUserId },
                IdsAllowedToWrite = new[] { currentUserId }
            };

            await _finMongoDbRepository.InsertOneAsync(approval);

            _logger.LogInformation("Loan application approval created with approval Id: {ItemId}", approval.ItemId);

            return approval.ItemId;
        }

        private async Task HandleCollectionTransferSubmissionAsync(
            CollectionFundTransferPostProcessingEvent command,
            SecurityContext securityContext,
            BuroActionByEmployeeInformation currentEmployee,
            BuroCollectionFundTransfer collectionFundTransfer)
        {
            var collections = await GetCollectionsAsync(currentEmployee.EmployeeItemId);
            var collectionItemIds = collections.Select(p => p.ItemId).ToList();

            await TrackCollectionMovementPerTransferAsync(collections, command.CollectionFundTransferItemId);
            await UpdateCollectionStatusAsync(collectionItemIds, securityContext.UserId);
            await SendFundTransferSubmissionNotificationAsync(command, securityContext);

            SendToHandleCollectionFundTransferEvent(collectionFundTransfer.BranchOfficeItemId, collectionFundTransfer.CollectedAmount, true);
        }

        private void SendToHandleCollectionFundTransferEvent(string branchOfficeItemId, double amount, bool shouldIncreaseAmount)
        {
            var payload = new AdjustCashInHandBalanceEvent
            {
                BranchOfficeItemId = branchOfficeItemId,
                Amount = amount,
                ShouldIncreaseAmount = shouldIncreaseAmount
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("Send to adjust cash in hand balance of branch with id: {BranchOfficeItemId}", branchOfficeItemId);
        }

        private async Task<BuroActionByEmployeeInformation> GetCurrentEmployeeInformationAsync()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var employees = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, BuroActionByEmployeeInformation>(
                dataFilters: e => e.UserItemId == securityContext.UserId,
                projectionExpression: e => new BuroActionByEmployeeInformation
                {
                    EmployeeItemId = e.ItemId,
                    EmployeePin = e.EmployeePin,
                    EmployeeName = e.EmployeeName,
                    EmployeeDesginationItemId = e.DesignationItemId,
                    EmployeeDesginationTitle = e.DesignationTitle,
                    EmployeeOfficeItemId = e.CurrentOfficeItemId,
                    EmployeeOfficeName = e.CurrentOfficeTitle,
                    EmployeeOfficeCode = e.CurrentOfficeCode
                });

            return employees.FirstOrDefault() ?? default!;
        }

        private async Task<List<BuroMemberAccountPayment>> GetCollectionsAsync(string currentEmployeeItemId)
        {
            return await _finMongoDbRepository.FindAsync<BuroMemberAccountPayment>(
                p => p.ProgramOrganizerEmployeeItemId == currentEmployeeItemId
                  && p.CreateDate.Date == DateTime.UtcNow.Date);
        }

        private async Task TrackCollectionMovementPerTransferAsync(
            List<BuroMemberAccountPayment> collections,
            string fundTransferRequestItemId)
        {
            var collectionMovements = new List<BuroCollectionMovementPerTransfer>();

            foreach (var p in collections)
            {
                var movementPerTransfer = new BuroCollectionMovementPerTransfer
                {
                    MemberSchedulePaymentItemId = p.ItemId,
                    CollectionFundTransferItemId = fundTransferRequestItemId,
                    CollectionAmount = p.PaidAmount,
                    CollectionDate = p.CreateDate.ToUniversalTime(),
                };

                movementPerTransfer.AddEntityBasicInfo();

                collectionMovements.Add(movementPerTransfer);
            }

            await _finMongoDbRepository.InsertManyAsync(collectionMovements);
        }

        private async Task SendFundTransferSubmissionNotificationAsync(CollectionFundTransferPostProcessingEvent command, SecurityContext securityContext)
        {
            var collectionFundTransferRequest = await GetCollectionFundTransferRequestAsync(command);
            var branchAccountantUserId = await GetUserIdOfAnEmployeeAsync(collectionFundTransferRequest.BranchAccountantEmployeeInformation.EmployeeItemId);
            var AssignedByEmployee = await GetCurrentEmployeeInformationAsync();

            var responseKey = BuroNotificationKeys.CollectionFundTransferCompleted;

            var payload = new
            {
                command.CollectionFundTransferItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.UserId,
                AssignedByEmployeeName = AssignedByEmployee.EmployeeName,
                AssignedByEmployeePin = AssignedByEmployee.EmployeePin,
                AssignedByEmployeeOfficeName = AssignedByEmployee.EmployeeOfficeName,
                Amount = collectionFundTransferRequest.CollectedAmount
            };

            await _sharedService.NotifyUserAsync(responseKey, payload, command.CollectionFundTransferItemId, branchAccountantUserId);
        }

        private async Task<string> GetUserIdOfAnEmployeeAsync(string employeeItemId)
        {
            var userIds = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, string>(
                dataFilters: e => e.ItemId == employeeItemId,
                projectionExpression: e => e.UserItemId);

            return userIds.FirstOrDefault() ?? string.Empty;
        }

        private async Task<BuroCollectionFundTransfer> GetCollectionFundTransferRequestAsync(CollectionFundTransferPostProcessingEvent command)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroCollectionFundTransfer>(t => t.ItemId == command.CollectionFundTransferItemId);
        }

        private async Task UpdateCollectionStatusAsync(List<string> collectionItemIds, string userId)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMemberAccountPayment.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroMemberAccountPayment.LastUpdatedBy), userId },
                {nameof(BuroMemberAccountPayment.IsSubmitted), true}
            };

            await _finMongoDbRepository.UpdateManyAsync<BuroMemberAccountPayment>(p => collectionItemIds.Contains(p.ItemId), properties);
        }
    }
}
