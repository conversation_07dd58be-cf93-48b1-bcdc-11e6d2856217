using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Collection;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Collection
{
    public class CollectionFundTransferRevertApprovalEventService : ICollectionFundTransferRevertApprovalEventService
    {
        private readonly ILogger<CollectionFundTransferRevertApprovalEventService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;
        private readonly IServiceClient _serviceClient;

        public CollectionFundTransferRevertApprovalEventService(
            ILogger<CollectionFundTransferRevertApprovalEventService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider,
            IInfrastructureSharedService sharedService,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
            _serviceClient = serviceClient;
        }

        public async Task RevertCollectionFundTransferAsync(CollectionFundTransferRevertApprovalEvent command)
        {
            _logger.LogInformation("Starting RevertCollectionFundTransferAsync for transfer Id: {CollectionFundTransferItemId}", command.CollectionFundTransferItemId);

            var collectionFundTransfer = await _finMongoDbRepository.FindOneAsync<BuroCollectionFundTransfer>(t => t.ItemId == command.CollectionFundTransferItemId);
            var programOrganizer = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.ItemId == collectionFundTransfer.ProgramOrganizerEmployeeInformation.EmployeeItemId);

            if (command.IsAccepted)
            {
                await RevertCollectionTransferAsync(command, programOrganizer, collectionFundTransfer);
            }
            else
            {
                await SendNotificationToProgramOrganizerAsync(command, programOrganizer, BuroNotificationKeys.CollectionFundTransferRevertBranchManagerRejected);
            }

            _logger.LogInformation("Successfully processed approval for revert request of collection transfer with Id: {TransactionItemId}", command.CollectionFundTransferItemId);
        }

        private async Task RevertCollectionTransferAsync(
            CollectionFundTransferRevertApprovalEvent command,
            BuroEmployee programOrganizer,
            BuroCollectionFundTransfer collectionFundTransfer)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            // NOSONAR create another task for resubmission to PO

            await Task.WhenAll(
                RevertCollectionMovementOfATransferAsync(command, securityContext),
                RevertAccountPaymentAsync(command, securityContext),
                RevertFundTransferAsync(command, securityContext)
            );

            await SendNotificationToProgramOrganizerAsync(command, programOrganizer, BuroNotificationKeys.CollectionFundTransferRevertBranchManagerReverted);

            SendToHandleCollectionFundTransferEvent(collectionFundTransfer.BranchOfficeItemId, collectionFundTransfer.CollectedAmount, false);
        }

        private async Task RevertAccountPaymentAsync(CollectionFundTransferRevertApprovalEvent command, SecurityContext securityContext)
        {
            var collectionItemIds = await _finMongoDbRepository.FindWithProjectionAsync<BuroCollectionMovementPerTransfer, string>(
                t => t.CollectionFundTransferItemId == command.CollectionFundTransferItemId,
                t => t.MemberSchedulePaymentItemId);

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMemberAccountPayment.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroMemberAccountPayment.LastUpdatedBy), securityContext.UserId },
                {nameof(BuroMemberAccountPayment.IsSubmitted), false}
            };

            await _finMongoDbRepository.UpdateManyAsync<BuroMemberAccountPayment>(p => collectionItemIds.Contains(p.ItemId), properties);
        }

        private async Task RevertFundTransferAsync(CollectionFundTransferRevertApprovalEvent command, SecurityContext securityContext)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCollectionFundTransfer.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroCollectionFundTransfer.LastUpdatedBy), securityContext.UserId },
                {nameof(BuroCollectionFundTransfer.Status), EnumCollectionFundTransferStatus.Reverted }
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroCollectionFundTransfer>(t => t.ItemId == command.CollectionFundTransferItemId, properties);
        }

        private async Task RevertCollectionMovementOfATransferAsync(CollectionFundTransferRevertApprovalEvent command, SecurityContext securityContext)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCollectionMovementPerTransfer.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroCollectionMovementPerTransfer.LastUpdatedBy), securityContext.UserId },
                {nameof(BuroCollectionMovementPerTransfer.IsMarkedToDelete), true },
            };

            await _finMongoDbRepository.UpdateManyAsync<BuroCollectionMovementPerTransfer>(t => t.CollectionFundTransferItemId == command.CollectionFundTransferItemId, properties);
        }

        private void SendToHandleCollectionFundTransferEvent(string branchOfficeItemId, double amount, bool shouldIncreaseAmount)
        {
            var payload = new AdjustCashInHandBalanceEvent
            {
                BranchOfficeItemId = branchOfficeItemId,
                Amount = amount,
                ShouldIncreaseAmount = shouldIncreaseAmount
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("Send to adjust cash in hand balance of branch with id: {BranchOfficeItemId}", branchOfficeItemId);
        }

        private async Task SendNotificationToProgramOrganizerAsync(CollectionFundTransferRevertApprovalEvent command, BuroEmployee programOrganizer, string responseKey)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var notificationPayload = new
            {
                command.CollectionFundTransferItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                command.CollectionFundTransferItemId,
                programOrganizer.UserItemId);
        }
    }
}
