using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Events.Holiday;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Holiday;

public class LoanMemberScheduleHolidayAdjustmentService : ILoanMemberScheduleHolidayAdjustmentService
{
    private readonly ILogger<LoanMemberScheduleHolidayAdjustmentService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IWorkingDayService _workingDayService;
    private readonly IFinMongoDbRepository _finMongoDbRepository;

    public LoanMemberScheduleHolidayAdjustmentService(
        ILogger<LoanMemberScheduleHolidayAdjustmentService> logger,
        ISecurityContextProvider securityContextProvider,
        IWorkingDayService workingDayService,
        IFinMongoDbRepository finMongoDbRepository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _workingDayService = workingDayService;
        _finMongoDbRepository = finMongoDbRepository;
    }

    public async Task AdjustLoanMemberSchedule(HolidayCreationEvent command, BuroMember member)
    {
        var startDate = command.HolidayStartDate!.Value.Date;
        var endDate = command.HolidayEndDate!.Value.Date;
        var securityContext = _securityContextProvider.GetSecurityContext();

        var loanAccounts = await _finMongoDbRepository.FindAsync<BuroLoanAccount>(
            x => x.MemberItemId == member.ItemId);

        var holidayHashSet = await _workingDayService.GetHolidaySetAsync(DateTime.UtcNow,
            member.CenterItemId, member.BranchOfficeItemId, member.ItemId);

        foreach (var account in loanAccounts)
        {
            _logger.LogInformation("Adjusting loan schedule for account {AccountId}", account.ItemId);

            var affectedSchedules = await GetAffectedSchedulesAsync(account.ItemId, startDate);
            if (!affectedSchedules.Any())
            {
                _logger.LogInformation("No schedules to update for account {AccountId}", account.ItemId);
                continue;
            }

            var nextWorkingDay = await GetNextWorkingDayAsync(endDate, holidayHashSet);
            await UpdateSchedulesAsync(affectedSchedules, nextWorkingDay, securityContext.UserId);

            _logger.LogInformation("Updated {Count} schedules for account {AccountId} to date {NewDate}",
                affectedSchedules.Count, account.ItemId, nextWorkingDay.ToShortDateString());
        }
    }

    private async Task<List<BuroMemberSchedule>> GetAffectedSchedulesAsync(string accountItemId, DateTime startDate)
    {
        var filter = Builders<BuroMemberSchedule>.Filter.And(
            Builders<BuroMemberSchedule>.Filter.Eq(s => s.ProductType, EnumProductType.ContractualSaving),
            Builders<BuroMemberSchedule>.Filter.Eq(s => s.PaymentInterval, EnumPaymentInterval.Weekly),
            Builders<BuroMemberSchedule>.Filter.Eq(s => s.AccountItemId, accountItemId),
            Builders<BuroMemberSchedule>.Filter.Gte(s => s.ActualPaymentDate, startDate)
        );

        return (await _finMongoDbRepository.FindAsync(filter))
            .OrderBy(s => s.ActualInstallmentNumber)
            .ToList();
    }

    private async Task<DateTime> GetNextWorkingDayAsync(DateTime current, HashSet<DateTime> holidayHashSet)
    {
        var nextDate = current.Date.AddDays(1);

        while (await _workingDayService.IsWeekendAsync(nextDate) ||
               await _workingDayService.IsHolidayAsync(nextDate, holidayHashSet))
        {
            _logger.LogInformation("Skipping non-working day: {Date}", nextDate.ToShortDateString());
            nextDate = nextDate.AddDays(1);
        }

        return nextDate;
    }

    private async Task UpdateSchedulesAsync(List<BuroMemberSchedule> schedules, DateTime newPaymentDate, string userId)
    {
        var ids = schedules.Select(x => x.ItemId).ToList();

        var updates = new Dictionary<string, object>
        {
            [nameof(BuroMemberSchedule.LastUpdateDate)] = DateTime.UtcNow,
            [nameof(BuroMemberSchedule.LastUpdatedBy)] = userId,
            [nameof(BuroMemberSchedule.ActualPaymentDate)] = newPaymentDate
        };

        await _finMongoDbRepository.UpdateManyAsync<BuroMemberSchedule>(
            x => ids.Contains(x.ItemId), updates);
    }
}