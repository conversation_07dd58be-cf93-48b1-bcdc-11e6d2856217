using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Events.Holiday;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace EventHandlers.Services.Holiday
{
    public class HolidayCreationEventService : IHolidayCreationEventService
    {
        private readonly ILogger<HolidayCreationEventService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ICSMemberScheduleHolidayAdjustmentService _cSMemberScheduleHolidayAdjustmentService;
        private readonly ILoanMemberScheduleHolidayAdjustmentService _loanMemberScheduleHolidayAdjustmentService;

        public HolidayCreationEventService(
            ILogger<HolidayCreationEventService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ICSMemberScheduleHolidayAdjustmentService cSMemberScheduleHolidayAdjustmentService,
            ILoanMemberScheduleHolidayAdjustmentService loanMemberScheduleHolidayAdjustmentService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _cSMemberScheduleHolidayAdjustmentService = cSMemberScheduleHolidayAdjustmentService;
            _loanMemberScheduleHolidayAdjustmentService = loanMemberScheduleHolidayAdjustmentService;
        }
        public async Task ApplyHolidayCreationImpactAsync(HolidayCreationEvent command)
        {
            try
            {
                int pageIndex = 0;
                const int pageSize = 100;

                var hidHashes = await GetOfficeHidsAsync(command);

                while (true)
                {
                    var affectedMembers = await GetPaginatedAffectedMembersAsync(command, pageIndex, pageSize, hidHashes.ToList());

                    if (!affectedMembers.Any()) break;

                    foreach (var member in affectedMembers)
                    {
                        await _cSMemberScheduleHolidayAdjustmentService.AdjustCSMemberSchedule(command, member);
                        await _loanMemberScheduleHolidayAdjustmentService.AdjustLoanMemberSchedule(command, member);
                    }

                    if (affectedMembers.Count < pageSize)
                        break;

                    pageIndex++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while applying impact of holiday creation on cs and loan schedules ");
            }
        }

        private async Task<HashSet<long>> GetOfficeHidsAsync(HolidayCreationEvent command)
        {
            if (command.OfficeItemIds == null || command.HolidayType != EnumHolidayType.OfficeSpecific)
                return new HashSet<long>();

            var offices = await _finMongoDbRepository.FindAsync<BuroOffice>(x => command.OfficeItemIds.Contains(x.ItemId));
            return offices.Select(x => x.Hid).ToHashSet();
        }

        private async Task<List<BuroMember>> GetPaginatedAffectedMembersAsync(HolidayCreationEvent command, int pageIndex, int pageSize, List<long> hidList)
        {
            _logger.LogInformation("Fetching affected members.");

            var builder = Builders<BuroMember>.Filter;
            FilterDefinition<BuroMember> filter = builder.Empty;

            switch (command.HolidayType)
            {
                case EnumHolidayType.Personal:
                    filter = builder.Eq(x => x.ItemId, command.MemberItemId);
                    break;
                case EnumHolidayType.CenterSpecific:
                    filter = builder.In(x => x.ItemId, command.CenterItemIds);
                    break;
                case EnumHolidayType.OfficeSpecific:
                    filter = builder.AnyIn(x => x.Path, hidList);
                    break;
            }

            var options = new FindOptions<BuroMember>
            {
                Skip = pageIndex * pageSize,
                Limit = pageSize
            };

            return await _finMongoDbRepository.FindWithOptionAsync(filter, options);
        }
    }
}
