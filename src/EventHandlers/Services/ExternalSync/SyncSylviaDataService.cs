using EventHandlers.Contracts;
using Infrastructure.Contracts;
using Infrastructure.Models.ExternalSync;
using Infrastructure.Models.ExternalSync.Responses;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Text.RegularExpressions;

namespace EventHandlers.Services.ExternalSync
{
    public class SyncSylviaDataService : ISyncSylviaDataService
    {
        private readonly IBusinessRepository _repository;
        private readonly ISylviaClient _sylviaClient;
        private readonly ILogger<SyncSylviaDataService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        public SyncSylviaDataService(IBusinessRepository repository, ISylviaClient sylviaClient, ILogger<SyncSylviaDataService> logger, ISecurityContextProvider securityContextProvider)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
            _sylviaClient = sylviaClient ?? throw new ArgumentNullException(nameof(sylviaClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _securityContextProvider = securityContextProvider ?? throw new ArgumentNullException(nameof(securityContextProvider));
        }

        public async Task<bool> UpdateExternalRequestStatus(string requestItemId, EnumnExternalSyncStatus syncStatus)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var loggedInUserId = securityContext.UserId;

            var filter = Builders<BuroExternalSyncRequest>.Filter.Eq(x => x.ItemId, requestItemId);
            var update = Builders<BuroExternalSyncRequest>.Update
                .Set(x => x.LastUpdateDate, DateTime.UtcNow)
                .Set(x => x.LastUpdatedBy, loggedInUserId)
                .Set(x => x.DataSyncStatus, syncStatus);

            await _repository.UpdateOneAsync<BuroExternalSyncRequest>(filter, update);
            return true;
        }

        public async Task<bool> SaveOffices(string requestItemId, List<SylviaOfficeData> officeList)
        {
            List<BuroSylviaOffice> buroSylviaOfficeList = new();
            foreach (var office in officeList)
            {
                buroSylviaOfficeList.Add(new BuroSylviaOffice()
                {
                    ItemId = Guid.NewGuid().ToString(),
                    Address = office.Address,
                    AreaOfficeName = string.IsNullOrWhiteSpace(office.AreaOfficeName) ? null : Regex.Replace(office.AreaOfficeName, @"\s{2,}", " ").Trim(),
                    ClosingDate = office.ClosingDate,
                    DistrictName = office.DistrictName,
                    DivisionName = office.DivisionName,
                    DivisionOfficeName = string.IsNullOrWhiteSpace(office.DivisionOfficeName) ? null : Regex.Replace(office.DivisionOfficeName, @"\s{2,}", " ").Trim(),
                    GpsLocation = office.GpsLocation,
                    IsActive = office.IsActive,
                    NumberOfEmployee = office.NumberOfEmployee,
                    OfficeCode = office.OfficeCode,
                    OfficeEmail = office.OfficeEmail,
                    OfficeMobileNumber = office.OfficeMobileNumber,
                    OfficeName = office.OfficeName,
                    OfficeType = office.OfficeType,
                    OpeningDate = office.OpeningDate,
                    ThanaName = office.ThanaName,
                    ZoneOfficeName = string.IsNullOrWhiteSpace(office.ZoneOfficeName) ? null : Regex.Replace(office.ZoneOfficeName, @"\s{2,}", " ").Trim(),
                    RequestItemId = requestItemId
                });
            }

            buroSylviaOfficeList.ForEach(office => office.ParentOfficeCode = getSylviaOfficeParentOfficeCode(office, buroSylviaOfficeList));

            await _repository.InsertManyAsync<BuroSylviaOffice>(buroSylviaOfficeList);
            return true;
        }

        public async Task<bool> ImportOfficeListFromSylvia(string requestItemId)
        {
            _logger.LogInformation("SyncSylviaDataService->ImportOfficeListFromSylvia->START");

            int pageNo = 1;
            int pageSize = 1000;
            bool isErrorWhileParsing = false;

            // Parse office
            bool isFetchNextPage = true;
            List<SylviaOfficeData> sylviaOffieList = new();
            await this._repository.DeleteAsync<BuroSylviaOffice>(r => r != null);
            while (isFetchNextPage && !isErrorWhileParsing)
            {
                SylviaGetOfficesResponse result = await _sylviaClient.GetOffices(pageNo, pageSize);
                if (!result.Success)
                {
                    _logger.LogInformation($"SyncSylviaDataService->ImportOfficeListFromSylvia->Failed GetOffices PageNo:{pageNo} PageSize: {pageSize}");
                    isErrorWhileParsing = true;
                    return false;
                }

                sylviaOffieList.AddRange(result.Data);

                if (result.Data.Count != pageSize)
                {
                    isFetchNextPage = false;
                }

                pageNo++;
            }

            await this.SaveOffices(requestItemId, sylviaOffieList);
            _logger.LogInformation("SyncSylviaDataService->ImportOfficeListFromSylvia->END");
            return true;
        }

        private async Task<List<BuroSylviaOffice>> GetBuroSylviaOfficeList(int pageNumber, int pageSize)
        {
            var findOptions = new FindOptions<BuroSylviaOffice, BuroSylviaOffice>
            {
                Limit = pageSize,
                Skip = (pageSize * pageNumber)
            };
            var cursor = await _repository.GetCollection<BuroSylviaOffice>().FindAsync(r => r.OfficeCode != null, findOptions);
            return await cursor.ToListAsync();
        }

        private async Task<bool> ProcessEmployeeForOffice(string requestItemId, BuroSylviaOffice sylviaOffice)
        {
            _logger.LogInformation("SyncSylviaDataService->ProcessEmployeeForOffice->START");

            List<BuroSylviaEmployee> buroSylviaemployeeList = new();
            SylviaGetOfficeEmployeesResponse response = await _sylviaClient.GetOfficeEmployees(sylviaOffice.OfficeCode);
            if (!response.Success)
            {
                _logger.LogInformation("SyncSylviaDataService->ProcessEmployeeForOffice->GetOfficeEmployees failed officeCode:", sylviaOffice.OfficeCode);
                return false;
            }

            foreach (var emplooyee in response.Data)
            {
                buroSylviaemployeeList.Add(new BuroSylviaEmployee
                {
                    ItemId = Guid.NewGuid().ToString(),
                    ConfirmDate = emplooyee.ConfirmDate,
                    DesignationName = emplooyee.DesignationName,
                    EmployeeName = emplooyee.EmployeeName,
                    EmployeeStatus = emplooyee.EmployeeStatus,
                    EmployeeType = emplooyee.EmployeeType,
                    GradeName = emplooyee.GradeName,
                    JoiningDate = emplooyee.JoiningDate,
                    LiabilitiesName = emplooyee.LiabilitiesName,
                    WorkstationName = emplooyee.WorkstationName,
                    WorkstationCode = emplooyee.WorkstationCode,
                    Pin = emplooyee.Pin,
                    OfficeCode = sylviaOffice.OfficeCode,
                    RequestItemId = requestItemId
                });
            }
            await _repository.DeleteAsync<BuroSylviaEmployee>(r => r.OfficeCode == sylviaOffice.OfficeCode);
            await _repository.InsertManyAsync<BuroSylviaEmployee>(buroSylviaemployeeList);

            return true;
        }

        private string getSylviaOfficeParentOfficeCode(BuroSylviaOffice currentOffice, List<BuroSylviaOffice> sylviaOfficeList)
        {
            string parentOfficeCode = string.Empty;

            if (currentOffice.OfficeType == "BRANCH")
            {
                string parentOfficeName = currentOffice.AreaOfficeName?.Trim();
                parentOfficeCode = sylviaOfficeList.Where(x => x.OfficeName == parentOfficeName).Select(r => r.OfficeCode).FirstOrDefault();
            }
            if (currentOffice.OfficeType == "AREA")
            {
                string parentOfficeName = currentOffice.ZoneOfficeName?.Trim();
                parentOfficeCode = sylviaOfficeList.Where(x => x.OfficeName == parentOfficeName).Select(r => r.OfficeCode).FirstOrDefault();
            }
            if (currentOffice.OfficeType == "ZONE")
            {
                string parentOfficeName = currentOffice.DivisionOfficeName?.Trim();
                parentOfficeCode = sylviaOfficeList.Where(x => x.OfficeName == parentOfficeName).Select(r => r.OfficeCode).FirstOrDefault();
            }
            if (currentOffice.OfficeType == "DIVISION")
            {
                parentOfficeCode = "0645";
            }

            return parentOfficeCode;
        }

        public async Task<bool> ImportEmployeeListFromSylvia(string requestItemId)
        {
            _logger.LogInformation("SyncSylviaDataService->ImportEmployeeListFromSylvia->START");

            bool isErrorWhileParsing = false;
            int pageSize = 100;
            long parsedTotal = 0;
            int pageNumber = 0;
            long totalOfficeCount = _repository.GetCollection<BuroSylviaOffice>().CountDocuments(r => r.OfficeCode != null);

            while (parsedTotal < totalOfficeCount)
            {
                var sylviaOfficeList = await GetBuroSylviaOfficeList(pageNumber, pageSize);
                foreach (var sylviaOffice in sylviaOfficeList)
                {
                    bool isSuccess = await ProcessEmployeeForOffice(requestItemId, sylviaOffice);
                    if (!isSuccess)
                    {
                        isErrorWhileParsing = true;
                        break;
                    }
                }

                pageNumber++;
                parsedTotal += pageSize;
            }

            if (isErrorWhileParsing)
            {
                return false;
            }


            return true;
        }

    }
}
