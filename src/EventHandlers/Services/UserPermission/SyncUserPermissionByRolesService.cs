using EventHandlers.Contracts;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.UserPermission;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.UserPermission;

public class SyncUserPermissionByRolesService : ISyncUserPermissionByRolesService
{
    private readonly ILogger<SyncUserPermissionByRolesService> _logger;
    private readonly IRepository _repository;
    private readonly IBusinessRepository _businessRepository;
    private readonly IBuroKeyStore _keyStore;

    public SyncUserPermissionByRolesService(ILogger<SyncUserPermissionByRolesService> logger,
        IRepository repository,
        IBusinessRepository businessRepository,
        IBuroKeyStore keyStore)
    {
        _logger = logger;
        _repository = repository;
        _businessRepository = businessRepository;
        _keyStore = keyStore;
    }

    public async Task<CommandResponse> SyncUserPermissionByRoles(SyncUserPermissionByRolesEvent command)
    {
        _logger.LogInformation("Inside SyncUserPermissionByRoles...");
        var commandResponse = new CommandResponse();
        try
        {
            if (command.RoleKeys.Count == 0)
            {
                _logger.LogInformation("Role keys are required");
                return commandResponse;
            }

            var users = await GetUsersByRolesAsync(command.RoleKeys);
            var userIds = users.Select(x => x.ItemId).ToList();
            if (!userIds.Any())
            {
                _logger.LogInformation("No users found");
                return commandResponse;
            }

            var featureRoleMaps = await GetBuroFeatureRoleMaps(command.RoleKeys);

            var userPermissions = await GetUserPermissionByUserIds(userIds);

            foreach (var userPermission in userPermissions)
            {
                _logger.LogInformation("Syncing user permission for {UserId}", userPermission.UserItemId);

                var user = users.FirstOrDefault(x => x.ItemId == userPermission.UserItemId);

                var featuresFromRoleMapWithoutRestriction = GetFeaturesFromRoleMap(featureRoleMaps, user);
                var featuresWithRestriction = GetFeaturesWithRestriction(featureRoleMaps, user);
                var roleMapFeatureItemIds = new HashSet<string>(featuresFromRoleMapWithoutRestriction.Select(f => f.ItemId));

                var customFeaturesWithoutRestriction = GetCustomFeaturesWithoutRestriction(userPermission, featuresWithRestriction);
                var defaultFeatures = GetDefaultFeatureForPermission(featuresFromRoleMapWithoutRestriction);

                var updatedFeatures = new List<FeatureForPermission>(defaultFeatures);
                updatedFeatures.AddRange(customFeaturesWithoutRestriction);

                var isCustom = updatedFeatures.Any(f => !roleMapFeatureItemIds.Contains(f.ItemId));

                await UpdateUserPermissionAsync(userPermission.ItemId, user.Roles, isCustom, updatedFeatures);

                await RemoveUserPermissionKeyAsync(user.ItemId);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error in SyncUserPermissionByRoles");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }
        return commandResponse;
    }

    private async Task RemoveUserPermissionKeyAsync(string userItemId)
    {
        var key = $"{userItemId}{BuroConstants.PermissionSuffix}";
        await _keyStore.RemoveKeyAsync(key);
    }

    private async Task UpdateUserPermissionAsync(string userPermissionId, string[] roles, bool isCustom, List<FeatureForPermission> updatedFeatures)
    {
        var updatedProperties = UpdatedProperties(updatedFeatures, roles, isCustom);
        await _repository.UpdateAsync<BuroUserPermission>(
            x => x.ItemId == userPermissionId,
            updatedProperties
        );
    }

    private Dictionary<string, object> UpdatedProperties(List<FeatureForPermission> updatedFeatures, string[] roles, bool isCustom)
    {
        var features = updatedFeatures.Select(x => new BsonDocument
        {
            { nameof(FeatureForPermission.ItemId), x.ItemId },
            { nameof(FeatureForPermission.FeatureName), x.FeatureName },
            { nameof(FeatureForPermission.FeaturePath), x.FeaturePath },
            { nameof(FeatureForPermission.ApiPath), x.ApiPath },
            { nameof(FeatureForPermission.IsCustomFeature), x.IsCustomFeature }
        }).ToList();

        var properties = new Dictionary<string, object>
        {
            { nameof(BuroUserPermission.Features), features },
            { nameof(BuroUserPermission.Roles), roles },
            { nameof(BuroUserPermission.IsCustom), isCustom },
            { nameof(BuroUserPermission.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroUserPermission.LastUpdatedBy), BuroConstants.LastUpdatedBy }
        };

        return properties;
    }

    private List<FeatureForRoleMap> GetFeaturesFromRoleMap(List<BuroFeatureRoleMap> featureRoleMaps, User user)
    {
        var features = featureRoleMaps
            .Where(x => user.Roles.Contains(x.RoleKey))
            .SelectMany(x => x.Features)
            .Where(x => x.FeatureStatus != EnumFeatureStatus.Restricted)
            .ToList();

        return features;
    }

    private List<FeatureForRoleMap> GetFeaturesWithRestriction(List<BuroFeatureRoleMap> featureRoleMaps, User user)
    {
        var features = featureRoleMaps
            .Where(x => user.Roles.Contains(x.RoleKey))
            .SelectMany(x => x.Features)
            .ToList();
        return features;
    }

    private List<FeatureForPermission> GetDefaultFeatureForPermission(List<FeatureForRoleMap> features)
    {
        var featuresForPermission = features.Select(x => new FeatureForPermission
        {
            ItemId = x.ItemId,
            FeatureName = x.FeatureName,
            FeaturePath = x.FeaturePath,
            ApiPath = x.ApiPath,
            IsCustomFeature = false
        }).ToList();
        return featuresForPermission;
    }

    private List<FeatureForPermission> GetCustomFeaturesWithoutRestriction(BuroUserPermission userPermission,
        List<FeatureForRoleMap> featuresFromRoleMap)
    {
        /*var featureForPermissions = userPermission.Features
            .Where(p => p.IsCustomFeature &&
                        !featuresFromRoleMap.Any(fm =>
                            fm.ItemId == p.ItemId && fm.FeatureStatus == EnumFeatureStatus.Restricted)).ToList();*/

        var featureForPermissions = userPermission.Features
            .Where(p => p.IsCustomFeature && featuresFromRoleMap.All(fm => fm.ItemId != p.ItemId)).ToList();

        return featureForPermissions;
    }


    private async Task<List<User>> GetUsersByRolesAsync(List<string> givenRoleKeys)
    {
        var users = await _businessRepository.GetItemsAsync<User>(user =>
            user.Roles.Any(role => givenRoleKeys.Contains(role)));
        return users;
    }

    private async Task<List<BuroUserPermission>> GetUserPermissionByUserIds(List<string> userIds)
    {
        var collection = _businessRepository.GetCollection<BuroUserPermission>();
        var filter = Builders<BuroUserPermission>.Filter.In(x => x.UserItemId, userIds);
        return await collection.Find(filter).ToListAsync();
    }

    private async Task<List<BuroFeatureRoleMap>> GetBuroFeatureRoleMaps(List<string> roleKeys)
    {
        var collection = _businessRepository.GetCollection<BuroFeatureRoleMap>();
        var filter = Builders<BuroFeatureRoleMap>.Filter.In(x => x.RoleKey, roleKeys);
        return await collection.Find(filter).ToListAsync();
    }
}