using EventHandlers.Contracts;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.UserPermission;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.UserPermission;

public class AssignUserPermissionService : IAssignUserPermissionService
{
    private readonly ILogger<AssignUserPermissionService> _logger;
    private readonly IBusinessRepository _businessRepository;
    private readonly IRepository _repository;
    private readonly IInfrastructureSharedService _commonService;
    private readonly ISecurityContextProvider _contextProvider;
    private readonly IBuroKeyStore _keyStore;

    public AssignUserPermissionService(ILogger<AssignUserPermissionService> logger,
        IBusinessRepository businessRepository,
        IRepository repository,
        IInfrastructureSharedService commonService,
        ISecurityContextProvider contextProvider,
        IBuroKeyStore keyStore)
    {
        _logger = logger;
        _businessRepository = businessRepository;
        _repository = repository;
        _commonService = commonService;
        _contextProvider = contextProvider;
        _keyStore = keyStore;
    }

    public async Task<CommandResponse> AssignUserPermission(AssignUserPermissionEvent userPermissionEvent)
    {
        _logger.LogInformation("Inside AssignUserPermission");
        var commandResponse = new CommandResponse();
        try
        {
            var userList = await GetUserList(userPermissionEvent.UserItemIds);

            foreach (var userItemId in userPermissionEvent.UserItemIds)
            {
                var user = userList.FirstOrDefault(x => x.ItemId == userItemId);
                if (user == null)
                {
                    _logger.LogInformation("User {UserId} does not exist", userItemId);
                    commandResponse.SetError(BuroErrorMessageKeys.UserInvalid, "User does not exist");
                    return commandResponse;
                }

                if (user.Roles.Length == 0)
                {
                    _logger.LogInformation("User {UserId} does not have any roles", userItemId);
                    await DeleteExistingUserPermissions(user.ItemId);
                    _logger.LogInformation("UserPermission with {UserId} has been deleted", userItemId);
                    continue;
                }

                var features = await GetFeaturesForRoles(user.Roles);

                await SaveUserPermissions(userItemId, user.Roles, features);
                _logger.LogInformation("UserPermission with {UserId} has been saved", userItemId);

                await RemoveUserPermissionKeyAsync(userItemId);
            }

            await SendCompletionNotification();
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error in AssignUserPermission");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }
        return commandResponse;
    }

    private async Task RemoveUserPermissionKeyAsync(string userItemId)
    {
        var key = $"{userItemId}{BuroConstants.PermissionSuffix}";
        await _keyStore.RemoveKeyAsync(key);
    }

    private async Task SendCompletionNotification()
    {
        var context = _contextProvider.GetSecurityContext();
        var userId = context.UserId;
        var payload = new
        {
            AssignedAt = DateTime.UtcNow,
            AssignedBy = context.UserName
        };
        await _commonService.NotifyUserAsync(BuroNotificationKeys.UserPermissionUpdated, payload, "", userId);
    }

    private async Task<List<User>> GetUserList(List<string> userItemIds)
    {
        return await _businessRepository.GetItemsAsync<User>(item => userItemIds.Contains(item.ItemId));
    }

    private async Task<List<FeatureForRoleMap>> GetFeaturesForRoles(string[] userRoleKeys)
    {
        var roles = await GetRolesByRoleKeys(userRoleKeys);
        var roleItemIds = roles.Select(x => x.ItemId).ToList();
        var featureRoleMapList = await GetFeatureRoleMapsByRoleIds(roleItemIds);
        var features = featureRoleMapList
            .SelectMany(x => x.Features)
            .Where(x => x.FeatureStatus != EnumFeatureStatus.Restricted)
            .ToList();
        return features;
    }

    private async Task<List<BuroRole>> GetRolesByRoleKeys(string[] userRoleKeys)
    {
        var collection = _businessRepository.GetCollection<BuroRole>();
        var filter = Builders<BuroRole>.Filter.In(x => x.RoleKey, userRoleKeys);
        return await collection.Find(filter).ToListAsync();
    }

    private async Task<List<BuroFeatureRoleMap>> GetFeatureRoleMapsByRoleIds(List<string> roleIds)
    {
        var collection = _businessRepository.GetCollection<BuroFeatureRoleMap>();
        var filter = Builders<BuroFeatureRoleMap>.Filter.In(x => x.RoleItemId, roleIds);
        return await collection.Find(filter).ToListAsync();
    }

    private BuroUserPermission PopulateUserPermission(string userItemId, string[] roles, List<FeatureForRoleMap> features)
    {
        var userPermission = new BuroUserPermission
        {
            ItemId = Guid.NewGuid().ToString(),
            UserItemId = userItemId,
            Roles = roles,
            Features = features.Select(x => new FeatureForPermission()
            {
                ItemId = x.ItemId,
                FeatureName = x.FeatureName,
                FeaturePath = x.FeaturePath,
                ApiPath = x.ApiPath,
                IsCustomFeature = false

            }).ToList()
        };
        return userPermission;
    }

    private async Task SaveUserPermissions(string userItemId, string[] roles, List<FeatureForRoleMap> features)
    {
        var userPermission = PopulateUserPermission(userItemId, roles, features);

        var isExistingUserPermission = await _repository.ExistsAsync<BuroUserPermission>(item => item.UserItemId == userItemId);

        if (isExistingUserPermission)
        {
            await DeleteExistingUserPermissions(userItemId);
        }

        await _repository.SaveAsync(userPermission);
    }

    private async Task DeleteExistingUserPermissions(string userItemId)
    {
        await _repository.DeleteAsync<BuroUserPermission>(item => item.UserItemId == userItemId);
    }
}