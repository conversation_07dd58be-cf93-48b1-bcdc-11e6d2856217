using EventHandlers.Contracts;
using Infrastructure.Contracts;
using Infrastructure.Events.Roles;
using Infrastructure.Events.UserPermission;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using System.Collections.Immutable;

namespace EventHandlers.Services.Role;

public class RoleAndRelatedDataDeleteService
{
    private readonly IBusinessRepository _businessRepository;
    private readonly IAssignUserPermissionService _assignUserPermissionService;
    private readonly ILogger<RoleAndRelatedDataDeleteService> _logger;

    public RoleAndRelatedDataDeleteService(ILogger<RoleAndRelatedDataDeleteService> logger,
        IBusinessRepository businessRepository,
        IAssignUserPermissionService assignUserPermissionService)
    {
        _logger = logger;
        _businessRepository = businessRepository;
        _assignUserPermissionService = assignUserPermissionService;
    }

    public async Task DeleteRoleAndRelatedDataAsync(RoleAndRelatedDataDeleteEvent command)
    {
        var users = await _businessRepository.GetItemsAsync<User>(item => item.Roles.Contains(command.RoleKey));
        var userIds = users.Select(x => x.ItemId).ToList();

        await _businessRepository.DeleteAsync<BuroRole>(item => item.RoleKey == command.RoleKey);

        await _businessRepository.DeleteAsync<BuroFeatureRoleMap>(item => item.RoleKey == command.RoleKey);

        //await RemoveRoleFromDesignationsAsync(command.RoleKey);

        await RemoveRoleFromUsersAsync(command.RoleKey);

        await AssignUserPermissionToUpdatedRoleAsync(userIds);
    }

    //private async Task RemoveRoleFromDesignationsAsync(string roleKey)
    //{
    //    var filter = Builders<BuroDesignation>.Filter.AnyEq(x => x.Roles, roleKey);
    //    var update = Builders<BuroDesignation>.Update.Pull(x => x.Roles, roleKey);
    //    await _businessRepository.UpdateManyAsync(filter, update);
    //}

    private async Task RemoveRoleFromUsersAsync(string roleKey)
    {
        var filter = Builders<User>.Filter.AnyEq(x => x.Roles, roleKey);
        var update = Builders<User>.Update.Pull(x => x.Roles, roleKey);
        await _businessRepository.UpdateManyAsync(filter, update);
    }

    private async Task AssignUserPermissionToUpdatedRoleAsync(List<string> userIds)
    {
        var command = new AssignUserPermissionEvent
        {
            UserItemIds = userIds
        };
        await _assignUserPermissionService.AssignUserPermission(command);
    }
}