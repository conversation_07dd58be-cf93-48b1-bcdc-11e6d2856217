using EventHandlers.Contracts;
using Infrastructure.Contracts;
using Infrastructure.Events.Roles;
using Infrastructure.Events.UserPermission;
using Microsoft.Extensions.Logging;
using Selise.Ecap.Entities.PrimaryEntities.PlatformDataService;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Role;

public class UserRoleUpdaterFromDesignationService
{
    private readonly IBusinessRepository _businessRepository;
    private readonly IAssignUserPermissionService _assignUserPermissionService;
    private readonly ILogger<UserRoleUpdaterFromDesignationService> _logger;
    private readonly IRepository _repository;

    public UserRoleUpdaterFromDesignationService(ILogger<UserRoleUpdaterFromDesignationService> logger,
        IRepository repository,
        IBusinessRepository businessRepository,
        IAssignUserPermissionService assignUserPermissionService)
    {
        _logger = logger;
        _repository = repository;
        _businessRepository = businessRepository;
        _assignUserPermissionService = assignUserPermissionService;
    }

    public async Task<CommandResponse> UpdateUserRolesFromDesignation(UserRolesUpdateEvent command)
    {
        var response = new CommandResponse();

        var role = await GetRoleByIdAsync(command.RoleItemId);
        if (role == null)
            return response;

        var designationIds = role.DesignationItemIds;
        var removedDesignationIds = command.RemovedDesignationIds;
        var allDesignationsIds = designationIds.Concat(removedDesignationIds).ToList();

        if (!allDesignationsIds.Any())
        {
            _logger.LogInformation("No designation found for role {RoleId}, and removedDesignation {designations}"
                , role.ItemId, command.RemovedDesignationIds.ToString());
            return response;
        }

        var allPersons = await GetPersonsByDesignationIdAsync(allDesignationsIds);
        var allUserIds = allPersons.Select(x => x.ProposedUserId).ToList();
        var userIds = GetUserIdsByDesignation(allPersons, designationIds);
        var removedUserIds = GetUserIdsByDesignation(allPersons, removedDesignationIds);


        if (!allUserIds.Any())
        {
            _logger.LogInformation("No users found for designation {DesignationIds}", allDesignationsIds.ToString());
            return response;
        }

        await ProcessUserRoleUpdatesAsync(userIds, GetRolesForUsers(role.RoleKey));
        await ProcessUserRoleUpdatesAsync(removedUserIds, GetRolesForRemovedUsers());
        await AssignRolesToUsersAsync(allUserIds);

        return response;

    }

    private async Task ProcessUserRoleUpdatesAsync(List<string> userIds, List<string> roles)
    {
        if (!userIds.Any())
            return;

        await UpdateUserRolesAsync(userIds, roles);
        await UpdateUserRoleMapForBlocksAsync(userIds, roles);
    }


    private List<string> GetRolesForUsers(string roleKey) =>
        new() { roleKey, BuiltInRoles.Anonymous, BuiltInRoles.Appuser };

    private List<string> GetRolesForRemovedUsers() =>
        new() { BuiltInRoles.Anonymous, BuiltInRoles.Appuser };

    private List<string> GetUserIdsByDesignation(IEnumerable<Person> persons, IEnumerable<string> designationIds)
    {
        return persons
            .Where(person => designationIds.Contains(person.Designation))
            .Select(person => person.ProposedUserId)
            .ToList();
    }


    private async Task<BuroRole> GetRoleByIdAsync(string roleItemId)
    {
        return await _repository.GetItemAsync<BuroRole>(item => item.ItemId == roleItemId);
    }


    private async Task<List<Person>> GetPersonsByDesignationIdAsync(List<string> designationIds)
    {
        return await _businessRepository.GetItemsAsync<Person>(item => designationIds.Contains(item.Designation));
    }

    private async Task UpdateUserRolesAsync(List<string> userIds, List<string> roles)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(User.Roles), roles }
        };

        await _repository.UpdateManyAsync<User>(item => userIds.Contains(item.ItemId), properties);
    }

    private async Task UpdateUserRoleMapForBlocksAsync(List<string> userIds, List<string> roles)
    {
        var userRoleMapList = new List<UserRoleMap>();

        userRoleMapList.AddRange(
            userIds.SelectMany(userId => roles.Select(role => new UserRoleMap
            {
                ItemId = Guid.NewGuid().ToString(),
                UserId = userId,
                RoleName = role
            }))
        );

        await _repository.DeleteAsync<UserRoleMap>(item => userIds.Contains(item.UserId));
        await _repository.SaveAsync(userRoleMapList);
    }

    private async Task AssignRolesToUsersAsync(List<string> userIds)
    {
        var assignUserPermissionEvent = new AssignUserPermissionEvent
        {
            UserItemIds = userIds
        };

        await _assignUserPermissionService.AssignUserPermission(assignUserPermissionEvent);
    }
}