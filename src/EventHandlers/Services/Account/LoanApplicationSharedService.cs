using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;

namespace EventHandlers.Services.Account
{
    public class LoanApplicationSharedService : ILoanApplicationSharedService
    {
        private readonly ILogger<LoanApplicationSharedService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;

        public LoanApplicationSharedService(
            ILogger<LoanApplicationSharedService> logger,
            IFinMongoDbRepository finMongoDbRepository)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
        }

        public async Task<BuroEmployee> GetNextLoanApproverAsync(double loanAmount, string templateItemId, BuroEmployee currentEmployee)
        {
            _logger.LogInformation("Starting GetLoanApproverAsync with loan amount: {LoanAmount} current employee id: {ItemId}", loanAmount, currentEmployee.ItemId);

            // NOSONAR WIP handle exceptional cases (advance rules) of approver

            var approvalLimits = await GetLoanApprovalLimitsByTemplateItemIdAsync(templateItemId);
            var officeLevel = GetLevelOfApprovalAccordingToOffice(approvalLimits, loanAmount, currentEmployee);

            _logger.LogInformation("Determined office level for next approver is: {OfficeLevel}", officeLevel.GetDisplayName());

            var (OfficeItemId, DesignationCode) = await GetOfficeItemIdAndDesignationOfNextApproverAsync(currentEmployee.CurrentOfficeItemId, officeLevel);

            _logger.LogInformation("Next approver's office item ID: {OfficeItemId}, Designation Code: {DesignationCode}", OfficeItemId, DesignationCode);

            var employee = await GetEmployeeByOfficeForSpecificDesignation(OfficeItemId, DesignationCode);

            return employee;
        }

        private static EnumLevelOfOffice GetLevelOfApprovalAccordingToOffice(
            LoanApprovalLimits approvalLimits,
            double loanAmount,
            BuroEmployee currentEmployee)
        {
            var officeLevel = EnumLevelOfOffice.None;

            if (currentEmployee.DesignationCode == BuroDesignationConstant.BranchAccountantDesignationCode
                && loanAmount.CompareTo(approvalLimits.BranchAccountant) > 0)
            {
                officeLevel = EnumLevelOfOffice.BranchOffice;
            }
            else if (currentEmployee.DesignationCode == BuroDesignationConstant.BranchManagerDesignationCode
                && loanAmount.CompareTo(approvalLimits.BranchManager) > 0)
            {
                officeLevel = EnumLevelOfOffice.AreaOffice;
            }
            else if (currentEmployee.DesignationCode == BuroDesignationConstant.AreaManagerDesignationCode
                && loanAmount.CompareTo(approvalLimits.AreaManager) > 0)
            {
                officeLevel = EnumLevelOfOffice.ZoneOffice;
            }
            else if (currentEmployee.DesignationCode == BuroDesignationConstant.ZoneManagerDesignationCode
                && loanAmount.CompareTo(approvalLimits.ZoneManager) > 0)
            {
                officeLevel = EnumLevelOfOffice.DivisionOffice;
            }
            else if (currentEmployee.DesignationCode == BuroDesignationConstant.DivisionManagerDesignationCode
                && loanAmount.CompareTo(approvalLimits.DivisionManager) > 0)
            {
                officeLevel = EnumLevelOfOffice.HeadOffice;
            }

            return officeLevel;
        }

        private async Task<LoanApprovalLimits> GetLoanApprovalLimitsByTemplateItemIdAsync(string templateItemId)
        {
            return (await _finMongoDbRepository.FindWithProjectionAsync<LoanProductTemplate, LoanApprovalLimits>(
                lpt => lpt.ItemId == templateItemId,
                lpt => lpt.ApprovalLimits))
                .FirstOrDefault();
        }

        private async Task<(string OfficeItemId, string DesignationCode)> GetOfficeItemIdAndDesignationOfNextApproverAsync(
            string currentEmployeeOfficeItemId,
            EnumLevelOfOffice officeLevel)
        {
            var nextOfficeItemId = officeLevel == EnumLevelOfOffice.BranchOffice
                ? currentEmployeeOfficeItemId
                : await GetParentOfficeItemIdAsync(currentEmployeeOfficeItemId);

            var appropriateDesignationCode = BuroDesignationConstant.ManagerDesignationCodeMapping
                .GetValueOrDefault(officeLevel) ?? string.Empty;

            return (nextOfficeItemId, appropriateDesignationCode);
        }

        public async Task<BuroEmployee> GetEmployeeByOfficeForSpecificDesignation(string officeItemId, string designationCode)
        {
            if (string.IsNullOrWhiteSpace(officeItemId) || string.IsNullOrWhiteSpace(designationCode))
            {
                return default!;
            }

            var employee = await GetEmployeeByDesignationForAnOfficeAsync(officeItemId, designationCode);

            return employee;
        }

        public async Task<BuroEmployee> GetEmployeeByDesignationForAnOfficeAsync(
            string officeItemId,
            string designationCode)
        {
            _logger.LogInformation("In GetEmployeeByDesignationForAnOfficeAsync");

            var employees = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(
                e => e.CurrentOfficeItemId == officeItemId
                    && e.DesignationCode == designationCode);

            return employees;
        }

        private async Task<string> GetParentOfficeItemIdAsync(string currentOfficeItemId)
        {
            var officeItemId = (await _finMongoDbRepository.FindWithProjectionAsync<BuroOffice, string>(
                o => o.ItemId == currentOfficeItemId,
                o => o.ParentOfficeItemId))
                .FirstOrDefault();

            return officeItemId;
        }
    }
}
