using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Account
{
    public class ContractualAccountApprovalEventService : IContractualAccountApprovalEventService
    {
        private readonly ILogger<ContractualAccountApprovalEventService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;
        private readonly IFinMongoDbRepository _repository;
        private readonly ICreateCSApplicationVoucherEventService _createCSApplicationVoucherEventService;
        private readonly IInfrastructureSharedService _sharedService;
        private readonly ITransactionClientService _transactionClientService;

        public ContractualAccountApprovalEventService(
            ILogger<ContractualAccountApprovalEventService> logger,
            ISecurityContextProvider securityContextProvider,
            IServiceClient serviceClient,
            IFinMongoDbRepository repository,
            ICreateCSApplicationVoucherEventService createCSApplicationVoucherEventService,
            IInfrastructureSharedService sharedService,
            ITransactionClientService transactionClientService)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _serviceClient = serviceClient;
            _repository = repository;
            _createCSApplicationVoucherEventService = createCSApplicationVoucherEventService;
            _sharedService = sharedService;
            _transactionClientService = transactionClientService;
        }

        public async Task SubmitApprovalForNewContractualAccountAsync(ContractualAccountApprovalEvent command)
        {
            _logger.LogInformation("Starting SubmitApprovalForNewContractualAccount for application Id: {AccountItemId}", command.ApplicationItemId);

            var currentContext = GetCurrentUserId();
            var currentEmployee = await GetEmployeeByUserItemIdAsync(currentContext);
            var application = await GetCSApplicationAsync(command);

            await UpdateCSApplicationStatusAsync(command, currentEmployee);

            if (command.IsAccepted)
            {
                var account = await CreateCSAccountAsync(command, application);

                await SaveCSAccountHistoryAsync(account, currentEmployee);
                await UpdateContractualSavingsCountofAMemberAsync(currentContext.UserId, application.MemberItemId);

                SendToCreateScheduleForCSAccount(account.ItemId);
            }
            else
            {
                await CreateVoucherOnCSApplicationRejectionAsync(application);
            }

            await SendApprovalNotificationAsync(command, application);

            _logger.LogInformation("Successfully approved Contractual Account application with application Id: {AccountItemId}", command.ApplicationItemId);
        }

        private async Task UpdateContractualSavingsCountofAMemberAsync(string userItemId, string memberItemId)
        {
            var currentCSAccountCount = (await _repository.FindWithProjectionAsync<BuroMember, int>(
                dataFilters: m => m.ItemId == memberItemId,
                projectionExpression: m => m.NumberOfContractualSavingsAccounts))
                .FirstOrDefault();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMember.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroMember.LastUpdatedBy), userItemId },
                {nameof(BuroMember.NumberOfContractualSavingsAccounts), currentCSAccountCount + 1 },
            };

            await _repository.UpdateOneAsync<BuroMember>(m => m.ItemId == memberItemId, properties);
        }

        private void SendToCreateScheduleForCSAccount(string accountItemId)
        {
            var payload = new ContractualAccountScheduleGenerationEvent
            {
                ContractualAccountItemId = accountItemId
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("CS account schedule creation initiated for account Id: {AccountItemId}", accountItemId);
        }

        private async Task SendApprovalNotificationAsync(
            ContractualAccountApprovalEvent command,
            BuroContractualSavingsApplication application)
        {
            var responseKey = command.IsAccepted
                ? BuroNotificationKeys.ContractualAccountBranchManagerApproved
                : BuroNotificationKeys.ContractualAccountBranchManagerRejected;

            var approval = await GetApprovalByApprovalItemIdAsync(application.ItemId);

            if (approval == null)
            {
                _logger.LogInformation("Approval for the the CS application not found. Skip sending notification.");

                return;
            }

            var requesterUserItemId = await GetEmployeeUserItemId(approval.RequestedFromEmployeeItemId);

            var notificationPayload = new
            {
                command.ApplicationItemId,
                application.MemberItemId,
                application.ProductLineItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = approval.ActionedByEmployeeName,
                ApprovalItemId = approval.ItemId,
                ApproverName = approval.RequestedFromEmployeeName,
                ApproverPin = approval.ActionedByEmployeePIN,
                ApproverDesignation = approval.ActionedByEmployeeDesignation,
                application.MemberName,
                application.CenterName
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                command.ApplicationItemId,
                requesterUserItemId);
        }

        private async Task<string> GetEmployeeUserItemId(string employeeItemId)
        {
            var employee = await _repository.FindWithProjectionAsync<BuroEmployee, string>(e => e.ItemId == employeeItemId, e => e.UserItemId);

            return employee.FirstOrDefault() ?? string.Empty;
        }

        private async Task<BuroApproval> GetApprovalByApprovalItemIdAsync(string itemId)
        {
            return await _repository.FindOneAsync<BuroApproval>(
                a => a.RelatedEntityItemId == itemId
                  && a.RelatedEntityName == nameof(BuroContractualSavingsApplication));
        }

        private async Task CreateVoucherOnCSApplicationRejectionAsync(BuroContractualSavingsApplication application)
        {
            var voucherCreationCommand = new CreateCSApplicationVoucherEvent
            {
                ApplicationItemId = application.ItemId,
                ApplicationName = nameof(BuroContractualSavingsApplication),
                ProductLineItemId = application.ProductLineItemId,
                Amount = application.InstalmentAmount ?? 0,
                IsDebited = true
            };

            await _createCSApplicationVoucherEventService.CreateCSApplicationVoucherAsync(voucherCreationCommand);
        }

        private async Task<BuroEmployee> GetEmployeeByUserItemIdAsync(SecurityContext context)
        {
            return await _repository.FindOneAsync<BuroEmployee>(e => e.UserItemId == context.UserId);
        }

        private async Task SaveCSAccountHistoryAsync(BuroContractualSavingsAccount account, BuroEmployee currentEmployee)
        {
            _logger.LogInformation("Starting SaveCSAccountHistoryAsync for account Id: {ItemId}", account.ItemId);

            var history = new BuroProductAccountHistory
            {
                AccountItemId = account.ItemId,
                ActionByEmployeeItemId = currentEmployee.ItemId,
                ActionByEmployeePin = currentEmployee.EmployeePin,
                ActionByEmployeeName = currentEmployee.EmployeeName,
                ActionByEmployeeDesginationItemId = currentEmployee.DesignationItemId,
                ActionByEmployeeDesginationTitle = currentEmployee.DesignationTitle,
                ActionByEmployeeOfficeItemId = currentEmployee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = currentEmployee.CurrentOfficeCode,
                ProductType = EnumProductType.ContractualSaving,
                Status = account.AccountStatus,
                MetaData = new List<MetaData>
                {
                    new()
                    {
                        Key = nameof(account.MemberItemId),
                        Value = account.MemberItemId
                    },
                    new()
                    {
                        Key = nameof(account.ProductLineItemId),
                        Value = account.ProductLineItemId
                    }
                }
            };

            history.AddEntityBasicInfo();

            await _repository.InsertOneAsync(history);
        }

        private async Task<BuroContractualSavingsAccount> CreateCSAccountAsync(ContractualAccountApprovalEvent command, BuroContractualSavingsApplication application)
        {
            _logger.LogInformation("Starting CreateCSAccountAsync for application Id: {AccountItemId}", command.ApplicationItemId);

            var accountCode = _sharedService.GenerateAbbreviation(nameof(EnumProductType.ContractualSaving));
            var sequenceNumber = await _sharedService.GetSequenceNumberAsync(BuroConstants.BuroContractualSavingsAccountIdentifier, accountCode);

            var account = new BuroContractualSavingsAccount
            {
                AccountCode = accountCode,
                AccountSequenceNumber = sequenceNumber,
                AccountStatus = EnumProductAccountStatus.Ongoing,
                OpeningBranchItemId = application.BranchItemId,
                OpeningBranchName = application.BranchName,
                CurrentBranchItemId = application.BranchItemId,
                CurrentBranchName = application.BranchName,
                OpeningDate = DateTime.UtcNow,
                MemberItemId = application.MemberItemId,
                PersonItemId = application.PersonItemId,
                CenterItemId = application.CenterItemId,
                CenterName = application.CenterName,
                ProductLineItemId = application.ProductLineItemId,
                ProductLineName = application.ProductLineName,
                InterestRate = application.InterestRate,
                ProgramOrganizerEmployeeItemId = application.ProgramOrganizerEmployeeItemId,
                ProgramOrganizerEmployeeName = application.ProgramOrganizerEmployeeName,
                InstalmentAmount = application.InstalmentAmount,
                ContractualSavingsApplicationItemId = command.ApplicationItemId,
                OriginalTenureDetails = application.TenureDetails,
                ActualTenureDetails = application.TenureDetails,
                PaymentInterval = application.PaymentInterval,
                MemberName = application.MemberName,
                MemberSequenceNumber = application.MemberSequenceNumber,
                MemberBranchOfficeItemId = application.BranchItemId,
                MemberBranchOfficeName = application.BranchName,
            };

            account.AddEntityBasicInfo();

            var ressult = await _transactionClientService.CreateAccountAsync(new Infrastructure.Models.Transaction.CreateAccountCommand
            {
                AccountNumber = account.AccountSequenceNumber,
                AccountHolderNumber = account.MemberSequenceNumber,
                AccountName = EnumProductType.ContractualSaving.GetDisplayName(),
                InitialBalance = 0,
                AccountType = TransactionServiceConstants.AccountTypeConstants.Current,
            });

            if (!ressult.IsSuccess())
            {
                _logger.LogError("Failed to create account in transaction service for member Id: {MemberItemId}", account.MemberItemId);

                throw new InvalidOperationException("Failed to create account in transaction service.");
            }

            await _repository.InsertOneAsync(account);

            return account;
        }

        private async Task<BuroContractualSavingsApplication> GetCSApplicationAsync(ContractualAccountApprovalEvent command)
        {
            return await _repository.FindOneAsync<BuroContractualSavingsApplication>(c => c.ItemId == command.ApplicationItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "CSAPPLICATION"));
        }

        private async Task UpdateCSApplicationStatusAsync(ContractualAccountApprovalEvent command, BuroEmployee currentEmployee)
        {
            _logger.LogInformation("Starting UpdateCSApplicationStatusAsync for application Id: {AccountItemId}", command.ApplicationItemId);

            var accountProperties = new Dictionary<string, object>
            {
                { nameof(BuroContractualSavingsApplication.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroContractualSavingsApplication.LastUpdatedBy), currentEmployee.UserItemId },
                { nameof(BuroContractualSavingsApplication.Status), GetProperApplicationStatus(command.IsAccepted) }
            };

            await _repository.UpdateOneAsync<BuroContractualSavingsApplication>(c => c.ItemId == command.ApplicationItemId, accountProperties);

            await SaveApplicationHistoryAsync(command, currentEmployee);
        }

        private async Task SaveApplicationHistoryAsync(ContractualAccountApprovalEvent command, BuroEmployee currentEmployee)
        {
            var history = new BuroProductApplicationHistory
            {
                ApplicationItemId = command.ApplicationItemId,
                ActionByEmployeeItemId = currentEmployee.ItemId,
                ActionByEmployeePin = currentEmployee.EmployeePin,
                ActionByEmployeeName = currentEmployee.EmployeeName,
                ActionByEmployeeDesginationItemId = currentEmployee.DesignationItemId,
                ActionByEmployeeDesginationTitle = currentEmployee.DesignationTitle,
                ActionByEmployeeOfficeItemId = currentEmployee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = currentEmployee.CurrentOfficeCode,
                ProductType = EnumProductType.ContractualSaving,
                Status = GetProperApplicationStatus(command.IsAccepted)
            };

            history.AddEntityBasicInfo();

            await _repository.InsertOneAsync(history);
        }

        private static EnumProductApplicationStatus GetProperApplicationStatus(bool isAccepted)
        {
            // NOSONAR Need to verify if the first installment has been paid
            return isAccepted ? EnumProductApplicationStatus.Approved : EnumProductApplicationStatus.Rejected;
        }

        private SecurityContext GetCurrentUserId()
        {
            return _securityContextProvider.GetSecurityContext();
        }
    }
}
