using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;

namespace EventHandlers.Services.Account
{
    public class CreateCSApplicationVoucherEventService : ICreateCSApplicationVoucherEventService
    {
        private readonly ILogger<CreateCSApplicationVoucherEventService> _logger;

        public CreateCSApplicationVoucherEventService(
            ILogger<CreateCSApplicationVoucherEventService> logger)
        {
            _logger = logger;
        }

        public async Task CreateCSApplicationVoucherAsync(CreateCSApplicationVoucherEvent command)
        {
            _logger.LogInformation("Starting CreateCSApplicationVoucherAsync for application Id: {AccountItemId}", command.ApplicationItemId);

            await Task.Delay(1000);

            _logger.LogInformation("Finished CreateCSApplicationVoucherAsync for application Id: {AccountItemId}", command.ApplicationItemId);
        }
    }
}
