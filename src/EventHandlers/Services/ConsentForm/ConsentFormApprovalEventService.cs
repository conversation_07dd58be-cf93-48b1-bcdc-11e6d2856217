using EventHandlers.Contracts;
using EventHandlers.Models.ConsentForm;
using FinMongoDbRepositories;
using Infrastructure.Events.ConsentForm;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.ConsentForm;

public class ConsentFormApprovalEventService : IConsentFormApprovalEventService
{
    private readonly ILogger<ConsentFormApprovalEventService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IFinMongoDbRepository _finMongoDbRepository;

    public ConsentFormApprovalEventService(
        ILogger<ConsentFormApprovalEventService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository finMongoDbRepository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _finMongoDbRepository = finMongoDbRepository;
    }

    public async Task HandleConsentFormApprovalEventAsync(ConsentFormApprovalEvent command)
    {
        _logger.LogInformation("Starting ConsentFormApprovalRequestAsync for Request Id: {ConsentFormItemId}", command.ConsentFormItemId);

        var currentUserId = GetCurrentUserId();
        await ConsentFormApprovalRequestAsync(command, currentUserId);
        _logger.LogInformation("Successfully updated consent form status: {ConsentFormItemId}", command.ConsentFormItemId);
    }


    private async Task ConsentFormApprovalRequestAsync(ConsentFormApprovalEvent command, string currentUserId)
    {
        _logger.LogInformation("ConsentFormApprovalEventService -> HandleConsentFormApprovalEventAsync-> ConsentFormApprovalRequestAsync-> START");
        var updatedProperties = PrepareToBeUpdatedData(command, currentUserId);
        await _finMongoDbRepository.UpdateOneAsync<BuroConsentForm>(x => x.ItemId == command.ConsentFormItemId, updatedProperties);

        if (command.IsAccepted == false)
        {
            var consentForm = await _finMongoDbRepository.FindWithProjectionAsync<BuroConsentForm, ConsentFormDto>(x => x.ItemId == command.ConsentFormItemId,
                x => new ConsentFormDto
                {
                    CreatedBy = x.CreatedBy,
                    MemberItemId = x.MemberItemId,
                    FundAccountSequenceNumber = x.FundAccountSequenceNumber,
                    ReceiveAccountSequenceNumber = x.ReceiveAccountSequenceNumber
                }
                );
            
            var notifiedPersons = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, string>(x => x.ItemId == consentForm[0].CreatedBy,
                x => x.UserItemId);
            var notificationPayload = MakeNotificationPayload(command);

        }
        
        _logger.LogInformation("ConsentFormApprovalEventService -> HandleConsentFormApprovalEventAsync-> ConsentFormApprovalRequestAsync->  END");
    }


    private Dictionary<string, object> PrepareToBeUpdatedData(ConsentFormApprovalEvent command, string currentUserId)
    {
        return new Dictionary<string, object>
        {
            { nameof(BuroConsentForm.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroConsentForm.LastUpdatedBy), currentUserId },
            { nameof(BuroConsentForm.Status), GetConsentFormApprovalStatus(command.IsAccepted) }
        };
    }

    private static EnumConsentFormApprovalStatus GetConsentFormApprovalStatus(bool isAccepted)
    {
        return isAccepted ? EnumConsentFormApprovalStatus.Approved : EnumConsentFormApprovalStatus.Rejected;
    }

    private string GetCurrentUserId()
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        return securityContext.UserId;
    }


    private static object MakeNotificationPayload(
        ConsentFormApprovalEvent command)
    {
        return new
        {
            command.Remarks,
            RequestItemId = command.ConsentFormItemId,
            RejectedAt = DateTime.UtcNow,
        };
    }


}