using EventHandlers.Contracts;
using EventHandlers.Models.AutoDebit;
using FinMongoDbRepositories;
using Infrastructure.Events.AutoDebit;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.AutoDebit;

public class ExecuteAutoDebitEventService : IExecuteAutoDebitEventService
{
    private readonly ILogger<ExecuteAutoDebitEventService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IFinMongoDbRepository _finMongoDbRepository ;

    public ExecuteAutoDebitEventService(
        ILogger<ExecuteAutoDebitEventService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository finMongoDbRepository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _finMongoDbRepository = finMongoDbRepository;
    }
    
    public async Task ExecuteAutoDebitAsync(ExecuteAutoDebitEvent command)
    {
       try
       { 
           _logger.LogInformation("Executing AutoDebit for {Date}", DateTime.UtcNow);
           
           var autoDebitMemberList = await GetApprovedAutoDebitMembersAsync();
           
           if (autoDebitMemberList == null)
           {
               return;
           }
           
           var memberIds = autoDebitMemberList.Select(m => m.MemberItemId).Distinct().ToList();
           
           var executableMemberSchedules = await GetMemberSchedulesAsync(memberIds, DateTime.UtcNow.Date);
           
           if (executableMemberSchedules == null || !executableMemberSchedules.Any())
           {
               _logger.LogInformation("No executable member schedules found for {Date}", DateTime.UtcNow.Date);
               return;
           }

           var finalAutoDebitList = await GetMembersWithSufficientBalanceAsync(autoDebitMemberList, executableMemberSchedules);
           
           _logger.LogInformation("Found {Count} members with sufficient balance for auto-debit", finalAutoDebitList.Count);
           
           //await ProcessAutoDebitsAsync(finalAutoDebitList);

           
       }
       catch (Exception e)
       {
           _logger.LogError(e, "Exception in ExecuteAutoDebitEventService\n{StackTrace}", e.StackTrace);
       } 
    }
    
    private async Task<List<MemberScheduleDto>> GetMemberSchedulesAsync(List<string> memberIds, DateTime date)
    {
        return await _finMongoDbRepository.FindWithProjectionAsync<BuroMemberSchedule, MemberScheduleDto>(
            x => memberIds.Contains(x.MemberItemId) 
                 && x.ActualPaymentDate == date
                 && x.PaymentStatus == PaymentStatus.Pending, 
            x => new MemberScheduleDto
            {
                AccountItemId = x.AccountItemId,
                AccountCode = x.AccountCode,
                AccountSequenceNumber = x.AccountSequenceNumber,
                MemberItemId = x.MemberItemId,
                OriginalPaymentDate = x.OriginalPaymentDate,
                ActualPaymentDate = x.ActualPaymentDate,
                PayableAmount = x.PayableAmount,
                PaidAmount = x.PaidAmount,
                DueAmount = x.DueAmount, 
                LateFeeAmount = x.LateFeeAmount,
                PaymentStatus = x.PaymentStatus,
                IsAutoDebited = x.IsAutoDebited
            });
    }
    
    private async Task<List<MemberConsentFormDto>> GetApprovedAutoDebitMembersAsync()
    {
        var autoDebitMemberList = await _finMongoDbRepository.FindWithProjectionAsync<BuroConsentForm, MemberConsentFormDto>(
            x => x.Status == EnumConsentFormApprovalStatus.Approved, 
            x => new MemberConsentFormDto
            {
                MemberItemId = x.MemberItemId,
                FundAccountItemId = x.FundAccountItemId,
                FundAccountProductType = x.FundAccountProductType,
                FundAccountSequenceNumber = x.FundAccountSequenceNumber,
                ReceiveAccountItemId = x.ReceiveAccountItemId,
                ReceiveAccountProductType = x.ReceiveAccountProductType,
                ReceiveAccountSequenceNumber = x.ReceiveAccountSequenceNumber
            });
            
        if (autoDebitMemberList == null || !autoDebitMemberList.Any())
        {
            _logger.LogWarning("No AutoDebit Member Found for {Date}", DateTime.UtcNow);
            return null;
        }
        
        return autoDebitMemberList;
    }

    private async Task<List<AutoDebitMemberWithBalanceDto>> GetMembersWithSufficientBalanceAsync(
        List<MemberConsentFormDto> consentForms,
        List<MemberScheduleDto> memberSchedules)
    {
        var autoDebitList = new List<AutoDebitMemberWithBalanceDto>();
        var schedulesByMember = memberSchedules.GroupBy(s => s.MemberItemId).ToList();
        var fundAccountIds = consentForms.Select(c => c.FundAccountItemId).Distinct().ToList();
        var savingsAccounts = await GetMemberSavingsAccountsAsync(fundAccountIds);
        
        var savingsAccountLookup = savingsAccounts.ToDictionary(
            sa => $"{sa.MemberItemId}_{sa.ItemId}", 
            sa => sa.Balance );

        foreach (var memberGroup in schedulesByMember)
        {
            var memberId = memberGroup.Key;
            var memberConsent = consentForms.FirstOrDefault(c => c.MemberItemId == memberId);  
            if (memberConsent == null)
                continue;
            
            var totalRequiredAmount = memberGroup.Sum(s => (s.PayableAmount + (s.LateFeeAmount ?? 0)));
            
            // Get available balance from lookup dictionary
            var lookupKey = $"{memberId}_{memberConsent.FundAccountItemId}";
            var availableBalance = savingsAccountLookup.GetValueOrDefault(lookupKey, 0);
            if (availableBalance >= totalRequiredAmount)
            {
                autoDebitList.Add(new AutoDebitMemberWithBalanceDto
                {
                    MemberConsent = memberConsent,
                    MemberSchedules = memberGroup.ToList(),
                    TotalRequiredAmount = totalRequiredAmount,
                    AvailableBalance = availableBalance,
                    CanExecuteAutoDebit = true
                });
                
                _logger.LogInformation("Member {MemberId} has sufficient balance. Required: {Required}, Available: {Available}", 
                    memberId, totalRequiredAmount, availableBalance);
            }
            else
            {
                _logger.LogWarning("Member {MemberId} has insufficient balance. Required: {Required}, Available: {Available}", 
                    memberId, totalRequiredAmount, availableBalance);
            }
        }
        
        return autoDebitList; 
    }
    
    private async Task<List<BuroGeneralSavingsAccount>> GetMemberSavingsAccountsAsync(List<string> fundAccountIds)
    {
        try
        {
            var savingsAccounts = await _finMongoDbRepository.FindAsync<BuroGeneralSavingsAccount>(
                x => fundAccountIds.Contains(x.ItemId));
                
            if (savingsAccounts == null || !savingsAccounts.Any())
            {
                _logger.LogWarning("No savings accounts found for the provided fund account IDs");
                return new List<BuroGeneralSavingsAccount>();
            }
            
            return savingsAccounts.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving savings accounts for fund account IDs");
            return new List<BuroGeneralSavingsAccount>();
        }
    }
    
}
