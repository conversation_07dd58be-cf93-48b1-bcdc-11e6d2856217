using EventHandlers.Contracts;
using Infrastructure.Events.Task;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Tasks
{
    public class CreateTaskService : ICreateTaskService
    {
        private readonly ILogger<CreateTaskService> _logger;
        private readonly IRepository _repository;
        public CreateTaskService(
            ILogger<CreateTaskService> logger,
            IRepository repository)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        public async Task CreateTask(CreateTaskEvent createTaskEvent)
        {
            try
            {
                _logger.LogInformation("CreateTaskService -> CreateTask -> START");
                await AddTaskForApproval();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Occured while Creating Task");
            }
        }

        private async Task AddTaskForApproval()
        {
            _logger.LogInformation("CreateTaskService -> AddTaskForApproval -> START");
            var newTask = PreapareBuroTask();
            await _repository.SaveAsync<BuroTask>(newTask);
            _logger.LogInformation("CreateTaskService -> AddTaskForApproval -> END");

        }

        private BuroTask PreapareBuroTask()
        {
            return new BuroTask();
        }

    }
}
