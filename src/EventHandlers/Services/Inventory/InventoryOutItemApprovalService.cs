using EventHandlers.Contracts.inventory;
using FinMongoDbRepositories;
using Infrastructure.Enums;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace EventHandlers.Services.Inventory;

public class InventoryOutItemApprovalService : BaseInventoryApprovalService
{
    private readonly ILogger<InventoryOutItemApprovalService> _logger;
    private readonly IFinMongoDbRepository _repository;

    public InventoryOutItemApprovalService(IFinMongoDbRepository repository,
        IInventoryStockManagementService stockManagementService,
        ILogger<InventoryOutItemApprovalService> logger) : base(repository, stockManagementService)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task ApproveInventoryOutItemAsync(InventoryOutItemApprovalEvent command)
    {
        var inventoryMovementPlan = await FetchInventoryMovementPlanAsync(command.InventoryMovementPlanItemId);
        if (inventoryMovementPlan == null)
        {
            _logger.LogError("Inventory movement plan not found");
            return;
        }

        await UpdateInventoryMovementPlanAsync(command, inventoryMovementPlan.ItemId);

        var requestedEmployee =
            await _repository.FindOneAsync<BuroEmployee>(item => item.UserItemId == inventoryMovementPlan.CreatedBy);
        if (requestedEmployee == null) _logger.LogError("Requested employee not found");
        var (totalQuantity, totalPrice) = CalculateTotals(inventoryMovementPlan);
        var inventoryMovement = PopulateInventoryMovement(inventoryMovementPlan, requestedEmployee, totalQuantity,
            totalPrice, EnumInventoryMovementType.Out);

        if (!command.IsAccepted)
        {
            await ProcessInventoryOutApprovalRejection(inventoryMovementPlan,
                inventoryMovement.InventoryItemId,
                inventoryMovement.SourceOfficeItemId,
                EnumInventoryMovementType.In,
                inventoryMovement.Quantity,
                inventoryMovement.UnitPrice);
            return;
        }

        await _repository.InsertOneAsync(inventoryMovement);

        await ProcessRegularEmployeeInventoryOut(inventoryMovementPlan, inventoryMovement.InventoryItemId,
            inventoryMovement.SourceOfficeItemId, inventoryMovement.MovementType, inventoryMovement.Quantity,
            inventoryMovement.UnitPrice);
    }

    private async Task ProcessInventoryOutApprovalRejection(
        BuroInventoryMovementPlan plan,
        string inventoryItemId,
        string officeItemId,
        EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice)
    {
        await UpdateInventoryStockAsync(inventoryItemId, officeItemId, movementType, quantity, unitPrice,
            plan.CreatedBy, EnumStockUpdateType.AvailableOnly);
        await UpdateInventoryStockBreakdownAsync(movementType, plan, EnumStockUpdateType.AvailableOnly);
    }

    private async Task ProcessRegularEmployeeInventoryOut(
        BuroInventoryMovementPlan plan,
        string inventoryItemId,
        string officeItemId,
        EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice)
    {
        await UpdateInventoryStockAsync(inventoryItemId, officeItemId, movementType, quantity, unitPrice,
            plan.CreatedBy, EnumStockUpdateType.ActualOnly);
        await UpdateInventoryStockBreakdownAsync(movementType, plan, EnumStockUpdateType.ActualOnly);
    }


    private BuroInventoryMovement PopulateInventoryMovement(BuroInventoryMovementPlan movementPlan,
        BuroEmployee employee,
        int totalQuantity, double totalPrice, EnumInventoryMovementType movementType)
    {
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            SourceOfficeItemId = employee.CurrentOfficeItemId,
            SourceOfficeName = employee.CurrentOfficeTitle,
            InventoryItemId = movementPlan.InventoryItemId,
            InventoryName = movementPlan.InventoryName,
            InventoryItemDefinition = movementPlan.InventoryItemDefinition,
            CategoryItemId = movementPlan.CategoryItemId,
            Category = movementPlan.Category,
            SubCategoryItemId = movementPlan.SubCategoryItemId,
            SubCategory = movementPlan.SubCategory,
            VendorItemId = movementPlan.VendorItemId,
            VendorName = movementPlan.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = movementPlan.HasSerialNumber,
            SerialNumber = string.Join(',', movementPlan.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = movementPlan.RequiresExpiryDate,
            ExpiryDate = movementPlan.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = movementType,
            OutDestination = movementPlan.OutDestination,
            RecipientEmployeeItemId = movementPlan.RecipientEmployeeItemId,
            RecipientEmployeeName = movementPlan.RecipientEmployeeName,
            RecipientOfficeItemId = movementPlan.OutDestination == EnumInventoryOutDestination.Office
                ? employee.CurrentOfficeItemId
                : null,
            RecipientOfficeName = movementPlan.OutDestination == EnumInventoryOutDestination.Office
                ? employee.CurrentOfficeTitle
                : null,
            Reason = movementPlan.Reason,
            CreatedBy = employee.UserItemId,
            CreateDate = DateTime.UtcNow
        };
        return inventoryMovement;
    }
}