using EventHandlers.Contracts.inventory;
using FinMongoDbRepositories;
using Infrastructure.Enums;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace EventHandlers.Services.Inventory;

public class InventoryTransferRecipientApprovalService : BaseInventoryApprovalService
{
    private readonly ILogger<InventoryTransferRecipientApprovalService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly IInventoryStockManagementService _stockManagementService;

    public InventoryTransferRecipientApprovalService(
        ILogger<InventoryTransferRecipientApprovalService> logger,
        IFinMongoDbRepository repository,
        IInventoryStockManagementService stockManagementService) : base(repository, stockManagementService)
    {
        _logger = logger;
        _repository = repository;
        _stockManagementService = stockManagementService;
    }

    public async Task ProcessRecipientTransferApproval(InventoryTransferRecipientApprovalEvent command)
    {
        var inventoryMovementPlan = await FetchInventoryMovementPlanAsync(command.InventoryMovementPlanItemId);
        if (inventoryMovementPlan == null)
        {
            _logger.LogError("Inventory movement plan not found");
            return;
        }

        await UpdateInventoryMovementPlanAsync(command, inventoryMovementPlan.ItemId);

        var (totalQuantity, totalPrice) = CalculateTotals(inventoryMovementPlan);

        var inventoryMovementOut = CreateInventoryMovement(
            inventoryMovementPlan,
            totalQuantity,
            totalPrice,
            inventoryMovementPlan.SourceOfficeItemId,
            inventoryMovementPlan.SourceOfficeName,
            inventoryMovementPlan.RecipientOfficeItemId,
            inventoryMovementPlan.RecipientOfficeName,
            EnumInventoryMovementType.Out);

        var inventoryMovementIn = CreateInventoryMovement(
            inventoryMovementPlan,
            totalQuantity,
            totalPrice,
            inventoryMovementPlan.RecipientOfficeItemId,
            inventoryMovementPlan.RecipientOfficeName,
            inventoryMovementPlan.SourceOfficeItemId,
            inventoryMovementPlan.SourceOfficeName,
            EnumInventoryMovementType.Out);

        await ManageInventoryStockChanges(command, inventoryMovementOut, inventoryMovementIn, inventoryMovementPlan);
        if (inventoryMovementPlan.InventoryItemDefinition == EnumInventoryItemDefinition.Request &&
            inventoryMovementPlan.InventoryRequestItemId != null)
        {
            await UpdateInventoryRequest(inventoryMovementPlan, totalQuantity);
        }
    }

    private async Task UpdateInventoryRequest(BuroInventoryMovementPlan inventoryMovementPlan, int totalQuantity)
    {
        var inventoryRequest = await FetchInventoryRequestAsync(inventoryMovementPlan.InventoryRequestItemId);
        if (inventoryRequest == null)
        {
            _logger.LogError(
                "Inventory request not found with {ItemId}",
                inventoryMovementPlan.InventoryRequestItemId
            );
            return;
        }

        var disbursedStatus = DetermineDisburseStatus(totalQuantity, inventoryRequest.RequestedQuantity);
        var updatedProperties = BuildUpdatedProperties(totalQuantity, disbursedStatus);

        await _repository.UpdateOneAsync<BuroInventoryRequest>(
            item => item.ItemId == inventoryRequest.ItemId,
            updatedProperties
        );
    }

    private async Task<BuroInventoryRequest?> FetchInventoryRequestAsync(string inventoryRequestItemId)
    {
        return await _repository.FindOneAsync<BuroInventoryRequest>(item => item.ItemId == inventoryRequestItemId
        );
    }

    private EnumInventoryRequestDisburseStatus DetermineDisburseStatus(int totalQuantity, int requestedQuantity)
    {
        return totalQuantity >= requestedQuantity
            ? EnumInventoryRequestDisburseStatus.Disbursed
            : EnumInventoryRequestDisburseStatus.PartiallyDisbursed;
    }

    private Dictionary<string, object> BuildUpdatedProperties(int totalQuantity,
        EnumInventoryRequestDisburseStatus disbursedStatus)
    {
        return new Dictionary<string, object>
        {
            { nameof(BuroInventoryRequest.DisbursedQuantity), totalQuantity },
            { nameof(BuroInventoryRequest.DisbursedDate), DateTime.UtcNow },
            { nameof(BuroInventoryRequest.DisburseStatus), disbursedStatus }
        };
    }


    private BuroInventoryMovement CreateInventoryMovement(
        BuroInventoryMovementPlan plan,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        string recipientOfficeItemId,
        string recipientOfficeName,
        EnumInventoryMovementType movementType)
    {
        return PopulateInventoryMovement(plan, plan.CreatedBy, totalQuantity, totalPrice, sourceOfficeItemId,
            sourceOfficeName, recipientOfficeItemId, recipientOfficeName, movementType);
    }

    private async Task ManageInventoryStockChanges(
        InventoryTransferRecipientApprovalEvent command,
        BuroInventoryMovement inventoryMovementOut,
        BuroInventoryMovement inventoryMovementIn,
        BuroInventoryMovementPlan inventoryMovementPlan)
    {
        if (command.IsAccepted)
        {
            await HandleAcceptedStockChanges(inventoryMovementOut, inventoryMovementIn, inventoryMovementPlan);
        }
        else
        {
            await HandleRejectedStockChanges(inventoryMovementOut, inventoryMovementIn, inventoryMovementPlan);
        }
    }

    private async Task HandleAcceptedStockChanges(
        BuroInventoryMovement inventoryMovementOut,
        BuroInventoryMovement inventoryMovementIn,
        BuroInventoryMovementPlan inventoryMovementPlan)
    {
        await UpdateInventoryStockAsync(inventoryMovementOut.InventoryItemId,
            inventoryMovementOut.SourceOfficeItemId,
            EnumInventoryMovementType.Out,
            inventoryMovementOut.Quantity,
            inventoryMovementOut.UnitPrice,
            inventoryMovementOut.CreatedBy,
            EnumStockUpdateType.ActualOnly);

        await _stockManagementService.CreateOrUpdateInventoryStock(inventoryMovementIn, EnumStockUpdateType.ActualOnly);
        await UpdateInventoryStockBreakdownAsync(EnumInventoryMovementType.Out,
            inventoryMovementPlan,
            EnumStockUpdateType.ActualOnly);
        await _stockManagementService.CreateOrUpdateInventoryStockBreakdownAsync(inventoryMovementPlan,
            inventoryMovementIn, EnumStockUpdateType.ActualOnly);
    }

    private async Task HandleRejectedStockChanges(
        BuroInventoryMovement inventoryMovementOut,
        BuroInventoryMovement inventoryMovementIn,
        BuroInventoryMovementPlan inventoryMovementPlan)
    {
        await UpdateInventoryStockAsync(inventoryMovementOut.InventoryItemId,
            inventoryMovementOut.SourceOfficeItemId,
            EnumInventoryMovementType.In,
            inventoryMovementOut.Quantity,
            inventoryMovementOut.UnitPrice,
            inventoryMovementOut.CreatedBy,
            EnumStockUpdateType.AvailableOnly);

        await UpdateInventoryStockAsync(inventoryMovementIn.InventoryItemId,
            inventoryMovementIn.SourceOfficeItemId,
            EnumInventoryMovementType.Out,
            inventoryMovementIn.Quantity,
            inventoryMovementIn.UnitPrice,
            inventoryMovementIn.CreatedBy,
            EnumStockUpdateType.AvailableOnly);

        await UpdateInventoryStockBreakdownAsync(EnumInventoryMovementType.In, inventoryMovementPlan,
            EnumStockUpdateType.AvailableOnly);
        await _stockManagementService.UpdateInventoryStockBreakdownAsync(inventoryMovementPlan, inventoryMovementIn,
            EnumInventoryMovementType.Out, EnumStockUpdateType.AvailableOnly);
    }


    private BuroInventoryMovement PopulateInventoryMovement(BuroInventoryMovementPlan movementPlan,
        string createdBy,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        string recipientOfficeItemId,
        string recipientOfficeName,
        EnumInventoryMovementType movementType)
    {
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = movementPlan.InventoryItemId,
            InventoryName = movementPlan.InventoryName,
            InventoryItemDefinition = movementPlan.InventoryItemDefinition,
            CategoryItemId = movementPlan.CategoryItemId,
            Category = movementPlan.Category,
            SubCategoryItemId = movementPlan.SubCategoryItemId,
            SubCategory = movementPlan.SubCategory,
            VendorItemId = movementPlan.VendorItemId,
            VendorName = movementPlan.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = movementPlan.HasSerialNumber,
            SerialNumber = string.Join(',', movementPlan.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = movementPlan.RequiresExpiryDate,
            ExpiryDate = movementPlan.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = movementType,
            IsTransferredItem = true,
            SourceOfficeItemId = sourceOfficeItemId,
            SourceOfficeName = sourceOfficeName,
            RecipientOfficeItemId = recipientOfficeItemId,
            RecipientOfficeName = recipientOfficeName,
            Reason = movementPlan.Reason,
            CreatedBy = createdBy,
            CreateDate = DateTime.UtcNow
        };
        return inventoryMovement;
    }
}