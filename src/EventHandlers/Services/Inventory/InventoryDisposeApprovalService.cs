using EventHandlers.Contracts.inventory;
using FinMongoDbRepositories;
using Infrastructure.Enums;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace EventHandlers.Services.Inventory;

public class InventoryDisposeApprovalService : BaseInventoryApprovalService
{
    private readonly ILogger<InventoryDisposeApprovalService> _logger;
    private readonly IFinMongoDbRepository _repository;

    public InventoryDisposeApprovalService(
        ILogger<InventoryDisposeApprovalService> logger,
        IFinMongoDbRepository repository,
        IInventoryStockManagementService stockManagementService) : base(repository, stockManagementService)
    {
        _logger = logger;
        _repository = repository;
    }

    public async Task DisposeInventoryAsync(InventoryDisposeApprovalEvent command)
    {
        var inventoryMovementPlan = await FetchInventoryMovementPlanAsync(command.InventoryMovementPlanItemId);
        if (inventoryMovementPlan == null)
        {
            _logger.LogError("Inventory movement plan not found");
            return;
        }

        await UpdateInventoryMovementPlanAsync(command, inventoryMovementPlan.ItemId);

        var (totalQuantity, totalPrice) = CalculateTotals(inventoryMovementPlan);

        var inventoryMovement = CreateInventoryMovement(
            inventoryMovementPlan,
            totalQuantity,
            totalPrice,
            inventoryMovementPlan.SourceOfficeItemId,
            inventoryMovementPlan.SourceOfficeName,
            EnumInventoryMovementType.Out);

        await ManageInventoryStockChange(command, inventoryMovementPlan, inventoryMovement);
    }

    private async Task ManageInventoryStockChange(InventoryDisposeApprovalEvent command, BuroInventoryMovementPlan inventoryMovementPlan, BuroInventoryMovement inventoryMovement)
    {
        if (!command.IsAccepted)
        {
            await ProcessInventoryDisposeApprovalRejection(inventoryMovementPlan,
                inventoryMovement.InventoryItemId,
                inventoryMovement.SourceOfficeItemId,
                EnumInventoryMovementType.In,
                inventoryMovement.Quantity,
                inventoryMovement.UnitPrice);
            return;
        }

        await _repository.InsertOneAsync(inventoryMovement);
        await ProcessRegularEmployeeInventoryOut(inventoryMovementPlan, inventoryMovement.InventoryItemId,
            inventoryMovement.SourceOfficeItemId, inventoryMovement.MovementType, inventoryMovement.Quantity,
            inventoryMovement.UnitPrice);

    }

    private async Task ProcessInventoryDisposeApprovalRejection(
        BuroInventoryMovementPlan plan,
        string inventoryItemId,
        string officeItemId,
        EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice)
    {
        await UpdateInventoryStockAsync(inventoryItemId, officeItemId, movementType, quantity, unitPrice,
            plan.CreatedBy, EnumStockUpdateType.AvailableOnly);
        await UpdateInventoryStockBreakdownAsync(movementType, plan, EnumStockUpdateType.AvailableOnly);
    }


    private async Task ProcessRegularEmployeeInventoryOut(
        BuroInventoryMovementPlan plan,
        string inventoryItemId,
        string officeItemId,
        EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice)
    {
        await UpdateInventoryStockAsync(inventoryItemId, officeItemId, movementType, quantity, unitPrice,
            plan.CreatedBy, EnumStockUpdateType.ActualOnly);
        await UpdateInventoryStockBreakdownAsync(movementType, plan, EnumStockUpdateType.ActualOnly);
    }


    private BuroInventoryMovement CreateInventoryMovement(
        BuroInventoryMovementPlan plan,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        EnumInventoryMovementType movementType)
    {
        return PopulateInventoryMovement(plan, plan.CreatedBy, totalQuantity, totalPrice, sourceOfficeItemId,
            sourceOfficeName, movementType);
    }

    private BuroInventoryMovement PopulateInventoryMovement(BuroInventoryMovementPlan movementPlan,
        string createdBy,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        EnumInventoryMovementType movementType)
    {
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = movementPlan.InventoryItemId,
            InventoryName = movementPlan.InventoryName,
            InventoryItemDefinition = movementPlan.InventoryItemDefinition,
            CategoryItemId = movementPlan.CategoryItemId,
            Category = movementPlan.Category,
            SubCategoryItemId = movementPlan.SubCategoryItemId,
            SubCategory = movementPlan.SubCategory,
            VendorItemId = movementPlan.VendorItemId,
            VendorName = movementPlan.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = movementPlan.HasSerialNumber,
            SerialNumber = string.Join(',', movementPlan.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = movementPlan.RequiresExpiryDate,
            ExpiryDate = movementPlan.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = movementType,
            SourceOfficeItemId = sourceOfficeItemId,
            SourceOfficeName = sourceOfficeName,
            Reason = movementPlan.Reason,
            CreatedBy = createdBy,
            CreateDate = DateTime.UtcNow
        };
        return inventoryMovement;
    }
}