using EventHandlers.Contracts.inventory;
using FinMongoDbRepositories;
using Infrastructure.Enums;
using Infrastructure.Events.Inventory;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace EventHandlers.Services.Inventory;

public abstract class BaseInventoryApprovalService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly IInventoryStockManagementService _stockManagementService;

    protected BaseInventoryApprovalService(
        IFinMongoDbRepository repository,
        IInventoryStockManagementService stockManagementService)
    {
        _repository = repository;
        _stockManagementService = stockManagementService;
    }

    protected async Task UpdateInventoryMovementPlanAsync<TCommand>(TCommand command, string inventoryMovementPlanItemId)
        where TCommand : BaseInventoryMovementEvent
    {
        var updatedProperties = GetUpdatedProperties(command);
        await _repository.UpdateOneAsync<BuroInventoryMovementPlan>(
            item => item.ItemId == inventoryMovementPlanItemId,
            updatedProperties);
    }

    protected Dictionary<string, object> GetUpdatedProperties<TCommand>(TCommand command)
        where TCommand : BaseInventoryMovementEvent
    {
        return new Dictionary<string, object>
        {
            {
                nameof(BuroInventoryMovementPlan.ApprovalStatus),
                command.IsAccepted ? EnumInventoryApprovalStatus.Approved : EnumInventoryApprovalStatus.Rejected
            }
        };
    }

    protected async Task<BuroInventoryMovementPlan?> FetchInventoryMovementPlanAsync(string itemId)
    {
        return await _repository.FindOneAsync<BuroInventoryMovementPlan>(item => item.ItemId == itemId);
    }

    protected (int TotalQuantity, double TotalPrice) CalculateTotals(BuroInventoryMovementPlan plan)
    {
        var totalQuantity = plan.InventoryItemDetails.Sum(item => item.Quantity);
        var totalPrice = plan.InventoryItemDetails.Sum(item => item.Price * item.Quantity);
        return (totalQuantity, totalPrice);
    }

    protected async Task UpdateInventoryStockAsync(
        string inventoryItemId,
        string officeItemId,
        EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice,
        string createdByUserId,
        EnumStockUpdateType updateType)
    {
        var currentStock = await _stockManagementService.GetInventoryStock(inventoryItemId, officeItemId);
        await _stockManagementService.UpdateInventoryStock(
            movementType,
            quantity,
            unitPrice,
            currentStock,
            createdByUserId,
            updateType);
    }

    protected async Task UpdateInventoryStockBreakdownAsync(
        EnumInventoryMovementType movementType,
        BuroInventoryMovementPlan plan,
        EnumStockUpdateType updateType)
    {
        var stockBreakdownItemIds = plan.InventoryItemDetails
            .Select(item => item.InventoryStockBreakdownItemId)
            .ToList();

        var stockBreakdownList = await _stockManagementService.GetInventoryStockBreakdownList(stockBreakdownItemIds);

        var tasks = plan.InventoryItemDetails
            .Select(inventoryDetail =>
            {
                var breakdown = stockBreakdownList.FirstOrDefault(item =>
                    item.ItemId == inventoryDetail.InventoryStockBreakdownItemId);
                if (breakdown == null) return null;
                return _stockManagementService.UpdateStocksInventoryStockBreakdown(
                    movementType,
                    breakdown,
                    inventoryDetail.Quantity,
                    plan.CreatedBy,
                    updateType);
            })
            .Where(task => task != null)
            .ToList();

        await Task.WhenAll(tasks);
    }

    protected BuroInventoryMovement CreateBaseInventoryMovement(
        BuroInventoryMovementPlan plan,
        int totalQuantity,
        double totalPrice,
        string sourceOfficeItemId,
        string sourceOfficeName,
        string? recipientOfficeItemId = null,
        string? recipientOfficeName = null,
        EnumInventoryMovementType movementType = EnumInventoryMovementType.Out)
    {
        return new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = plan.InventoryItemId,
            InventoryName = plan.InventoryName,
            InventoryItemDefinition = plan.InventoryItemDefinition,
            CategoryItemId = plan.CategoryItemId,
            Category = plan.Category,
            SubCategoryItemId = plan.SubCategoryItemId,
            SubCategory = plan.SubCategory,
            VendorItemId = plan.VendorItemId,
            VendorName = plan.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = plan.HasSerialNumber,
            SerialNumber = string.Join(',', plan.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = plan.RequiresExpiryDate,
            ExpiryDate = plan.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = movementType,
            SourceOfficeItemId = sourceOfficeItemId,
            SourceOfficeName = sourceOfficeName,
            RecipientOfficeItemId = recipientOfficeItemId,
            RecipientOfficeName = recipientOfficeName,
            Reason = plan.Reason,
            CreatedBy = plan.CreatedBy,
            CreateDate = DateTime.UtcNow
        };
    }

}