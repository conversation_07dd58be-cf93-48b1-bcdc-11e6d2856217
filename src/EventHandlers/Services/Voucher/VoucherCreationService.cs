using EventHandlers.Contracts;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Voucher;

public class VoucherCreationService : IVoucherCreationService
{
    private readonly ILogger<VoucherCreationService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly UnifiedVoucherCreationStrategy _voucherCreationStrategy;

    public VoucherCreationService(
        ILogger<VoucherCreationService> logger,
        ISecurityContextProvider securityContextProvider,
        UnifiedVoucherCreationStrategy voucherCreationStrategy)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _voucherCreationStrategy = voucherCreationStrategy;
    }

    public async Task CreateVoucher(IVoucherEvent command)
    {
        _logger.LogInformation("Starting voucher creation for scope: {VoucherScope}", command.VoucherScopeCategory);

        try
        {
            if (command.Amount < 0)
            {
                throw new ArgumentException("Voucher amount must be a positive value.");
            }

            var securityContext = _securityContextProvider.GetSecurityContext();

            await _voucherCreationStrategy.ProcessVoucherAsync(command, securityContext);

            _logger.LogInformation("Voucher creation completed successfully for scope: {VoucherScope}", command.VoucherScopeCategory);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during voucher creation for scope: {VoucherScope}",
                command.VoucherScopeCategory);
            throw;
        }
    }
}