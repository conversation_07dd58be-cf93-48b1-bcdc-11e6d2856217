using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Contracts;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace EventHandlers.Services.Voucher;

public class ProductLineVoucherConfigurationProvider : IProductLineVoucherConfigurationProvider
{
    private readonly IFinMongoDbRepository _finMongoDbRepository;

    public ProductLineVoucherConfigurationProvider(IFinMongoDbRepository finMongoDbRepository)
    {
        _finMongoDbRepository = finMongoDbRepository;
    }

    public async Task<BuroVoucherConfigurationBase> GetVoucherConfigurationAsync(IVoucherEvent command)
    {

        var voucherConfig = await _finMongoDbRepository.FindOneAsync<BuroProductLineVoucherConfiguration>(
            v => v.ProductLineItemId == command.ProductLineItemId &&
                 v.TransactionTypeTag == command.TransactionTypeTag);

        return voucherConfig;
    }
}