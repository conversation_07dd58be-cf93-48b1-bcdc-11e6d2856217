using EventHandlers.Contracts;
using Infrastructure.Events.Voucher;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Voucher;

public class UpdateAutoVoucherEventService : IUpdateAutoVoucherEventService
{

    private readonly ILogger<UpdateAutoVoucherEventService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateAutoVoucherEventService(
        ILogger<UpdateAutoVoucherEventService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }



    public async Task UpdateAutoVoucherAsync(UpdateAutoVoucherEvent command)
    {
        //_logger.LogInformation("UpdateAutoVoucherAsync process initiated. VoucherGroupItemId {GroupItemId}", command.VoucherGroupItemId);

        //try
        //{
        //    await ProcessUpdateAutoVoucher(command);
        //}
        //catch (Exception ex)
        //{
        //    _logger.LogError(ex, "Error occurred during UpdateAutoVoucherAsync process.");
        //}

    }

    //private async Task ProcessUpdateAutoVoucher(UpdateAutoVoucherEvent command)
    //{
    //    var securityContext = _securityContextProvider.GetSecurityContext();
    //    var currentDateTime = DateTime.UtcNow;

    //    var existingVouchers = _repository.GetItems<BuroVoucher>(
    //        c => c.VoucherGroupItemId == command.VoucherGroupItemId && c.VoucherCreationType == EnumVoucherCreationType.Auto).ToList();

    //    if (!existingVouchers.Any())
    //    {
    //        _logger.LogWarning("No vouchers found for group: {VoucherGroupItemId} and type: {VoucherCreationType}", command.VoucherGroupItemId, EnumVoucherCreationType.Auto);
    //        return;
    //    }

    //    var voucherConfig = await
    //        _repository.GetItemAsync<BuroAutoVoucherConfiguration>(v => v.ProductLineItemId == command.ProductLineItemId && v.TransactionType == command.TransactionType);

    //    if (voucherConfig == null)
    //    {
    //        _logger.LogWarning("Voucher config not found: {ProductLineItemId}", command.ProductLineItemId);
    //        return;
    //    }

    //    var bulkOperations = new List<Task>();

    //    foreach (var voucher in existingVouchers)
    //    {
    //        var matchingLedger = voucherConfig.LedgerEntries?
    //            .FirstOrDefault(le => le.ChartOfAccountItemId == voucher.ChartOfAccountItemId);

    //        if (matchingLedger == null) continue;

    //        var updates = new Dictionary<string, object>
    //        {
    //            { nameof(BuroVoucher.LastUpdateDate), currentDateTime },
    //            { nameof(BuroVoucher.LastUpdatedBy), securityContext.UserId },
    //            { nameof(BuroVoucher.Narration), command.Narration },
    //            {
    //                nameof(BuroVoucher.DebitAmount),
    //                matchingLedger.VoucherType == EnumBuroVoucherType.Debit ? command.Amount : 0
    //            },
    //            {
    //                nameof(BuroVoucher.CreditAmount),
    //                matchingLedger.VoucherType == EnumBuroVoucherType.Credit ? command.Amount : 0
    //            }
    //        };

    //        bulkOperations.Add(
    //            _repository.UpdateAsync<BuroVoucher>(
    //                x => x.ItemId == voucher.ItemId,
    //                updates
    //            )
    //        );
    //    }

    //    await Task.WhenAll(bulkOperations);

    //    _logger.LogInformation("Updated {Count} vouchers in group: {VoucherGroupItemId}",
    //        existingVouchers.Count, command.VoucherGroupItemId);
    //}
}