using EventHandlers.Contracts;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace EventHandlers.Services.Voucher;

public class VoucherConfigurationProviderFactory
{
    private readonly IServiceProvider _serviceProvider;

    public VoucherConfigurationProviderFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public IVoucherConfigurationProvider GetProvider(EnumVoucherScopeCategory scopeCategory)
    {
        return scopeCategory switch
        {
            EnumVoucherScopeCategory.Product => _serviceProvider.GetRequiredService<IProductLineVoucherConfigurationProvider>(),
            _ => _serviceProvider.GetRequiredService<IStandardVoucherConfigurationProvider>()
        };
    }
}