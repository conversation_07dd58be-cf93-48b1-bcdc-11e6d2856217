using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Contracts;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace EventHandlers.Services.Voucher;

public class StandardVoucherConfigurationProvider : IStandardVoucherConfigurationProvider
{
    private readonly IFinMongoDbRepository _finMongoDbRepository;

    public StandardVoucherConfigurationProvider(IFinMongoDbRepository finMongoDbRepository)
    {
        _finMongoDbRepository = finMongoDbRepository;
    }

    public async Task<BuroVoucherConfigurationBase> GetVoucherConfigurationAsync(IVoucherEvent command)
    {
        var builder = Builders<BuroStandardVoucherConfiguration>.Filter;
        var filter = builder.Empty;

        if (!string.IsNullOrWhiteSpace(command.VoucherConfigItemId))
        {
            filter &= builder.Eq(c => c.ItemId, command.VoucherConfigItemId);
        }
        else
        {
            filter &= builder.Eq(v => v.TransactionTypeTag, command.TransactionTypeTag);
        }

        var voucherConfig = await _finMongoDbRepository.FindOneAsync(filter);

        return voucherConfig;
    }
}