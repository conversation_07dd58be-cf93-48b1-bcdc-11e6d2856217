using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Events.Register;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Register;

public class ChequeRegisterEventService : IChequeRegisterEventService
{
    private readonly ILogger<ChequeRegisterEventService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public ChequeRegisterEventService(
        ILogger<ChequeRegisterEventService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task HandleChequeRegisterEventAsync(ChequeRegisterPostProcessingEvent command)
    {
        _logger.LogInformation("Starting HandleChequeRegisterEventAsync for MemberItemId: {MemberItemId}", command.MemberItemId);

        var securityContext = _securityContextProvider.GetSecurityContext();

        try
        {
            var chequeRegisterItemId = await _repository.FindWithProjectionAsync<BuroChequeRegister, string>(
                c => c.ChequeNumber == command.ChequeNumber && c.MemberItemId == command.MemberItemId,
                c => c.ItemId);

            if (string.IsNullOrEmpty(chequeRegisterItemId[0]) && !command.IsEligibleReturn)
            {
                await InsertChequeRegisterAsync(command, securityContext);
            }
            else
            {
                await UpdateChequeRegisterAsync(command, chequeRegisterItemId[0], securityContext);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during HandleChequeRegisterEventAsync for MemberItemId: {MemberItemId}", command.MemberItemId);
        }
    }

    private async Task InsertChequeRegisterAsync(ChequeRegisterPostProcessingEvent command, SecurityContext securityContext)
    {
        var employeeInfo = await GetEmployeeInfoAsync(securityContext.UserId);
        var chequeRegister = PrepareChequeRegisterEntity(command, securityContext, employeeInfo);
        _logger.LogInformation("Cheque register created with cheque number: {ChequeNumber}", command.ChequeNumber);
        await _repository.InsertOneAsync(chequeRegister);
        _logger.LogInformation("Cheque register created with cheque number: {ChequeNumber}", command.ChequeNumber);
    }
    private async Task UpdateChequeRegisterAsync(ChequeRegisterPostProcessingEvent command, string chequeRegisterItemId, SecurityContext securityContext)
    {
        var updatedProperties = PrepareUpdatedProperties(command, securityContext.UserId);
        _logger.LogInformation("Cheque register updated with cheque number: {ChequeNumber}", command.ChequeNumber);
        await _repository.UpdateOneAsync<BuroChequeRegister>(c => c.ItemId == chequeRegisterItemId, updatedProperties);
        _logger.LogInformation("Cheque register updated with cheque number: {ChequeNumber}", command.ChequeNumber);

    }

    private static BuroChequeRegister PrepareChequeRegisterEntity(ChequeRegisterPostProcessingEvent command, SecurityContext securityContext, EmployeeIdentityDto employee)
    {
        var chequeRegister = new BuroChequeRegister
        {
            ItemId = Guid.NewGuid().ToString(),
            BranchItemId = command.BranchItemId,
            MemberItemId = command.MemberItemId,
            MemberName = command.MemberName,
            MemberId = command.MemberId,
            ChequeNumber = command.ChequeNumber,
            BankName = command.BankName,
            RoutingNumber = command.RoutingNumber,
            MICRLine = command.MICRLine,
            ChequeBeneficiaryName = command.ChequeBeneficiaryName,
            Amount = command.Amount,
            ChequeFileStorageItemId = command.ChequeFileStorageItemId,
            DepositAccountItemId = command.DepositAccountItemId,
            DepositAccountCode = command.DepositAccountCode,
            DepositAccountNumber = command.DepositAccountNumber,
            ChequeDepositedByEmployeeName = employee.EmployeeName,
            ChequeDepositedByEmployeePin = employee.EmployeePin,
            ChequeDepositedByEmployeeDesignation = employee.DesignationTitle,
            DepositDate = DateTime.UtcNow,
            IsEligibleReturn = command.IsEligibleReturn,
            Status = EnumChequeRegisterStatus.Deposit,
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
            Tags = new[] { Tags.IsAChequeRegister },

        };

        return chequeRegister;
    }
    private static Dictionary<string, object> PrepareUpdatedProperties(ChequeRegisterPostProcessingEvent command, string userId)
    {
        var updatedProperties = new Dictionary<string, object>
        {
            { nameof(BuroChequeRegister.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroChequeRegister.LastUpdatedBy), userId },
            { nameof(BuroChequeRegister.IsEligibleReturn), command.IsEligibleReturn },
        };

        return updatedProperties;
    }

    private async Task<EmployeeIdentityDto> GetEmployeeInfoAsync(string userId)
    {
        var employeeInfo = await _repository.FindWithProjectionAsync<BuroEmployee, EmployeeIdentityDto>(
            e => e.UserItemId == userId,
            e => new EmployeeIdentityDto
            {
                EmployeeName = e.EmployeeName,
                EmployeePin = e.EmployeePin,
                DesignationTitle = e.DesignationTitle,
                CurrentOfficeTitle = e.CurrentOfficeTitle,
                CurrentOfficeCode = e.CurrentOfficeCode
            });

        return employeeInfo[0];
    }
}