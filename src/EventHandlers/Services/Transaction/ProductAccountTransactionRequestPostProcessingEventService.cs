using EventHandlers.Contracts;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;

namespace EventHandlers.Services.Transaction
{
    public class ProductAccountTransactionRequestPostProcessingEventService : IProductAccountTransactionRequestPostProcessingEventService
    {
        private readonly ILogger<ProductAccountTransactionRequestPostProcessingEventService> _logger;
        private readonly IProductAccountTransactionService _productAccountTransactionService;

        public ProductAccountTransactionRequestPostProcessingEventService(
            ILogger<ProductAccountTransactionRequestPostProcessingEventService> logger,
            IProductAccountTransactionService productAccountTransactionService)
        {
            _logger = logger;
            _productAccountTransactionService = productAccountTransactionService;
        }

        public async Task ProcessProductTransactionRequestAsync(ProductAccountTransactionRequestPostProcessingEvent command)
        {
            _logger.LogInformation("Starting processing product account transaction request with Id: {TransactionItemId}", 
                string.Join(", ", command.ProductAccountTransactionRequestItemIds));

            foreach (var requestId in command.ProductAccountTransactionRequestItemIds)
            {
                _logger.LogInformation("Processing product account transaction request with Id: {TransactionItemId}", requestId);

                await _productAccountTransactionService.ExecuteAsync(requestId);

                _logger.LogInformation("Successfully processed product account transaction request with Id: {TransactionItemId}", requestId);
            }

            _logger.LogInformation("Successfully processed product account transaction request with Id: {TransactionItemId}",
                string.Join(", ", command.ProductAccountTransactionRequestItemIds));
        }
    }
}
