using EventHandlers.Contracts;
using FinMongoDbRepositories;
using Infrastructure.Events.Transaction;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Transaction
{
    public class TrackLoanPaymentEventService : ITrackAccountPaymentEventService
    {
        private readonly ILogger<TrackLoanPaymentEventService> _logger;
        private readonly IFinMongoDbRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;

        public TrackLoanPaymentEventService(
            ILogger<TrackLoanPaymentEventService> logger,
            IFinMongoDbRepository repository,
            ISecurityContextProvider securityContextProvider)
        {
            _logger = logger;
            _repository = repository;
            _securityContextProvider = securityContextProvider;
        }

        public async Task TrackAccountPaymentAsync(TrackAccountPaymentEvent command)
        {
            _logger.LogInformation("Starting TrackAccountPaymentAsync");

            var currentEmployee = await GetCurrentEmployeeAsync();
            var branchOfficeInformation = await GetBranchOfficeByOfficeItemIdAsync(currentEmployee.CurrentOfficeItemId);
            (Dictionary<string, string> memberToCenterMapping, Dictionary<string, BuroCenter> centersDictionary) = await GetMemberToCenterMappingsAsync(command);

            var payments = new List<BuroMemberAccountPayment>();

            foreach (var payment in command.AccountPayments)
            {
                var centerItemId = memberToCenterMapping[payment.MemberItemId];
                var centerInformation = centersDictionary[centerItemId];

                var loanPayment = new BuroMemberAccountPayment
                {
                    AccountItemId = payment.AccountItemId,
                    MemberItemId = payment.MemberItemId,
                    MemberScheduleItemId = payment.MemberScheduleItemId,
                    PaidAmount = payment.PaidAmount,
                    ProgramOrganizerEmployeeItemId = currentEmployee?.ItemId,
                    ProgramOrganizerEmployeeName = currentEmployee?.EmployeeName,
                    BranchOfficeItemId = branchOfficeInformation?.ItemId,
                    BranchOfficeName = branchOfficeInformation?.OfficeName,
                    BranchOfficeCode = branchOfficeInformation?.OfficeCode,
                    BranchOfficeSequenceNumber = branchOfficeInformation?.OfficeId,
                    CenterItemId = centerInformation?.ItemId,
                    CenterName = centerInformation?.Name,
                    CenterSequenceNumber = centerInformation?.CenterId,
                    ProductType = payment.ProductType
                };

                loanPayment.AddEntityBasicInfo();
                payments.Add(loanPayment);
            }


            await _repository.InsertManyAsync(payments);

            _logger.LogInformation("Successfully processed payment");
        }

        private async Task<(Dictionary<string, string> memberToCenterMapping, Dictionary<string, BuroCenter> centersDictionary)> GetMemberToCenterMappingsAsync(TrackAccountPaymentEvent command)
        {
            var memberItemIds = command.AccountPayments.Select(x => x.MemberItemId).Distinct().ToList();

            var centerItemIds = await _repository.FindWithProjectionAsync<BuroMember, Tuple<string, string>>(
                dataFilters: m => memberItemIds.Contains(m.ItemId),
                projectionExpression: m => Tuple.Create(m.ItemId, m.CenterItemId),
                applyHierarchy: false);

            var memberToCenterMapping = centerItemIds.ToDictionary(x => x.Item1, x => x.Item2);

            var centers = await _repository.FindAsync<BuroCenter>(c => memberToCenterMapping.Values.Contains(c.ItemId) && !c.IsMarkedToDelete,
                applyHierarchy: false);

            var centersDictionary = centers.ToDictionary(c => c.ItemId);

            return (memberToCenterMapping, centersDictionary);
        }

        private async Task<BuroEmployee> GetCurrentEmployeeAsync()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var currentEmployee = await _repository.FindOneAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId && !e.IsMarkedToDelete);

            return currentEmployee;
        }

        private async Task<BuroOffice> GetBranchOfficeByOfficeItemIdAsync(string currentOfficeItemId)
        {
            var office = await _repository.FindOneAsync<BuroOffice>(
                b => b.ItemId == currentOfficeItemId
                  && b.OfficeType == EnumOfficeType.BranchOffice
                  && !b.IsMarkedToDelete);

            return office;
        }
    }
}
