using EventHandlers.Contracts;
using Infrastructure.Events.MemberSurvey;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Member
{
    public class MemberAddressChangeApprovalEventService : IMemberAddressChangeApprovalEventService
    {
        private readonly ILogger<MemberAddressChangeApprovalEventService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IRepository _repository;

        public MemberAddressChangeApprovalEventService(
            ILogger<MemberAddressChangeApprovalEventService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
        }

        public async Task ChangeMemberAddressAsync(MemberAddressChangeApprovalEvent command)
        {
            _logger.LogInformation("Starting ChangeMemberAddressAsync for Request Id: {ChangeRequestItemId}", command.ChangeRequestItemId);

            var currentUserId = GetCurrentUserId();

            await AddressChangeRequestAsync(command, currentUserId);

            _logger.LogInformation("Successfully updated address for request Id: {ChangeRequestItemId}", command.ChangeRequestItemId);
        }

        private async Task AddressChangeRequestAsync(MemberAddressChangeApprovalEvent command, string currentUserId)
        {
            await UpdateMemberSurveyChangeRequestStatusAsync(command, currentUserId);

            if (command.IsAccepted)
            {
                var changeRequest = await GetBuroMemberSurveyChangeRequestAsync(command.ChangeRequestItemId);
                var addressProperty = PrepareUpdatedAddress(changeRequest.PropertyChanges);

                await UpdateMemberSurveyAddressAsync(changeRequest, addressProperty, currentUserId);
                await UpdateMemberAdditionalDocumentsAsync(changeRequest, currentUserId);
            }
        }

        private async Task UpdateMemberAdditionalDocumentsAsync(BuroMemberSurveyChangeRequest changeRequest, string currentUserId)
        {
            if (changeRequest.AdditionalDocumentIds.Any())
            {
                return;
            }

            _logger.LogInformation("Inside UpdateMemberAdditionalDocumentsAsync");

            var documentIds = GetMemberAdditionalDocumentIds(changeRequest.MemberItemId);
            documentIds.AddRange(changeRequest.AdditionalDocumentIds);

            var properties = new Dictionary<string, object>
            {
                { nameof(BuroMemberSurvey.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroMemberSurvey.LastUpdatedBy), currentUserId },
                { nameof(BuroMemberSurvey.AdditionalDocumentIds), new BsonArray(documentIds) }
            };

            await _repository.UpdateAsync<BuroMember>(m => m.ItemId == changeRequest.MemberItemId, properties);
        }

        private List<string> GetMemberAdditionalDocumentIds(string memberItemId)
        {
            return _repository.GetItems<BuroMember>(m => m.ItemId == memberItemId)
                .Select(m => m.AdditionalDocumentIds)
                .FirstOrDefault() ?? new List<string>();
        }

        private async Task<BuroMemberSurveyChangeRequest> GetBuroMemberSurveyChangeRequestAsync(string changeRequestItemId)
        {
            return await _repository.GetItemAsync<BuroMemberSurveyChangeRequest>(r => r.ItemId == changeRequestItemId);
        }

        private async Task UpdateMemberSurveyAddressAsync(
            BuroMemberSurveyChangeRequest changeRequest,
            (string propertyName, BuroMemberAddress address) addressProperty,
            string currentUserId)
        {
            _logger.LogInformation("Inside UpdateMemberSurveyAddressAsync");

            if (addressProperty.address == null)
            {
                return;
            }

            var properties = new Dictionary<string, object>
            {
                { nameof(BuroMemberSurvey.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroMemberSurvey.LastUpdatedBy), currentUserId },
                { addressProperty.propertyName, addressProperty.address.ToBsonDocument() }
            };

            if (changeRequest.AdditionalDocumentIds.Any())
            {
                var documentIds = GetMemberSurveyAdditionalDocumentIds(changeRequest.SurveyItemId);
                documentIds.AddRange(changeRequest.AdditionalDocumentIds);

                properties.Add(nameof(BuroMemberSurvey.AdditionalDocumentIds), new BsonArray(documentIds));
            }

            await _repository.UpdateAsync<BuroMemberSurvey>(r => r.ItemId == changeRequest.SurveyItemId, properties);
        }

        private List<string> GetMemberSurveyAdditionalDocumentIds(string surveyItemId)
        {
            return _repository.GetItems<BuroMemberSurvey>(s => s.ItemId == surveyItemId)
                .Select(s => s.AdditionalDocumentIds)
                .FirstOrDefault() ?? new List<string>();
        }

        private static (string propertyName, BuroMemberAddress address) PrepareUpdatedAddress(List<PropertyChange> propertyChanges)
        {
            if (!propertyChanges.Any())
            {
                return default;
            }

            return (propertyChanges[0].PropertyName, JsonConvert.DeserializeObject<BuroMemberAddress>(propertyChanges[0].NewValue));
        }

        private async Task UpdateMemberSurveyChangeRequestStatusAsync(MemberAddressChangeApprovalEvent command, string currentUserId)
        {
            var properties = new Dictionary<string, object>
            {
                { nameof(BuroMemberSurveyChangeRequest.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroMemberSurveyChangeRequest.LastUpdatedBy), currentUserId },
                { nameof(BuroMemberSurveyChangeRequest.Status), GetMemberSurveyChangeRequestStatus(command.IsAccepted) }
            };

            await _repository.UpdateAsync<BuroMemberSurveyChangeRequest>(r => r.ItemId == command.ChangeRequestItemId, properties);
        }

        private static EnumChangeRequestStatus GetMemberSurveyChangeRequestStatus(bool isAccepted)
        {
            return isAccepted ? EnumChangeRequestStatus.Approved : EnumChangeRequestStatus.Rejected;
        }

        private string GetCurrentUserId()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            return securityContext.UserId;
        }
    }
}
