using EventHandlers.Contracts;
using Infrastructure.Events.MemberSurvey;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Member
{
    public class MemberPhoneNumberChangeApprovalEventService : IMemberPhoneNumberChangeApprovalEventService
    {
        private readonly ILogger<MemberPhoneNumberChangeApprovalEventService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IRepository _repository;

        public MemberPhoneNumberChangeApprovalEventService(
            ILogger<MemberPhoneNumberChangeApprovalEventService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
        }

        public async Task ChangeMemberPhoneNumberAsync(MemberPhoneNumberChangeApprovalEvent command)
        {
            _logger.LogInformation("Starting ChangeMemberPhoneNumberAsync for Request Id: {ChangeRequestItemId}", command.ChangeRequestItemId);

            var currentUserId = GetCurrentUserId();

            await PhoneNumberChangeRequestAsync(command, currentUserId);

            _logger.LogInformation("Successfully updated phone number for request Id: {ChangeRequestItemId}", command.ChangeRequestItemId);
        }

        private async Task PhoneNumberChangeRequestAsync(MemberPhoneNumberChangeApprovalEvent command, string currentUserId)
        {
            await UpdateMemberSurveyChangeRequestStatusAsync(command, currentUserId);

            if (command.IsAccepted)
            {
                var changeRequest = await _repository.GetItemAsync<BuroMemberSurveyChangeRequest>(r => r.ItemId == command.ChangeRequestItemId);
                var newContacts = PrepareUpdatedContacts(changeRequest.PropertyChanges);

                await UpdateMemberSurveyPhoneNumberAsync(changeRequest, newContacts, currentUserId);
                await UpdateMemberPhoneNumberAsync(changeRequest, newContacts, currentUserId);
            }
        }

        private async Task UpdateMemberSurveyPhoneNumberAsync(
            BuroMemberSurveyChangeRequest changeRequest,
            List<string> newContacts,
            string currentUserId)
        {
            if (newContacts.Any())
            {
                var properties = new Dictionary<string, object>
                {
                    { nameof(BuroMemberSurvey.LastUpdateDate), DateTime.UtcNow },
                    { nameof(BuroMemberSurvey.LastUpdatedBy), currentUserId },
                    { nameof(BuroMemberSurvey.ContactNumbers), new BsonArray(newContacts) }
                };

                await _repository.UpdateAsync<BuroMemberSurvey>(r => r.ItemId == changeRequest.SurveyItemId, properties);
            }
        }

        private async Task UpdateMemberPhoneNumberAsync(
            BuroMemberSurveyChangeRequest changeRequest,
            List<string> newContacts,
            string currentUserId)
        {
            if (newContacts.Any())
            {
                var properties = new Dictionary<string, object>
                {
                    { nameof(BuroMember.LastUpdateDate), DateTime.UtcNow },
                    { nameof(BuroMember.LastUpdatedBy), currentUserId },
                    { nameof(BuroMember.ContactNumbers), new BsonArray(newContacts) }
                };

                await _repository.UpdateAsync<BuroMember>(r => r.ItemId == changeRequest.MemberItemId, properties);
            }
        }

        private static List<string> PrepareUpdatedContacts(List<PropertyChange> propertyChanges)
        {
            if (!propertyChanges.Any())
            {
                return new List<string>();
            }

            return JsonConvert.DeserializeObject<List<string>>(propertyChanges[0].NewValue);
        }

        private async Task UpdateMemberSurveyChangeRequestStatusAsync(MemberPhoneNumberChangeApprovalEvent command, string currentUserId)
        {
            var properties = new Dictionary<string, object>
            {
                { nameof(BuroMemberSurveyChangeRequest.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroMemberSurveyChangeRequest.LastUpdatedBy), currentUserId },
                { nameof(BuroMemberSurveyChangeRequest.Status), GetMemberSurveyChangeRequestStatus(command.IsAccepted) }
            };

            await _repository.UpdateAsync<BuroMemberSurveyChangeRequest>(r => r.ItemId == command.ChangeRequestItemId, properties);
        }

        private static EnumChangeRequestStatus GetMemberSurveyChangeRequestStatus(bool isAccepted)
        {
            return isAccepted ? EnumChangeRequestStatus.Approved : EnumChangeRequestStatus.Rejected;
        }

        private string GetCurrentUserId()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            return securityContext.UserId;
        }
    }
}
