using EventHandlers.Contracts;
using Infrastructure.Constants;
using Infrastructure.Events.Center;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Center
{
    public class ChangePOrForMembersService : IChangePOrForMembersService
    {
        private readonly ILogger<ChangePOrForMembersService> _logger;
        private readonly IRepository _repository;
        public ChangePOrForMembersService(
            IRepository repository,
            ILogger<ChangePOrForMembersService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        public async Task ChangeProgramOrganizerForMembers(ChangePOrForMembersEvent changePoEvent)
        {
            try
            {
                _logger.LogInformation("ChangePOrForMembersService -> ChangeProgramOrganizerForMembers-> START");
                var programOrganizer = await _repository.GetItemAsync<BuroEmployee>(e => e.ItemId == changePoEvent.ProgramOrganizerItemId);
                await UpdateMembersDataWithNewProgramOrganizer(programOrganizer, changePoEvent.CenterItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Occured while Updating Members");
            }
        }

        private async Task UpdateMembersDataWithNewProgramOrganizer(BuroEmployee programOrganizer, string centerItemId)
        {
            _logger.LogInformation("ChangePOrForMembersService -> UpdateMembersDataWithNewProgramOrganizer-> START");
            var updatedProperties = PrepareToBeUpdatedData(programOrganizer);
            await _repository.UpdateManyAsync<BuroMember>(x => x.CenterItemId == centerItemId, updatedProperties);
            _logger.LogInformation("ChangePOrForMembersService -> UpdateMembersDataWithNewProgramOrganizer -> END");
        }

        private Dictionary<string, object> PrepareToBeUpdatedData(BuroEmployee programOrganizer)
        {
            _logger.LogInformation("ChangePOrForMembersService -> PrepareToBeUpdatedData -> START");
            return new Dictionary<string, object>
            {
                    {nameof(BuroMember.LastUpdateDate), DateTime.UtcNow},
                    {nameof(BuroMember.LastUpdatedBy), BuroConstants.SuperAdmin },
                    {nameof(BuroMember.ProgramOrganizerEmployeeItemId), programOrganizer.ItemId },
                    {nameof(BuroMember.ProgramOrganizerEmployeeName), programOrganizer.EmployeeName },
                    {nameof(BuroMember.ProgramOrganizerEmployeePin), programOrganizer.EmployeePin },
            };
        }
    }
}
