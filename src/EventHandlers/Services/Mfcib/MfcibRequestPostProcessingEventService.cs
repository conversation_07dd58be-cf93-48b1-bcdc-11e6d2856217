using EventHandlers.Contracts;
using EventHandlers.Models.Mfcib;
using Infrastructure.Constants;
using Infrastructure.Events.Mfcib;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Mfcib;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Mfcib
{
    public class MfcibRequestPostProcessingEventService : IMfcibRequestPostProcessingEventService
    {
        // NOSONAR WIP
        private readonly DateTime _ProductionDate = DateTime.UtcNow;
        private readonly DateTime _AccountingDate = DateTime.UtcNow;

        private readonly ILogger<MfcibRequestPostProcessingEventService> _logger;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;

        public MfcibRequestPostProcessingEventService(
            ILogger<MfcibRequestPostProcessingEventService> logger,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task ProcessMfcibRequestAsync(MfcibRequestPostProcessingEvent command)
        {
            _logger.LogInformation("Start processing CIB request for Request Ids: {MfcibRequestItemId}", command.MfcibRequestItemId);

            try
            {
                var errors = new List<MfcibPreproceingErrorInfoDto>();
                var mfcibRequest = await GetMfcibRequestAsync(command);
                var members = GetRequestedMembersData(mfcibRequest);

                var headerResponse = CreateCibComponent(() => CreateCibHeaderData(mfcibRequest), errors, EnumMfcibDataType.Header);
                var personResponse = CreateCibComponent(() => CreateCibPersonData(mfcibRequest, members), errors, EnumMfcibDataType.Person);

                var filteredMembers = FilterMembersWithErrors(members, personResponse.Errors);
                var contractResponse = CreateCibComponent(() => CreateCibContractData(mfcibRequest, filteredMembers), errors, EnumMfcibDataType.Contract);

                await SaveMfcibComponentsAsync(headerResponse.Data, personResponse.Data, contractResponse.Data);

                SendToHandleCIBDataSubmission(mfcibRequest);

                HandleErrors(errors);

                _logger.LogInformation("CIB request successfully processed for Request Ids: {MfcibRequestItemId}", command.MfcibRequestItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during CIB request post processing for Request Ids: {MfcibRequestItemId}", command.MfcibRequestItemId);
            }
        }

        private void HandleErrors(List<MfcibPreproceingErrorInfoDto> errors)
        {
            LogErrorsByDataType(errors, EnumMfcibDataType.Person);
            LogErrorsByDataType(errors, EnumMfcibDataType.Contract);
        }

        void LogErrorsByDataType(List<MfcibPreproceingErrorInfoDto> errors, EnumMfcibDataType dataType)
        {
            var dataErrors = errors.Where(e => e.MfcibDataType == dataType)
                .ToList();

            if (!dataErrors.Any())
            {
                return;
            }

            var unknownErrors = dataErrors.Find(e => e.HasUnknownException);

            if (unknownErrors != null)
            {
                _logger.LogError("Error occurred while creating {DataType} data. Message: {Message}", dataType, unknownErrors.Message);
            }

            var memberItemIds = dataErrors.Where(e => !e.HasUnknownException)
                .Select(e => e.MemberItemId)
                .ToList();

            if (memberItemIds.Any())
            {
                _logger.LogError("Error occurred while creating {DataType} data with Member Ids: {MemberItemIds}", dataType, string.Join(",", memberItemIds));
            }
        }

        private async Task SaveMfcibComponentsAsync(
            List<BuroMfcibHeaderData> headers,
            List<BuroMfcibPersonData> persons,
            List<BuroMfcibContractData> contracts)
        {
            await TrySaveAsync(headers);
            await TrySaveAsync(persons);
            await TrySaveAsync(contracts);
        }

        private async Task TrySaveAsync<T>(List<T> data)
        {
            if (!data.Any())
            {
                return;
            }

            await _repository.SaveAsync(data);
        }

        private static (List<MfcibPreproceingErrorInfoDto> Errors, List<T> Data) CreateCibComponent<T>(
            Func<(List<MfcibPreproceingErrorInfoDto> Errors, List<T> Data)> createCibData,
            List<MfcibPreproceingErrorInfoDto> errors,
            EnumMfcibDataType mfcibDataType)
            where T : class
        {
            var (Errors, Data) = createCibData();

            errors.TryAddRange(Errors);

            ValidateErrors(errors, mfcibDataType);

            return (errors, Data);
        }

        private static void ValidateErrors(List<MfcibPreproceingErrorInfoDto> errors, EnumMfcibDataType mfcibDataType)
        {
            if (errors.Exists(e => e.MfcibDataType == mfcibDataType && e.HasUnknownException))
            {
                throw new InvalidOperationException($"An error occurred while creating {Enum.GetName(typeof(EnumMfcibDataType), mfcibDataType)} data.");
            }
        }

        private static List<BuroMember> FilterMembersWithErrors(List<BuroMember> members, List<MfcibPreproceingErrorInfoDto> personErrors)
        {
            var personErrorWithMemberItemIds = personErrors.Where(e => e.MfcibDataType == EnumMfcibDataType.Person && !e.HasUnknownException)
                .Select(e => e.MemberItemId)
                .ToList();

            return members.Where(m => personErrorWithMemberItemIds.Contains(m.ItemId)).ToList();
        }

        private async Task<BuroMfcibRequest> GetMfcibRequestAsync(MfcibRequestPostProcessingEvent command)
        {
            return await _repository.GetItemAsync<BuroMfcibRequest>(r => r.ItemId == command.MfcibRequestItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroMfcibRequest)));
        }

        private (List<MfcibPreproceingErrorInfoDto> Errors, List<BuroMfcibContractData> Contracts) CreateCibContractData(
            BuroMfcibRequest mfcibRequest,
            List<BuroMember> members)
        {
            _logger.LogInformation("Inside CreateCibContractData with request Id: {ItemId}", mfcibRequest.ItemId);

            var mfcibDataType = EnumMfcibDataType.Contract;
            var valueOfMfcibDataType = BuroCibConstants.MfcibDataTypeMapping.GetValueOrDefault(mfcibDataType);
            var contracts = new List<BuroMfcibContractData>();
            var errors = new List<MfcibPreproceingErrorInfoDto>();

            var memberItemIds = members.Select(m => m.ItemId);
            var loanAccounts = GetLoanAccountByMemberIds(memberItemIds);

            if (!loanAccounts.Any())
            {
                errors.Add(new MfcibPreproceingErrorInfoDto
                {
                    MfcibRequestItemId = mfcibRequest.ItemId,
                    MfcibDataType = mfcibDataType,
                    Message = string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroLoanAccount))
                });

                return (errors, contracts);
            }

            for (int i = 1; i <= members.Count; i++)
            {
                try
                {
                    var loanAccount = loanAccounts.FirstOrDefault(la => la.MemberItemId == members[i].ItemId);
                    var contract = CreateContractData(mfcibRequest, members, valueOfMfcibDataType, i, loanAccount);

                    contract.AddEntityBasicInfo();
                    contracts.Add(contract);
                }
                catch (Exception ex)
                {
                    errors.Add(new MfcibPreproceingErrorInfoDto
                    {
                        MfcibRequestItemId = mfcibRequest.ItemId,
                        MemberItemId = members[i].ItemId,
                        MfcibDataType = mfcibDataType,
                        Message = ex.Message,
                        StackTrace = ex.StackTrace
                    });
                }
            }

            return (errors, contracts);
        }

        private IQueryable<BuroLoanAccount> GetLoanAccountByMemberIds(IEnumerable<string> memberItemIds)
        {
            return _repository.GetItems<BuroLoanAccount>(la => memberItemIds.Contains(la.MemberItemId));
        }

        private static BuroMfcibContractData CreateContractData(BuroMfcibRequest mfcibRequest, List<BuroMember> members, string dataType, int i, BuroLoanAccount loanAccount)
        {
            return new BuroMfcibContractData
            {
                RecordNumber = i.ToString(),
                MfcibRequestItemId = mfcibRequest.ItemId,
                DataType = dataType,
                BranchOfficeItemId = members[i].BranchOfficeItemId,
                BranchOfficeCode = members[i].BranchOfficeCode,
                MemberItemId = members[i].ItemId,
                MemberSequenceNumber = members[i].MemberSequenceNumber,
                LoanAccountSequenceNumber = loanAccount.AccountSequenceNumber,
                LoanType = BuroCibConstants.LoanTypeMapping.GetValueOrDefault(loanAccount.LoanType),
                LoanDisbursementDate = loanAccount.LoanDisbursementDate.ToStringCibFormate(),
                PlanedLoanEndDate = loanAccount.PlanedLoanEndDate.ToStringCibFormate(),
                LastInstallmentPaidDate = loanAccount.LastInstallmentPaidDate.ToStringCibFormate(),
                DisbursedAmount = loanAccount.DisbursedAmount.ToString(),
                TotalOutstandingAmount = loanAccount.Balance.ToString(),
                PaymentInterval = BuroCibConstants.PaymentIntervalMapping.GetValueOrDefault(loanAccount.PaymentInterval),
                TotalNumberOfInstallment = loanAccount.TotalNumberOfInstallment.ToString(),
                InstallmentAmount = loanAccount.InstallmentAmount.ToString(),
                TotalNumberOfRemainingInstallment = loanAccount.TotalNumberOfRemainingInstallment.ToString(),
                TotalNumberOfOverDueInstallment = loanAccount.TotalNumberOfOverDueInstallment.ToString(),
                OverDueAmount = loanAccount.OverDueAmount.ToString(),
                LoanStatus = BuroCibConstants.ProductAccountStatusMapping.GetValueOrDefault(loanAccount.AccountStatus),
                RescheduleNumber = loanAccount.RescheduleNumber,
                LastRescheduleDate = loanAccount.LastRescheduleDate.ToStringCibFormate(),
                WriteOffAmount = loanAccount.WriteOffAmount.ToString(),
                WriteOffDate = loanAccount.WriteOffDate.ToStringCibFormate(),
                ContractPhase = BuroCibConstants.ContractPhaseMapping.GetValueOrDefault(loanAccount.ContractPhase),
                TotalLoanDurationMonth = loanAccount.TotalLoanDurationMonth.ToString(),
                ActualLoanEndDate = loanAccount.ActualLoanEndDate.ToStringCibFormate(),
                LoanApplicationPurposeCode = loanAccount.LoanSectorCode,
                CompulsorySavingsAmount = loanAccount.CompulsorySavingsAmount.ToString(),
                VoluntarySavingsAmount = loanAccount.VoluntarySavingsAmount.ToString(),
                TermSavingsAmount = loanAccount.TermSavingsAmount.ToString(),
                IsSubsidizedCredit = loanAccount.IsSubsidizedCredit.ToStringCibFormate(),
                LoanServiceCharge = loanAccount.LoanServiceCharge.ToString(),
                TransactionMedium = BuroCibConstants.TransactionMediumMapping.GetValueOrDefault(loanAccount.TransactionMedium),
                AdvancePaymentAmount = loanAccount.AdvancePaymentAmount.ToString(),
                HasLawSuit = loanAccount.HasLawSuit.ToStringCibFormate(),
                IsMicroEnterprise = loanAccount.IsMicroEnterprise.ToStringCibFormate(),
                HasMemberWelfareFundCoverage = loanAccount.HasMemberWelfareFundCoverage.ToStringCibFormate(),
                HasInsuranceCoverage = loanAccount.HasInsuranceCoverage.ToStringCibFormate()
            };
        }

        private (List<MfcibPreproceingErrorInfoDto> Errors, List<BuroMfcibPersonData> Persons) CreateCibPersonData(
            BuroMfcibRequest mfcibRequest,
            List<BuroMember> members)
        {
            _logger.LogInformation("Inside CreateCibPersonData with request Id: {ItemId}", mfcibRequest.ItemId);

            var mfcibDataType = EnumMfcibDataType.Person;
            var valueOfMfcibDataType = BuroCibConstants.MfcibDataTypeMapping.GetValueOrDefault(mfcibDataType);

            var persons = new List<BuroMfcibPersonData>();
            var errors = new List<MfcibPreproceingErrorInfoDto>();
            var surveys = GetSurveysAssociatedWithMembers(members);

            if (!surveys.Any())
            {
                errors.Add(new MfcibPreproceingErrorInfoDto
                {
                    MfcibRequestItemId = mfcibRequest.ItemId,
                    MfcibDataType = mfcibDataType,
                    Message = string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroMemberSurvey))
                });

                return (errors, persons);
            }

            for (int i = 1; i <= members.Count; i++)
            {
                try
                {
                    var survey = surveys.FirstOrDefault(s => s.ItemId == members[i].SurveyItemId);
                    var person = CreatePersonData(mfcibRequest, valueOfMfcibDataType, i, members[i], survey);

                    persons.Add(person);
                }
                catch (Exception ex)
                {
                    errors.Add(new MfcibPreproceingErrorInfoDto
                    {
                        MfcibRequestItemId = mfcibRequest.ItemId,
                        MemberItemId = members[i].ItemId,
                        MfcibDataType = mfcibDataType,
                        Message = ex.Message,
                        StackTrace = ex.StackTrace
                    });
                }
            }

            return (errors, persons);
        }

        private IQueryable<BuroMemberSurvey> GetSurveysAssociatedWithMembers(List<BuroMember> members)
        {
            var memberSurveyItemIds = members.Select(m => m.SurveyItemId);

            return _repository.GetItems<BuroMemberSurvey>(ms => memberSurveyItemIds.Contains(ms.ItemId));
        }

        private List<BuroMember> GetRequestedMembersData(BuroMfcibRequest cibRequest)
        {
            var members = _repository.GetItems<BuroMember>(m => cibRequest.MemberItemIds.Contains(m.ItemId)).ToList();

            if (!members.Any())
            {
                throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroMember)));
            }

            return members;
        }

        private static BuroMfcibPersonData CreatePersonData(BuroMfcibRequest cibRequest, string dataType, int recordNumber, BuroMember member, BuroMemberSurvey survey)
        {
            var person = new BuroMfcibPersonData
            {
                RecordNumber = recordNumber.ToString(),
                MfcibRequestItemId = cibRequest.ItemId,
                DataType = dataType,
                MemberItemId = member.ItemId,
                MemberName = member.MemberName,
                MemberSequenceNumber = member.MemberSequenceNumber,
                BranchOfficeItemId = member.BranchOfficeItemId,
                BranchOfficeCode = member.BranchOfficeCode,
                OccupationType = survey.Occupation.ToString(),
                FatherName = member.FatherName,
                MotherName = member.MotherName,
                MaritalStatus = member.MaritalStatus.ToString(),
                SpouseName = survey.SpouseName,
                Gender = BuroCibConstants.GenderOptionMapping.GetValueOrDefault(survey.Gender),
                DateOfBirth = survey.DateOfBirth.ToStringCibFormate(),
                NIDNumber = survey.NIDNumber,
                SmartCardIdNumber = survey.SmartCardIdNumber,
                BirthCertificateNumber = survey.BirthCertificateNumber,
                TINNumber = survey.TINNumber,
                OtherIdType = survey.OtherIdType.ToString(),
                OtherIdNumber = survey.OtherIdNumber,
                OtherIdExpiryDate = survey.OtherIdExpiryDate.ToStringCibFormate(),
                OtherIdProvidingCountry = survey.OtherIdProvidingCountry,
                ContactNumber = survey.ContactNumbers[0],
                PresentAddress = survey.PresentAddress.ToStringCibFormate(),
                PresentAddressThanaCode = survey.PresentAddress.UpazilaId,
                PresentAddressDistrictCode = survey.PresentAddress.DistrictId,
                PresentAddressCountryCode = BuroCibConstants.CountryCode,
                PermanentAddress = survey.PermanentAddress.ToStringCibFormate(),
                PermanentAddressThanaCode = survey.PermanentAddress.UpazilaId,
                PermanentAddressDistrictCode = survey.PermanentAddress.DistrictId,
                PermanentAddressCountryCode = BuroCibConstants.CountryCode,
                AcademicQualification = survey.AcademicQualification.ToString(),
            };

            person.AddEntityBasicInfo();

            return person;
        }

        private (List<MfcibPreproceingErrorInfoDto> Errors, List<BuroMfcibHeaderData> Headers) CreateCibHeaderData(BuroMfcibRequest mfcibRequest)
        {
            _logger.LogInformation("Inside CreateCibHeaderData with request Id: {ItemId}", mfcibRequest.ItemId);

            var mfcibDataType = EnumMfcibDataType.Header;
            var dataType = BuroCibConstants.MfcibDataTypeMapping.GetValueOrDefault(mfcibDataType);
            var headers = new List<BuroMfcibHeaderData>();
            var errors = new List<MfcibPreproceingErrorInfoDto>();

            try
            {
                var header = new BuroMfcibHeaderData
                {
                    MfcibRequestItemId = mfcibRequest.ItemId,
                    DataType = dataType,
                    MFICode = BuroCibConstants.MFICode,
                    ProductionDate = _ProductionDate.ToStringCibFormate(),
                    AccountingDate = _AccountingDate.ToStringCibFormate()
                };

                header.AddEntityBasicInfo();
                headers.Add(header);
            }
            catch (Exception ex)
            {
                errors.Add(new MfcibPreproceingErrorInfoDto
                {
                    MfcibRequestItemId = mfcibRequest.ItemId,
                    MfcibDataType = mfcibDataType,
                    Message = ex.Message,
                    StackTrace = ex.StackTrace
                });
            }

            return (errors, headers);
        }

        private void SendToHandleCIBDataSubmission(BuroMfcibRequest cibRequest)
        {
            var payload = new MfcibDataSubmissionPostProcessingEvent
            {
                MfcibRequestItemId = cibRequest.ItemId
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("CIB data submission post processing initiated for request Id: {ItemId}", cibRequest.ItemId);
        }
    }
}
