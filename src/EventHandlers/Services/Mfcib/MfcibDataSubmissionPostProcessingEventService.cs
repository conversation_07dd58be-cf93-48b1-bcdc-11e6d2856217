using EventHandlers.Contracts;
using EventHandlers.Models.Mfcib;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Events.Mfcib;
using Infrastructure.Extensions;
using Infrastructure.Services;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Mfcib;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Mfcib
{
    public class MfcibDataSubmissionPostProcessingEventService : IMfcibDataSubmissionPostProcessingEventService
    {
        private readonly string _UnknownCode = "0222";
        private readonly ILogger<MfcibDataSubmissionPostProcessingEventService> _logger;
        private readonly IBusinessConfig _businessConfig;
        private readonly IRepository _repository;
        private readonly IBuroStorageServiceClient _buroStorageServiceClient;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IExcelGenerationService _excelGenerationService;

        public MfcibDataSubmissionPostProcessingEventService(
            ILogger<MfcibDataSubmissionPostProcessingEventService> logger,
            IBusinessConfig businessConfig,
            IRepository repository,
            IBuroStorageServiceClient buroStorageServiceClient,
            ISecurityContextProvider securityContextProvider,
            IExcelGenerationService excelGenerationService)
        {
            _logger = logger;
            _businessConfig = businessConfig;
            _repository = repository;
            _buroStorageServiceClient = buroStorageServiceClient;
            _securityContextProvider = securityContextProvider;
            _excelGenerationService = excelGenerationService;
        }

        public async Task ProcessMfcibDataAsync(MfcibDataSubmissionPostProcessingEvent command)
        {
            _logger.LogInformation("Start processing CIB data for Request Ids: {MfcibRequestItemId}", command.MfcibRequestItemId);

            try
            {
                var mfcibFileInformations = new List<BuroMfcibFileInformation>();
                var request = new MfcibProcessingRequestDto
                {
                    RequestId = command.MfcibRequestItemId,
                    Headers = GetMfcibHeaderAsync(command.MfcibRequestItemId),
                    Persons = GetMfcibPersonAsync(command.MfcibRequestItemId),
                    Contracts = GetMfcibContractAsync(command.MfcibRequestItemId)
                };

                await ProcessAndUploadFilesAsync(request, EnumMfcibDataType.Person, request.Persons, mfcibFileInformations);
                await ProcessAndUploadFilesAsync(request, EnumMfcibDataType.Contract, request.Contracts, mfcibFileInformations);
                await CompressFileAsync(request, mfcibFileInformations);

                await SaveMfcibFileIds(command.MfcibRequestItemId, mfcibFileInformations);

                _logger.LogInformation("CIB Data successfully processed for Request Ids: {MfcibRequestItemId}", command.MfcibRequestItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during CIB Data post processing for Request Ids: {MfcibRequestItemId}", command.MfcibRequestItemId);
            }
        }

        private async Task CompressFileAsync(MfcibProcessingRequestDto request, List<BuroMfcibFileInformation> mfcibFileInformations)
        {
            var compressedFileName = MakeMfcibFileName(EnumMfcibDataType.None, request.Headers, string.Empty);
            var compressedFileItemId = await _buroStorageServiceClient.CompressFileAsync(compressedFileName, StorageServiceConstants.ParentDirectoryIdForMfcibFiles, mfcibFileInformations.Select(f => f.FileItemId).ToArray());

            if (!string.IsNullOrWhiteSpace(compressedFileItemId))
            {
                mfcibFileInformations.Add(CreateMfcibFileInfo(compressedFileItemId, compressedFileName, EnumMfcibDocumentType.Unspecified, EnumMfcibContentType.ZIP));
            }
        }

        private async Task ProcessAndUploadFilesAsync<T>(
            MfcibProcessingRequestDto request,
            EnumMfcibDataType dataType,
            List<T> data,
            List<BuroMfcibFileInformation> fileList)
        {
            await ProcessFileAsync(data, request, dataType, EnumMfcibContentType.JSON, fileList);
            await ProcessFileAsync(data, request, dataType, EnumMfcibContentType.EXCEL, fileList);
        }

        private async Task ProcessFileAsync<T>(
            List<T> data,
            MfcibProcessingRequestDto request,
            EnumMfcibDataType dataType,
            EnumMfcibContentType contentType,
            List<BuroMfcibFileInformation> fileList)
        {
            var filePath = GetRequestedMfcibFilePath(request.RequestId, dataType, request.Headers, contentType);

            if (contentType == EnumMfcibContentType.JSON)
            {
                GenerateMfcibJson(filePath, request.Headers, data);
            }
            else
            {
                await GenerateMfcibExcel(filePath, data, dataType);
            }

            var fileItemId = await _buroStorageServiceClient.UploadFileAsync(filePath, StorageServiceConstants.ParentDirectoryIdForMfcibFiles);

            if (!string.IsNullOrWhiteSpace(fileItemId))
            {
                var MfcibFileInfo = CreateMfcibFileInfo(fileItemId, Path.GetFileName(filePath), GetDocumentType(dataType), contentType);

                fileList.Add(MfcibFileInfo);
            }
        }

        private static BuroMfcibFileInformation CreateMfcibFileInfo(
            string fileItemId,
            string fileName,
            EnumMfcibDocumentType documentType,
            EnumMfcibContentType contentType)
        {
            return new BuroMfcibFileInformation
            {
                ItemId = Guid.NewGuid().ToString(),
                FileItemId = fileItemId,
                Name = fileName,
                DocumentType = documentType,
                ContentType = contentType,
                CreatedDate = DateTime.UtcNow,
            };
        }

        private static EnumMfcibDocumentType GetDocumentType(EnumMfcibDataType dataType)
        {
            return dataType switch
            {
                EnumMfcibDataType.Person => EnumMfcibDocumentType.Subject,
                EnumMfcibDataType.Contract => EnumMfcibDocumentType.Contract,
                _ => EnumMfcibDocumentType.Unspecified,
            };
        }

        private async Task SaveMfcibFileIds(string mfcibRequestItemId, List<BuroMfcibFileInformation> mfcibFileInformations)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMfcibRequest.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroMfcibRequest.LastUpdatedBy), securityContext.UserId },
                {nameof(BuroMfcibRequest.Status), EnumMfCibRequestStatus.Completed },
                {nameof(BuroMfcibRequest.GeneratedFiles), new BsonArray(mfcibFileInformations.Select(f => f.ToBsonDocument())) }
            };

            await _repository.UpdateAsync<BuroMfcibRequest>(r => r.ItemId == mfcibRequestItemId, properties);
        }

        private async Task GenerateMfcibExcel<T>(string destinationFilePath, List<T> data, EnumMfcibDataType dataType)
        {
            var templatePath = _businessConfig.GetMfcibExcelTemplatePath();
            var excelTemplateCategory = dataType switch
            {
                EnumMfcibDataType.Person => EnumExcelTemplateCategory.MfcibPersonData,
                EnumMfcibDataType.Contract => EnumExcelTemplateCategory.MfcibContractData,
                _ => default
            };

            await _excelGenerationService.GenerateAsync(data, templatePath, destinationFilePath, excelTemplateCategory);
        }

        private string GetRequestedMfcibFilePath(string mfcibRequestItemId, EnumMfcibDataType dataType, List<BuroMfcibHeaderDataDto> headers, EnumMfcibContentType contentType)
        {
            var fileExtension = GetFileExtensionByContentType(contentType);
            var fileName = MakeMfcibFileName(dataType, headers, fileExtension);
            var basePath = _businessConfig.GetRequestedMfcibFilePath();

            if (!Directory.Exists(basePath))
            {
                Directory.CreateDirectory(basePath);
            }

            var subFolderPath = mfcibRequestItemId;
            var filePath = Path.Combine(basePath, subFolderPath, fileName);

            return filePath;
        }

        private static string GetFileExtensionByContentType(EnumMfcibContentType contentType)
        {
            var extensionType = contentType switch
            {
                EnumMfcibContentType.JSON => EnumFileExtension.Json,
                EnumMfcibContentType.EXCEL => EnumFileExtension.Xlsx,
                _ => default
            };

            return FileExtensionHelperService.GetEnumFileExtension(extensionType);
        }

        private string MakeMfcibFileName(EnumMfcibDataType dataType, List<BuroMfcibHeaderDataDto> headers, string extension)
        {
            var mfiCode = headers.Select(h => h.MFICode).FirstOrDefault();
            string cibDataType = GetPartOfContentTypeName(dataType);

            var fileName = $"{mfiCode}_{cibDataType}_{_UnknownCode}{extension}";

            return fileName;
        }

        private static string GetPartOfContentTypeName(EnumMfcibDataType dataType)
        {
            return dataType switch
            {
                EnumMfcibDataType.Person => "sub",
                EnumMfcibDataType.Contract => "con",
                _ => default
            };
        }

        private static void GenerateMfcibJson<T>(string filePath, List<BuroMfcibHeaderDataDto> headers, List<T> datas)
        {
            var headerJsonArray = JArray.FromObject(headers);
            var bodyJsonArray = JArray.FromObject(datas);
            var footerJsonArray = JArray.FromObject(new
            {
                DATATYPE = "F",
                TOTALRECORD = datas.Count,
            });

            var finalArrayOfObject = new JArray(headerJsonArray.Concat(bodyJsonArray).Concat(footerJsonArray));

            using var streamWriter = new StreamWriter(filePath);
            using var jsonTextWriter = new JsonTextWriter(streamWriter);
            jsonTextWriter.Formatting = Formatting.Indented;
            finalArrayOfObject.WriteTo(jsonTextWriter);
        }

        private List<BuroMfcibHeaderDataDto> GetMfcibHeaderAsync(string mfcibRequestItemId)
        {
            var headers = _repository.GetItems<BuroMfcibHeaderData>(h => h.MfcibRequestItemId == mfcibRequestItemId)
                .ToList();

            if (!headers.Any())
            {
                throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroMfcibHeaderData)));
            }

            var headersData = headers.Select(h => h.MapToTarget<BuroMfcibHeaderDataDto>())
                .ToList();

            return headersData;
        }

        private List<BuroMfcibPersonDataDto> GetMfcibPersonAsync(string mfcibRequestItemId)
        {
            var persons = _repository.GetItems<BuroMfcibPersonData>(p => p.MfcibRequestItemId == mfcibRequestItemId)
                .ToList();

            var personsData = persons.Select(p => p.MapToTarget<BuroMfcibPersonDataDto>())
                .ToList();

            return personsData;
        }

        private List<BuroMfcibContractDataDto> GetMfcibContractAsync(string mfcibRequestItemId)
        {
            var contracts = _repository.GetItems<BuroMfcibContractData>(c => c.MfcibRequestItemId == mfcibRequestItemId)
                .ToList();

            var contractsData = contracts.Select(c => c.MapToTarget<BuroMfcibContractDataDto>())
                .ToList();

            return contractsData;
        }
    }
}
