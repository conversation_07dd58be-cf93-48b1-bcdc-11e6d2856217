using EventHandlers.Contracts;
using Infrastructure.Constants;
using Infrastructure.Enums;
using Infrastructure.Events.Center;
using Infrastructure.Events.Office;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Services.Office
{
    public class SyncNumberOfOfficeService : ISyncNumberOfOfficeService
    {
        private readonly ILogger<SyncNumberOfOfficeService> _logger;
        private readonly IRepository _repository;

        public SyncNumberOfOfficeService(
            IRepository repository,
            ILogger<SyncNumberOfOfficeService> logger)
        {
            _logger = logger;
            _repository = repository;
        }

        public async Task UpdateNumberOfOffices(SyncNumberOfOfficeEvent syncEvent)
        {
            try
            {
                _logger.LogInformation("Inside UpdateNumberOfOffices");
                await ProcessNumberOfOfficeCounts(syncEvent.OfficeId);
                _logger.LogInformation("Finished UpdateNumberOfOffices");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in UpdateNumberOfOffices.");
            }
        }

        private async Task ProcessNumberOfOfficeCounts(string officeId)
        {
            _logger.LogInformation("Inside ProcessNumberOfOfficeCounts - START");

            var officeInfo = await GetOfficeById(officeId);
            var levelsRemaining = BuroConstants.MaximumDepthOfOffice;

            while (!string.IsNullOrEmpty(officeInfo.ParentOfficeItemId) && levelsRemaining > 0)
            {
                officeInfo = await GetOfficeById(officeInfo.ParentOfficeItemId);

                var propertiesToBeUpdated = PrepareDataToUpdateOffice(officeInfo.NumberOfChildOffice + 1);
                await UpdateBuroOffice(propertiesToBeUpdated, officeInfo.ItemId);

                if (string.IsNullOrEmpty(officeInfo.ParentOfficeItemId))
                {
                    break;
                }
                levelsRemaining--;
            }
            _logger.LogInformation("Inside ProcessNumberOfOfficeCounts - END");
        }

        private Dictionary<string, object> PrepareDataToUpdateOffice(int countOfChildOffice)
        {
            _logger.LogInformation("SyncNumberOfOfficeService -> PrepareDataToUpdateOffice -> START");
            var updatedProperties = new Dictionary<string, object>
            {
                {nameof(BuroOffice.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroOffice.LastUpdatedBy), BuroConstants.SuperAdmin },
                {nameof(BuroOffice.NumberOfChildOffice),  countOfChildOffice}
            };
            _logger.LogInformation("SyncNumberOfOfficeService -> PrepareDataToUpdateOffice -> END");
            return updatedProperties;
        }

        private async Task UpdateBuroOffice(Dictionary<string, object> properties, string officeId)
        {
            await _repository.UpdateAsync<BuroOffice>(x => x.ItemId == officeId, properties);
        }

        private async Task<BuroOffice> GetOfficeById(string officeId)
        {
            return await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == officeId);
        }

        private async Task<BuroCenter> GetCenterById(string centerId)
        {
            return await _repository.GetItemAsync<BuroCenter>(x => x.ItemId == centerId);
        }

        public async Task UpdateNumberOfCenters(SyncNumberOfCenterEvent syncEvent)
        {
            try
            {
                _logger.LogInformation("Inside UpdateNumberOfCenters");
                await ProcessNumberOfCentersCount(syncEvent);
                _logger.LogInformation("Finished UpdateNumberOfCenters");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred in UpdateNumberOfCenters.");
            }
        }

        private async Task ProcessNumberOfCentersCount(SyncNumberOfCenterEvent syncEvent)
        {
            _logger.LogInformation("Inside ProcessNumberOfCentersCount - START");
            var centerInfo = await GetCenterById(syncEvent.CenterId);
            var branchInfo = await GetOfficeById(centerInfo.BranchId);

            Dictionary<string, object> updatedProperties = new Dictionary<string, object>();

            if (syncEvent.Action == EnumOperationTypes.ADD)
            {
                updatedProperties = PrepareDataToUpdateOffice(branchInfo.NumberOfChildOffice + 1);
            }
            else if (syncEvent.Action == EnumOperationTypes.REMOVE)
            {
                updatedProperties = PrepareDataToUpdateOffice(branchInfo.NumberOfChildOffice - 1);
            }

            await UpdateBuroOffice(updatedProperties, centerInfo.BranchId);
            _logger.LogInformation("Inside ProcessNumberOfCentersCount - END");
        }
    }
}