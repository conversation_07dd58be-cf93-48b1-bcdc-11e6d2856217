using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace EventHandlers.Models.AutoDebit;

public class MemberScheduleDto
{
    public string AccountItemId { get; set; }

    public string AccountCode { get; set; }

    public string AccountSequenceNumber { get; set; }

    public string MemberItemId { get; set; }
    
    public DateTime OriginalPaymentDate { get; set; }

    public DateTime ActualPaymentDate { get; set; }
    public double PayableAmount { get; set; }

    public double? PaidAmount { get; set; }

    public double? DueAmount { get; set; }

    public double? LateFeeAmount { get; set; }

    public PaymentStatus PaymentStatus { get; set; }

    public bool IsAutoDebited { get; set; }

}