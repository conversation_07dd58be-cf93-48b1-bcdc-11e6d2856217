using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace EventHandlers.Models.Mfcib
{
    public class MfcibPreproceingErrorInfoDto
    {
        public string MfcibRequestItemId { get; set; } = default!;
        public EnumMfcibDataType MfcibDataType { get; set; }
        public string MemberItemId { get; set; } = default!;
        public string Message { get; set; } = default!;
        public string StackTrace { get; set; } = default!;

        public bool HasUnknownException
        {
            get { return string.IsNullOrWhiteSpace(MemberItemId); }
        }
    }
}
