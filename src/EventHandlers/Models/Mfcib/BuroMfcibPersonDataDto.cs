using Newtonsoft.Json;

namespace EventHandlers.Models.Mfcib
{
    public class BuroMfcibPersonDataDto : BuroMfcibBaseDto
    {
        [JsonProperty("RECORD_NO")]
        public string RecordNumber { get; set; }

        [JsonIgnore]
        public string BranchOfficeItemId { get; set; }

        [Json<PERSON>roperty("BRANCH_CODE")]
        public string BranchOfficeCode { get; set; }

        [JsonIgnore]
        public string MemberItemId { get; set; }

        [JsonProperty("MEMBERID")]
        public string MemberSequenceNumber { get; set; }

        [JsonProperty("NAME")]
        public string MemberName { get; set; }

        [JsonProperty("OCCUPATION")]
        public string OccupationType { get; set; }

        [JsonProperty("FATHERS_NAME")]
        public string FatherName { get; set; }

        [JsonProperty("MOTHERS_NAME")]
        public string MotherName { get; set; }

        [JsonProperty("MARITAL_STATUS")]
        public string MaritalStatus { get; set; }

        [JsonProperty("SPOUSE_NAME")]
        public string SpouseName { get; set; }

        [Json<PERSON>roperty("GENDER")]
        public string Gender { get; set; }

        [JsonProperty("DOB")]
        public string DateOfBirth { get; set; }

        [JsonProperty("NID")]
        public string NIDNumber { get; set; }

        [JsonProperty("SMARTCARD_NO")]
        public string SmartCardIdNumber { get; set; }

        [JsonProperty("BIRTH_CERTIFICATE_NO")]
        public string BirthCertificateNumber { get; set; }

        [JsonProperty("TIN")]
        public string TINNumber { get; set; }

        [JsonProperty("OTHER_ID_TYPE")]
        public string OtherIdType { get; set; }

        [JsonProperty("OTHER_ID_NO")]
        public string OtherIdNumber { get; set; }

        [JsonProperty("EXPIRY_DATE")]
        public string OtherIdExpiryDate { get; set; }

        [JsonProperty("ISSUE_COUNTRY")]
        public string OtherIdProvidingCountry { get; set; }

        [JsonProperty("CONTACTNO")]
        public string ContactNumber { get; set; }

        [JsonProperty("P_ADDRESS")]
        public string PresentAddress { get; set; }

        [JsonProperty("P_THANA")]
        public string PresentAddressThanaCode { get; set; }

        [JsonProperty("P_DISTRICT")]
        public string PresentAddressDistrictCode { get; set; }

        [JsonProperty("P_COUNTRY")]
        public string PresentAddressCountryCode { get; set; }

        [JsonProperty("PR_ADDRESS")]
        public string PermanentAddress { get; set; }

        [JsonProperty("PR_THANA")]
        public string PermanentAddressThanaCode { get; set; }

        [JsonProperty("PR_DISTRICT")]
        public string PermanentAddressDistrictCode { get; set; }

        [JsonProperty("PR_COUNTRY")]
        public string PermanentAddressCountryCode { get; set; }

        [JsonProperty("ACADEMIC_QUALIFICATION")]
        public string AcademicQualification { get; set; }
    }
}
