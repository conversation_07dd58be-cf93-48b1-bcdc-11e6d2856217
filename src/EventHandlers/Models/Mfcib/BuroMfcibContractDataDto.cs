using Newtonsoft.Json;

namespace EventHandlers.Models.Mfcib
{
    public class BuroMfcibContractDataDto : BuroMfcibBaseDto
    {
        [JsonProperty("RECORD_NO")]
        public string RecordNumber { get; set; }

        [JsonIgnore]
        public string BranchOfficeItemId { get; set; }

        [JsonProperty("BRANCH_CODE")]
        public string BranchOfficeCode { get; set; }

        [JsonIgnore]
        public string MemberItemId { get; set; }

        [JsonProperty("MEMBERID")]
        public string MemberSequenceNumber { get; set; }

        [JsonProperty("LOAN_CODE")]
        public string LoanAccountSequenceNumber { get; set; }

        [JsonProperty("LOAN_TYPE")]
        public string LoanType { get; set; }

        [JsonProperty("LOAN_DISBURSEMENT_DATE")]
        public string LoanDisbursementDate { get; set; }

        [JsonProperty("END_DATE_CONTRACT")]
        public string PlanedLoanEndDate { get; set; }

        [JsonProperty("LAST_INSTALLMENT_PAID_DATE")]
        public string LastInstallmentPaidDate { get; set; }

        [JsonProperty("DISBURSED_AMOUNT")]
        public string DisbursedAmount { get; set; }

        [JsonProperty("TOTAL_OUTSTANDING_AMT")]
        public string TotalOutstandingAmount { get; set; }

        [JsonProperty("PERIODICITY_PAYMENT")]
        public string PaymentInterval { get; set; }

        [JsonProperty("TOTAL_NUM_INSTALLMENT")]
        public string TotalNumberOfInstallment { get; set; }

        [JsonProperty("INSTALLMENT_AMT")]
        public string InstallmentAmount { get; set; }

        [JsonProperty("NUM_REMAINING_INSTALLMENT")]
        public string TotalNumberOfRemainingInstallment { get; set; }

        [JsonProperty("NUM_OVERDUE_INSTALLMENT")]
        public string TotalNumberOfOverDueInstallment { get; set; }

        [JsonProperty("OVERDUE_AMT")]
        public string OverDueAmount { get; set; }

        [JsonProperty("LOAN_STATUS")]
        public string LoanStatus { get; set; }

        [JsonProperty("RESCHEDULE_NO")]
        public string RescheduleNumber { get; set; }

        [JsonProperty("LAST_RESCHEDULE_DATE")]
        public string LastRescheduleDate { get; set; }

        [JsonProperty("WRITE_OFF_AMT")]
        public string WriteOffAmount { get; set; }

        [JsonProperty("WRITE_OFF_DATE")]
        public string WriteOffDate { get; set; }

        [JsonProperty("CONTRACT_PHASE")]
        public string ContractPhase { get; set; }

        [JsonProperty("LOAN_DURATION")]
        public string TotalLoanDurationMonth { get; set; }

        [JsonProperty("ACTUAL_END_DATE_CONTRACT")]
        public string ActualLoanEndDate { get; set; }

        [JsonProperty("ECONOMIC_PURPOSE_CODE")]
        public string LoanApplicationPurposeCode { get; set; }

        [JsonProperty("COMPULSORY_SAVING_AMT")]
        public string CompulsorySavingsAmount { get; set; }

        [JsonProperty("VOLUNTARY_SAVING_AMT")]
        public string VoluntarySavingsAmount { get; set; }

        [JsonProperty("TERM_SAVING_AMT")]
        public string TermSavingsAmount { get; set; }

        [JsonProperty("SUBSIDIZED_CREDIT_FLAG")]
        public string IsSubsidizedCredit { get; set; }

        [JsonProperty("SERVICE_CHARGE_RATE")]
        public string LoanServiceCharge { get; set; }

        [JsonProperty("PAYMENT_MODE")]
        public string TransactionMedium { get; set; }

        [JsonProperty("ADVANCE_PAYMENT_AMT")]
        public string AdvancePaymentAmount { get; set; }

        [JsonProperty("LAW_SUIT")]
        public string HasLawSuit { get; set; }

        [JsonProperty("ME")]
        public string IsMicroEnterprise { get; set; }

        [JsonProperty("MEMBER_WELFARE_FUND")]
        public string HasMemberWelfareFundCoverage { get; set; }

        [JsonProperty("INSURENCE_COVERAGE")]
        public string HasInsuranceCoverage { get; set; }
    }
}
