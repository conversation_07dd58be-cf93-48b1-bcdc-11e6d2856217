using Infrastructure.Models.ExternalSync.Responses;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace EventHandlers.Contracts
{
    public interface ISyncSylviaDataService
    {
        Task<bool> UpdateExternalRequestStatus(string requestItemId, EnumnExternalSyncStatus syncStatus);
        Task<bool> SaveOffices(string requestItemId, List<SylviaOfficeData> officeList);
        Task<bool> ImportOfficeListFromSylvia(string requestItemId);
        Task<bool> ImportEmployeeListFromSylvia(string requestItemId);
    }
}
