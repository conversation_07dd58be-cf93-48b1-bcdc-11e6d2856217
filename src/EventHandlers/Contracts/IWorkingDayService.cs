using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace EventHandlers.Contracts
{
    public interface IWorkingDayService
    {
        Task<HashSet<DateTime>> GetHolidaySetAsync(DateTime startDate, string centerItemId, string officeItemId, string memberItemId);
        Task<BuroWeekend> GetWeekendAsync();
        Task<bool> IsHolidayAsync(DateTime date, HashSet<DateTime> holidayHashSet, string centerItemId = null, string officeItemId = null, string memberItemId = null);
        Task<bool> IsWeekendAsync(DateTime date, BuroWeekend weekend = null);
    }
}
