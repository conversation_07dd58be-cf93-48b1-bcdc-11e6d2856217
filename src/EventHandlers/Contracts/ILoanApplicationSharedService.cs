using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace EventHandlers.Contracts
{
    public interface ILoanApplicationSharedService
    {
        Task<BuroEmployee> GetNextLoanApproverAsync(double loanAmount, string templateItemId, BuroEmployee currentEmployee);
        Task<BuroEmployee> GetEmployeeByOfficeForSpecificDesignation(string officeItemId, string designationCode);
        Task<BuroEmployee> GetEmployeeByDesignationForAnOfficeAsync(string officeItemId, string designationCode);
    }
}
