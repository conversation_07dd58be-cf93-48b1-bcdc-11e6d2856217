using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Account
{
    public class ContractualAccountApprovalEventHandler : ICommandHandler<ContractualAccountApprovalEvent, bool>
    {
        private readonly ILogger<ContractualAccountApprovalEventHandler> _logger;
        private readonly IContractualAccountApprovalEventService _service;

        public ContractualAccountApprovalEventHandler(
            ILogger<ContractualAccountApprovalEventHandler> logger,
            IContractualAccountApprovalEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(ContractualAccountApprovalEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(ContractualAccountApprovalEvent command)
        {
            _logger.LogInformation("Handling ContractualAccountApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.SubmitApprovalForNewContractualAccountAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ContractualAccountApprovalEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
