using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Account
{
    public class GSChangeApprovalEventHandler : ICommandHandler<GSChangeApprovalEvent, bool>
    {
        private readonly ILogger<GSChangeApprovalEventHandler> _logger;
        private readonly IGSChangeApprovalEventService _eventService;

        public GSChangeApprovalEventHandler(ILogger<GSChangeApprovalEventHandler> logger,
            IGSChangeApprovalEventService eventService)
        {
            _logger = logger;
            _eventService = eventService;
        }
        public bool Handle(GSChangeApprovalEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(GSChangeApprovalEvent command)
        {
            _logger.LogInformation("Handling GSChangeApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _eventService.ChangeGSAccount(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in GSChangeApprovalEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
