using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Account
{
    public class LoanAccountScheduleGenerationEventHandler : ICommandHandler<LoanAccountScheduleGenerationEvent, bool>
    {
        private readonly ILogger<LoanAccountScheduleGenerationEventHandler> _logger;
        private readonly ILoanAccountScheduleGenerationService _loanAccountScheduleGenerationService;

        public LoanAccountScheduleGenerationEventHandler(ILogger<LoanAccountScheduleGenerationEventHandler> logger,
            ILoanAccountScheduleGenerationService loanAccountScheduleGenerationService)
        {
            _logger = logger;
            _loanAccountScheduleGenerationService = loanAccountScheduleGenerationService;
        }
        public bool Handle(LoanAccountScheduleGenerationEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(LoanAccountScheduleGenerationEvent command)
        {
            _logger.LogInformation("Handling LoanAccountScheduleGenerationEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _loanAccountScheduleGenerationService.GenerateLoanAccountSchedule(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in LoanAccountScheduleGenerationEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
