using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Account
{
    public class CreateCSApplicationVoucherEventHandler : ICommandHandler<CreateCSApplicationVoucherEvent, bool>
    {
        private readonly ILogger<CreateCSApplicationVoucherEventHandler> _logger;
        private readonly ICreateCSApplicationVoucherEventService _service;

        public CreateCSApplicationVoucherEventHandler(
            ILogger<CreateCSApplicationVoucherEventHandler> logger,
            ICreateCSApplicationVoucherEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(CreateCSApplicationVoucherEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(CreateCSApplicationVoucherEvent command)
        {
            _logger.LogInformation("Handling CreateCSApplicationVoucherEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.CreateCSApplicationVoucherAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CreateCSApplicationVoucherEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
