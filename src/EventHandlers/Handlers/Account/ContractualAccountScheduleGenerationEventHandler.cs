using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Account
{
    public class ContractualAccountScheduleGenerationEventHandler : ICommandHandler<ContractualAccountScheduleGenerationEvent, bool>
    {
        private readonly ILogger<ContractualAccountScheduleGenerationEventHandler> _logger;
        private readonly IContractualAccountScheduleGenerationService _contractualAccountScheduleGenerationService;

        public ContractualAccountScheduleGenerationEventHandler(ILogger<ContractualAccountScheduleGenerationEventHandler> logger,
            IContractualAccountScheduleGenerationService contractualAccountScheduleGenerationService)
        {
            _logger = logger;
            _contractualAccountScheduleGenerationService = contractualAccountScheduleGenerationService;
        }
        public bool Handle(ContractualAccountScheduleGenerationEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(ContractualAccountScheduleGenerationEvent command)
        {
            _logger.LogInformation("Handling ContractualAccountScheduleGenerationEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _contractualAccountScheduleGenerationService.GenerateContractualAccountSchedule(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ContractualAccountScheduleGenerationEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
