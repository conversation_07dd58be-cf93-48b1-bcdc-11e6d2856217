using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Account
{
    public class LoanDisbursementPostProcessingEventHandler : ICommandHandler<LoanDisbursementPostProcessingEvent, bool>
    {
        private readonly ILogger<LoanDisbursementPostProcessingEventHandler> _logger;
        private readonly ILoanDisbursementPostProcessingEventService _service;

        public LoanDisbursementPostProcessingEventHandler(
            ILogger<LoanDisbursementPostProcessingEventHandler> logger,
            ILoanDisbursementPostProcessingEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(LoanDisbursementPostProcessingEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(LoanDisbursementPostProcessingEvent command)
        {
            _logger.LogInformation("Handling LoanDisbursementPostProcessingEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.LoanDisbursementPostProcessingAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in LoanDisbursementPostProcessingEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
