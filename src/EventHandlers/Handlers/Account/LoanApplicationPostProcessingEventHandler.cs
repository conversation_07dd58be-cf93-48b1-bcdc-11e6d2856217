using EventHandlers.Contracts;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Account
{
    public class LoanApplicationPostProcessingEventHandler : ICommandHandler<LoanApplicationPostProcessingEvent, bool>
    {
        private readonly ILogger<LoanApplicationPostProcessingEventHandler> _logger;
        private readonly ILoanApplicationPostProcessingEventService _service;

        public LoanApplicationPostProcessingEventHandler(
            ILogger<LoanApplicationPostProcessingEventHandler> logger,
            ILoanApplicationPostProcessingEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(LoanApplicationPostProcessingEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(LoanApplicationPostProcessingEvent command)
        {
            _logger.LogInformation("Handling LoanApplicationPostProcessingEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.LoanApplicationPostProcessingAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in LoanApplicationPostProcessingEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
