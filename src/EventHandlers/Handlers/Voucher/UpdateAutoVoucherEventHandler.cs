using EventHandlers.Contracts;
using Infrastructure.Events.Voucher;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Voucher;

public class UpdateAutoVoucherEventHandler : ICommandHandler<UpdateAutoVoucherEvent, bool>
{

    private readonly ILogger<UpdateAutoVoucherEventHandler> _logger;
    private readonly IUpdateAutoVoucherEventService _service;

    public UpdateAutoVoucherEventHandler(
        ILogger<UpdateAutoVoucherEventHandler> logger,
        IUpdateAutoVoucherEventService service)
    {
        _logger = logger;
        _service = service;
    }

    public bool Handle(UpdateAutoVoucherEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<bool> HandleAsync(UpdateAutoVoucherEvent command)
    {
        _logger.LogInformation("Handling UpdateAutoVoucherEventHandler: {Command}", JsonConvert.SerializeObject(command));

        try
        {
            await _service.UpdateAutoVoucherAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in UpdateAutoVoucherEventHandler\n{StackTrace}", ex.StackTrace);

            return false;
        }

        return true;
    }
}
