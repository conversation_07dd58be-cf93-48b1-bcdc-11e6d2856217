using EventHandlers.Contracts;
using Infrastructure.Events.Voucher;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Voucher;

public class CreateVoucherEventHandler : ICommandHandler<CreateVoucherEvent, bool>
{

    private readonly ILogger<CreateVoucherEventHandler> _logger;
    private readonly IVoucherCreationService _service;

    public CreateVoucherEventHandler(
        ILogger<CreateVoucherEventHandler> logger,
        IVoucherCreationService service)
    {
        _logger = logger;
        _service = service;
    }

    public bool Handle(CreateVoucherEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<bool> HandleAsync(CreateVoucherEvent command)
    {
        _logger.LogInformation("Handling CreateVoucherEventHandler: {Command}", JsonConvert.SerializeObject(command));

        try
        {
            await _service.CreateVoucher(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateVoucherEventHandler\n{StackTrace}", ex.StackTrace);

            return false;
        }

        return true;
    }
}