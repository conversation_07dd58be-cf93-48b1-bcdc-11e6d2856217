using EventHandlers.Contracts;
using Infrastructure.Events.MemberSurvey;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Member
{
    public class MemberAddressChangeApprovalEventHandler : ICommandHandler<MemberAddressChangeApprovalEvent, bool>
    {
        private readonly ILogger<MemberAddressChangeApprovalEventHandler> _logger;
        private readonly IMemberAddressChangeApprovalEventService _service;

        public MemberAddressChangeApprovalEventHandler(
            ILogger<MemberAddressChangeApprovalEventHandler> logger,
            IMemberAddressChangeApprovalEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(MemberAddressChangeApprovalEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(MemberAddressChangeApprovalEvent command)
        {
            _logger.LogInformation("Handling MemberAddressChangeApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.ChangeMemberAddressAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in MemberAddressChangeApprovalEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
