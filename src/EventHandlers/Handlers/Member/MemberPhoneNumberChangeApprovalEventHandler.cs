using EventHandlers.Contracts;
using Infrastructure.Events.MemberSurvey;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Member
{
    public class MemberPhoneNumberChangeApprovalEventHandler : ICommandHandler<MemberPhoneNumberChangeApprovalEvent, bool>
    {
        private readonly ILogger<MemberPhoneNumberChangeApprovalEventHandler> _logger;
        private readonly IMemberPhoneNumberChangeApprovalEventService _service;

        public MemberPhoneNumberChangeApprovalEventHandler(
            ILogger<MemberPhoneNumberChangeApprovalEventHandler> logger,
            IMemberPhoneNumberChangeApprovalEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(MemberPhoneNumberChangeApprovalEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(MemberPhoneNumberChangeApprovalEvent command)
        {
            _logger.LogInformation("Handling MemberPhoneNumberChangeApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.ChangeMemberPhoneNumberAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in MemberPhoneNumberChangeApprovalEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
