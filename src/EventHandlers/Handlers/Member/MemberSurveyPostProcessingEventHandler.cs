using EventHandlers.Contracts;
using Infrastructure.Events.MemberSurvey;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Member
{
    public class MemberSurveyPostProcessingEventHandler : ICommandHandler<MemberSurveyPostProcessingEvent, bool>
    {
        private readonly IMemberSurveyPostProcessingEventService _service;
        private readonly ILogger<MemberSurveyPostProcessingEventHandler> _logger;

        public MemberSurveyPostProcessingEventHandler(
            IMemberSurveyPostProcessingEventService service,
            ILogger<MemberSurveyPostProcessingEventHandler> logger)
        {
            _service = service;
            _logger = logger;
        }

        public bool Handle(MemberSurveyPostProcessingEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(MemberSurveyPostProcessingEvent command)
        {
            try
            {
                _logger.LogInformation("Inside MemberSurveyPostProcessingEventHandler");

                await _service.ProcessMemberSurveyAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MemberSurveyPostProcessingEventHandler Error: {Error}", ex.Message);
                return false;
            }

            return true;
        }
    }
}
