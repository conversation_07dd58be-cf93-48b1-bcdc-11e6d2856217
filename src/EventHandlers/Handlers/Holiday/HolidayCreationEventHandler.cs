using EventHandlers.Contracts;
using Infrastructure.Events.Holiday;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Holiday
{
    public class HolidayCreationEventHandler : ICommandHandler<HolidayCreationEvent, bool>
    {
        private readonly ILogger<HolidayCreationEventHandler> _logger;
        private readonly IHolidayCreationEventService _holidayCreationEventService;

        public HolidayCreationEventHandler(ILogger<HolidayCreationEventHandler> logger,
            IHolidayCreationEventService holidayCreationEventService)
        {
            _logger = logger;
            _holidayCreationEventService = holidayCreationEventService;
        }
        public bool Handle(HolidayCreationEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(HolidayCreationEvent command)
        {
            _logger.LogInformation("Handling HolidayCreationEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _holidayCreationEventService.ApplyHolidayCreationImpactAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in HolidayCreationEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
