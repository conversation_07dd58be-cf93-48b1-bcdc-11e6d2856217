using EventHandlers.Contracts;
using Infrastructure.Events.Collection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Collection
{
    public class CollectionFundTransferRevertApprovalEventHandler : ICommandHandler<CollectionFundTransferRevertApprovalEvent, bool>
    {
        private readonly ILogger<CollectionFundTransferRevertApprovalEventHandler> _logger;
        private readonly ICollectionFundTransferRevertApprovalEventService _service;

        public CollectionFundTransferRevertApprovalEventHandler(
            ILogger<CollectionFundTransferRevertApprovalEventHandler> logger,
            ICollectionFundTransferRevertApprovalEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(CollectionFundTransferRevertApprovalEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(CollectionFundTransferRevertApprovalEvent command)
        {
            _logger.LogInformation("Handling CollectionFundTransferRevertApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.RevertCollectionFundTransferAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CollectionFundTransferRevertApprovalEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
