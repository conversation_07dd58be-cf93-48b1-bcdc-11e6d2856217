using EventHandlers.Contracts;
using Infrastructure.Events.Collection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Collection
{
    public class CollectionFundTransferPostProcessingEventHandler : ICommandHandler<CollectionFundTransferPostProcessingEvent, bool>
    {
        private readonly ILogger<CollectionFundTransferPostProcessingEventHandler> _logger;
        private readonly ICollectionFundTransferPostProcessingEventService _service;

        public CollectionFundTransferPostProcessingEventHandler(
            ILogger<CollectionFundTransferPostProcessingEventHandler> logger,
            ICollectionFundTransferPostProcessingEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(CollectionFundTransferPostProcessingEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(CollectionFundTransferPostProcessingEvent command)
        {
            _logger.LogInformation("Handling CollectionFundTransferPostProcessingEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.CollectionFundTransferPostProcessingAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CollectionFundTransferPostProcessingEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
