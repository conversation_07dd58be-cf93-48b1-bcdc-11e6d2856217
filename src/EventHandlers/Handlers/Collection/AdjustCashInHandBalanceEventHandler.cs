using EventHandlers.Contracts;
using Infrastructure.Events.Collection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Collection
{
    public class AdjustCashInHandBalanceEventHandler : ICommandHandler<AdjustCashInHandBalanceEvent, bool>
    {
        private readonly ILogger<AdjustCashInHandBalanceEventHandler> _logger;
        private readonly IAdjustCashInHandBalanceEventService _service;

        public AdjustCashInHandBalanceEventHandler(
            ILogger<AdjustCashInHandBalanceEventHandler> logger,
            IAdjustCashInHandBalanceEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(AdjustCashInHandBalanceEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(AdjustCashInHandBalanceEvent command)
        {
            _logger.LogInformation("Handling AdjustCashInHandBalanceEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.AdjustCashInHandBalanceAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in AdjustCashInHandBalanceEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
