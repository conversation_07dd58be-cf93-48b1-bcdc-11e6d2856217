using EventHandlers.Contracts;
using Infrastructure.Events.ConsentForm;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.ConsentForm;

public class ConsentFormApprovalEventHandler : ICommandHandler<ConsentFormApprovalEvent, bool>
{
    private readonly ILogger<ConsentFormApprovalEventHandler> _logger;
    private readonly IConsentFormApprovalEventService _service;

    public ConsentFormApprovalEventHandler(
        ILogger<ConsentFormApprovalEventHandler> logger,
        IConsentFormApprovalEventService service)
    {
        _logger = logger;
        _service = service;
    }
    public bool Handle(ConsentFormApprovalEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<bool> HandleAsync(ConsentFormApprovalEvent command)
    {
        _logger.LogInformation("Handling ConsentFormApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

        try
        {
            await _service.HandleConsentFormApprovalEventAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in ConsentFormApprovalEventHandler\n{StackTrace}", ex.StackTrace);

            return false;
        }

        return true;
    }
}