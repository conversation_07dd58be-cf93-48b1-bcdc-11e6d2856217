using EventHandlers.Contracts;
using Infrastructure.Events.ExternalSync;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.ExternalSync
{
    public class SyncSylviaDataEventHandler : ICommandHandler<SyncSylviaDataEvent, bool>
    {
        private readonly ILogger<SyncSylviaDataEventHandler> _logger;
        private readonly ISyncSylviaDataService _syncSylviaDataService;

        public SyncSylviaDataEventHandler(ISyncSylviaDataService syncSylviaDataService, ILogger<SyncSylviaDataEventHandler> logger)
        {
            _syncSylviaDataService = syncSylviaDataService ?? throw new ArgumentNullException(nameof(syncSylviaDataService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        public bool Handle(SyncSylviaDataEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(SyncSylviaDataEvent command)
        {
            _logger.LogInformation("SyncSylviaDataEventHandler->HandleAsync->START", command.RequestItemId);
            await _syncSylviaDataService.UpdateExternalRequestStatus(command.RequestItemId, EnumnExternalSyncStatus.InProgress);

            bool isSuccess = false;
            if (command.DataSyncType == EnumExternalSyncType.Office)
            {
                isSuccess = await _syncSylviaDataService.ImportOfficeListFromSylvia(command.RequestItemId);
                // calculate diff?
            }

            if (command.DataSyncType == EnumExternalSyncType.Employee)
            {
                isSuccess = await _syncSylviaDataService.ImportEmployeeListFromSylvia(command.RequestItemId);
                // calculate diff?
            }

            var syncStatus = isSuccess ? EnumnExternalSyncStatus.Completed : EnumnExternalSyncStatus.Failed;
            await _syncSylviaDataService.UpdateExternalRequestStatus(command.RequestItemId, syncStatus);
            _logger.LogInformation("SyncSylviaDataEventHandler->HandleAsync->END");
            return true;
        }


    }
}
