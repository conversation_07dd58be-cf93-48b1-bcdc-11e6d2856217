using EventHandlers.Services.Center;
using Infrastructure.Events.Center;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Center
{
    public class ChangePOrForMembersEventHandler : ICommandHandler<ChangePOrForMembersEvent, bool>
    {
        private readonly ChangePOrForMembersService _service;
        private readonly ILogger<ChangePOrForMembersEventHandler> _logger;

        public ChangePOrForMembersEventHandler(
            ChangePOrForMembersService service,
            ILogger<ChangePOrForMembersEventHandler> logger)
        {
            _service = service ?? throw new ArgumentNullException(nameof(service));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        public bool Handle(ChangePOrForMembersEvent changePOrEvent)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(ChangePOrForMembersEvent changePOrEvent)
        {
            try
            {
                _logger.LogInformation("Inside ChangePOrForMembersEventHandler");
                await _service.ChangeProgramOrganizerForMembers(changePOrEvent);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ChangePOrForMembersEventHandler Error Occured");
                return false;
            }
        }
    }
}
