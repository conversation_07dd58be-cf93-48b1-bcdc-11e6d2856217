using EventHandlers.Contracts;
using Infrastructure.Events.Register;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Register;

public class ChequeRegisterEventHandler : ICommandHandler<ChequeRegisterPostProcessingEvent, bool>
{
    private readonly ILogger<ChequeRegisterEventHandler> _logger;
    private readonly IChequeRegisterEventService _service;

    public ChequeRegisterEventHandler(
        ILogger<ChequeRegisterEventHandler> logger,
        IChequeRegisterEventService service)
    {
        _logger = logger;
        _service = service;
    }
    public bool Handle(ChequeRegisterPostProcessingEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<bool> HandleAsync(ChequeRegisterPostProcessingEvent command)
    {
        _logger.LogInformation("Handling ChequeRegisterEventHandler: {Command}", JsonConvert.SerializeObject(command));

        try
        {
            await _service.HandleChequeRegisterEventAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in ChequeRegisterEventHandler\n{StackTrace}", ex.StackTrace);

            return false;
        }

        return true;
    }
}