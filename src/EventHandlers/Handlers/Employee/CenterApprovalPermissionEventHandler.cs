using EventHandlers.Employee.CenterApprovalPermission;
using Infrastructure.Events;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Employee.CenterApprovalPermission
{
    public class CenterApprovalPermissionEventHandler : ICommandHandler<CenterApprovalPermissionEvent, bool>
    {
        private readonly CenterApprovalPermissionService _service;
        private readonly ILogger<CenterApprovalPermissionEventHandler> _logger;
        public CenterApprovalPermissionEventHandler(
            CenterApprovalPermissionService service,
            ILogger<CenterApprovalPermissionEventHandler> logger)
        {
            _service = service ?? throw new ArgumentNullException(nameof(service));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        public bool Handle(CenterApprovalPermissionEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(CenterApprovalPermissionEvent command)
        {
            try
            {
                _logger.LogInformation("Inside CenterApprovalPermissionEventHandler");
                //await _service.UpdateBuroApprovalForStaffing(command.StaffingRequestItemId);
                await _service.CreateApprovalForCenter(command);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError("CenterApprovalCreationFailed Error: {Error}", ex.Message);
                return false;
            }
        }
    }
}
