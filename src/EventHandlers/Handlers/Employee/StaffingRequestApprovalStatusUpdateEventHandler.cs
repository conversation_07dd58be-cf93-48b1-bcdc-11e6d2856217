using EventHandlers.Employee.StaffingRequestApprovalStatusUpdate;
using Infrastructure.Events;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Employee
{
    public class StaffingRequestApprovalStatusUpdateEventHandler : ICommandHandler<StaffingRequestApprovalStatusUpdateEvent, bool>
    {
        private readonly StaffingRequestApprovalStatusUpdateService _service;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<StaffingRequestApprovalStatusUpdateEventHandler> _logger;
        public StaffingRequestApprovalStatusUpdateEventHandler(
            StaffingRequestApprovalStatusUpdateService service,
            ISecurityContextProvider securityContextProvider,
            ILogger<StaffingRequestApprovalStatusUpdateEventHandler> logger)
        {
            _service = service ?? throw new ArgumentNullException(nameof(service));
            _securityContextProvider = securityContextProvider ?? throw new ArgumentNullException(nameof(securityContextProvider));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        public bool Handle(StaffingRequestApprovalStatusUpdateEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(StaffingRequestApprovalStatusUpdateEvent command)
        {
            _logger.LogInformation("StaffingRequestApprovalStatusUpdateEventHandler->HandleAsync->START");

            var approval = await _service.GetBuroApproval(command.ApprovalItemId);
            if (approval == null) return false;

            var staffingRequest = await _service.GetStaffingRequest(approval.RelatedEntityItemId);
            if (staffingRequest == null) return false;

            var loggedInUserId = _securityContextProvider.GetSecurityContext().UserId;
            var loggedInEmployee = await _service.GetEmployeeByUserId(loggedInUserId);

            bool isLoggedInEmployeeAllowedForApproval = staffingRequest.ApprovalRequiredFromEmployeeIds.Contains(loggedInEmployee.ItemId);
            if (!isLoggedInEmployeeAllowedForApproval)
            {
                _logger.LogError("StaffingRequestApprovalStatusUpdateEventHandler->ERROR-> isLoggedInEmployeeAllowedForApproval is false");
                return false;
            }

            if (staffingRequest.Status == EnumStaffingRequestStatus.Rejected)
            {
                return true;
            }

            bool isAccepted = command.Status == SubmissionStatus.Approved;
            if (!isAccepted)
            {
                await _service.UpdateStaffingRequestStatus(staffingRequest.ItemId, EnumStaffingRequestStatus.Rejected);
            }
            if (isAccepted && staffingRequest.MovingFromOfficeId == null)
            {
                var employee = await _service.GetEmployee(staffingRequest.EmployeeItemId);
                await _service.UpdateStaffingRequestStatus(staffingRequest.ItemId, EnumStaffingRequestStatus.Completed);
                await _service.UpdateUserInfoForActivationOfEmployee(employee.UserItemId);
            }

            _logger.LogInformation("StaffingRequestApprovalStatusUpdateEventHandler->HandleAsync->END");

            return true;
        }
    }
}
