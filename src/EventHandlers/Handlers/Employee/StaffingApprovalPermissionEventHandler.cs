using EventHandlers.Employee.StaffingApprovalPermission;
using Infrastructure.Events;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Employee
{
    public class StaffingApprovalPermissionEventHandler : ICommandHandler<StaffingApprovalPermissionEvent, bool>
    {
        private readonly StaffingApprovalPermissionService _service;
        private readonly ILogger<StaffingApprovalPermissionEventHandler> _logger;
        public StaffingApprovalPermissionEventHandler(
            StaffingApprovalPermissionService service,
            ILogger<StaffingApprovalPermissionEventHandler> logger)
        {
            _service = service ?? throw new ArgumentNullException(nameof(service));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public bool Handle(StaffingApprovalPermissionEvent command)
        {
            throw new NotImplementedException();
        }



        public async Task<bool> HandleAsync(StaffingApprovalPermissionEvent command)
        {
            try
            {
                _logger.LogInformation("Inside StaffingApprovalPermissionEventHandler");
                await _service.UpdateBuroApprovalForStaffing(command.StaffingRequestItemId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogInformation("StaffingApprovalCreationFailed Error: {error}", ex.Message);
                return false;
            }

        }
    }
}
