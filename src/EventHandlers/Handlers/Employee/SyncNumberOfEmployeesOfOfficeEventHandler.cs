using EventHandlers.Employee.UpdateEmployeeNumberUnderOffice;
using Infrastructure.Events.Employee;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Employee
{
    public class SyncNumberOfEmployeesOfOfficeEventHandler : ICommandHandler<SyncNumberOfEmployeesEvent, bool>
    {
        private readonly SyncNumberOfEmployeesOfOfficeService _service;
        private readonly ILogger<SyncNumberOfEmployeesOfOfficeEventHandler> _logger;
        public SyncNumberOfEmployeesOfOfficeEventHandler(
            SyncNumberOfEmployeesOfOfficeService service,
            ILogger<SyncNumberOfEmployeesOfOfficeEventHandler> logger)
        {
            _logger = logger;
            _service = service;
        }
        public bool Handle(SyncNumberOfEmployeesEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(SyncNumberOfEmployeesEvent command)
        {
            try
            {
                _logger.LogInformation("Inside => SyncNumberOfEmployeesOfOfficeEventHandler");
                await _service.UpdateNumberOfEmployeesOfOffice(command);
                _logger.LogInformation("End => SyncNumberOfEmployeesOfOfficeEventHandler");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Occured");
                return false;
            }
        }
    }
}
