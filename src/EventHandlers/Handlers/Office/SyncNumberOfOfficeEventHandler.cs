using EventHandlers.Services.Office;
using Infrastructure.Events.Office;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Office
{
    public class SyncNumberOfOfficeEventHandler : ICommandHandler<SyncNumberOfOfficeEvent, bool>
    {
        private readonly ILogger<SyncNumberOfOfficeEventHandler> _logger;
        private readonly SyncNumberOfOfficeService _syncNumberOfficeService;
        public SyncNumberOfOfficeEventHandler(
            ILogger<SyncNumberOfOfficeEventHandler> logger, SyncNumberOfOfficeService syncNumberOfficeService)
        {
            _logger = logger;
            _syncNumberOfficeService = syncNumberOfficeService;
        }
        public bool Handle(SyncNumberOfOfficeEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(SyncNumberOfOfficeEvent command)
        {
            try
            {
                _logger.LogInformation("Inside SyncNumberOfOfficeEventHandler");
                await _syncNumberOfficeService.UpdateNumberOfOffices(command);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Occured in SyncNumberOfOfficeEventHandler");
                return false;
            }
        }
    }
}
