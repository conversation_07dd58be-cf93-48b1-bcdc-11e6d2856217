using EventHandlers.Services.Office;
using Infrastructure.Events.Center;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Office
{
    public class SyncNumberOfCentersEventHandler : ICommandHandler<SyncNumberOfCenterEvent, bool>
    {
        private readonly ILogger<SyncNumberOfCentersEventHandler> _logger;
        private readonly SyncNumberOfOfficeService _syncOfficeService;
        public SyncNumberOfCentersEventHandler(
            SyncNumberOfOfficeService syncOfficeService,
            ILogger<SyncNumberOfCentersEventHandler> logger)
        {
            _logger = logger;
            _syncOfficeService = syncOfficeService;
        }

        public bool Handle(SyncNumberOfCenterEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(SyncNumberOfCenterEvent command)
        {
            try
            {
                _logger.LogInformation("Inside SyncNumberOfCentersEventHandler");
                await _syncOfficeService.UpdateNumberOfCenters(command);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error Occured in SyncNumberOfCentersEventHandler");
                return false;
            }
        }
    }
}
