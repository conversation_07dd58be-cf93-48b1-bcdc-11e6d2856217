using EventHandlers.Services.EmployeeHistory;
using Infrastructure.Events.EmployeeHistory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.EmployeeHistory;

public class CreateEmployeeTransferHistoryEventHandler : ICommandHandler<CreateEmployeeTransferHistoryEvent, CommandResponse>
{
    private readonly ILogger<CreateEmployeeTransferHistoryEventHandler> _logger;
    private readonly CreateEmployeeTransferHistoryService _service;

    public CreateEmployeeTransferHistoryEventHandler(ILogger<CreateEmployeeTransferHistoryEventHandler> logger,
        CreateEmployeeTransferHistoryService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateEmployeeTransferHistoryEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateEmployeeTransferHistoryEvent command)
    {
        _logger.LogInformation(
            "Inside CreateEmployeeTransferHistoryCommandHandler :: CreateEmployeeTransferHistoryCommand :: {command} ",
            JsonConvert.SerializeObject(command));
        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _service.CreateEmployeeTransferHistory(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in CreateEmployeeTransitionCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}