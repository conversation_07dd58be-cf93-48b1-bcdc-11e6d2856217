using EventHandlers.Services.EmployeeHistory;
using Infrastructure.Constants;
using Infrastructure.Events.EmployeeHistory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.EmployeeHistory;

public class UpdateEmployeeAssignmentHistoryEventHandler : ICommandHandler<UpdateEmployeeAssignmentHistoryEvent, CommandResponse>
{
    private readonly ILogger<UpdateEmployeeAssignmentHistoryEventHandler> _logger;
    private readonly UpdateEmployeeAssignmentHistoryService _service;

    public UpdateEmployeeAssignmentHistoryEventHandler(ILogger<UpdateEmployeeAssignmentHistoryEventHandler> logger,
        UpdateEmployeeAssignmentHistoryService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateEmployeeAssignmentHistoryEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateEmployeeAssignmentHistoryEvent command)
    {
        _logger.LogInformation("UpdateEmployeeAssignmentHistoryEventHandler:: UpdateEmployeeAssignmentHistoryEvent :: {event}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.UpdateEmployeeAssignmentHistory(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in UpdateEmployeeAssignmentHistoryEventHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }
        return response;
    }
}