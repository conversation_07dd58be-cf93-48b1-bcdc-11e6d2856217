using EventHandlers.Services.EmployeeHistory;
using Infrastructure.Events.EmployeeHistory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.EmployeeHistory;

public class CreateEmployeeAssignmentHistoryEventHandler : ICommandHandler<CreateEmployeeAssignmentHistoryEvent, CommandResponse>
{
    private readonly ILogger<CreateEmployeeAssignmentHistoryEventHandler> _logger;
    private readonly CreateEmployeeAssignmentHistoryService _service;

    public CreateEmployeeAssignmentHistoryEventHandler(ILogger<CreateEmployeeAssignmentHistoryEventHandler> logger,
        CreateEmployeeAssignmentHistoryService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateEmployeeAssignmentHistoryEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateEmployeeAssignmentHistoryEvent command)
    {
        _logger.LogInformation("Inside CreateEmployeeAssignmentHistoryEventHandler :: CreateEmployeeAssignmentHistoryEvent:: {command}",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _service.CreateEmployeeAssignmentHistory(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in CreateEmployeeAssignmentHistoryEventHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}