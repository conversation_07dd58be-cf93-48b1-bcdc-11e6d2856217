using EventHandlers.Contracts;
using Infrastructure.Events.AutoDebit;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.AutoDebit;

public class ExecuteAutoDebitEventHandler : ICommandHandler<ExecuteAutoDebitEvent, bool>
{
    private readonly ILogger<ExecuteAutoDebitEventHandler> _logger;
    private readonly IExecuteAutoDebitEventService _service;

    public ExecuteAutoDebitEventHandler(
        ILogger<ExecuteAutoDebitEventHandler> logger,
        IExecuteAutoDebitEventService service)
    {
        _logger = logger;
        _service = service;
    }
    
    public bool Handle(ExecuteAutoDebitEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<bool> HandleAsync(ExecuteAutoDebitEvent command)
    {
        _logger.LogInformation("Handling ExecuteAutoDebitEventHandler: {Command}", JsonConvert.SerializeObject(command));

        try
        {
            await _service.ExecuteAutoDebitAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in ExecuteAutoDebitEventHandler\n{StackTrace}", ex.StackTrace);

            return false;
        }

        return true;
    }
}