using Infrastructure.Events.Task;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Tasks.CreateTaskHandler
{
    public class CreateTaskEventHandler : ICommandHandler<CreateTaskEvent, bool>
    {
        private readonly ILogger<CreateTaskEventHandler> _logger;
        public CreateTaskEventHandler(ILogger<CreateTaskEventHandler> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }
        public bool Handle(CreateTaskEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(CreateTaskEvent command)
        {
            try
            {
                _logger.LogInformation("Inside CreateTaskEventHandler ");
                //await _service.ChangeProgramOrganizerForMembers(changePOrEvent);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CreateTaskEventHandler  Error Occured");
                return false;
            }
        }
    }
}
