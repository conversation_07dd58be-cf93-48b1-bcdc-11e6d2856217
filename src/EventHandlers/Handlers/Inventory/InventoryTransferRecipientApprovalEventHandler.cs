using EventHandlers.Services.Inventory;
using Infrastructure.Constants;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Inventory;

public class InventoryTransferRecipientApprovalEventHandler : ICommandHandler<InventoryTransferRecipientApprovalEvent, CommandResponse>
{
    private readonly ILogger<InventoryTransferRecipientApprovalEventHandler> _logger;
    private readonly InventoryTransferRecipientApprovalService _approvalService;

    public InventoryTransferRecipientApprovalEventHandler(ILogger<InventoryTransferRecipientApprovalEventHandler> logger,
        InventoryTransferRecipientApprovalService approvalService)
    {
        _logger = logger;
        _approvalService = approvalService;
    }
    public CommandResponse Handle(InventoryTransferRecipientApprovalEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(InventoryTransferRecipientApprovalEvent command)
    {
        _logger.LogInformation("Inside InventoryTransferRecipientApprovalEventHandler :: Event :: {command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _approvalService.ProcessRecipientTransferApproval(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in InventoryTransferRecipientApprovalEventHandler\n{StackTrace}", ex.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }

        return response;
    }
}