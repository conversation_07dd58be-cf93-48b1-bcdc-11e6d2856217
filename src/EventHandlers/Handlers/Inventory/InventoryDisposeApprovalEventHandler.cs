using EventHandlers.Services.Inventory;
using Infrastructure.Constants;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Inventory;

public class InventoryDisposeApprovalEventHandler : ICommandHandler<InventoryDisposeApprovalEvent, CommandResponse>
{
    private readonly ILogger<InventoryDisposeApprovalEventHandler> _logger;
    private readonly InventoryDisposeApprovalService _service;

    public InventoryDisposeApprovalEventHandler(ILogger<InventoryDisposeApprovalEventHandler> logger,
        InventoryDisposeApprovalService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(InventoryDisposeApprovalEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(InventoryDisposeApprovalEvent command)
    {
        _logger.LogInformation("Inside InventoryDisposeApprovalEventHandler :: command :: {command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.DisposeInventoryAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in InventoryDisposeApprovalEventHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }
        return response;
    }
}