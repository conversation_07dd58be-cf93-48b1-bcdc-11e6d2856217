using EventHandlers.Services.Inventory;
using Infrastructure.Constants;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Inventory;

public class InventoryTransferSourceApprovalEventHandler : ICommandHandler<InventoryTransferSourceApprovalEvent, CommandResponse>
{
    private readonly ILogger<InventoryTransferSourceApprovalEventHandler> _logger;
    private readonly InventoryTransferSourceApprovalService _approvalService;

    public InventoryTransferSourceApprovalEventHandler(ILogger<InventoryTransferSourceApprovalEventHandler> logger,
        InventoryTransferSourceApprovalService approvalService)
    {
        _logger = logger;
        _approvalService = approvalService;
    }
    public CommandResponse Handle(InventoryTransferSourceApprovalEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(InventoryTransferSourceApprovalEvent command)
    {
        _logger.LogInformation("Inside InventoryTransferSourceApprovalEventHandler :: Event :: {command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _approvalService.ProcessSourceTransferApproval(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in InventoryTransferSourceApprovalEventHandler\n{StackTrace}", ex.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }

        return response;
    }
}