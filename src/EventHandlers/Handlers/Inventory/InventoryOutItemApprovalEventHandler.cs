using EventHandlers.Services.Inventory;
using Infrastructure.Constants;
using Infrastructure.Events.Inventory;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Inventory;

public class InventoryOutItemApprovalEventHandler : ICommandHandler<InventoryOutItemApprovalEvent, CommandResponse>
{
    private readonly ILogger<InventoryOutItemApprovalEventHandler> _logger;
    private readonly InventoryOutItemApprovalService _service;

    public InventoryOutItemApprovalEventHandler(ILogger<InventoryOutItemApprovalEventHandler> logger,
        InventoryOutItemApprovalService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(InventoryOutItemApprovalEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(InventoryOutItemApprovalEvent command)
    {
        var response = new CommandResponse();
        _logger.LogInformation("Inside InventoryOutItemApprovalEventHandler :: Command :: {Event}", JsonConvert.SerializeObject(command));
        try
        {
            await _service.ApproveInventoryOutItemAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in InventoryOutItemApprovalEventHandler\n{StackTrace}", ex.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }
        return response;
    }
}