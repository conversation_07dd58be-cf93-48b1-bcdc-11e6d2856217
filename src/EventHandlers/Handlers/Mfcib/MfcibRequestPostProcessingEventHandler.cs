using EventHandlers.Contracts;
using Infrastructure.Events.Mfcib;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Mfcib
{
    public class MfcibRequestPostProcessingEventHandler : ICommandHandler<MfcibRequestPostProcessingEvent, bool>
    {
        private readonly ILogger<MfcibRequestPostProcessingEventHandler> _logger;
        private readonly IMfcibRequestPostProcessingEventService _service;

        public MfcibRequestPostProcessingEventHandler(
            ILogger<MfcibRequestPostProcessingEventHandler> logger,
            IMfcibRequestPostProcessingEventService service)
        {
            _service = service;
            _logger = logger;
        }

        public bool Handle(MfcibRequestPostProcessingEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(MfcibRequestPostProcessingEvent command)
        {
            try
            {
                _logger.LogInformation("Inside MfcibRequestPostProcessingEventHandler");

                await _service.ProcessMfcibRequestAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MfcibRequestPostProcessingEventHandler Error: {Error}", ex.Message);

                return false;
            }

            return true;
        }
    }
}
