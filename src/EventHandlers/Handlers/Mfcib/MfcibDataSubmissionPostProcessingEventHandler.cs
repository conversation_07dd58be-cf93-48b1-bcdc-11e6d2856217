using EventHandlers.Contracts;
using Infrastructure.Events.Mfcib;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Mfcib
{
    public class MfcibDataSubmissionPostProcessingEventHandler : ICommandHandler<MfcibDataSubmissionPostProcessingEvent, bool>
    {
        private readonly ILogger<MfcibDataSubmissionPostProcessingEventHandler> _logger;
        private readonly IMfcibDataSubmissionPostProcessingEventService _service;

        public MfcibDataSubmissionPostProcessingEventHandler(
            ILogger<MfcibDataSubmissionPostProcessingEventHandler> logger,
            IMfcibDataSubmissionPostProcessingEventService service)
        {
            _service = service;
            _logger = logger;
        }

        public bool Handle(MfcibDataSubmissionPostProcessingEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(MfcibDataSubmissionPostProcessingEvent command)
        {
            try
            {
                _logger.LogInformation("Inside MfcibDataSubmissionPostProcessingEventHandler");

                await _service.ProcessMfcibDataAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "MfcibDataSubmissionPostProcessingEventHandler Error: {Error}", ex.Message);

                return false;
            }

            return true;
        }
    }
}
