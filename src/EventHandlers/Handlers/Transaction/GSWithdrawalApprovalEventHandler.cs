using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Transaction
{
    public class GSWithdrawalApprovalEventHandler : ICommandHandler<GSWithdrawalApprovalEvent, bool>
    {
        private readonly ILogger<GSWithdrawalApprovalEventHandler> _logger;
        private readonly IGSWithdrawalApprovalEventService _service;

        public GSWithdrawalApprovalEventHandler(
            ILogger<GSWithdrawalApprovalEventHandler> logger,
            IGSWithdrawalApprovalEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(GSWithdrawalApprovalEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(GSWithdrawalApprovalEvent command)
        {
            _logger.LogInformation("Handling GSWithdrawalApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.ProcessGSWithdrawalApprovalAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in GSWithdrawalApprovalEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
