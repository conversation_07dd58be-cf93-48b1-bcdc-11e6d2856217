using EventHandlers.Contracts;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Transaction
{
    public class ProductAccountTransactionRequestPostProcessingEventHandler : ICommandHandler<ProductAccountTransactionRequestPostProcessingEvent, bool>
    {
        private readonly ILogger<ProductAccountTransactionRequestPostProcessingEventHandler> _logger;
        private readonly IProductAccountTransactionRequestPostProcessingEventService _service;

        public ProductAccountTransactionRequestPostProcessingEventHandler(
            ILogger<ProductAccountTransactionRequestPostProcessingEventHandler> logger,
            IProductAccountTransactionRequestPostProcessingEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(ProductAccountTransactionRequestPostProcessingEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(ProductAccountTransactionRequestPostProcessingEvent command)
        {
            _logger.LogInformation("Handling ProductAccountTransactionRequestPostProcessingEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.ProcessProductTransactionRequestAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ProductAccountTransactionRequestPostProcessingEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
