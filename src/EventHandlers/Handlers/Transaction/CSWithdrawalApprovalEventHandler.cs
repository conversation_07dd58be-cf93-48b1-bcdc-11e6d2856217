using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Transaction
{
    public class CSWithdrawalApprovalEventHandler : ICommandHandler<CSWithdrawalApprovalEvent, bool>
    {
        private readonly ILogger<CSWithdrawalApprovalEventHandler> _logger;
        private readonly ICSWithdrawalApprovalEventService _service;

        public CSWithdrawalApprovalEventHandler(
            ILogger<CSWithdrawalApprovalEventHandler> logger,
            ICSWithdrawalApprovalEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(CSWithdrawalApprovalEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(CSWithdrawalApprovalEvent command)
        {
            _logger.LogInformation("Handling CSWithdrawalApprovalEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.ProcessCSWithdrawalApprovalAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CSWithdrawalApprovalEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
