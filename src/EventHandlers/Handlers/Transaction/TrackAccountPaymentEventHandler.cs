using EventHandlers.Contracts;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Handlers.Transaction
{
    public class TrackAccountPaymentEventHandler : ICommandHandler<TrackAccountPaymentEvent, bool>
    {
        private readonly ILogger<TrackAccountPaymentEventHandler> _logger;
        private readonly ITrackAccountPaymentEventService _service;

        public TrackAccountPaymentEventHandler(
            ILogger<TrackAccountPaymentEventHandler> logger,
            ITrackAccountPaymentEventService service)
        {
            _logger = logger;
            _service = service;
        }

        public bool Handle(TrackAccountPaymentEvent command)
        {
            throw new NotImplementedException();
        }

        public async Task<bool> HandleAsync(TrackAccountPaymentEvent command)
        {
            _logger.LogInformation("Handling TrackAccountPaymentEventHandler: {Command}", JsonConvert.SerializeObject(command));

            try
            {
                await _service.TrackAccountPaymentAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in TrackAccountPaymentEventHandler\n{StackTrace}", ex.StackTrace);

                return false;
            }

            return true;
        }
    }
}
