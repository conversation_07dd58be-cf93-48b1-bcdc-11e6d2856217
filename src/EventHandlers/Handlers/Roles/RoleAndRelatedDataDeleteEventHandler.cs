using EventHandlers.Services.Role;
using Infrastructure.Constants;
using Infrastructure.Events.Roles;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Roles;

public class RoleAndRelatedDataDeleteEventHandler : ICommandHandler<RoleAndRelatedDataDeleteEvent, CommandResponse>
{
    private readonly ILogger<RoleAndRelatedDataDeleteEventHandler> _logger;
    private readonly RoleAndRelatedDataDeleteService _service;

    public RoleAndRelatedDataDeleteEventHandler(ILogger<RoleAndRelatedDataDeleteEventHandler> logger,
        RoleAndRelatedDataDeleteService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(RoleAndRelatedDataDeleteEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(RoleAndRelatedDataDeleteEvent command)
    {
        var commandResponse = new CommandResponse();
        _logger.LogInformation(
            "Inside RoleAndRelatedDataDeleteEventHandler :: RoleAndRelatedDataDeleteEvent :: {Command}",
            JsonConvert.SerializeObject(command));

        try
        {
            await _service.DeleteRoleAndRelatedDataAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in RoleAndRelatedDataDeleteEventHandler\n{StackTrace}", e.StackTrace);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
        }

        return commandResponse;
    }
}