using EventHandlers.Services.Role;
using Infrastructure.Events.Roles;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace EventHandlers.Roles;

public class
    UserRolesUpdateEventHandler : ICommandHandler<UserRolesUpdateEvent,
    CommandResponse>
{
    private readonly ILogger<UserRolesUpdateEventHandler> _logger;
    private readonly UserRoleUpdaterFromDesignationService _service;

    public UserRolesUpdateEventHandler(ILogger<UserRolesUpdateEventHandler> logger,
        UserRoleUpdaterFromDesignationService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(UserRolesUpdateEvent command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UserRolesUpdateEvent command)
    {
        var commandResponse = new CommandResponse();
        try
        {
            _logger.LogInformation(
                "Inside UserRolesUpdateEventHandler :: UserRolesUpdateEvent :: {Command}",
                JsonConvert.SerializeObject(command));

            commandResponse = await _service.UpdateUserRolesFromDesignation(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in UserRolesUpdateEventHandler\n{StackTrace}", ex.StackTrace);
        }
        return commandResponse;
    }
}