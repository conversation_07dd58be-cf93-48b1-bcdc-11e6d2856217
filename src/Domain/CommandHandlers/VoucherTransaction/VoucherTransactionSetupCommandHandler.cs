using Domain.Commands.VoucherTransaction;
using Domain.Contracts.VoucherTransaction;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.VoucherTransaction;

public class VoucherTransactionSetupCommandHandler : ICommandHandler<VoucherTransactionSetupCommand, CommandResponse>
{
    private readonly ILogger<VoucherTransactionSetupCommandHandler> _logger;
    private readonly IVoucherTransactionSetupCommandService _service;

    public VoucherTransactionSetupCommandHandler(
        ILogger<VoucherTransactionSetupCommandHandler> logger,
        IVoucherTransactionSetupCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(VoucherTransactionSetupCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(VoucherTransactionSetupCommand command)
    {
        _logger.LogInformation("Handling VoucherTransactionSetupCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.SetupVoucherTransactionAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in VoucherTransactionSetupCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}