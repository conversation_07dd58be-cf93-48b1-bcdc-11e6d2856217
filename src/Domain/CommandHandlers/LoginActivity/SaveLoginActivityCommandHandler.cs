using Domain.Commands.LoginActivity;
using Domain.Services.LoginActivity;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.LoginActivity;

public class SaveLoginActivityCommandHandler : ICommandHandler<SaveLoginActivityCommand, CommandResponse>
{
    private readonly ILogger<SaveLoginActivityCommandHandler> _logger;
    private readonly SaveLoginActivityService _saveLoginActivityService;

    public SaveLoginActivityCommandHandler(ILogger<SaveLoginActivityCommandHandler> logger,
        SaveLoginActivityService saveLoginActivityService)
    {
        _logger = logger;
        _saveLoginActivityService = saveLoginActivityService;
    }
    public CommandResponse Handle(SaveLoginActivityCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(SaveLoginActivityCommand command)
    {
        _logger.LogInformation(
            "Inside SaveLoginActivityCommandHandler :: SaveLoginActivityCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _saveLoginActivityService.SaveLoginActivity(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            _logger.LogError(e, "Exception in SaveLoginActivityCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}