using Domain.Commands.Vendor;
using Domain.Services.Vendor;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Vendor;

public class UpdateVendorStatusCommandHandler : ICommandHandler<UpdateVendorStatusCommand, CommandResponse>
{
    private readonly ILogger<UpdateVendorStatusCommandHandler> _logger;
    private readonly UpdateVendorStatusService _service;

    public UpdateVendorStatusCommandHandler(ILogger<UpdateVendorStatusCommandHandler> logger,
        UpdateVendorStatusService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateVendorStatusCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateVendorStatusCommand command)
    {
        _logger.LogInformation("Inside UpdateVendorStatusCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.UpdateVendorStatus(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateVendorStatusCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}