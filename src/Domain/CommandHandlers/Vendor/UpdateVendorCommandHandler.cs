using Domain.Commands.Vendor;
using Domain.Services.Vendor;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Vendor;

public class UpdateVendorCommandHandler : ICommandHandler<UpdateVendorCommand, CommandResponse>
{
    private readonly ILogger<UpdateVendorCommandHandler> _logger;
    private readonly UpdateVendorService _service;

    public UpdateVendorCommandHandler(ILogger<UpdateVendorCommandHandler> logger,
        UpdateVendorService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateVendorCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateVendorCommand command)
    {
        _logger.LogInformation("Inside UpdateVendorCommandHandler :: Command {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.UpdateVendorAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateVendorCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}