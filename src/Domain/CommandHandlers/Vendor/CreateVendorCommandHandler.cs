using Domain.Commands.Vendor;
using Domain.Services.Vendor;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Vendor;

public class CreateVendorCommandHandler : ICommandHandler<CreateVendorCommand, CommandResponse>
{
    private readonly ILogger<CreateVendorCommandHandler> _logger;
    private readonly CreateVendorService _service;

    public CreateVendorCommandHandler(ILogger<CreateVendorCommandHandler> logger,
        CreateVendorService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateVendorCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateVendorCommand command)
    {
        _logger.LogInformation("Inside CreateVendorCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.CreateVendor(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            _logger.LogError(e, "Exception occurred in CreateVendorCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}