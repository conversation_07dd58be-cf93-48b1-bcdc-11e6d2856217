using Domain.Commands.ExternalSync;
using Domain.Services.ExternalSync;
using Domain.Services.Helper;
using Infrastructure.Constants;
using Infrastructure.Events.ExternalSync;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.ExternalSync
{
    public class SyncSylviaDataCommandHandler : ICommandHandler<SyncSylviaDataCommand, CommandResponse>
    {
        private readonly IServiceClient _serviceClient;
        private readonly ILogger<SyncSylviaDataCommandHandler> _logger;
        private readonly ExternalSyncService _externalSyncService;
        private readonly CommonHelperService _commonHelperService;

        public SyncSylviaDataCommandHandler(
            IServiceClient serviceClient,
            ILogger<SyncSylviaDataCommandHandler> logger,
            ExternalSyncService externalSyncService,
            CommonHelperService commonHelperService)
        {
            _serviceClient = serviceClient ?? throw new ArgumentNullException(nameof(serviceClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _externalSyncService = externalSyncService ?? throw new ArgumentNullException(nameof(externalSyncService));
            _commonHelperService = commonHelperService ?? throw new ArgumentNullException(nameof(commonHelperService));
        }

        public CommandResponse Handle(SyncSylviaDataCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(SyncSylviaDataCommand command)
        {
            _logger.LogInformation("SyncSylviaDataCommandHandler->HandleAsync->START");
            var response = new CommandResponse();

            string requestId = Guid.NewGuid().ToString();
            var sequence = await _commonHelperService.GetSequenceNumberAsync(BuroConstants.BuroExternalSyncRequestIdentifier, "");
            await _externalSyncService.AddExternalRequestEntry(requestId, sequence, command.SyncType);

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, new SyncSylviaDataEvent { RequestItemId = requestId, DataSyncType = command.SyncType });
            _logger.LogInformation("SyncSylviaDataCommandHandler->HandleAsync->END");
            return response;
        }
    }
}
