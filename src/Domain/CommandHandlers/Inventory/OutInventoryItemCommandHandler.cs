using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class OutInventoryItemCommandHandler : ICommandHandler<OutInventoryItemCommand, CommandResponse>
{
    private readonly ILogger<OutInventoryItemCommandHandler> _logger;
    private readonly OutInventoryItemService _service;

    public OutInventoryItemCommandHandler(ILogger<OutInventoryItemCommandHandler> logger,
        OutInventoryItemService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(OutInventoryItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(OutInventoryItemCommand command)
    {
        _logger.LogInformation("Inside OutInventoryItemCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.OutInventoryItemAsync(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            _logger.LogError(e, "Exception occurred in OutInventoryItemCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}