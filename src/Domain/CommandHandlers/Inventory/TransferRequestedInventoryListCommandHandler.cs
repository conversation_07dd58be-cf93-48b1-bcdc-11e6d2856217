using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class TransferRequestedInventoryListCommandHandler : ICommandHandler<TransferRequestedInventoryListCommand, CommandResponse>
{
    private readonly ILogger<TransferRequestedInventoryListCommandHandler> _logger;
    private readonly TransferInventoryItemService _service;

    public TransferRequestedInventoryListCommandHandler(ILogger<TransferRequestedInventoryListCommandHandler> logger,
        TransferInventoryItemService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(TransferRequestedInventoryListCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(TransferRequestedInventoryListCommand command)
    {
        _logger.LogInformation("Inside TransferRequestedInventoryCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.TransferInventoryItemAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in TransferRequestedInventoryCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}