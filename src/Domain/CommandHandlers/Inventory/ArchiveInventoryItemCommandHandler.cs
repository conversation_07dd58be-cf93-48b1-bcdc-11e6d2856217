using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class ArchiveInventoryItemCommandHandler : ICommandHandler<ArchiveInventoryItemCommand, CommandResponse>
{
    private readonly ILogger<ArchiveInventoryItemCommandHandler> _logger;
    private readonly ArchiveInventoryItemService _service;

    public ArchiveInventoryItemCommandHandler(ILogger<ArchiveInventoryItemCommandHandler> logger,
        ArchiveInventoryItemService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(ArchiveInventoryItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(ArchiveInventoryItemCommand command)
    {
        _logger.LogInformation("Inside ArchiveInventoryItemCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.ArchiveInventoryItem(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in ArchiveInventoryItemCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return response;
    }
}