using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class AddInventoryMovementCommandHandler : ICommandHandler<AddInventoryMovementCommand, CommandResponse>
{
    private readonly ILogger<AddInventoryMovementCommandHandler> _logger;
    private readonly AddInventoryMovementService _service;

    public AddInventoryMovementCommandHandler(ILogger<AddInventoryMovementCommandHandler> logger,
        AddInventoryMovementService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(AddInventoryMovementCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(AddInventoryMovementCommand command)
    {
        _logger.LogInformation("Inside AddInventoryTransactionCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.AddInventoryManagement(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in AddInventoryTransactionCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return response;
    }
}