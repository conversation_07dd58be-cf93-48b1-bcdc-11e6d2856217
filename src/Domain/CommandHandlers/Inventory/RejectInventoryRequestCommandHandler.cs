using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class RejectInventoryRequestCommandHandler : I<PERSON>ueryH<PERSON>ler<RejectInventoryRequestCommand, CommandResponse>
{
    private readonly ILogger<RejectInventoryRequestCommandHandler> _logger;
    private readonly RejectInventoryRequestService _service;

    public RejectInventoryRequestCommandHandler(ILogger<RejectInventoryRequestCommandHandler> logger,
        RejectInventoryRequestService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(RejectInventoryRequestCommand query)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(RejectInventoryRequestCommand query)
    {
        _logger.LogInformation("Inside RejectInventoryRequestCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(query));
        try
        {
            return await _service.RejectInventoryRequestAsync(query);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in RejectInventoryRequestCommandHandler\n{StackTrace}", e.StackTrace);
            var response = new CommandResponse();
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            return response;
        }
    }
}