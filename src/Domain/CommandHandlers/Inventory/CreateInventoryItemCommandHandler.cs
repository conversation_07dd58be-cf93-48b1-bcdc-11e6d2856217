using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class CreateInventoryItemCommandHandler : ICommandHandler<CreateInventoryItemCommand, CommandResponse>
{
    private readonly ILogger<CreateInventoryItemCommandHandler> _logger;
    private readonly CreateInventoryItemService _service;

    public CreateInventoryItemCommandHandler(ILogger<CreateInventoryItemCommandHandler> logger,
        CreateInventoryItemService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreateInventoryItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateInventoryItemCommand command)
    {
        _logger.LogInformation("Inside CreateInventoryItemCommandHandler :: Command :: {Command}",
            JsonConvert.SerializeObject(command));
        var commandResponse = new CommandResponse();
        try
        {
            await _service.CreateInventoryItem(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in CreateInventoryItemCommandHandler\n{StackTrace}", e.StackTrace);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return commandResponse;
    }
}