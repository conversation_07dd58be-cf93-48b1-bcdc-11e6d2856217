using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class TransferInventoryItemCommandHandler : ICommandHandler<TransferInventoryItemCommand, CommandResponse>
{
    private readonly ILogger<TransferInventoryItemCommandHandler> _logger;
    private readonly TransferInventoryItemService _service;

    public TransferInventoryItemCommandHandler(ILogger<TransferInventoryItemCommandHandler> logger,
        TransferInventoryItemService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(TransferInventoryItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(TransferInventoryItemCommand command)
    {
        _logger.LogInformation("Inside TransferInventoryItemCommandHandler :: TransferInventoryItemCommand :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.TransferInventoryItemAsync(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            _logger.LogError(e, "Exception occurred in OutInventoryItemCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}