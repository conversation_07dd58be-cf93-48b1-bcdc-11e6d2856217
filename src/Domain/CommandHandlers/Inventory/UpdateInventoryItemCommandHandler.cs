using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class UpdateInventoryItemCommandHandler : ICommandHandler<UpdateInventoryItemCommand, CommandResponse>
{
    private readonly ILogger<UpdateInventoryItemCommandHandler> _logger;
    private readonly UpdateInventoryItemService _service;

    public UpdateInventoryItemCommandHandler(ILogger<UpdateInventoryItemCommandHandler> logger,
        UpdateInventoryItemService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(UpdateInventoryItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateInventoryItemCommand command)
    {
        _logger.LogInformation("Inside UpdateInventoryItemCommandHandler :: Command :: {Command}",
            JsonConvert.SerializeObject(command));
        var commandResponse = new CommandResponse();
        try
        {
            await _service.UpdateInventoryItem(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateInventoryItemCommandHandler\n{StackTrace}", e.StackTrace);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return commandResponse;
    }
}