using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class DisposeInventoryItemCommandHandler : ICommandHandler<DisposeInventoryItemCommand, CommandResponse>
{
    private readonly ILogger<DisposeInventoryItemCommandHandler> _logger;
    private readonly DisposeInventoryItemService _service;

    public DisposeInventoryItemCommandHandler(ILogger<DisposeInventoryItemCommandHandler> logger,
        DisposeInventoryItemService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(DisposeInventoryItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DisposeInventoryItemCommand command)
    {
        _logger.LogInformation("Inside DisposeInventoryItemCommandHandler :: Command :: {command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.DisposeInventoryItemAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in DisposeInventoryItemCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}