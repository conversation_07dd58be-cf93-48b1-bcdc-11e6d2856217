using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Inventory;

public class CreateInventoryRequestCommandHandler : ICommandHandler<CreateInventoryRequestCommand, CommandResponse>
{
    private readonly ILogger<CreateInventoryRequestCommandHandler> _logger;
    private readonly CreateInventoryRequestService _service;

    public CreateInventoryRequestCommandHandler(ILogger<CreateInventoryRequestCommandHandler> logger,
        CreateInventoryRequestService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateInventoryRequestCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateInventoryRequestCommand command)
    {
        _logger.LogInformation("Inside CreateInventoryRequestCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.CreateInventoryRequestAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in CreateInventoryRequestCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}