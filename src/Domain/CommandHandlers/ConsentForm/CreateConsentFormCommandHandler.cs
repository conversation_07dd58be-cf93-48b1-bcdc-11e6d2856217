using Domain.Commands.ConsentForm;
using Domain.Contracts.ConsentForm;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.ConsentForm;

public class CreateConsentFormCommandHandler : ICommandHandler<CreateConsentFormCommand, CommandResponse>
{

    private readonly ICreateConsentFormCommandService _service;
    private readonly ILogger<CreateConsentFormCommandHandler> _logger;

    public CreateConsentFormCommandHandler(
        ILogger<CreateConsentFormCommandHandler> logger,
        ICreateConsentFormCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreateConsentFormCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateConsentFormCommand command)
    {
        _logger.LogInformation("Handling CreateConsentFormCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.CreateConsentFormAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateConsentFormCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}