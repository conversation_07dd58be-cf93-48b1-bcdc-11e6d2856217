using Domain.Commands.ConsentForm;
using Domain.Contracts.ConsentForm;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.ConsentForm;

public class RevokeConsentFormCommandHandler : ICommandHandler<RevokeConsentFormCommand, CommandResponse>
{
    private readonly IRevokeConsentFormCommandService _service;
    private readonly ILogger<RevokeConsentFormCommandHandler> _logger;

    public RevokeConsentFormCommandHandler(
        ILogger<RevokeConsentFormCommandHandler> logger,
        IRevokeConsentFormCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(RevokeConsentFormCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(RevokeConsentFormCommand command)
    {
        _logger.LogInformation("Handling RevokeConsentFormCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.RevokeConsentFormAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in RevokeConsentFormCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}