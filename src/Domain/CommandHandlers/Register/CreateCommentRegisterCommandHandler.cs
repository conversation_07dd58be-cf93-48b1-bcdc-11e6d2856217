using Domain.Commands.Register;
using Domain.Contracts.Register;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Register;

public class CreateCommentRegisterCommandHandler :  ICommandHandler<CreateCommentRegisterCommand, CommandResponse>
{
    private readonly ILogger<CreateCommentRegisterCommandHandler> _logger;
    private readonly ICreateCommentRegisterCommandService _service;

    public CreateCommentRegisterCommandHandler(
        ILogger<CreateCommentRegisterCommandHandler> logger,
        ICreateCommentRegisterCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreateCommentRegisterCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateCommentRegisterCommand command)
    {
        _logger.LogInformation("Handling CreateCommentRegisterCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.CreateCommentRegisterAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateCommentRegisterCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}