using Domain.Commands.Register;
using Domain.Contracts.Register;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Register;

public class ReturnChequeCommandHandler : ICommandHandler<ReturnChequeCommand, CommandResponse>
{
    private readonly ILogger<ReturnChequeCommandHandler> _logger;
    private readonly IChequeReturnService _service;

    public ReturnChequeCommandHandler(
        ILogger<ReturnChequeCommandHandler> logger,
        IChequeReturnService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(ReturnChequeCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(ReturnChequeCommand command)
    {
        _logger.LogInformation("Handling ReturnChequeCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.ChequeReturnAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in ReturnChequeCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}