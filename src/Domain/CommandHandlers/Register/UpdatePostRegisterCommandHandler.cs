using Domain.Commands.Register;
using Domain.Contracts.Register;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Register;

public class UpdatePostRegisterCommandHandler : ICommandHandler<UpdatePostRegisterCommand, CommandResponse>
{
    private readonly ILogger<UpdatePostRegisterCommandHandler> _logger;
    private readonly IUpdatePostRegisterCommandService _service;

    public UpdatePostRegisterCommandHandler(
        ILogger<UpdatePostRegisterCommandHandler> logger,
        IUpdatePostRegisterCommandService service)
    {
        _logger = logger;
        _service = service;
    }
    
    public CommandResponse Handle(UpdatePostRegisterCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdatePostRegisterCommand command)
    {
        _logger.LogInformation("Handling UpdatePostRegisterCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.UpdatePostRegisterAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in UpdatePostRegisterCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}