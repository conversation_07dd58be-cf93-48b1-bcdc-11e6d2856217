using Domain.Commands.Register;
using Domain.Contracts.Register;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Register;

public class DeletePostRegisterCommandHandler : ICommandHandler<DeletePostRegisterCommand, CommandResponse>
{
    
    private readonly ILogger<DeletePostRegisterCommandHandler> _logger;
    private readonly IDeletePostRegisterCommandService _service;

    public DeletePostRegisterCommandHandler(
        ILogger<DeletePostRegisterCommandHandler> logger,
        IDeletePostRegisterCommandService service)
    {
        _logger = logger;
        _service = service;
    }
    
    public CommandResponse Handle(DeletePostRegisterCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DeletePostRegisterCommand command)
    {
        _logger.LogInformation("Handling DeletePostRegisterCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.DeletePostRegisterAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in DeletePostRegisterCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}