using Domain.Commands.Register;
using Domain.Contracts.Register;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Register;

public class CreateIssueChequeRegisterCommandHandler : ICommandHandler<CreateIssueChequeRegisterCommand, CommandResponse>
{
    private readonly ILogger<CreateIssueChequeRegisterCommandHandler> _logger;
    private readonly ICreateIssueChequeRegisterCommandService _service;

    public CreateIssueChequeRegisterCommandHandler(
        ILogger<CreateIssueChequeRegisterCommandHandler> logger,
        ICreateIssueChequeRegisterCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreateIssueChequeRegisterCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateIssueChequeRegisterCommand command)
    {
        _logger.LogInformation("Handling CreateIssueChequeRegisterCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.CreateIssueChequeAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateIssueChequeRegisterCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}