using Domain.Commands.Register;
using Domain.Contracts.Register;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Register;

public class CreatePostRegisterCommandHandler : ICommandHandler<CreatePostRegisterCommand, CommandResponse>
{
    
    private readonly ILogger<CreatePostRegisterCommandHandler> _logger;
    private readonly ICreatePostRegisterCommandService _service;

    public CreatePostRegisterCommandHandler(
        ILogger<CreatePostRegisterCommandHandler> logger,
        ICreatePostRegisterCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreatePostRegisterCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreatePostRegisterCommand command)
    {
        _logger.LogInformation("Handling CreatePostRegisterCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.CreatePostRegisterAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreatePostRegisterCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}