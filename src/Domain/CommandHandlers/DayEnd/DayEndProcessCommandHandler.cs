using Domain.Commands.DayEnd;
using Domain.Services.DayEnd;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.DayEnd;

public class DayEndProcessCommandHandler : ICommandHandler<UpdateDayEndProcessCommand, CommandResponse>
{
    private readonly DayEndProcessService _dayEndProcessService;
    private readonly ILogger<DayEndProcessCommandHandler> _logger;

    public DayEndProcessCommandHandler(
        ILogger<DayEndProcessCommandHandler> logger,
        DayEndProcessService dayEndProcessService)
    {
        _logger = logger;
        _dayEndProcessService = dayEndProcessService;
    }

    public CommandResponse Handle(UpdateDayEndProcessCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateDayEndProcessCommand command)
    {
        _logger.LogInformation(
            "Inside DayEndProcessCommandHandler :: UpdateDayEndProcessCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _dayEndProcessService.UpdateDayEndStatus(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateDayEndProcessCommand\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}