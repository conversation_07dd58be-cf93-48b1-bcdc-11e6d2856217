using Domain.Commands.Tasks;
using Domain.Services.Tasks;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Tasks
{
    public class UpdateManualAssignmentCommandHandler : ICommandHandler<UpdateManualAssignmentCommand, CommandResponse>
    {
        private readonly ILogger<UpdateManualAssignmentCommandHandler> _logger;
        private readonly TasksCommandService _taskService;


        public UpdateManualAssignmentCommandHandler(
            ILogger<UpdateManualAssignmentCommandHandler> logger,
            TasksCommandService taskService)
        {
            _logger = logger;
            _taskService = taskService;
        }

        public CommandResponse Handle(UpdateManualAssignmentCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(UpdateManualAssignmentCommand command)
        {
            _logger.LogInformation("Handling AddCenterCommand: {SerializedCommand}", JsonConvert.SerializeObject(command));
            var commandResponse = new CommandResponse();
            try
            {
                _logger.LogInformation("Inside -> UpdateManualAssignmentCommandHandler -> START");
                commandResponse = await _taskService.UpdateManualAssignment(command);
                _logger.LogInformation("Inside -> UpdateManualAssignmentCommandHandler -> END");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ERROR_OCCURED");
            }

            return commandResponse;
        }
    }
}
