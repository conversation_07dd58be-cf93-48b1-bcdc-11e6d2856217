using Domain.Commands.Role;
using Domain.Services.Role;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Role;

public class RoleDeleteCommandHandler : ICommandHandler<RoleDeleteCommand, CommandResponse>
{
    private readonly ILogger<RoleDeleteCommandHandler> _logger;
    private readonly RoleDeleteService _service;

    public RoleDeleteCommandHandler(ILogger<RoleDeleteCommandHandler> logger,
        RoleDeleteService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(RoleDeleteCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(RoleDeleteCommand command)
    {
        var commandResponse = new CommandResponse();
        _logger.LogInformation(
            "Inside RoleDeleteCommandHandler :: RoleDeleteCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        try
        {
            commandResponse = await _service.DeleteRoleAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in RoleDeleteCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return commandResponse;
    }
}