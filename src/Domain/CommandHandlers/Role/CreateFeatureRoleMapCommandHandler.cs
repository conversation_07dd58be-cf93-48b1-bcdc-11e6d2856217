using Domain.Commands.Role;
using Domain.Contracts.Role;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Role
{
    public class CreateFeatureRoleMapCommandHandler : ICommandHandler<CreateFeatureRoleMapCommand, CommandResponse>
    {
        private readonly ILogger<CreateFeatureRoleMapCommandHandler> _logger;
        private readonly ICreateFeatureRoleMapService _service;

        public CreateFeatureRoleMapCommandHandler(
            ILogger<CreateFeatureRoleMapCommandHandler> logger,
            ICreateFeatureRoleMapService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(CreateFeatureRoleMapCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(CreateFeatureRoleMapCommand command)
        {
            _logger.LogInformation("Handling CreateFeatureRoleMapCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.CreateFeatureRoleMapAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CreateFeatureRoleMapCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
