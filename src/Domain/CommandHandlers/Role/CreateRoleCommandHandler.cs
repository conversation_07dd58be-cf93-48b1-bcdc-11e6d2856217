using Domain.Commands;
using Domain.Services.Role;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Role;

public class CreateRoleCommandHandler : ICommandHandler<CreateRoleCommand, CommandResponse>
{
    private readonly ILogger<CreateRoleCommandHandler> _logger;
    private readonly CreateRoleService _service;

    public CreateRoleCommandHandler(ILogger<CreateRoleCommandHandler> logger, CreateRoleService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreateRoleCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateRoleCommand command)
    {
        _logger.LogInformation(
            "Inside CreateRoleCommandHandler :: CreateRoleCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        var commandResponse = new CommandResponse();
        try
        {
            await _service.CreateRole(command);
        }
        catch (Exception e)
        {
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Role creation failed");
            _logger.LogError(e, "Exception in CreateRoleCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}