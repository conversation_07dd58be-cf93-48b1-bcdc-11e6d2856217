using Domain.Commands.Role;
using Domain.Contracts.Role;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Role
{
    public class UpdateFeatureRoleMapWithStatusCommandHandler : ICommandHandler<UpdateFeatureRoleMapWithStatusCommand, CommandResponse>
    {
        private readonly ILogger<UpdateFeatureRoleMapWithStatusCommandHandler> _logger;
        private readonly IUpdateFeatureRoleMapService _service;

        public UpdateFeatureRoleMapWithStatusCommandHandler(
            ILogger<UpdateFeatureRoleMapWithStatusCommandHandler> logger,
            IUpdateFeatureRoleMapService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(UpdateFeatureRoleMapWithStatusCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(UpdateFeatureRoleMapWithStatusCommand command)
        {
            _logger.LogInformation("Handling UpdateFeatureRoleMapWithStatusCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.UpdateFeatureRoleMapAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in UpdateFeatureRoleMapWithStatusCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
