using Domain.Commands.Role;
using Domain.Services.Role;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Role
{
    public class UpdateRolesCommandHandler : ICommandHandler<UpdateRolesCommand, CommandResponse>
    {
        public readonly ILogger<UpdateRolesCommandHandler> _logger;
        public readonly UpdateRolesService _service;

        public UpdateRolesCommandHandler(
            ILogger<UpdateRolesCommandHandler> logger,
            UpdateRolesService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(UpdateRolesCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(UpdateRolesCommand command)
        {
            _logger.LogInformation("Handling UpdateRolesCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var response = new CommandResponse();

            try
            {
                response = await _service.UpdateRolesAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in UpdateRolesCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return response;
        }
    }
}
