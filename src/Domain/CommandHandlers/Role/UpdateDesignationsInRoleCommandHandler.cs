using Domain.Commands.Role;
using Domain.Services.Role;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Role;

public class UpdateDesignationsInRoleCommandHandler : ICommandHandler<UpdateDesignationsInRoleCommand, CommandResponse>
{
    private readonly ILogger<UpdateDesignationsInRoleCommandHandler> _logger;
    private readonly UpdateDesignationsInRoleService _service;

    public UpdateDesignationsInRoleCommandHandler(ILogger<UpdateDesignationsInRoleCommandHandler> logger,
        UpdateDesignationsInRoleService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateDesignationsInRoleCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateDesignationsInRoleCommand command)
    {
        _logger.LogInformation(
            "Inside UpdateDesignationsInRoleCommandHandler :: UpdateDesignationsInRoleCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _service.UpdateDesignationRoleMap(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in UpdateDesignationsInRoleCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}