using Domain.Commands.Role;
using Domain.Contracts.Role;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Role
{
    public class UpdateFeatureRoleMapCommandHandler : ICommandHandler<UpdateFeatureRoleMapCommand, CommandResponse>
    {
        private readonly ILogger<UpdateFeatureRoleMapCommandHandler> _logger;
        private readonly IUpdateFeatureRoleMapService _service;

        public UpdateFeatureRoleMapCommandHandler(
            ILogger<UpdateFeatureRoleMapCommandHandler> logger,
            IUpdateFeatureRoleMapService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(UpdateFeatureRoleMapCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(UpdateFeatureRoleMapCommand command)
        {
            _logger.LogInformation("Handling UpdateFeatureRoleMapCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.UpdateFeatureRoleMapAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in UpdateFeatureRoleMapCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
