using Domain.Commands.Account;
using Domain.Contracts.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Account
{
    public class CloneLoanCommandHandler : ICommandHandler<CloneLoanCommand, CommandResponse>
    {
        private readonly ILogger<CloneLoanCommandHandler> _logger;
        private readonly ICloneLoanCommandService _service;

        public CloneLoanCommandHandler(
            ILogger<CloneLoanCommandHandler> logger,
            ICloneLoanCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(CloneLoanCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(CloneLoanCommand command)
        {
            _logger.LogInformation("Handling CloneLoanCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.CloneLoanAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CloneLoanCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
