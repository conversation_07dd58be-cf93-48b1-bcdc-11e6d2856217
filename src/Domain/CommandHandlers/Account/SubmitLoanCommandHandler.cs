using Domain.Commands.Account;
using Domain.Contracts.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Account
{
    public class SubmitLoanCommandHandler : ICommandHandler<SubmitLoanCommand, CommandResponse>
    {
        private readonly ILogger<SubmitLoanCommandHandler> _logger;
        private readonly ISubmitLoanCommandService _service;

        public SubmitLoanCommandHandler(
            ILogger<SubmitLoanCommandHandler> logger,
            ISubmitLoanCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(SubmitLoanCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(SubmitLoanCommand command)
        {
            _logger.LogInformation("Handling SubmitLoanCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.SubmitLoanAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in SubmitLoanCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
