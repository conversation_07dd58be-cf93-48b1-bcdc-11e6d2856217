using Domain.Commands.Account;
using Domain.Contracts.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Account
{
    public class ApplyLoanCommandHandler : ICommandHandler<ApplyLoanCommand, CommandResponse>
    {
        private readonly ILogger<ApplyLoanCommandHandler> _logger;
        private readonly IApplyLoanCommandService _service;

        public ApplyLoanCommandHandler(
            ILogger<ApplyLoanCommandHandler> logger,
            IApplyLoanCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(ApplyLoanCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(ApplyLoanCommand command)
        {
            _logger.LogInformation("Handling ApplyLoanCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.ApplyForLoanAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ApplyLoanCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
