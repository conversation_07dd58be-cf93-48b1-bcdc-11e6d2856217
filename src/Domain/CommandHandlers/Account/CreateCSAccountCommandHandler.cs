using Domain.Commands.Account;
using Domain.Contracts.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Account
{
    public class CreateCSAccountCommandHandler : ICommandHandler<CreateCSAccountCommand, CommandResponse>
    {
        private readonly ILogger<CreateCSAccountCommandHandler> _logger;
        private readonly ICreateCSAccountCommandService _service;

        public CreateCSAccountCommandHandler(
            ILogger<CreateCSAccountCommandHandler> logger,
            ICreateCSAccountCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(CreateCSAccountCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(CreateCSAccountCommand command)
        {
            _logger.LogInformation("Handling CreateCSAccountCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.ApplyForCSAccountAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in CreateCSAccountCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
