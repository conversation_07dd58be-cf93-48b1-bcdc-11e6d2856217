using Domain.Commands.Account;
using Domain.Contracts.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Account
{
    public class GenerateLoanScheduleCommandHandler : ICommandHandler<GenerateLoanScheduleCommand, CommandResponse>
    {
        private readonly ILogger<GenerateLoanScheduleCommandHandler> _logger;
        private readonly IGenerateLoanScheduleCommandService _generateLoanScheduleCommandService;

        public GenerateLoanScheduleCommandHandler(ILogger<GenerateLoanScheduleCommandHandler> logger,
            IGenerateLoanScheduleCommandService generateLoanScheduleCommandService)
        {
            _logger = logger;
            _generateLoanScheduleCommandService = generateLoanScheduleCommandService;
        }
        public CommandResponse Handle(GenerateLoanScheduleCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(GenerateLoanScheduleCommand command)
        {
            _logger.LogInformation("Handling GenerateLoanScheduleCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _generateLoanScheduleCommandService.GenerateLoanSchedule(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in GenerateLoanScheduleCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
