using Domain.Commands.Account;
using Domain.Contracts.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Account
{
    public class SubmitDisbursementCommandHandler : ICommandHandler<SubmitDisbursementCommand, CommandResponse>
    {
        private readonly ILogger<SubmitDisbursementCommandHandler> _logger;
        private readonly ISubmitDisbursementCommandService _service;

        public SubmitDisbursementCommandHandler(
            ILogger<SubmitDisbursementCommandHandler> logger,
            ISubmitDisbursementCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(SubmitDisbursementCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(SubmitDisbursementCommand command)
        {
            _logger.LogInformation("Handling SubmitDisbursementCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.SubmitDisbursementAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in SubmitDisbursementCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
