using Domain.Commands.Account;
using Domain.Contracts.Account;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Account
{
    public class ChangeGSAccountCommandHandler : ICommandHandler<ChangeGSAccountCommand, CommandResponse>
    {
        private readonly ILogger<ChangeGSAccountCommandHandler> _logger;
        private readonly IChangeGSAccountCommandService _changeGSAccountCommandService;

        public ChangeGSAccountCommandHandler(ILogger<ChangeGSAccountCommandHandler> logger,
            IChangeGSAccountCommandService changeGSAccountCommandService)
        {
            _logger = logger;
            _changeGSAccountCommandService = changeGSAccountCommandService;
        }
        public CommandResponse Handle(ChangeGSAccountCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(ChangeGSAccountCommand command)
        {
            _logger.LogInformation("Handling ChangeGSAccountCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _changeGSAccountCommandService.ChangeGSAccount(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ChangeGSAccountCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
