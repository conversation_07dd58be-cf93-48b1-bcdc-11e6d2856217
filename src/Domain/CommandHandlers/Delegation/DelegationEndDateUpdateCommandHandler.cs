using Domain.Commands.Delegation;
using Domain.Services.Delegation;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Delegation;

public class DelegationEndDateUpdateCommandHandler : ICommandHandler<DelegationEndDateUpdateCommand, CommandResponse>
{
    private readonly ILogger<DelegationEndDateUpdateCommandHandler> _logger;
    private readonly DelegationUpdateService _delegationUpdateService;

    public DelegationEndDateUpdateCommandHandler(ILogger<DelegationEndDateUpdateCommandHandler> logger,
        DelegationUpdateService delegationUpdateService)
    {
        _logger = logger;
        _delegationUpdateService = delegationUpdateService;
    }

    public CommandResponse Handle(DelegationEndDateUpdateCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DelegationEndDateUpdateCommand command)
    {
        var response = new CommandResponse();
        _logger.LogInformation("Inside DelegationEndDateUpdateCommandHandler :: DelegationEndDateUpdateCommand :: {Command} ", JsonConvert.SerializeObject(command));
        try
        {
            response = await _delegationUpdateService.UpdateDelegationEndDate(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Delegation update failed");
            _logger.LogError(e, "Exception in DelegationEndDateUpdateCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}