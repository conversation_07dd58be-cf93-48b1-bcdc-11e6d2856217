using Domain.Commands.Delegation;
using Domain.Services.Delegation;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Delegation;

public class DelegationCreateCommandHandler : ICommandHandler<DelegationCreateCommand, CommandResponse>
{
    private readonly ILogger<DelegationCreateCommandHandler> _logger;
    private readonly DelegationCreateService _delegationCreateService;

    public DelegationCreateCommandHandler(ILogger<DelegationCreateCommandHandler> logger,
        DelegationCreateService delegationCreateService)
    {
        _logger = logger;
        _delegationCreateService = delegationCreateService;
    }
    public CommandResponse Handle(DelegationCreateCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DelegationCreateCommand command)
    {
        _logger.LogInformation(
            "Inside DelegationCreateCommandHandler :: DelegationCreateCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            response = await _delegationCreateService.CreateAsync(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Delegation creation failed");
            _logger.LogError(e, "Exception in DelegationCreateCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}