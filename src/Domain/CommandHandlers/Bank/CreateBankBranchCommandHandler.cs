using Domain.Commands.Bank;
using Domain.Contracts.Bank;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Bank;

public class CreateBankBranchCommandHandler : ICommandHandler<CreateBankBranchCommand, CommandResponse>
{
    private readonly ICreateBankBranchCommandService _service;
    private readonly ILogger<CreateBankBranchCommandHandler> _logger;

    public CreateBankBranchCommandHandler(
        ILogger<CreateBankBranchCommandHandler> logger,
        ICreateBankBranchCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreateBankBranchCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateBankBranchCommand command)
    {
        _logger.LogInformation("Handling CreateBankBranchCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.CreateBranchAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateBankBranchCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}