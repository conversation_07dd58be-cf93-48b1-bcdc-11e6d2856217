using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Voucher.Configuration;

public class DeleteVoucherConfigurationCommandHandler : ICommandHandler<DeleteVoucherConfigCommand, CommandResponse>
{

    private readonly ILogger<DeleteVoucherConfigurationCommandHandler> _logger;
    private readonly IDeleteVoucherConfigurationStrategyFactory _strategyFactory;

    public DeleteVoucherConfigurationCommandHandler(
        ILogger<DeleteVoucherConfigurationCommandHandler> logger,
        IDeleteVoucherConfigurationStrategyFactory strategyFactory)
    {
        _logger = logger;
        _strategyFactory = strategyFactory;
    }

    public CommandResponse Handle(DeleteVoucherConfigCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DeleteVoucherConfigCommand command)
    {
        _logger.LogInformation(
            "Inside DeleteVoucherConfigurationCommandHandler :: CreateHolidayCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _strategyFactory.ExecuteStrategyAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in DeleteVoucherConfigurationCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }

}
