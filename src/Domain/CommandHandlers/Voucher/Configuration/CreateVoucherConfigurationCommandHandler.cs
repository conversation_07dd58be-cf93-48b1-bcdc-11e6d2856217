using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Voucher.Configuration;

public class CreateVoucherConfigurationCommandHandler : ICommandHandler<CreateVoucherConfigCommand, CommandResponse>
{
    private readonly ICreateVoucherConfigurationStrategyFactory _strategyFactory;
    private readonly ILogger<CreateVoucherConfigurationCommandHandler> _logger;

    public CreateVoucherConfigurationCommandHandler(
        ILogger<CreateVoucherConfigurationCommandHandler> logger,
        ICreateVoucherConfigurationStrategyFactory strategyFactory)
    {
        _logger = logger;
        _strategyFactory = strategyFactory;
    }


    public CommandResponse Handle(CreateVoucherConfigCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateVoucherConfigCommand command)
    {
        _logger.LogInformation(
            "Inside CreateVoucherConfigurationCommandHandler :: CreateHolidayCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _strategyFactory.ExecuteStrategyAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in CreateVoucherConfigurationCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}