using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Voucher.Configuration;

public class UpdateVoucherConfigurationCommandHandler : ICommandHandler<UpdateVoucherConfigCommand, CommandResponse>
{

    private readonly ILogger<UpdateVoucherConfigurationCommandHandler> _logger;
    private readonly IUpdateVoucherConfigurationStrategyFactory _strategyFactory;

    public UpdateVoucherConfigurationCommandHandler(
        ILogger<UpdateVoucherConfigurationCommandHandler> logger,
        IUpdateVoucherConfigurationStrategyFactory strategyFactory)
    {
        _logger = logger;
        _strategyFactory = strategyFactory;
    }


    public CommandResponse Handle(UpdateVoucherConfigCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateVoucherConfigCommand command)
    {
        _logger.LogInformation(
            "Inside UpdateVoucherConfigurationCommandHandler :: CreateHolidayCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _strategyFactory.ExecuteStrategyAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateVoucherConfigurationCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}