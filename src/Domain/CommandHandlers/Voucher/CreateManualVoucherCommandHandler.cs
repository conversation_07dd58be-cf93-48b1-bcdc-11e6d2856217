using Domain.Commands.Voucher;
using Domain.Contracts.Voucher;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Voucher;

public class CreateManualVoucherCommandHandler : ICommandHandler<CreateManualVoucherCommand, CommandResponse>
{

    private readonly ICreateManualVoucherCommandService _service;
    private readonly ILogger<CreateManualVoucherCommandHandler> _logger;

    public CreateManualVoucherCommandHandler(
        ILogger<CreateManualVoucherCommandHandler> logger,
        ICreateManualVoucherCommandService service)
    {
        _logger = logger;
        _service = service;
    }


    public CommandResponse Handle(CreateManualVoucherCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateManualVoucherCommand command)
    {
        _logger.LogInformation("Handling CreateManualVoucherCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.CreateManualVoucherAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateManualVoucherCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}