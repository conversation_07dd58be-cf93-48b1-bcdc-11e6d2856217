using Domain.Commands.Voucher;
using Domain.Contracts.Voucher;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Voucher;

public class UpdateManualVoucherCommandHandler : ICommandHandler<UpdateManualVoucherCommand, CommandResponse>
{

    private readonly IUpdateManualVoucherCommandService _service;
    private readonly ILogger<UpdateManualVoucherCommandHandler> _logger;

    public UpdateManualVoucherCommandHandler(
        ILogger<UpdateManualVoucherCommandHandler> logger,
        IUpdateManualVoucherCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(UpdateManualVoucherCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateManualVoucherCommand command)
    {
        _logger.LogInformation("Handling UpdateManualVoucherCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.UpdateManualVoucherAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in UpdateManualVoucherCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}