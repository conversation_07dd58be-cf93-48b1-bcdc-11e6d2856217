using Domain.Commands.Voucher;
using Domain.Contracts.Voucher;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Voucher;

public class DeleteManualVoucherCommandHandler : ICommandHandler<DeleteManualVoucherCommand, CommandResponse>
{

    private readonly IDeleteManualVoucherCommandService _service;
    private readonly ILogger<DeleteManualVoucherCommandHandler> _logger;

    public DeleteManualVoucherCommandHandler(
        ILogger<DeleteManualVoucherCommandHandler> logger,
        IDeleteManualVoucherCommandService service)
    {
        _logger = logger;
        _service = service;
    }


    public CommandResponse Handle(DeleteManualVoucherCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DeleteManualVoucherCommand command)
    {
        _logger.LogInformation("Handling DeleteManualVoucherCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.DeleteManualVoucherAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in DeleteManualVoucherCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}