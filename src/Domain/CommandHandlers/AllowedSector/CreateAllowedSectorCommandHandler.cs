using Domain.Commands.AllowedSector;
using Domain.Services.AllowedSector;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.AllowedSector;
public class CreateAllowedSectorCommandHandler : ICommandHandler<CreateAllowedSectorCommand, CommandResponse>
{
    private readonly ILogger<CreateAllowedSectorCommandHandler> _logger;
    private readonly CreateAllowedSectorService _service;

    public CreateAllowedSectorCommandHandler(ILogger<CreateAllowedSectorCommandHandler> logger,
        CreateAllowedSectorService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateAllowedSectorCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateAllowedSectorCommand command)
    {
        _logger.LogInformation("Inside CreateAllowedSectorCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.CreateAllowedSector(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            _logger.LogError(e, "Exception occurred in CreateAllowedSectorCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}