using Domain.Commands.AllowedSector;
using Domain.Services.AllowedSector;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.AllowedSector;

public class UpdateAllowedSectorCommandHandler : ICommandHandler<UpdateAllowedSectorCommand, CommandResponse>
{
    private readonly ILogger<UpdateAllowedSectorCommandHandler> _logger;
    private readonly UpdateAllowedSectorService _service;

    public UpdateAllowedSectorCommandHandler(ILogger<UpdateAllowedSectorCommandHandler> logger,
        UpdateAllowedSectorService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateAllowedSectorCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateAllowedSectorCommand command)
    {
        _logger.LogInformation("Inside UpdateAllowedSectorCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.UpdateAllowedSectorAsync(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            _logger.LogError(e, "Exception occurred in UpdateAllowedSectorCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}

