using Domain.Commands.AllowedSector;
using Domain.Services.AllowedSector;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.AllowedSector;

public class UpdateAllowedSectorStatusCommandHandler : ICommandHandler<UpdateAllowedSectorStatusCommand, CommandResponse>
{
    private readonly ILogger<UpdateAllowedSectorStatusCommandHandler> _logger;
    private readonly UpdateAllowedSectorStatusService _service;

    public UpdateAllowedSectorStatusCommandHandler(ILogger<UpdateAllowedSectorStatusCommandHandler> logger,
        UpdateAllowedSectorStatusService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateAllowedSectorStatusCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateAllowedSectorStatusCommand command)
    {
        _logger.LogInformation("Inside UpdateAllowedSectorStatusCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.UpdateAllowedSectorStatus(command);
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            _logger.LogError(e, "Exception occurred in UpdateAllowedSectorStatusCommandHandler\n{StackTrace}", e.StackTrace);
        }
        return response;
    }
}


