using Domain.Commands.User;
using Domain.Services.Users;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.User;

public class CreateUserPermissionCommandHandler : ICommandHandler<CreateUserPermissionCommand, CommandResponse>
{
    private readonly CreateUserPermissionService _userPermissionService;
    private readonly ILogger<CreateUserPermissionCommandHandler> _logger;

    public CreateUserPermissionCommandHandler(CreateUserPermissionService userPermissionService,
        ILogger<CreateUserPermissionCommandHandler> logger)
    {
        _userPermissionService = userPermissionService;
        _logger = logger;
    }
    public CommandResponse Handle(CreateUserPermissionCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateUserPermissionCommand command)
    {
        _logger.LogInformation(
            "Inside CreateUserPermissionCommandHandler :: CreateUserPermissionCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            response = await _userPermissionService.CreateUserPermissionAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in CreateUserPermissionCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}