using Domain.Commands.User;
using Domain.Services.Users;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.User;

public class UpdateUserPermissionCommandHandler : ICommandHandler<UpdateUserPermissionCommand, CommandResponse>
{
    private readonly ILogger<UpdateUserPermissionCommandHandler> _logger;
    private readonly UpdateUserPermissionService _updateUserPermissionService;

    public UpdateUserPermissionCommandHandler(
        ILogger<UpdateUserPermissionCommandHandler> logger, UpdateUserPermissionService updateUserPermissionService)
    {
        _logger = logger;
        _updateUserPermissionService = updateUserPermissionService;
    }
    public CommandResponse Handle(UpdateUserPermissionCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateUserPermissionCommand command)
    {
        _logger.LogInformation(
            "Inside UpdateUserPermissionCommandHandler :: UpdateUserPermissionCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            response = await _updateUserPermissionService.UpdateUserPermissionAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in UpdateUserPermissionCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}