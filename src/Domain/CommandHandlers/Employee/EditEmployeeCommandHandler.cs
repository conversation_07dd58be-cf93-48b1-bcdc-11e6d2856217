using Domain.Commands.Employee;
using Domain.Services.Employee;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Employee
{
    public class EditEmployeeCommandHandler : ICommandHandler<EditEmployeeCommand, CommandResponse>
    {
        private readonly UpdateEmployeeService _updateEmployeeService;
        private readonly ILogger<EditEmployeeCommandHandler> _logger;

        public EditEmployeeCommandHandler(UpdateEmployeeService updateEmployeeService, ILogger<EditEmployeeCommandHandler> logger)
        {
            _updateEmployeeService = updateEmployeeService;
            _logger = logger;
        }

        public CommandResponse Handle(EditEmployeeCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(EditEmployeeCommand command)
        {
            _logger.LogInformation("Inside EditEmployeeCommandHandler :: EditEmployeeCommand :: {command} ", JsonConvert.SerializeObject(command));

            var response = new CommandResponse();
            try
            {
                response = await _updateEmployeeService.EditEmployeeAsync(command);
            }
            catch (Exception e)
            {
                _logger.LogError("Exception in EditEmployeeCommandHandler");
                _logger.LogError("{Message}", e.Message);
                _logger.LogError("{StackTrace}", e.StackTrace);
            }

            return response;
        }
    }
}
