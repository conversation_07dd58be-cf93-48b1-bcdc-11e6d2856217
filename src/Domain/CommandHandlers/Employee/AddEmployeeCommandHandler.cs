using Domain.Commands.Employee;
using Domain.Contracts.Employee;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers;

public class AddEmployeeCommandHandler : ICommandHandler<AddEmployeeCommand, CommandResponse>
{
    private readonly IAddEmployeeService _addEmployeeService;
    private readonly ILogger<AddEmployeeCommandHandler> _logger;

    public AddEmployeeCommandHandler(IAddEmployeeService addEmployeeService, ILogger<AddEmployeeCommandHandler> logger)
    {
        _addEmployeeService = addEmployeeService;
        _logger = logger;
    }
    public CommandResponse Handle(AddEmployeeCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(AddEmployeeCommand command)
    {
        _logger.LogInformation(
            "Inside AddEmployeeCommandHandler :: AddEmployeeCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var response = new CommandResponse();
        try
        {
            response = await _addEmployeeService.SaveEmployee(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in AddEmployeeCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}