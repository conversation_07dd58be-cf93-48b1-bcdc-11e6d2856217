using Domain.Commands.Employee;
using Domain.Services.Employee;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Employee
{
    public class AddStaffingRequestCommandHandler : ICommandHandler<AddStaffingRequestCommand, CommandResponse>
    {
        private readonly ILogger<AddEmployeeDesignationCommandHandler> _logger;
        private readonly EmployeeStaffingRequestService _employeeStaffingRequestService;

        public AddStaffingRequestCommandHandler(
            ILogger<AddEmployeeDesignationCommandHandler> logger,
            EmployeeStaffingRequestService employeeStaffingRequestService)
        {
            _logger = logger;
            _employeeStaffingRequestService = employeeStaffingRequestService;
        }
        public CommandResponse Handle(AddStaffingRequestCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(AddStaffingRequestCommand command)
        {
            _logger.LogInformation($"Handling AddStaffingRequestCommandHandler: {JsonConvert.SerializeObject(command)}");

            var commandResponse = new CommandResponse();
            try
            {
                commandResponse = await _employeeStaffingRequestService.AddStaffingRequest(command);
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception in AddStaffingRequestCommandHandler");
                _logger.LogError("{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
