using Domain.Commands.Employee;
using Domain.Services.Employee;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Employee
{
    public class AddEmployeeDesignationCommandHandler : ICommandHandler<AddEmployeeDesignationCommand, CommandResponse>
    {
        private readonly ILogger<AddEmployeeDesignationCommandHandler> _logger;
        private readonly AddEmployeeDesignationService _addEmployeeDesignationService;

        public AddEmployeeDesignationCommandHandler(ILogger<AddEmployeeDesignationCommandHandler> logger,
            AddEmployeeDesignationService addEmployeeDesignationService)
        {
            _logger = logger;
            _addEmployeeDesignationService = addEmployeeDesignationService;
        }
        public CommandResponse Handle(AddEmployeeDesignationCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(AddEmployeeDesignationCommand command)
        {
            _logger.LogInformation("Handling AddEmployeeDesignationCommand: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _addEmployeeDesignationService.AddEmployeeDesignation(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in AddEmployeeDesignationCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
