using Domain.Commands.Asset;
using Domain.Services.Asset;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Asset;

public class UpdateAssetSubGroupCommandHandler : ICommandHandler<UpdateAssetSubGroupCommand, CommandResponse>
{
    private readonly ILogger<UpdateAssetSubGroupCommandHandler> _logger;
    private readonly UpdateAssetSubGroupService _service;

    public UpdateAssetSubGroupCommandHandler(ILogger<UpdateAssetSubGroupCommandHandler> logger,
        UpdateAssetSubGroupService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateAssetSubGroupCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateAssetSubGroupCommand command)
    {
        _logger.LogInformation("Inside UpdateAssetSubGroupCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        try
        {
            return await _service.UpdateAssetSubGroupAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateAssetSubGroupCommandHandler\n{StackTrace}", e.StackTrace);
            var response = new CommandResponse();
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            return response;
        }
    }
}