using Domain.Commands.Asset;
using Domain.Services.Asset;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Asset;

public class CreateAssetSubGroupCommandHandler : ICommandHandler<CreateAssetSubGroupCommand, CommandResponse>
{
    private readonly ILogger<CreateAssetSubGroupCommandHandler> _logger;
    private readonly CreateAssetSubGroupService _service;

    public CreateAssetSubGroupCommandHandler(ILogger<CreateAssetSubGroupCommandHandler> logger,
        CreateAssetSubGroupService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateAssetSubGroupCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateAssetSubGroupCommand command)
    {
        _logger.LogInformation("Inside CreateAssetSubGroupCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        try
        {
            return await _service.CreateAssetSubGroupAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in CreateAssetSubGroupCommandHandler\n{StackTrace}", e.StackTrace);
            var response = new CommandResponse();
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            return response;
        }
    }
}