using Domain.Commands.Asset;
using Domain.Services.Asset;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Asset;

public class DeleteAssetGroupCommandHandler : ICommandHandler<DeleteAssetGroupCommand, CommandResponse>
{
    private readonly ILogger<DeleteAssetGroupCommandHandler> _logger;
    private readonly DeleteAssetGroupService _service;

    public DeleteAssetGroupCommandHandler(ILogger<DeleteAssetGroupCommandHandler> logger,
        DeleteAssetGroupService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(DeleteAssetGroupCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DeleteAssetGroupCommand command)
    {
        _logger.LogInformation("Inside DeleteAssetGroupCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        try
        {
            return await _service.DeleteAssetGroupAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in DeleteAssetGroupCommandHandler\n{StackTrace}", e.StackTrace);
            var response = new CommandResponse();
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            return response;
        }
    }
}