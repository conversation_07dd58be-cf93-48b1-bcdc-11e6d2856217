using Domain.Commands.Asset;
using Domain.Services.Asset;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Asset;

public class UpdateAssetGroupCommandHandler : ICommandHandler<UpdateAssetGroupCommand, CommandResponse>
{
    private readonly ILogger<UpdateAssetGroupCommandHandler> _logger;
    private readonly UpdateAssetGroupService _service;

    public UpdateAssetGroupCommandHandler(ILogger<UpdateAssetGroupCommandHandler> logger,
        UpdateAssetGroupService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(UpdateAssetGroupCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateAssetGroupCommand command)
    {
        var response = new CommandResponse();
        _logger.LogInformation("Inside UpdateAssetGroupCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        try
        {
            await _service.UpdateAssetGroupAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateAssetGroupCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}