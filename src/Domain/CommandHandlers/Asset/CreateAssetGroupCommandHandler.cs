using Domain.Commands.Asset;
using Domain.Services.Asset;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Asset;

public class CreateAssetGroupCommandHandler : ICommandHandler<CreateAssetGroupCommand, CommandResponse>
{
    private readonly ILogger<CreateAssetGroupCommandHandler> _logger;
    private readonly CreateAssetGroupService _service;

    public CreateAssetGroupCommandHandler(ILogger<CreateAssetGroupCommandHandler> logger,
        CreateAssetGroupService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateAssetGroupCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateAssetGroupCommand command)
    {
        var response = new CommandResponse();
        _logger.LogInformation("Inside CreateAssetGroupCommandHandler :: Command :: {command}", JsonConvert.SerializeObject(command));
        try
        {
            await _service.CreateAssetGroupAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in CreateAssetGroupCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}