using Domain.Commands.Asset;
using Domain.Services.Asset;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Asset;

public class CreateAssetItemCommandHandler : ICommandHandler<CreateAssetItemCommand, CommandResponse>
{
    private readonly ILogger<CreateAssetItemCommandHandler> _logger;
    private readonly CreateAssetItemService _service;

    public CreateAssetItemCommandHandler(ILogger<CreateAssetItemCommandHandler> logger,
        CreateAssetItemService service)
    {
        _logger = logger;
        _service = service;
    }
    public CommandResponse Handle(CreateAssetItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateAssetItemCommand command)
    {
        _logger.LogInformation("Inside CreateAssetItemCommandHandler :: Command :: {Command}", JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            await _service.CreateAssetItemAsync(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in CreateAssetItemCommandHandler\n{StackTrace}", e.StackTrace);
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }
        return response;
    }
}