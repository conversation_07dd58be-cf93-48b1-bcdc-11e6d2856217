using Domain.Commands.Authentication;
using Domain.Contracts.Authentication;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Authentication;

public class ResetPasswordCommandHandler : ICommandHandler<ResetPasswordCommand, CommandResponse>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<ResetPasswordCommandHandler> _logger;

    public ResetPasswordCommandHandler(IAuthenticationService authenticationService,
        ILogger<ResetPasswordCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public CommandResponse Handle(ResetPasswordCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(ResetPasswordCommand command)
    {
        _logger.LogInformation(
            "Inside ResetPasswordCommandHandler :: ResetPasswordCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var response = new CommandResponse();
        try
        {
            response = await _authenticationService.ResetPassword(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ResetPasswordCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}