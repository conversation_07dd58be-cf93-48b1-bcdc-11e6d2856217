using Domain.Commands.Authentication;
using Domain.Contracts.Authentication;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Authentication;

public class SendOtpForForgetPasswordCommandHandler : ICommandHandler<SendOtpForForgetPasswordCommand, CommandResponse>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<SendOtpForForgetPasswordCommandHandler> _logger;

    public SendOtpForForgetPasswordCommandHandler(IAuthenticationService authenticationService,
        ILogger<SendOtpForForgetPasswordCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public CommandResponse Handle(SendOtpForForgetPasswordCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(SendOtpForForgetPasswordCommand command)
    {
        _logger.LogInformation(
            "Inside SendOtpForForgetPasswordCommandHandler :: SendOtpForForgetPasswordCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var response = new CommandResponse();
        try
        {
            response = await _authenticationService.SendOtpForForgetPassword(command.OtpChannel);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in SendOtpForForgetPasswordCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}