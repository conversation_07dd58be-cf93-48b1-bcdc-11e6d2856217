using Domain.Commands.Authentication;
using Domain.Contracts.Authentication;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Authentication;

public class
    MatchOtpForForgetPasswordCommandHandler : ICommandHandler<MatchOtpForForgetPasswordCommand, CommandResponse>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<MatchOtpForForgetPasswordCommandHandler> _logger;

    public MatchOtpForForgetPasswordCommandHandler(IAuthenticationService authenticationService,
        ILogger<MatchOtpForForgetPasswordCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public CommandResponse Handle(MatchOtpForForgetPasswordCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(MatchOtpForForgetPasswordCommand command)
    {
        _logger.LogInformation(
            "Inside MatchOtpForForgetPasswordCommandHandler :: MatchOtpForForgetPasswordCommand :: {Command} ",
            JsonConvert.SerializeObject(command));
        var response = new CommandResponse();
        try
        {
            response = await _authenticationService.MatchOtpForForgetPassword(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in MatchOtpForForgetPasswordCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}