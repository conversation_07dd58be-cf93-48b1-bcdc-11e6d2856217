using Domain.Commands.Authentication;
using Domain.Contracts.Authentication;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Authentication;

public class VerifyEmployeePinForForgetPasswordCommandHandler :
    ICommandHandler<VerifyEmployeePinForForgetPasswordCommand, CommandResponse>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<VerifyEmployeePinForForgetPasswordCommandHandler> _logger;

    public VerifyEmployeePinForForgetPasswordCommandHandler(IAuthenticationService authenticationService,
        ILogger<VerifyEmployeePinForForgetPasswordCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }

    public CommandResponse Handle(VerifyEmployeePinForForgetPasswordCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(VerifyEmployeePinForForgetPasswordCommand command)
    {
        _logger.LogInformation(
            "Inside VerifyEmployeePinForForgetPasswordCommandHandler :: VerifyEmployeePinForForgetPasswordCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _authenticationService.VerifyEmployeePinForForgetPassword(command.EmployeePin);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in VerifyEmployeePinForForgetPasswordCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}