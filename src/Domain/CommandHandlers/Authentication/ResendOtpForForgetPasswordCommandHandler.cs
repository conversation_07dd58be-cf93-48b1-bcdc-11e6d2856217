using Domain.Commands.Authentication;
using Domain.Contracts.Authentication;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Authentication;

public class ResendOtpForForgetPasswordCommandHandler : ICommandHandler<ResendOtpForForgetPasswordCommand, CommandResponse>
{
    private readonly IAuthenticationService _authenticationService;
    private readonly ILogger<ResendOtpForForgetPasswordCommandHandler> _logger;

    public ResendOtpForForgetPasswordCommandHandler(IAuthenticationService authenticationService,
        ILogger<ResendOtpForForgetPasswordCommandHandler> logger)
    {
        _authenticationService = authenticationService;
        _logger = logger;
    }
    public CommandResponse Handle(ResendOtpForForgetPasswordCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(ResendOtpForForgetPasswordCommand command)
    {
        _logger.LogInformation(
            "Inside ResendOtpForForgetPasswordCommandHandler :: ResendOtpForForgetPasswordCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var response = new CommandResponse();
        try
        {
            response = await _authenticationService.SendOtpForForgetPassword(command.OtpChannel);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in ResendOtpForForgetPasswordCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return response;
    }
}