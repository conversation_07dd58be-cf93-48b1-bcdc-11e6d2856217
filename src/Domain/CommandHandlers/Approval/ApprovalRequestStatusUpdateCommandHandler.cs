using Domain.Commands.Approval;
using Domain.Services.Approval;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Approval
{
    public class ApprovalRequestStatusUpdateCommandHandler : ICommandHandler<ApprovalRequestStatusUpdateCommand, CommandResponse>
    {
        private readonly ILogger<ApprovalRequestStatusUpdateCommandHandler> _logger;
        private readonly ApprovalService _approvalService;
        public ApprovalRequestStatusUpdateCommandHandler(ILogger<ApprovalRequestStatusUpdateCommandHandler> logger,
            ApprovalService approvalService)
        {
            _logger = logger;
            _approvalService = approvalService;
        }
        public CommandResponse Handle(ApprovalRequestStatusUpdateCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(ApprovalRequestStatusUpdateCommand command)
        {
            _logger.LogInformation("Handling ApprovalRequestStatusUpdateCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();
            try
            {
                commandResponse = await _approvalService.UpdateApprovalStatus(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ApprovalRequestStatusUpdateCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
