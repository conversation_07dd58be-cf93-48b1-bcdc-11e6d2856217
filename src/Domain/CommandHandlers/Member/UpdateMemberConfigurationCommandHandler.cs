using Domain.Commands.Member;
using Domain.Services.Member;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member
{
    public class UpdateMemberConfigurationCommandHandler : ICommandHandler<UpdateMemberConfigurationCommand, CommandResponse>
    {
        private readonly ILogger<UpdateMemberConfigurationCommandHandler> _logger;
        private readonly UpdateMemberConfigurationService _updateMemberConfigurationService;

        public UpdateMemberConfigurationCommandHandler(
            ILogger<UpdateMemberConfigurationCommandHandler> logger,
            UpdateMemberConfigurationService updateMemberConfigurationService)
        {
            _logger = logger;
            _updateMemberConfigurationService = updateMemberConfigurationService;
        }

        public CommandResponse Handle(UpdateMemberConfigurationCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(UpdateMemberConfigurationCommand command)
        {
            _logger.LogInformation("Handling UpdateMemberConfigurationCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _updateMemberConfigurationService.UpdateMemberConfigurationAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in UpdateMemberConfigurationCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
