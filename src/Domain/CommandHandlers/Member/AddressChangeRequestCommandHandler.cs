using Domain.Commands.Member;
using Domain.Contracts.Member;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member
{
    public class AddressChangeRequestCommandHandler : ICommandHandler<AddressChangeRequestCommand, CommandResponse>
    {
        private readonly ILogger<AddressChangeRequestCommandHandler> _logger;
        private readonly IAddressChangeRequestCommandService _service;

        public AddressChangeRequestCommandHandler(
            ILogger<AddressChangeRequestCommandHandler> logger,
            IAddressChangeRequestCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(AddressChangeRequestCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(AddressChangeRequestCommand command)
        {
            _logger.LogInformation("Handling AddressChangeRequestCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.SendAddressChangeRequestAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in AddressChangeRequestCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
