using Domain.Commands.Member;
using Domain.Contracts.Member;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member;

public class MembershipFeeSetupCommandHandler : ICommandHandler<MembershipFeeSetupCommand, CommandResponse>
{

    private readonly ILogger<MembershipFeeSetupCommandHandler> _logger;
    private readonly ICreateMembershipFeeSetupService _service;

    public MembershipFeeSetupCommandHandler(
        ILogger<MembershipFeeSetupCommandHandler> logger,
        ICreateMembershipFeeSetupService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(MembershipFeeSetupCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(MembershipFeeSetupCommand command)
    {
        _logger.LogInformation("Handling MembershipFeeSetupCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.MembershipFeeSetupAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in MembershipFeeSetupCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}