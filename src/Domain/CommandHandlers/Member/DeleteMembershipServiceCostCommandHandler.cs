using Domain.Commands.Member;
using Domain.Contracts.Member;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member;

public class DeleteMembershipServiceCostCommandHandler : ICommandHandler<DeleteMembershipInventoryItemCommand, CommandResponse>
{

    private readonly ILogger<DeleteMembershipServiceCostCommandHandler> _logger;
    private readonly IDeleteMembershipInventoryItemService _deleteMembershipInventoryItemService;

    public DeleteMembershipServiceCostCommandHandler(
        ILogger<DeleteMembershipServiceCostCommandHandler> logger,
        IDeleteMembershipInventoryItemService deleteMembershipInventoryItemService
    )
    {
        _logger = logger;
        _deleteMembershipInventoryItemService = deleteMembershipInventoryItemService;

    }

    public CommandResponse Handle(DeleteMembershipInventoryItemCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DeleteMembershipInventoryItemCommand command)
    {
        _logger.LogInformation("Handling DeleteMembershipServiceCostCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _deleteMembershipInventoryItemService.DeleteMembershipInventoryItemAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in DeleteMembershipServiceCostCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}