using Domain.Commands.Member;
using Domain.Contracts.Member;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member
{
    public class PhoneNumberChangeRequestCommandHandler : ICommandHandler<PhoneNumberChangeRequestCommand, CommandResponse>
    {
        private readonly ILogger<PhoneNumberChangeRequestCommandHandler> _logger;
        private readonly IPhoneNumberChangeRequestCommandService _service;

        public PhoneNumberChangeRequestCommandHandler(
            ILogger<PhoneNumberChangeRequestCommandHandler> logger,
            IPhoneNumberChangeRequestCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(PhoneNumberChangeRequestCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(PhoneNumberChangeRequestCommand command)
        {
            _logger.LogInformation("Handling PhoneNumberChangeRequestCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.SendPhoneNumberChangeRequestAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in PhoneNumberChangeRequestCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
