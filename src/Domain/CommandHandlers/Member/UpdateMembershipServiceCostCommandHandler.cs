using Domain.Commands.Member;
using Domain.Contracts.Member;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member;

public class UpdateMembershipServiceCostCommandHandler : ICommandHandler<UpdateMembershipServiceCostCommand, CommandResponse>
{
    private readonly ILogger<UpdateMembershipServiceCostCommandHandler> _logger;
    private readonly IUpdateMembershipServiceCostService _updateMembershipServiceCostService;

    public UpdateMembershipServiceCostCommandHandler(
        ILogger<UpdateMembershipServiceCostCommandHandler> logger,
        IUpdateMembershipServiceCostService updateMembershipServiceCostService)
    {
        _logger = logger;
        _updateMembershipServiceCostService = updateMembershipServiceCostService;
    }
    public CommandResponse Handle(UpdateMembershipServiceCostCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateMembershipServiceCostCommand command)
    {
        _logger.LogInformation("Handling UpdateMembershipServiceCostCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _updateMembershipServiceCostService.UpdateMembershipServiceCostAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in UpdateMembershipServiceCostCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}