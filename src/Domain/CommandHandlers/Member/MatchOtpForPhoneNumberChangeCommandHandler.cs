using Domain.Commands.Member;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member
{
    public class MatchOtpForPhoneNumberChangeCommandHandler : ICommandHandler<MatchOtpForPhoneNumberChangeCommand, CommandResponse>
    {
        private readonly ILogger<MatchOtpForPhoneNumberChangeCommandHandler> _logger;
        private readonly IOtpVarificationForPhoneNumberChangeService _service;

        public MatchOtpForPhoneNumberChangeCommandHandler(
            ILogger<MatchOtpForPhoneNumberChangeCommandHandler> logger,
            IOtpVarificationForPhoneNumberChangeService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(MatchOtpForPhoneNumberChangeCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(MatchOtpForPhoneNumberChangeCommand command)
        {
            _logger.LogInformation("Handling MatchOtpForPhoneNumberChangeCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.MatchOtpForPhoneNumberChangeAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in MatchOtpForPhoneNumberChangeCommandHandler\n{StackTrace}", ex.StackTrace);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, ex.Message);
            }

            return commandResponse;
        }
    }
}
