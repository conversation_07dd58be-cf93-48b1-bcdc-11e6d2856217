using Domain.Commands.Member;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Member
{
    public class SendOtpForPhoneNumberChangeCommandHandler : ICommandHandler<SendOtpForPhoneNumberChangeCommand, CommandResponse>
    {
        private readonly ILogger<SendOtpForPhoneNumberChangeCommandHandler> _logger;
        private readonly IOtpVarificationForPhoneNumberChangeService _service;

        public SendOtpForPhoneNumberChangeCommandHandler(
            ILogger<SendOtpForPhoneNumberChangeCommandHandler> logger,
            IOtpVarificationForPhoneNumberChangeService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(SendOtpForPhoneNumberChangeCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(SendOtpForPhoneNumberChangeCommand command)
        {
            _logger.LogInformation("Handling SendOtpForPhoneNumberChangeCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.SendOtpForPhoneNumberChangeAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in SendOtpForPhoneNumberChangeCommandHandler\n{StackTrace}", ex.StackTrace);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, ex.Message);
            }

            return commandResponse;
        }
    }
}
