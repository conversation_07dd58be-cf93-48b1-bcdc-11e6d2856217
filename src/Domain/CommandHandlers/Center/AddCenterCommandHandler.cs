using Domain.Commands.Center;
using Domain.Services.Center;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Center
{
    public class AddCenterCommandHandler : ICommandHandler<AddCenterCommand, CommandResponse>
    {
        private readonly ILogger<AddCenterCommandHandler> _logger;
        private readonly CenterCommandService _centerCommandService;
        public AddCenterCommandHandler(ILogger<AddCenterCommandHandler> logger, CenterCommandService centerCommandService)
        {
            _logger = logger;
            _centerCommandService = centerCommandService;
        }
        public CommandResponse Handle(AddCenterCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(AddCenterCommand command)
        {
            _logger.LogInformation("Handling AddCenterCommand: {SerializedCommand}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _centerCommandService.AddCenter(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in AddCenterCommandHandler");
            }

            return commandResponse;
        }
    }
}
