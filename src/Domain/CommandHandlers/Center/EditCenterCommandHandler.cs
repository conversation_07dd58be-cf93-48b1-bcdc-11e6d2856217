using Domain.Commands.Center;
using Domain.Services.Center;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Center
{
    public class EditCenterCommandHandler : ICommandHandler<EditCenterCommand, CommandResponse>
    {
        private readonly ILogger<EditCenterCommandHandler> _logger;
        private readonly UpdateCenterService _editCenterService;
        public EditCenterCommandHandler(ILogger<EditCenterCommandHandler> logger, UpdateCenterService editCenterService)
        {
            _logger = logger;
            _editCenterService = editCenterService;
        }
        public CommandResponse Handle(EditCenterCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(EditCenterCommand command)
        {
            _logger.LogInformation("Handling EditCenterCommand: {SerializedCommand}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _editCenterService.EditCenter(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in EditCenterCommandHandler");
            }

            return commandResponse;
        }
    }
}
