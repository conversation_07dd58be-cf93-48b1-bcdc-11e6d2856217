using Domain.Commands.Center;
using Domain.Services.Center;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Center
{
    public class DeleteCenterCommandHandler : ICommandHandler<DeleteCenterCommand, CommandResponse>
    {
        private readonly ILogger<DeleteCenterCommandHandler> _logger;
        private readonly UpdateCenterService _updateCenterService;
        public DeleteCenterCommandHandler(
            ILogger<DeleteCenterCommandHandler> logger,
            UpdateCenterService updateCenterService)
        {
            _logger = logger;
            _updateCenterService = updateCenterService;
        }

        public CommandResponse Handle(DeleteCenterCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(DeleteCenterCommand command)
        {
            _logger.LogInformation("Handling DeleteCenterCommand: {SerializedCommand}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _updateCenterService.DeleteCenter(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in DeleteCenterCommandHandler");
            }

            return commandResponse;
        }
    }
}
