using Domain.Commands.Holiday;
using Domain.Services.Holiday;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Holiday;

public class EarlyEndHolidayCommandHandler : ICommandHandler<EarlyEndHolidayCommand, CommandResponse>
{
    private readonly EarlyEndHolidayService _earlyEndHolidayService;
    private readonly ILogger<EarlyEndHolidayCommandHandler> _logger;

    public EarlyEndHolidayCommandHandler(
        ILogger<EarlyEndHolidayCommandHandler> logger,
        EarlyEndHolidayService earlyEndHolidayService)
    {
        _logger = logger;
        _earlyEndHolidayService = earlyEndHolidayService;
    }

    public CommandResponse Handle(EarlyEndHolidayCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(EarlyEndHolidayCommand command)
    {
        _logger.LogInformation(
            "Inside EarlyEndHolidayCommandHandler :: EarlyEndHolidayCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _earlyEndHolidayService.EarlyEndHoliday(command);
        }
        catch (Exception e)
        {

            _logger.LogError(e, "Exception occurred in EarlyEndHolidayCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}