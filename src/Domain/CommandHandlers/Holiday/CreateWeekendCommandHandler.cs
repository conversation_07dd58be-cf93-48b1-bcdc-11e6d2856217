using Domain.Commands.Holiday;
using Domain.Services.Holiday;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Holiday;

public class CreateWeekendCommandHandler : ICommandHandler<CreateWeekendCommand, CommandResponse>
{

    private readonly CreateWeekendService _createWeekendService;
    private readonly ILogger<CreateWeekendCommandHandler> _logger;

    public CreateWeekendCommandHandler(
        ILogger<CreateWeekendCommandHandler> logger,
        CreateWeekendService createWeekendService)
    {
        _logger = logger;
        _createWeekendService = createWeekendService;
    }

    public CommandResponse Handle(CreateWeekendCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateWeekendCommand command)
    {

        _logger.LogInformation(
            "Inside CreateWeekendCommandHandler :: CreateWeekendCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _createWeekendService.CreateWeekend(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in CreateWeekendCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}