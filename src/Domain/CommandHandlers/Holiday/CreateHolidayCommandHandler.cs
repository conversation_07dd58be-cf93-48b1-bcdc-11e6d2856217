using Domain.Commands.Holiday;
using Domain.Services.Holiday;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Holiday
{
    public class CreateHolidayCommandHandler : ICommandHandler<CreateHolidayCommand, CommandResponse>
    {

        private readonly CreateHolidayService _createHolidayService;
        private readonly ILogger<CreateHolidayCommandHandler> _logger;

        public CreateHolidayCommandHandler(
            ILogger<CreateHolidayCommandHandler> logger,
            CreateHolidayService createHolidayService)
        {
            _logger = logger;
            _createHolidayService = createHolidayService;
        }


        public CommandResponse Handle(CreateHolidayCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(CreateHolidayCommand command)
        {
            _logger.LogInformation(
                "Inside CreateHolidayCommandHandler :: CreateHolidayCommand :: {Command} ",
                JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();
            try
            {
                commandResponse = await _createHolidayService.CreateHoliday(command);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Exception occurred in CreateHolidayCommandHandler\n{StackTrace}", e.StackTrace);
            }

            return commandResponse;
        }



    }
}