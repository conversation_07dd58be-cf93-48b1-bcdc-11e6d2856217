using Domain.Commands.Holiday;
using Domain.Services.Holiday;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Holiday;

public class DeleteHolidayCommandHandler : ICommandHandler<DeleteHolidayCommand, CommandResponse>
{

    private readonly DeleteHolidayService _deleteHolidayService;
    private readonly ILogger<DeleteHolidayCommandHandler> _logger;

    public DeleteHolidayCommandHandler(
        ILogger<DeleteHolidayCommandHandler> logger,
        DeleteHolidayService deleteHolidayService)
    {
        _logger = logger;
        _deleteHolidayService = deleteHolidayService;
    }


    public CommandResponse Handle(DeleteHolidayCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(DeleteHolidayCommand command)
    {
        _logger.LogInformation(
            "Inside DeleteHolidayCommandHandler :: DeleteHolidayCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _deleteHolidayService.DeleteHoliday(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in DeleteHolidayCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}