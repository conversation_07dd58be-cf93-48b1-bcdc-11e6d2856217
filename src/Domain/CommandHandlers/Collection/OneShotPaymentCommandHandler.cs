using Domain.Commands.Collection;
using Domain.Contracts.Collection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Collection
{
    public class OneShotPaymentCommandHandler : ICommandHandler<OneShotPaymentCommand, CommandResponse>
    {
        private readonly ILogger<OneShotPaymentCommandHandler> _logger;
        private readonly IOneShotPaymentService _oneShotPaymentService;

        public OneShotPaymentCommandHandler(
            ILogger<OneShotPaymentCommandHandler> logger,
            IOneShotPaymentService oneShotPaymentService)
        {
            _logger = logger;
            _oneShotPaymentService = oneShotPaymentService;
        }
        public CommandResponse Handle(OneShotPaymentCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(OneShotPaymentCommand command)
        {
            _logger.LogInformation("Handling OneShotPaymentCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _oneShotPaymentService.ProcessOneShotPaymentAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in OneShotPaymentCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
