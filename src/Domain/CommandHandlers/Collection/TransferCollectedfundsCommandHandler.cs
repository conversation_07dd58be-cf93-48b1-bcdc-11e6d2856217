using Domain.Commands.Collection;
using Domain.Contracts.Collection;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Collection
{
    public class TransferCollectedfundsCommandHandler : ICommandHandler<TransferCollectedfundsCommand, CommandResponse>
    {
        private readonly ILogger<TransferCollectedfundsCommandHandler> _logger;
        private readonly ITransferCollectedfundsCommandService _service;

        public TransferCollectedfundsCommandHandler(
            ILogger<TransferCollectedfundsCommandHandler> logger,
            ITransferCollectedfundsCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(TransferCollectedfundsCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(TransferCollectedfundsCommand command)
        {
            _logger.LogInformation("Handling TransferCollectedfundsCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.TransferCollectedfundsAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in TransferCollectedfundsCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
