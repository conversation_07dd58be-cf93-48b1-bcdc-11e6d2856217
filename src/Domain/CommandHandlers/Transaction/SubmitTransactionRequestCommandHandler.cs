using Domain.Commands.Transaction;
using Domain.Contracts.Transaction;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Transaction
{
    public class SubmitTransactionRequestCommandHandler : ICommandHandler<SubmitTransactionRequestCommand, CommandResponse>
    {
        private readonly ILogger<SubmitTransactionRequestCommandHandler> _logger;
        private readonly ISubmitTransactionRequestCommandService _service;

        public SubmitTransactionRequestCommandHandler(
            ILogger<SubmitTransactionRequestCommandHandler> logger,
            ISubmitTransactionRequestCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(SubmitTransactionRequestCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(SubmitTransactionRequestCommand command)
        {
            _logger.LogInformation("Handling SubmitTransactionRequestCommandHandler: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.SubmitTransactionRequestAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in SubmitTransactionRequestCommandHandler\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
