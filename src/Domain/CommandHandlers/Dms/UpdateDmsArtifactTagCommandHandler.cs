using Domain.Commands.Dms;
using Domain.Services.Dms;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Dms;

public class UpdateDmsArtifactTagCommandHandler : ICommandHandler<UpdateDmsArtifactTagCommand, CommandResponse>
{

    private readonly UpdateDmsArtifactTagService _updateDmsArtifactTagService;
    private readonly ILogger<UpdateDmsArtifactTagCommandHandler> _logger;

    public UpdateDmsArtifactTagCommandHandler(
        ILogger<UpdateDmsArtifactTagCommandHandler> logger,
        UpdateDmsArtifactTagService updateDmsArtifactTagService)
    {
        _logger = logger;
        _updateDmsArtifactTagService = updateDmsArtifactTagService;
    }


    public CommandResponse Handle(UpdateDmsArtifactTagCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(UpdateDmsArtifactTagCommand command)
    {
        _logger.LogInformation(
            "Inside UpdateDmsArtifactTagCommandHandler :: UpdateDmsArtifactTagCommand :: {Command} ",
            JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();
        try
        {
            commandResponse = await _updateDmsArtifactTagService.UpdateDmsArtifactTag(command);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception occurred in UpdateDmsArtifactTagCommandHandler\n{StackTrace}", e.StackTrace);
        }

        return commandResponse;
    }
}