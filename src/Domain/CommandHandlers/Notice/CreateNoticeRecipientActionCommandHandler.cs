using Domain.Commands.Notice;
using Domain.Services.Notice;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Notice
{
    public class CreateNoticeRecipientActionCommandHandler : ICommandHandler<CreateNoticeRecipientActionCommand, CommandResponse>
    {
        private readonly ILogger<CreateNoticeRecipientActionCommandHandler> _logger;
        private readonly NoticeService _noticeService;
        private readonly IServiceClient _serviceClient;
        public CreateNoticeRecipientActionCommandHandler(ILogger<CreateNoticeRecipientActionCommandHandler> logger, NoticeService noticeService, IServiceClient serviceClient)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _noticeService = noticeService ?? throw new ArgumentNullException(nameof(noticeService));
            _serviceClient = serviceClient ?? throw new ArgumentNullException(nameof(serviceClient));
        }

        public CommandResponse Handle(CreateNoticeRecipientActionCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(CreateNoticeRecipientActionCommand command)
        {
            var response = new CommandResponse();
            try
            {
                await _noticeService.SaveNoticeRicipientAction(command);
            }
            catch (Exception ex)
            {
                response.SetError("CreateNoticeRecipientActionCommand", ex.Message);
                return response;
            }


            response.IsSuccess();
            return response;
        }
    }
}
