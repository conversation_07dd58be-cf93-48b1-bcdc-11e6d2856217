using Domain.Commands.Notice;
using Domain.Services.Notice;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Notice
{
    public class CreateNoticeCommandHandler : ICommandHandler<CreateNoticeCommand, CommandResponse>
    {
        private readonly ILogger<CreateNoticeCommandHandler> _logger;
        private readonly NoticeService _noticeService;
        private readonly IServiceClient _serviceClient;
        public CreateNoticeCommandHandler(ILogger<CreateNoticeCommandHandler> logger, NoticeService noticeService, IServiceClient serviceClient)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _noticeService = noticeService ?? throw new ArgumentNullException(nameof(noticeService));
            _serviceClient = serviceClient ?? throw new ArgumentNullException(nameof(serviceClient));
        }
        public CommandResponse Handle(CreateNoticeCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(CreateNoticeCommand command)
        {
            var response = new CommandResponse();
            var noticeModel = new BuroNotice
            {
                ItemId = command.ItemId,
                Title = command.Title,
                Description = command.Description,
                StartDate = command.StartDate,
                EndDate = command.EndDate,
                AttachmentFileItemIds = command.AttachmentFileItemIds?.Where(x => x != null).ToArray(),
                PrimaryButtonActionList = command.PrimaryButtonActionList,
                PrimaryButtonTitle = command.PrimaryButtonTitle,
                IsDefaultPrimaryButton = command.IsDefaultPrimaryButton,
                SecondaryButtonActionList = command.SecondaryButtonActionList,
                SecondaryButtonTitle = command.SecondaryButtonTitle,
                IsDefaultSecondaryButton = command.IsDefaultSecondaryButton,
                RecipientDesignationItemIds = command.RecipientDesignationItemIds,
                RecipientOfficeItemIds = command.RecipientOfficeItemIds
            };

            await _noticeService.SaveNotice(noticeModel);
            _serviceClient.SendToQueue<bool>(BuroConstants.DefaultQueue, command);

            response.IsSuccess();
            return response;
        }
    }
}
