using Domain.Commands.Mfcib;
using Domain.Contracts.Mfcib;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Mfcib
{
    public class ExportRequestCommandHander : ICommandHandler<ExportRequestCommand, CommandResponse>
    {
        private readonly ILogger<ExportRequestCommandHander> _logger;
        private readonly IExportRequestCommandService _service;

        public ExportRequestCommandHander(
            ILogger<ExportRequestCommandHander> logger,
            IExportRequestCommandService service)
        {
            _logger = logger;
            _service = service;
        }

        public CommandResponse Handle(ExportRequestCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(ExportRequestCommand command)
        {
            _logger.LogInformation("Handling ExportRequestCommandHander: {Command}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _service.CreateRequestAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in ExportRequestCommandHander\n{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
