using Domain.Commands.BankCheque;
using Domain.Contracts.BankCheque;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.BankCheque;

public class CreateChequeBookCommandHandler : ICommandHandler<CreateChequeBookCommand, CommandResponse>
{
    private readonly ICreateChequeBookCommandService _service;
    private readonly ILogger<CreateChequeBookCommandHandler> _logger;

    public CreateChequeBookCommandHandler(
        ILogger<CreateChequeBookCommandHandler> logger,
        ICreateChequeBookCommandService service)
    {
        _logger = logger;
        _service = service;
    }

    public CommandResponse Handle(CreateChequeBookCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateChequeBookCommand command)
    {
        _logger.LogInformation("Handling CreateChequeBookCommandHandler: {Command}", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _service.CreateChequeBookAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateChequeBookCommandHandler\n{StackTrace}", ex.StackTrace);
        }

        return commandResponse;
    }
}