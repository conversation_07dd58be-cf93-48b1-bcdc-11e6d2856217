using Domain.Commands.Office;
using Domain.Services.Office;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.Office
{
    public class UpdateOfficeCommandHandler : ICommandHandler<UpdateOfficeCommand, CommandResponse>
    {
        private readonly ILogger<UpdateOfficeCommandHandler> _logger;
        private readonly UpdateOfficeService _updateOfficeService;
        private readonly IRepository _repository;
        public UpdateOfficeCommandHandler(IRepository repository,
            ILogger<UpdateOfficeCommandHandler> logger,
            UpdateOfficeService updateOfficeService)
        {
            _logger = logger;
            _updateOfficeService = updateOfficeService;
            _repository = repository;
        }
        public CommandResponse Handle(UpdateOfficeCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(UpdateOfficeCommand command)
        {
            _logger.LogInformation($"Handling UpdateOfficeCommandHandler: {JsonConvert.SerializeObject(command)}");

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _updateOfficeService.UpdateOffice(command);
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception in UpdateOfficeCommandHandler");
                _logger.LogError("{StackTrace}", ex.StackTrace);
            }

            return commandResponse;
        }
    }
}
