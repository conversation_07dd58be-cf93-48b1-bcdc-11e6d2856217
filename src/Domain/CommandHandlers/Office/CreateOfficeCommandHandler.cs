using Domain.Commands.Office;
using Domain.Services;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers;

public class CreateOfficeCommandHandler : ICommandHandler<CreateOfficeCommand, CommandResponse>
{
    private readonly CreateOfficeService _createOfficeService;
    private readonly ILogger<CreateOfficeCommandHandler> _logger;

    public CreateOfficeCommandHandler(
        ILogger<CreateOfficeCommandHandler> logger,
        CreateOfficeService createOfficeService)
    {
        _logger = logger;
        _createOfficeService = createOfficeService;
    }

    public CommandResponse Handle(CreateOfficeCommand command)
    {
        throw new NotImplementedException();
    }

    public async Task<CommandResponse> HandleAsync(CreateOfficeCommand command)
    {
        _logger.LogInformation("Inside CreateOfficeCommandHandler :: CreateOfficeCommand :: {command} ", JsonConvert.SerializeObject(command));

        var commandResponse = new CommandResponse();

        try
        {
            commandResponse = await _createOfficeService.CreateOffice(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in CreateOfficeCommandHandler");
        }

        return commandResponse;
    }
}