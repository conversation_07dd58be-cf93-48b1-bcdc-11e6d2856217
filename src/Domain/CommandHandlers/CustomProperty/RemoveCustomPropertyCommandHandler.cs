using Domain.Commands.CustomProperty;
using Domain.Services.CustomProperty;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.CustomProperty
{
    public class RemoveCustomPropertyCommandHandler : ICommandHandler<RemoveCustomPropertyCommand, CommandResponse>
    {
        private readonly ILogger<RemoveCustomPropertyCommandHandler> _logger;
        private readonly RemoveCustomPropertyService _removeCustomPropertyService;

        public RemoveCustomPropertyCommandHandler(
            ILogger<RemoveCustomPropertyCommandHandler> logger,
            RemoveCustomPropertyService removeCustomPropertyService)
        {
            _logger = logger;
            _removeCustomPropertyService = removeCustomPropertyService;
        }

        public CommandResponse Handle(RemoveCustomPropertyCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(RemoveCustomPropertyCommand command)
        {
            _logger.LogInformation("Handling RemoveCustomPropertyCommand: {SerializedCommand}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _removeCustomPropertyService.RemoveCustomPropertyAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in RemoveCustomPropertyCommandHandler");
            }

            return commandResponse;
        }
    }
}
