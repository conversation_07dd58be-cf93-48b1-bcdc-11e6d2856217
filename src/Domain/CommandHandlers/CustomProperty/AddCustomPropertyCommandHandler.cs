using Domain.Commands.CustomProperty;
using Domain.Services.CustomProperty;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.CustomProperty
{
    public class AddCustomPropertyCommandHandler : ICommandHandler<AddCustomPropertyCommand, CommandResponse>
    {
        private readonly ILogger<AddCustomPropertyCommandHandler> _logger;
        private readonly AddCustomPropertyService _addCustomPropertyService;

        public AddCustomPropertyCommandHandler(
            ILogger<AddCustomPropertyCommandHandler> logger,
            AddCustomPropertyService addCustomPropertyService)
        {
            _logger = logger;
            _addCustomPropertyService = addCustomPropertyService;
        }

        public CommandResponse Handle(AddCustomPropertyCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(AddCustomPropertyCommand command)
        {
            _logger.LogInformation("Handling AddCustomPropertyCommand: {SerializedCommand}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _addCustomPropertyService.AddCustomPropertyAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in AddCustomPropertyCommandHandler");
            }

            return commandResponse;
        }
    }
}
