using Domain.Commands.CustomProperty;
using Domain.Services.CustomProperty;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.CommandHandlers.CustomProperty
{
    public class EditCustomPropertyCommandHandler : ICommandHandler<EditCustomPropertyCommand, CommandResponse>
    {
        private readonly ILogger<EditCustomPropertyCommandHandler> _logger;
        private readonly EditCustomPropertyService _editCustomPropertyService;

        public EditCustomPropertyCommandHandler(
            ILogger<EditCustomPropertyCommandHandler> logger,
            EditCustomPropertyService editCustomPropertyService)
        {
            _logger = logger;
            _editCustomPropertyService = editCustomPropertyService;
        }

        public CommandResponse Handle(EditCustomPropertyCommand command)
        {
            throw new NotImplementedException();
        }

        public async Task<CommandResponse> HandleAsync(EditCustomPropertyCommand command)
        {
            _logger.LogInformation("Handling EditCustomPropertyCommand: {SerializedCommand}", JsonConvert.SerializeObject(command));

            var commandResponse = new CommandResponse();

            try
            {
                commandResponse = await _editCustomPropertyService.EditCustomPropertyAsync(command);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception in EditCustomPropertyCommandHandler");
            }

            return commandResponse;
        }
    }
}
