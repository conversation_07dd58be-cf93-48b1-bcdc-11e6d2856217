using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Voucher.Configuration;

public class CreateVoucherConfigCommand : BaseCommand
{
    public string ItemId { get; set; } = default!;
    public string ProductLineItemId { get; set; } = default!;
    public string TransactionTypeItemId { get; set; } = default!;
    public EnumVoucherScopeCategory VoucherScopeCategory { get; set; }
    public List<LedgerEntry> LedgerEntries { get; set; } = new List<LedgerEntry>();

}

public class LedgerEntry
{
    public string ChartOfAccountItemId { get; set; } = default!;
    public EnumBuroVoucherType VoucherType { get; set; }
}