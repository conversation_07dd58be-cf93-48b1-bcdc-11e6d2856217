using SeliseBlocks.Entities.PrimaryEntities.BURO.Assets;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Asset;

public class CreateAssetSubGroupCommand : BaseCommand
{
    public string GroupItemId { get; set; }

    public string SubGroupName { get; set; }

    public string SubGroupCode { get; set; }

    public string SubGroupDescription { get; set; }

    public Decimal DepreciationRate { get; set; }

    public EnumAssetPartialSale PartialSale { get; set; }

    public List<AssetSubGroupAdditionalProperty> AdditionalProperties { get; set; } = new List<AssetSubGroupAdditionalProperty>();
}