using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Vendor;

public class UpdateVendorCommand : BaseCommand
{
    public string ItemId { get; set; }
    public EnumBuroVendorType VendorType { get; set; }
    public string GroupItemId { get; set; }
    public string GroupName { get; set; }
    public string SubGroupItemId { get; set; }
    public string SubGroupName { get; set; }
    public string CategoryItemId { get; set; }
    public string CategoryName { get; set; }
    public string SubCategoryItemId { get; set; }
    public string SubCategoryName { get; set; }
    public string VendorLegalName { get; set; }
    public string OfficePhoneNo { get; set; }
    public string Email { get; set; }
    public DateTime? BusinessStartDate { get; set; }
    public string TradeLicenseNo { get; set; }
    public string EtinNumber { get; set; }
    public string VatRegistrationNumber { get; set; }
    public string Remarks { get; set; }
    public string HouseRoad { get; set; }
    public string Ward { get; set; }
    public string WardItemId { get; set; }
    public string PostOffice { get; set; }
    public string PostOfficeItemId { get; set; }
    public string PostCode { get; set; }
    public string Union { get; set; }
    public string UnionItemId { get; set; }
    public string Upazila { get; set; }
    public string UpazilaItemId { get; set; }
    public string District { get; set; }
    public string DistrictItemId { get; set; }
    public string Country { get; set; }
    public List<VendorContactPerson> VendorContactPersons { get; set; }
    public EnumBuroCorporateStatus CorporateStatus { get; set; }
    public EnumBuroVendorStatus VendorStatus { get; set; }
    public List<EnumTransactionMedium> TransactionMediums { get; set; }
}