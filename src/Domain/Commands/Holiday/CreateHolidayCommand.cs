using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Holiday;

public class CreateHolidayCommand : BaseCommand
{
    public string? GlobalHolidayName { get; set; }

    public DateTime? HolidayStartDate { get; set; }

    public DateTime? HolidayEndDate { get; set; }

    public EnumHolidayType HolidayType { get; set; }
    public string? Reason { get; set; }

    // Office Specific 
    public string[]? OfficeItemIds { get; set; }

    // Person Specific
    public string? MemberItemId { get; set; }

    // Center Specific 
    public string[]? CenterItemIds { get; set; }



}