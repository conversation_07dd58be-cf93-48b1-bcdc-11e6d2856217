using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Center
{
    public class BaseCenterCommand : BaseCommand
    {
        public string Name { get; set; }
        public EnumCollectionGroupType Type { get; set; }
        public string Address { get; set; }
        public DayOfWeek CenterDay { get; set; }
        public string CenterTime { get; set; }
        public LocationCoordinates GPSLocation { get; set; }
        public string DivisionId { get; set; }
        public string DivisionName { get; set; }
        public string DistrictId { get; set; }
        public string DistrictName { get; set; }
        public string UpazilaId { get; set; }
        public string UpazilaName { get; set; }
        public string UnionId { get; set; }
        public string UnionName { get; set; }
        public string PostOfficeId { get; set; }
        public string PostOfficeName { get; set; }
        public string PostCode { get; set; }
        public string WardId { get; set; }
        public string WardName { get; set; }
        public string ProgramOrganizerId { get; set; }
    }
}
