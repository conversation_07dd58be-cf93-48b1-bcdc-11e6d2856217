using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.ConsentForm;

public class CreateConsentFormCommand : BaseCommand
{
    public string ConsentFormItemId { get; set; } = default!;
    public string MemberItemId { get; set; } = default!;
    public string ConsentFormDocumentItemId { get; set; } = default!;
    public string FundAccountItemId { get; set; } = default!;
    public EnumProductType FundAccountProductType{ get; set; }
    public string ReceiveAccountItemId { get; set; } = default!;
    public EnumProductType ReceiveAccountProductType{ get; set; }
}