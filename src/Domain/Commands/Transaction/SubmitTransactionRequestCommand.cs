using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Transaction
{
    public class SubmitTransactionRequestCommand : BaseCommand
    {
        public string TransactionItemId { get; set; } = Guid.NewGuid().ToString();
        public string MemberItemId { get; set; } = default!;
        public double Amount { get; set; }
        public EnumTransactionMedium TransactionMedium { get; set; }
        public EnumProductType ProductType { get; set; }
        public EnumBuroTransactionType TransactionType { get; set; }
        public string AccountItemId { get; set; } = default!;
        public string MemberScheduleItemId { get; set; } = default!;
        public List<string> AdditionalDocumentIds { get; set; } = new List<string>();
    }
}
