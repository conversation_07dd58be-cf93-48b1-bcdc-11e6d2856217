using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.AllowedSector
{
    public class CreateAllowedSectorCommand : BaseCommand
    {
        public string ParentItemId { get; set; }
        public string Name { get; set; }
        public AllowedSectorType CategoryType { get; set; }
        public string BuroSectorCode { get; set; }
        public string MFCIBCode { get; set; }
        public bool IsActive { get; set; }
    }
}
