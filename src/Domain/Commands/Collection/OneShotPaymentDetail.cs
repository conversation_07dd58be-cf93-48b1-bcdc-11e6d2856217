using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Collection
{
    public class OneShotPaymentDetail
    {
        public BuroProductAccountBase Account { get; set; }
        public BuroMemberSchedule MemberSchedule { get; set; }
        public EnumProductType AccountType { get; set; }
        public double RealizableAmount { get; set; }
        public double RealizedAmount { get; set; }
        public double? LateFeeAmount { get; set; }
        public double? DueAmount { get; set; }
    }
}
