using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace Domain.Commands.Inventory;

public class BaseInventoryItemOutCommand : BaseCommand
{
    public string InventoryItemId { get; set; }
    public EnumInventoryItemDefinition ItemDefinition { get; set; }
    public string VendorItemId { get; set; }
    public string VendorName { get; set; }
    public string Reason { get; set; }
    public List<InventoryItemDetail> InventoryItemDetails { get; set; }
}

