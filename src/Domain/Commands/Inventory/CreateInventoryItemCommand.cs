using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Inventory;

public class CreateInventoryItemCommand : BaseCommand
{
    public EnumInventoryItemDefinition ItemDefinition { get; set; }
    public string InventoryItemName { get; set; }
    public string CategoryItemId { get; set; }
    public string CategoryName { get; set; }
    public string SubCategoryItemId { get; set; }
    public string SubCategoryName { get; set; }
    public string VendorItemId { get; set; }
    public string VendorName { get; set; }
    public string ChartOfAccountItemId { get; set; }
    public string ChartOfAccount { get; set; }
    public EnumInventoryPriceType PriceType { get; set; }
    public double? MinPrice { get; set; }
    public double? MaxPrice { get; set; }
    public double? FixedPrice { get; set; }
    public bool HasSerialNumber { get; set; }
    public bool RequiresExpiryDate { get; set; }
}