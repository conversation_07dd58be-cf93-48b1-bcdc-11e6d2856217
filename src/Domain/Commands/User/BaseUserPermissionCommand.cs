namespace Domain.Commands.User;

public class BaseUserPermissionCommand : BaseCommand
{
    public string UserItemId { get; set; }
    public string FeatureRoleItemId { get; set; }
    public List<FeatureCommand> Features { get; set; }

}

public class FeatureCommand
{
    public string FeatureItemId { get; set; }
    public string FeatureName { get; set; }
    public string FeaturePath { get; set; }
    public string ApiPath { get; set; }
    public string IconName { get; set; }
}