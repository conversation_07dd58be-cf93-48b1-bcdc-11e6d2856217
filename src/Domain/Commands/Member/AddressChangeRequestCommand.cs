using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Domain.Commands.Member
{
    public class AddressChangeRequestCommand : BaseCommand
    {
        public string ChangeRequestItemId { get; set; } = Guid.NewGuid().ToString();
        public string MemberItemId { get; set; } = default!;
        public BuroMemberAddress Address { get; set; } = default!;
        public bool IsPresentAddress { get; set; } = true;
        public List<string> AdditionalDocumentIds { get; set; } = new List<string>();
    }
}
