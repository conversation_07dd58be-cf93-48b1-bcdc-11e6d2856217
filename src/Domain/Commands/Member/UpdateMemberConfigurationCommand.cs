using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Member
{
    public class UpdateMemberConfigurationCommand : BaseCommand
    {
        public string ItemId { get; set; } = default!;
        public bool ShouldHaveFeeForAccountClosing { get; set; }
        public int FeeForAccountClosing { get; set; }
        public bool ShouldHaveDormantAccountConfiguration { get; set; }
        public int DormantAccountTimeLineLimit { get; set; }
        public EnumTimeLineUnit DormantAccountTimeLineLimitUnit { get; set; }
        public bool ShouldGetFuneralContribution { get; set; }
        public double FuneralContributionAmount { get; set; }
        public bool ShouldImposeForcedPasswordChange { get; set; }
        public int PasswordExpirationLimit { get; set; }
        public EnumTimeLineUnit PasswordExpirationLimitUnit { get; set; }
        public int SkipPasswordExpirationLimit { get; set; }
        public EnumTimeLineUnit SkipPasswordExpirationLimitUnit { get; set; }
        public int MemberIdStartingNumber { get; set; }
        public double MinimumIncomeForSamityCriteria { get; set; }
        public double MaximumIncomeForSamityCriteria { get; set; }
        public bool ShouldTransferSMSCost { get; set; }
        public double UnitCostOfSMS { get; set; }
        public List<MemberType> MemberTypes { get; set; } = new List<MemberType>();
    }
}
