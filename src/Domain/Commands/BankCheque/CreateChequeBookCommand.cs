namespace Domain.Commands.BankCheque;

public class CreateChequeBookCommand : BaseCommand
{
    public string OfficeItemId { get; set; } = default!;
    public string BankBranchItemId { get; set; } = default!;
    public string AccountNumber { get; set; } = default!;
    public string AccountName { get; set; } = default!;
    public string LeafPrefix { get; set; } = default!;
    public int TotalLeaves { get; set; }
    public int LeavesThreshold { get; set; }
    public long StartingLeavesSerial { get; set; }
    public DateTime? InitializedDate { get; set; }

}