using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.CustomProperty
{
    public class BaseCustomPropertyCommand : BaseCommand
    {
        public EnumCustomPropertyValueType CustomPropertyValueType { get; set; }
        public string DefaultValue { get; set; } = default!;
        public double MinimumValue { get; set; } = default!;
        public double MaximumValue { get; set; } = default!;
    }
}
