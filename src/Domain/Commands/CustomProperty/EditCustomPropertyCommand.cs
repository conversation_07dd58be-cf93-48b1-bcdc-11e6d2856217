using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.CustomProperty
{
    public class EditCustomPropertyCommand : BaseCustomPropertyCommand
    {
        public string ItemId { get; set; } = default!;
        public string PropertyName { get; set; } = default!;
        public EnumCustomPropertyCategory CustomPropertyCategory { get; set; }
        public bool IsMandatory { get; set; }
        public bool IsEditable { get; set; }
    }
}
