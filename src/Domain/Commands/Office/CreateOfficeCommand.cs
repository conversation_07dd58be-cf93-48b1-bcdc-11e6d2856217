using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Office;

public class CreateOfficeCommand : BaseCommand
{
    public string? ItemId { get; set; } = Guid.NewGuid().ToString();
    public string? SurveyItemId { get; set; }
    public string[] AuthorizationLetterFileIds { get; set; } = Array.Empty<string>();
    public string? OfficeName { get; set; }
    public EnumOfficeType OfficeType { get; set; }
    public string? ParentOfficeItemId { get; set; }
    public string? ParentOfficeName { get; set; }
    public DateTime OpeningDate { get; set; }
    public EnumLocaitionType LocationType { get; set; }
    public string? DivisionId { get; set; }
    public string? DivisionName { get; set; }
    public string? DistrictId { get; set; }
    public string? DistrictName { get; set; }
    public string? UpazilaId { get; set; }
    public string? UpazilaName { get; set; }
    public string? UnionId { get; set; }
    public string? UnionName { get; set; }
    public string? PostOfficeId { get; set; }
    public string? PostOfficeCode { get; set; }
    public string? PostOfficeName { get; set; }
    public string? WardId { get; set; }
    public string? WardName { get; set; }
    public string? Address { get; set; }
    public double Latitude { get; set; }
    public double Longitude { get; set; }
    public CustomPropertiesCommand[] CustomProperties { get; set; } = Array.Empty<CustomPropertiesCommand>();

}