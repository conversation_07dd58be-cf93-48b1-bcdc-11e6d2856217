namespace Domain.Commands.Office
{
    public class UpdateOfficeCommand : BaseCommand
    {
        public string OfficeItemId { get; set; }
        public string Address { get; set; }
        public string DistrictId { get; set; }
        public string DistrictName { get; set; }
        public string UpazilaId { get; set; }
        public string UpazilaName { get; set; }
        public string UnionId { get; set; }
        public string UnionName { get; set; }
        public string PostOfficeId { get; set; }
        public string PostOfficeName { get; set; }
        public string PostOfficeCode { get; set; }
        public string WardId { get; set; }
        public string WardName { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public CustomPropertiesCommand[] CustomProperties { get; set; } = Array.Empty<CustomPropertiesCommand>();
    }
}
