using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Domain.Commands.Bank;

public class CreateBankBranchCommand : BaseCommand
{
    public string BankItemId { get; set; } = default!;
    public string BranchName { get; set; } = default!;
    public string RoutingNumber { get; set; } = default!;
    public BuroBankBranchAddress Address { get; set; } = default!;
    public List<string> PhoneNumbers { get; set; } = default!;

}