using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Employee
{
    public class AddStaffingRequestCommand : BaseCommand
    {
        public string EmployeeItemId { get; set; }
        public string MovingToOfficeItemId { get; set; }
        public EnumEmployeeTransferType TransferType { get; set; }
        public DateTime TransferStartDate { get; set; }
        public DateTime? TransferEndDate { get; set; }
        public string? TransferReason { get; set; }
    }
}
