namespace Domain.Commands.Employee;

public class AddEmployeeCommand : BaseCommand
{
    public string EmployeeName { get; set; }
    public string FatherName { get; set; }
    public string MotherName { get; set; }
    public string NidNo { get; set; }
    public string WorkPhone { get; set; }
    public string PersonalPhone { get; set; }
    public string DesignationId { get; set; }
    public string DistrictId { get; set; }
    public string DistrictName { get; set; }
    public string DistrictLangKey { get; set; }
    public string Address { get; set; }
    public string PostCode { get; set; }
    public string PostOfficeId { get; set; }
    public string PostOfficeName { get; set; }
    public string PostOfficeLangKey { get; set; }
    public string UnionId { get; set; }
    public string UnionName { get; set; }
    public string UnionLangKey { get; set; }
    public string UpazilaId { get; set; }
    public string UpazilaName { get; set; }
    public string UpazilaLangKey { get; set; }
    public string WardId { get; set; }
    public string WardName { get; set; }
    public string WardLang<PERSON>ey { get; set; }
    public CustomPropertiesCommand[] CustomProperties { get; set; } = Array.Empty<CustomPropertiesCommand>();
}