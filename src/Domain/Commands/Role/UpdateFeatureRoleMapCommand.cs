namespace Domain.Commands.Role
{
    public class UpdateFeatureRoleMapCommand : BaseFeatureRoleMapCommand
    {
        public string FeatureRoleMapItemId { get; set; } = default!;
    }

    public class UpdateFeatureRoleMapWithStatusCommand : UpdateFeatureRoleMapCommand
    {
        public new List<FeatureRequestWithStatus> FeatureRequests { get; set; } = new List<FeatureRequestWithStatus>();

    }
}
