using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Role
{
    public class BaseFeatureRoleMapCommand : BaseCommand
    {
        public string RoleItemId { get; set; } = default!;
        public string RoleName { get; set; } = default!;
        public string RoleKey { get; set; } = default!;
        public List<FeatureRequest> FeatureRequests { get; set; } = new List<FeatureRequest>();
    }

    public class FeatureRequest
    {
        public string FeatureItemId { get; set; } = default!;
        public string FeatureName { get; set; } = default!;
        public string FeaturePath { get; set; } = default!;
        public string ApiPath { get; set; } = default!;
        public string IconName { get; set; } = default!;
    }

    public class FeatureRequestWithStatus : FeatureRequest
    {
        public EnumFeatureStatus FeatureStatus { get; set; } = EnumFeatureStatus.None;
    }
}
