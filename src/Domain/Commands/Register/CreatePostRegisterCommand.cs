using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Register;

public class CreatePostRegisterCommand : BaseCommand
{
    public string OfficeItemId { get; set; } = default!;
    public  EnumOfficeType OfficeType { get; set; }
    public string PostText { get; set; } = default!;
    public List<string> TaggedEmployeeItemIds { get; set; } = default!;
    public string PostMediaFileItemId { get; set; } = default!;
}