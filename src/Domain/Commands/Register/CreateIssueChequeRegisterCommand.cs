using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Register;

public class CreateIssueChequeRegisterCommand : BaseCommand
{
    public string ItemId { get; set; }
    public string ChequeBookItemId { get; set; }
    public string ChequeLeafItemId { get; set; }
    public string PayeeName { get; set; }
    public double? Amount { get; set; }
    public string TransactionTypeItemId { get; set; }
    public DateTime? IssueDate { get; set; }
    public EnumBankInstrumentType InstrumentType { get; set; }
    public EnumBuroChequeIssueType ChequeIssueType { get; set; }
    public string OfficeItemId { get; set; }
    public string ChequeFileStorageItemId { get; set; }
}