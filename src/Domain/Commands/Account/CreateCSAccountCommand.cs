using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;

namespace Domain.Commands.Account
{
    public class CreateCSAccountCommand : BaseCommand
    {
        public string ApplicationItemId { get; set; } = Guid.NewGuid().ToString();
        public string MemberItemId { get; set; } = default!;
        public double InstalmentAmount { get; set; }
        public string ProductLineItemId { get; set; } = default!;
        public Tenure TenureDetails { get; set; } = default!;
        public EnumPaymentInterval PaymentInterval { get; set; }
        public string MemberSignatureId { get; set; } = default!;
        public EnumProductApplicationStatus Status { get; set; }
        public List<Nominee> Nominees { get; set; } = new List<Nominee>();
    }
}
