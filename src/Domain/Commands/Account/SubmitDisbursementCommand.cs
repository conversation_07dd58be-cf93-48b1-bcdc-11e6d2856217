using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Account
{
    public class SubmitDisbursementCommand : BaseCommand
    {
        public string LoanApplicationDisbursementItemId { get; set; } = Guid.NewGuid().ToString();
        public string LoanApplicationItemId { get; set; } = default!;
        public EnumLoanApplicationDisbursementStatus Status { get; set; }
        public DateTime? FirstInstallmentDate { get; set; } = default!;
        public LoanDisbursementChequeInformation ChequeInformation { get; set; } = default!;
        public string LoanAgreementFormFileItemId { get; set; } = default!;
        public string SecurityChequeFileItemId { get; set; } = default!;
        public LoanDisbursementAcknowledgementDetails DisbursementAcknowledgementDetails { get; set; } = default!;
        public double FinalMicroInsuranceFee { get; set; }
        public string Remarks { get; set; } = default!;
    }
}
