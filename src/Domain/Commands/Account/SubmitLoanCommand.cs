using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Commands.Account
{
    public class SubmitLoanCommand : BaseCommand
    {
        public string LoanApplicationItemId { get; set; } = default!;
        public EnumProductApplicationStatus Status { get; set; }
        public string Remarks { get; set; } = default!;
        public double? FinalLoanAmount { get; set; } = default;
        public double? FinalMicroInsuranceFee { get; set; } = default;
    }
}
