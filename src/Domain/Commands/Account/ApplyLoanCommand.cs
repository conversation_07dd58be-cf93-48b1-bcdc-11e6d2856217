using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;

namespace Domain.Commands.Account
{
    public class ApplyLoanCommand : BaseCommand
    {
        public string LoanApplicationItemId { get; set; } = Guid.NewGuid().ToString();
        public string ProductLineItemId { get; set; } = default!;
        public string LoanSectorItemId { get; set; } = default!;
        public LoanLineTenure TenureDetails { get; set; } = default!;
        public EnumPaymentInterval PaymentInterval { get; set; }
        public EnumProductApplicationStatus Status { get; set; }
        public string MemberItemId { get; set; } = default!;
        public double LoanAmount { get; set; }
        public DateTime? LoanRequiredByDate { get; set; } = default!;
        public string FatherOrSpouseName { get; set; } = default!;
        public string MotherName { get; set; } = default!;
        public BuroInvestment Investment { get; set; } = default!;
        public BusinessInformation BusinessInformation { get; set; } = default!;
        public NetIncome SelfNetIncome { get; set; } = default!;
        public NetIncome FamilyNetIncome { get; set; } = default!;
        public List<Guarantor> Guarantors { get; set; } = new List<Guarantor>();
        public List<string> AdditionalDocumentIds { get; set; } = new List<string>();
        public string Reason { get; set; } = default!;
        public double InitialMicroInsuranceFee { get; set; }
    }
}
