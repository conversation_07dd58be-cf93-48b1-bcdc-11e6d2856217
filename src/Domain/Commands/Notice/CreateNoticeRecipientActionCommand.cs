using System.ComponentModel;

namespace Domain.Commands.Notice
{
    public class CreateNoticeRecipientActionCommand : BaseCommand
    {
        public string NoticeItemId { get; set; }
        public EnumButtonType ClickedButtonType { get; set; }
    }

    public enum EnumButtonType
    {
        [Description("PRIMARY")]
        PRIMARY,
        [Description("SECONDARY")]
        SECONDARY,
        [Description("REMINDLATER")]
        REMINDLATER
    }
}
