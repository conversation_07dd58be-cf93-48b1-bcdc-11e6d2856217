using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Domain.Commands.Notice
{
    public class CreateNoticeCommand : BaseCommand
    {
        public string? ItemId { get; set; } = Guid.NewGuid().ToString();
        public string Title { get; set; }
        public string Description { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string[] AttachmentFileItemIds { get; set; }
        public bool IsDefaultPrimaryButton { get; set; }
        public string PrimaryButtonTitle { get; set; }
        public List<BuroNoticeButtonAction> PrimaryButtonActionList { get; set; }
        public bool IsDefaultSecondaryButton { get; set; }
        public string SecondaryButtonTitle { get; set; }
        public List<BuroNoticeButtonAction> SecondaryButtonActionList { get; set; }
        public string[] RecipientOfficeItemIds { get; set; }
        public string[] RecipientDesignationItemIds { get; set; }

    }
}
