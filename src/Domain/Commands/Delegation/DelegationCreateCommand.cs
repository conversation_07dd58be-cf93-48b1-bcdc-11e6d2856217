using Domain.Commands.User;

namespace Domain.Commands.Delegation;

public class DelegationCreateCommand : BaseCommand
{
    public string SourceEmployeeItemId { get; set; }
    public string DelegateeEmployeeItemId { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public bool IsLifeTimeDelegation { get; set; }
    public List<FeatureCommand> FeatureList { get; set; }
}