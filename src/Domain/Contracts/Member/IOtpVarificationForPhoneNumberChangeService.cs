using Domain.Commands.Member;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Contracts.Member
{
    public interface IOtpVarificationForPhoneNumberChangeService
    {
        Task<CommandResponse> SendOtpForPhoneNumberChangeAsync(SendOtpForPhoneNumberChangeCommand command);
        Task<CommandResponse> MatchOtpForPhoneNumberChangeAsync(MatchOtpForPhoneNumberChangeCommand command);
    }
}
