using Infrastructure.Models;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Domain.Contracts.Member
{
    public interface ISharedService
    {
        Task<BuroEmployee> GetEmployeeByDesignationForAnOfficeAsync(string officeItemId, string designationCode);
        Task NotifyUserAsync(string responseKey, object notificationPayload, string entityItemId, string userId, bool skipToSaveOfflineNotification = false);
        Task<string> GetSequenceNumberAsync(string context, string prefix = "");
        string GenerateAbbreviation(string camelCasedStr);
        Task<EmployeeIdentityDto> GetEmployeeInfoAsync(string userId);
    }
}
