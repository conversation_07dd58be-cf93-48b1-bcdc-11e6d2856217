using Infrastructure.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace Domain.Contracts.Inventory;

public interface IInventoryStockManagementService
{
    Task<BuroInventoryStock> GetInventoryStock(string inventoryItemId, string officeItemId);

    Task<List<BuroInventoryStockBreakdown>> GetInventoryStockBreakdownList(List<string> inventoryStockBreakdownItemIds);

    Task UpdateStocksInventoryStockBreakdown(EnumInventoryMovementType movementType,
        BuroInventoryStockBreakdown stockBreakdown,
        int quantity,
        string createdByUserId,
        EnumStockUpdateType updateType);

    Task ManageInventoryStock(BuroInventoryMovement inventoryMovement);

    Task CreateOrUpdateInventoryStockBreakdownAsync(BuroInventoryMovement inventoryMovement,
        EnumStockUpdateType updateType);

    Task CreateOrUpdateInventoryStockBreakdownAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroInventoryMovement inventoryMovement,
        EnumStockUpdateType updateType);

    Task CreateOrUpdateInventoryStock(BuroInventoryMovement inventoryMovement, EnumStockUpdateType updateType);

    Task UpdateInventoryStock(EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice,
        BuroInventoryStock currentInventoryStock,
        string createdByUserId,
        EnumStockUpdateType updateType);

}