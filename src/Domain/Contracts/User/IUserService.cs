using Selise.Ecap.Entities.PrimaryEntities.PlatformDataService;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Contracts.User;

public interface IUserService
{
    Task<CommandResponse> CreateUserFromEmployee(BuroEmployee employee, string userId);
    Task<CommandResponse> CreateUserFromPerson(string userId, Person person, BuroEmployee employee);
}