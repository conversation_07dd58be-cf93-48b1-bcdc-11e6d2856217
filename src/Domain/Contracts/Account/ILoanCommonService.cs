using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;

namespace Domain.Contracts.Account
{
    public interface ILoanCommonService
    {
        Task<EmployeeInfo> GetEmployeeInfoByDesignationForAnOfficeAsync(string officeItemId, string designationCode);
        Task<string> GetLoanApplicationSequenceNumberAsync();
        EmployeeInfo MakeEmployeeInfo(BuroEmployee employee);
        int CalculateTotalNumberOfInstallment(EnumPaymentInterval paymentInterval, Period? duration);
        Task<BuroLoanApplication?> GetLoanApplicationByItemId(string loanApplicationItemId);
        Task<BuroCenter> GetCenterByItemIdAsync(string centerItemId);
        Task<BuroMember> GetMemberByItemIdAsync(string memberItemId);
        Task<LoanProductLine> GetProductLineByItemIdAsync(string productLineItemId);
    }
}
