using Domain.Commands.Authentication;
using Infrastructure.Constants;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Contracts.Authentication;

public interface IAuthenticationService
{
    Task<CommandResponse> VerifyEmployeePinForForgetPassword(string employeePin);
    Task<CommandResponse> SendOtpForForgetPassword(OtpChannel otpChannel);
    Task<CommandResponse> MatchOtpForForgetPassword(MatchOtpForForgetPasswordCommand command);
    Task<CommandResponse> ResetPassword(ResetPasswordCommand command);
}