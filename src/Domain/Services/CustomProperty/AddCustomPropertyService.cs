using Domain.Commands.CustomProperty;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.CustomProperty
{
    public class AddCustomPropertyService
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<AddCustomPropertyService> _logger;
        private readonly IRepository _repository;

        public AddCustomPropertyService(
            ISecurityContextProvider securityContextProvider,
            ILogger<AddCustomPropertyService> logger,
            IRepository repository)
        {
            _securityContextProvider = securityContextProvider;
            _logger = logger;
            _repository = repository;
        }

        public async Task<CommandResponse> AddCustomPropertyAsync(AddCustomPropertyCommand command)
        {
            _logger.LogInformation("Inside AddCustomPropertyAsync");

            var commandResponse = new CommandResponse();

            try
            {
                var securityContext = _securityContextProvider.GetSecurityContext();

                var newCustomProperty = new BuroCustomProperties
                {
                    PropertyName = command.PropertyName,
                    CustomPropertyCategory = command.CustomPropertyCategory,
                    CustomPropertyValueType = command.CustomPropertyValueType,
                    DefaultValue = command.DefaultValue,
                    MinimumValue = command.MinimumValue,
                    MaximumValue = command.MaximumValue,
                    IsMandatory = command.IsMandatory,
                    IsEditable = command.IsEditable,
                    CreatedBy = securityContext.UserId
                };

                newCustomProperty.AddEntityBasicInfo();

                await _repository.SaveAsync(newCustomProperty);

                _logger.LogInformation("Finished AddCustomPropertyAsync");
            }
            catch (Exception)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            }

            return commandResponse;
        }
    }
}
