using Domain.Commands.CustomProperty;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.CustomProperty
{
    public class RemoveCustomPropertyService
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<RemoveCustomPropertyService> _logger;
        private readonly IRepository _repository;

        public RemoveCustomPropertyService(
            ISecurityContextProvider securityContextProvider,
            ILogger<RemoveCustomPropertyService> logger,
            IRepository repository)
        {
            _securityContextProvider = securityContextProvider;
            _logger = logger;
            _repository = repository;
        }

        public async Task<CommandResponse> RemoveCustomPropertyAsync(RemoveCustomPropertyCommand command)
        {
            _logger.LogInformation("Inside RemoveCustomPropertyAsync");
            var commandResponse = new CommandResponse();

            try
            {
                var customPropertyData = PreparePropertiesOfCustomPropertyForRemoval(command);

                await _repository.UpdateAsync<BuroCustomProperties>(c => c.ItemId == command.ItemId, customPropertyData);

                _logger.LogInformation("Finished RemoveCustomPropertyAsync");
            }
            catch (Exception)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            }

            return commandResponse;
        }

        private Dictionary<string, object> PreparePropertiesOfCustomPropertyForRemoval(RemoveCustomPropertyCommand command)
        {
            _logger.LogInformation("Preparing properties of CustomProperty for CustomProperty ID: {ItemId}", command.ItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCustomProperties.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroCustomProperties.LastUpdatedBy), securityContext.UserId},
                {nameof(BuroCustomProperties.IsMarkedToDelete), true}
            };

            return properties;
        }
    }
}
