using Domain.Commands.CustomProperty;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.CustomProperty
{
    public class EditCustomPropertyService
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<EditCustomPropertyService> _logger;
        private readonly IRepository _repository;

        public EditCustomPropertyService(
            ISecurityContextProvider securityContextProvider,
            ILogger<EditCustomPropertyService> logger,
            IRepository repository)
        {
            _securityContextProvider = securityContextProvider;
            _logger = logger;
            _repository = repository;
        }

        public async Task<CommandResponse> EditCustomPropertyAsync(EditCustomPropertyCommand command)
        {
            _logger.LogInformation("Inside EditCustomPropertyAsync");

            var commandResponse = new CommandResponse();

            try
            {
                var existingCustomProperty = await _repository.GetItemAsync<BuroCustomProperties>(c => c.ItemId == command.ItemId)
                    ?? throw new InvalidOperationException("Given Custom property does not exist");

                var customPropertyData = PreparePropertiesOfCustomPropertyForUpdate(command, existingCustomProperty);

                await _repository.UpdateAsync<BuroCustomProperties>(c => c.ItemId == command.ItemId, customPropertyData);

                _logger.LogInformation("Finished EditCustomPropertyAsync");
            }
            catch (Exception)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            }

            return commandResponse;
        }

        private Dictionary<string, object> PreparePropertiesOfCustomPropertyForUpdate(
            EditCustomPropertyCommand command,
            BuroCustomProperties existingCustomProperty)
        {
            _logger.LogInformation("Preparing properties of CustomProperty for CustomProperty ID: {ItemId}", command.ItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCustomProperties.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroCustomProperties.LastUpdatedBy), securityContext.UserId}
            };

            if (existingCustomProperty.CustomPropertyCategory != command.CustomPropertyCategory)
            {
                properties.Add(nameof(BuroCustomProperties.CustomPropertyCategory), command.CustomPropertyCategory);
            }

            if (existingCustomProperty.CustomPropertyValueType != command.CustomPropertyValueType)
            {
                properties.Add(nameof(BuroCustomProperties.CustomPropertyValueType), command.CustomPropertyValueType);
            }

            if (existingCustomProperty.DefaultValue != command.DefaultValue)
            {
                properties.Add(nameof(BuroCustomProperties.DefaultValue), command.DefaultValue);
            }

            if (existingCustomProperty.IsEditable != command.IsEditable)
            {
                properties.Add(nameof(BuroCustomProperties.IsEditable), command.IsEditable);
            }

            if (existingCustomProperty.IsMandatory != command.IsMandatory)
            {
                properties.Add(nameof(BuroCustomProperties.IsMandatory), command.IsMandatory);
            }

            if (existingCustomProperty.MinimumValue.CompareTo(command.MinimumValue) != 0)
            {
                properties.Add(nameof(BuroCustomProperties.MinimumValue), command.MinimumValue);
            }

            if (existingCustomProperty.MaximumValue.CompareTo(command.MaximumValue) != 0)
            {
                properties.Add(nameof(BuroCustomProperties.MaximumValue), command.MaximumValue);
            }

            return properties;
        }
    }
}
