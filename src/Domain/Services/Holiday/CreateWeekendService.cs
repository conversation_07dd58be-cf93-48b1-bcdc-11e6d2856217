using Domain.Commands.Holiday;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Holiday;

public class CreateWeekendService
{

    private readonly ILogger<CreateWeekendService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateWeekendService(
        ILogger<CreateWeekendService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> CreateWeekend(CreateWeekendCommand command)
    {
        _logger.LogInformation("CreateWeekend process initiated Weekend");

        var commandResponse = new CommandResponse();
        try
        {
            await ProcessWeekendCreationAsync(command);
            _logger.LogInformation("CreateWeekend process completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during CreateWeekend process");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred,
                "An error occurred while creating the weekend.");
        }

        return commandResponse;
    }

    private async Task ProcessWeekendCreationAsync(CreateWeekendCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var existingData = await _repository.GetItemAsync<BuroWeekend>(x => true);

        if (existingData != null)
        {
            _logger.LogInformation("Updating weekend entity for Item ID: {ItemId}", existingData.ItemId);

            var updatedProperties = PrepareWeekendProperties(command, securityContext.UserId);

            await _repository.UpdateManyAsync<BuroWeekend>(x => x.ItemId == existingData.ItemId, updatedProperties);

            _logger.LogInformation("Updated weekend entity successfully for Item ID: {ItemId}", existingData.ItemId);
        }
        else
        {
            _logger.LogInformation("Creating new weekend entity...");

            var weekendEntity = PrepareWeekendEntityCreate(command, securityContext.UserId);
            await _repository.SaveAsync(weekendEntity);

            _logger.LogInformation("Created new weekend entity successfully.");
        }
    }

    private static Dictionary<string, object> PrepareWeekendProperties(CreateWeekendCommand command, string userId)
    {

        var currentDateTime = DateTime.UtcNow;
        var updatedProperties = new Dictionary<string, object>
        {
            { nameof(BuroWeekend.DaysOfWeek), command.DaysOfWeek},
            { nameof(BuroWeekend.LastUpdatedBy), userId },
            { nameof(BuroWeekend.LastUpdateDate), currentDateTime }
        };

        return updatedProperties;
    }

    private static BuroWeekend PrepareWeekendEntityCreate(CreateWeekendCommand command, string userId)
    {
        var weekendEntity = new BuroWeekend
        {
            ItemId = Guid.NewGuid().ToString(),
            DaysOfWeek = command.DaysOfWeek,
            Tags = new[] { Tags.IsAWeekend },
            CreatedBy = userId,
            LastUpdatedBy = userId,

        };
        weekendEntity.AddEntityBasicInfo();
        return weekendEntity;
    }

}



