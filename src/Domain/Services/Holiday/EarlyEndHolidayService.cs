using Domain.Commands.Holiday;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Holiday;

public class EarlyEndHolidayService
{
    private readonly ILogger<EarlyEndHolidayService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IRepository _repository;


    public EarlyEndHolidayService(
        ILogger<EarlyEndHolidayService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository,
        IBusinessRepository businessRepository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> EarlyEndHoliday(EarlyEndHolidayCommand command)
    {
        _logger.LogInformation("EarlyEnd Holiday process initiated");

        var commandResponse = new CommandResponse();
        try
        {
            await ProcessEarlyHolidayAsync(command);
            _logger.LogInformation("EarlyEnd Holiday process completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during EarlyEndHoliday process");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred,
                "An error occurred while EarlyEnd the holiday.");
        }

        return commandResponse;
    }

    private async Task ProcessEarlyHolidayAsync(EarlyEndHolidayCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var userPersonName = securityContext.DisplayName;
        var existingData =
            await _repository.GetItemAsync<BuroHoliday>(c => c.ItemId == command.ItemId && !c.IsMarkedToDelete);

        if (existingData != null)
        {
            var employeePin = _repository.GetItems<BuroEmployee>(x => x.UserItemId == securityContext.UserId)
                .Select(x => x.EmployeePin)
                .FirstOrDefault();

            _logger.LogInformation("Early End holiday entity for Item ID: {ItemId}", existingData.ItemId);
            var currentDateTime = DateTime.UtcNow;
            if (!IsWithinDateRange(command.HolidayEndDate, existingData))
            {
                throw new ArgumentException("Invalid Date Range; the early end holiday is outside the early end date.");
            }

            var updatedProperties = PrepareHolidayProperties(command, command.HolidayEndDate, existingData, currentDateTime, securityContext.UserId, userPersonName, employeePin);
            await _repository.UpdateManyAsync<BuroHoliday>(x => x.ItemId == existingData.ItemId, updatedProperties);
            _logger.LogInformation("updated early end holiday entity successfully for Item ID: {ItemId}", command.ItemId);
        }
        else
        {
            _logger.LogWarning("No existing holiday  found for Item ID: {ItemId}, skipping holiday update.", command.ItemId);
        }


    }

    private static Dictionary<string, object> PrepareHolidayProperties(EarlyEndHolidayCommand command, DateTime holidayEndDate, BuroHoliday existingData, DateTime currentDateTime, string userId, string userPersonName, string employeePin)
    {

        var updatedProperties = new Dictionary<string, object>
                {
                    { nameof(BuroHoliday.LastUpdateDate), currentDateTime },
                    { nameof(BuroHoliday.LastUpdatedBy), userId },
                    { nameof(BuroHoliday.IsEarlyEnd), true},
                    { nameof(BuroHoliday.PreviousHolidayEndDate), existingData.HolidayEndDate ?? holidayEndDate },
                    { nameof(BuroHoliday.HolidayEndDate), holidayEndDate},
                    { nameof(BuroHoliday.EarlyEndedByEmployeeName), userPersonName },
                    { nameof(BuroHoliday.EarlyEndedByEmployeePin), employeePin},
                    { nameof(BuroHoliday.Reason), command.Reason}
                };
        return updatedProperties;
    }

    private static bool IsWithinDateRange(DateTime holidayEndDate, BuroHoliday existingData)
    {
        return holidayEndDate >= existingData.HolidayStartDate && holidayEndDate < existingData.HolidayEndDate;
    }

}