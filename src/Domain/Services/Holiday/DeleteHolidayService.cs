using Domain.Commands.Holiday;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Holiday;

public class DeleteHolidayService
{
    private readonly ILogger<DeleteHolidayService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DeleteHolidayService(
        ILogger<DeleteHolidayService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> DeleteHoliday(DeleteHolidayCommand command)
    {
        _logger.LogInformation("DeleteHoliday  process initiated Holiday");

        var commandResponse = new CommandResponse();
        try
        {
            await ProcessHolidayDeletionAsync(command);
            _logger.LogInformation("DeleteHoliday process completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during DeleteHoliday process");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred,
                "An error occurred while deleting the holiday.");
        }

        return commandResponse;
    }

    private async Task ProcessHolidayDeletionAsync(DeleteHolidayCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var userPersonName = securityContext.DisplayName;
        var existingData = await _repository.GetItemAsync<BuroHoliday>(c => c.ItemId == command.ItemId && !c.IsMarkedToDelete);

        if (existingData != null)
        {
            var employeePin = _repository.GetItems<BuroEmployee>(x => x.UserItemId == securityContext.UserId)
                .Select(x => x.EmployeePin)
                .FirstOrDefault();

            _logger.LogInformation("Deleting holiday for Item ID: {ItemId}", existingData.ItemId);

            if (employeePin != null)
            {
                var updatedProperties = PrepareHolidayProperties(securityContext.UserId, userPersonName, employeePin);

                await _repository.UpdateManyAsync<BuroHoliday>(x => x.ItemId == existingData.ItemId, updatedProperties);
            }

            _logger.LogInformation("Deleted holiday successfully for Item ID: {ItemId}", existingData.ItemId);
        }
        else
        {
            _logger.LogWarning("No existing holiday found for Item ID: {ItemId}, skipping holiday delete.", command.ItemId);
            throw new ArgumentException("Item not found.");
        }
    }

    private static Dictionary<string, object> PrepareHolidayProperties(string userId, string userPersonName, string employeePin)
    {

        var currentDateTime = DateTime.UtcNow;
        var updatedProperties = new Dictionary<string, object>
        {
            { nameof(BuroHoliday.LastUpdateDate), currentDateTime },
            { nameof(BuroHoliday.LastUpdatedBy), userId },
            { nameof(BuroHoliday.IsMarkedToDelete), true},
            { nameof(BuroHoliday.IsActive), false },
            { nameof(BuroHoliday.DeletedByEmployeeName), userPersonName },
            { nameof(BuroHoliday.DeletedByEmployeePin), employeePin}
        };

        return updatedProperties;
    }



}