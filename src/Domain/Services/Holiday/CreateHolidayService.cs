using Domain.Commands.Holiday;
using Domain.Commands.Holiday.HolidaySpecificDtos;
using Infrastructure.Constants;
using Infrastructure.Events.Holiday;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Holiday;

public class CreateHolidayService
{
    private readonly ILogger<CreateHolidayService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IRepository _repository;
    private readonly IServiceClient _serviceClient;

    public CreateHolidayService(
        ILogger<CreateHolidayService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository,
        IServiceClient serviceClient
        )
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _serviceClient = serviceClient;
    }

    public async Task<CommandResponse> CreateHoliday(CreateHolidayCommand command)
    {
        _logger.LogInformation("CreateHoliday process initiated. Holiday Type: {HolidayType}", command.HolidayType);

        var commandResponse = new CommandResponse();
        try
        {
            await ProcessHolidayCreationAsync(command);
            SendHolidayCreatedEventAsync(command);
            _logger.LogInformation("CreateHoliday process completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during CreateHoliday process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating the holiday.");
        }

        return commandResponse;
    }

    private async Task ProcessHolidayCreationAsync(CreateHolidayCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();


        var employeePin = _repository.GetItems<BuroEmployee>(x => x.UserItemId == securityContext.UserId)
            .Select(x => x.EmployeePin)
            .FirstOrDefault();

        switch (command.HolidayType)
        {
            case EnumHolidayType.Global:
                var globalHoliday = PrepareGlobalHolidayEntity(command, securityContext, employeePin);
                _logger.LogInformation("Preparing to insert Global holiday...");
                await _repository.SaveAsync(globalHoliday);
                _logger.LogInformation("Successfully inserted Global holiday.");
                break;

            case EnumHolidayType.OfficeSpecific:
                var officeList = GetOffices(command);

                foreach (var officeSpecificHoliday in officeList.Select(office => PrepareOfficeSpecificHolidayEntity(command, office, securityContext, employeePin)))
                {
                    _logger.LogInformation("Preparing to insert Office-Specific holiday...");
                    await _repository.SaveAsync(officeSpecificHoliday);
                    _logger.LogInformation("Successfully inserted Office-Specific holiday.");
                }
                break;

            case EnumHolidayType.CenterSpecific:
                var centerList = GetCenters(command);
                foreach (var centerSpecificHoliday in centerList.Select(center => PrepareCenterSpecificHolidayEntity(command, center, securityContext, employeePin)))
                {
                    _logger.LogInformation("Preparing to insert Center-Specific holiday...");
                    await _repository.SaveAsync(centerSpecificHoliday);
                    _logger.LogInformation("Successfully inserted Center-Specific holiday.");
                }
                break;

            case EnumHolidayType.Personal:
                var member = GetMember(command);
                var memberSpecificHoliday = PrepareMemberSpecificHolidayEntity(command, member, securityContext, employeePin);
                _logger.LogInformation("Preparing to insert Member-Specific holiday...");
                await _repository.SaveAsync(memberSpecificHoliday);
                _logger.LogInformation("Successfully inserted Member-Specific holiday.");
                break;

            default:
                throw new ArgumentException("Invalid holiday type.");
        }
    }

    public void SendHolidayCreatedEventAsync(CreateHolidayCommand command)
    {
        var holidayCreationEvent = new HolidayCreationEvent
        {
            HolidayStartDate = command.HolidayStartDate,
            HolidayEndDate = command.HolidayEndDate,
            HolidayType = EnumHolidayType.Global,
            CenterItemIds = command.CenterItemIds,
            OfficeItemIds = command.OfficeItemIds,
            MemberItemId = command.MemberItemId
        };
        _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, holidayCreationEvent);
    }

    private List<OfficeSpecificHolidayDto> GetOffices(CreateHolidayCommand command)
    {
        var offices = _repository.GetItems<BuroOffice>(x => command.OfficeItemIds != null && command.OfficeItemIds.Contains(x.ItemId))
            .Select(x => new OfficeSpecificHolidayDto
            {
                OfficeItemId = x.ItemId,
                OfficeCode = x.OfficeCode,
                OfficeName = x.OfficeName,
                OfficeType = x.OfficeType,
            })
            .ToList();

        if (!offices.Any())
        {
            throw new InvalidOperationException("No office found with the provided parameters.");
        }

        return offices;
    }

    private List<CenterSpecificHolidayDto> GetCenters(CreateHolidayCommand command)
    {
        var centers = _repository.GetItems<BuroCenter>(x => command.CenterItemIds != null && command.CenterItemIds.Contains(x.ItemId))
            .Select(center => new
            {
                center.ItemId,
                center.Name,
                center.CenterId, // CenterCode still pending as per note
                center.Type,
                center.BranchId
            })
            .ToList();

        var branchIds = centers.Select(c => c.BranchId).Distinct();
        var offices = _repository.GetItems<BuroOffice>(x => branchIds.Contains(x.ItemId))
            .ToDictionary(o => o.ItemId, o => new { o.OfficeName, o.OfficeCode });

        var result = centers.Select(center => new CenterSpecificHolidayDto
        {
            CenterItemId = center.ItemId,
            CenterName = center.Name,
            CenterCode = center.CenterId,
            CenterType = center.Type,
            OfficeName = offices.TryGetValue(center.BranchId, out var office) ? office.OfficeName : null,
            OfficeCode = offices.TryGetValue(center.BranchId, out office) ? office.OfficeCode : null
        }).ToList();

        if (!centers.Any())
        {
            throw new InvalidOperationException("No center found with the provided parameters.");
        }

        return result;
    }

    private PersonalSpecificHolidayDto GetMember(CreateHolidayCommand command)
    {
        var member = _repository.GetItems<BuroMember>(x => x.ItemId == command.MemberItemId)
            .Select(x => new
            {
                MemberItemId = x.ItemId,
                MemberName = x.MemberName,
                MemberId = x.MemberSequenceNumber,
                CenterItemId = x.CenterItemId,

            }).FirstOrDefault();

        var memberAndCenter = _repository.GetItems<BuroCenter>(x => member != null && x.ItemId == member.CenterItemId)
            .Select(x => new PersonalSpecificHolidayDto
            {
                MemberItemId = member.MemberItemId,
                MemberId = member.MemberId,
                MemberName = member.MemberName,
                CenterItemId = x.ItemId,
                CenterCode = x.CenterId,
                CenterName = x.Name,
                CenterType = x.Type,
                PoName = x.ProgramOrganizerEmployeeName
            }).FirstOrDefault();

        if (memberAndCenter == null)
        {
            throw new InvalidOperationException(
                $"No center found for MemberId: {member?.MemberId} with the provided parameters.");
        }

        return memberAndCenter;
    }

    private static BuroHoliday PrepareGlobalHolidayEntity(CreateHolidayCommand command, SecurityContext securityContext, string? employeePin)
    {


        var holidayEntity = new BuroHoliday
        {
            ItemId = Guid.NewGuid().ToString(),
            HolidayStartDate = command.HolidayStartDate,
            HolidayEndDate = command.HolidayEndDate,
            CreatedByEmployeeName = securityContext.DisplayName,
            CreatedByEmployeePin = employeePin,
            Reason = command.Reason,
            GlobalHolidayName = command.GlobalHolidayName,
            HolidayType = EnumHolidayType.Global,
            Tags = new[] { Tags.IsAHoliday },
            IsActive = true,
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
            IdsAllowedToRead = new[] { securityContext.UserId },
            IdsAllowedToUpdate = new[] { securityContext.UserId },
            IdsAllowedToDelete = new[] { securityContext.UserId },
            IdsAllowedToWrite = new[] { securityContext.UserId }
        };

        holidayEntity.AddEntityBasicInfo();
        return holidayEntity;
    }

    private static BuroHoliday PrepareCenterSpecificHolidayEntity(CreateHolidayCommand command, CenterSpecificHolidayDto center, SecurityContext securityContext, string? employeePin)
    {

        var holidayEntity = new BuroHoliday
        {
            ItemId = Guid.NewGuid().ToString(),
            HolidayStartDate = command.HolidayStartDate,
            HolidayEndDate = command.HolidayEndDate,
            CreatedByEmployeeName = securityContext.DisplayName,
            CreatedByEmployeePin = employeePin,
            Reason = command.Reason,
            CenterItemId = center.CenterItemId,
            CenterName = center.CenterName,
            CenterCode = center.CenterCode,
            CenterType = center.CenterType,
            CenterParentOfficeCode = center.OfficeCode,
            CenterParentOfficeName = center.OfficeName,
            HolidayType = EnumHolidayType.CenterSpecific,
            Tags = new[] { Tags.IsAHoliday },
            IsActive = true,
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
            IdsAllowedToRead = new[] { securityContext.UserId },
            IdsAllowedToUpdate = new[] { securityContext.UserId },
            IdsAllowedToDelete = new[] { securityContext.UserId },
            IdsAllowedToWrite = new[] { securityContext.UserId }
        };

        holidayEntity.AddEntityBasicInfo();
        return holidayEntity;
    }

    private static BuroHoliday PrepareOfficeSpecificHolidayEntity(CreateHolidayCommand command, OfficeSpecificHolidayDto office, SecurityContext securityContext, string? employeePin)
    {


        var holidayEntity = new BuroHoliday
        {
            ItemId = Guid.NewGuid().ToString(),
            HolidayStartDate = command.HolidayStartDate,
            HolidayEndDate = command.HolidayEndDate,
            CreatedByEmployeeName = securityContext.DisplayName,
            CreatedByEmployeePin = employeePin,
            Reason = command.Reason,
            IsActive = true,
            HolidayType = EnumHolidayType.OfficeSpecific,
            OfficeItemId = office.OfficeItemId,
            OfficeType = office.OfficeType,
            OfficeName = office.OfficeName,
            OfficeCode = office.OfficeCode,
            Tags = new[] { Tags.IsAHoliday },
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
            IdsAllowedToRead = new[] { securityContext.UserId },
            IdsAllowedToUpdate = new[] { securityContext.UserId },
            IdsAllowedToDelete = new[] { securityContext.UserId },
            IdsAllowedToWrite = new[] { securityContext.UserId }
        };

        holidayEntity.AddEntityBasicInfo();
        return holidayEntity;
    }

    private static BuroHoliday PrepareMemberSpecificHolidayEntity(CreateHolidayCommand command, PersonalSpecificHolidayDto member, SecurityContext securityContext, string? employeePin)
    {

        var holidayEntity = new BuroHoliday
        {
            ItemId = Guid.NewGuid().ToString(),
            HolidayStartDate = command.HolidayStartDate,
            HolidayEndDate = command.HolidayEndDate,
            CreatedByEmployeeName = securityContext.DisplayName,
            CreatedByEmployeePin = employeePin,
            Reason = command.Reason,
            MemberItemId = member.MemberItemId,
            MemberName = member.MemberName,
            MemberId = member.MemberId,
            PoName = member.PoName,
            CenterItemId = member.CenterItemId,
            CenterName = member.CenterName,
            CenterCode = member.CenterCode,
            CenterType = member.CenterType,
            HolidayType = EnumHolidayType.Personal,
            Tags = new[] { Tags.IsAHoliday },
            IsActive = true,
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
            IdsAllowedToRead = new[] { securityContext.UserId },
            IdsAllowedToUpdate = new[] { securityContext.UserId },
            IdsAllowedToDelete = new[] { securityContext.UserId },
            IdsAllowedToWrite = new[] { securityContext.UserId }
        };

        holidayEntity.AddEntityBasicInfo();
        return holidayEntity;
    }


}
