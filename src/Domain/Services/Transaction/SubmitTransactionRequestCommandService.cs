using Domain.Commands.Transaction;
using Domain.Contracts.Transaction;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Transaction;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Transaction
{
    public class SubmitTransactionRequestCommandService : ISubmitTransactionRequestCommandService
    {
        private readonly ILogger<SubmitTransactionRequestCommandService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly IServiceClient _serviceClient;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;

        public SubmitTransactionRequestCommandService(
            ILogger<SubmitTransactionRequestCommandService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            IServiceClient serviceClient,
            ISecurityContextProvider securityContextProvider,
            IInfrastructureSharedService sharedService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _serviceClient = serviceClient;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
        }

        public async Task<CommandResponse> SubmitTransactionRequestAsync(SubmitTransactionRequestCommand command)
        {
            _logger.LogInformation("Start submitting transaction request with transaction ID: {TransactionItemId}", command.TransactionItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var currentEmployee = await GetCurrentEmployeeAsync();
                var member = await GetMemberAsync(command.MemberItemId);
                var account = await GetProductAccountByAccountItemIdAsync(command.AccountItemId, command.ProductType);

                await CreateTransactionRequestAsync(command, currentEmployee, member, account);

                SendToHandleTransactionRequest(command.TransactionItemId);

                _logger.LogInformation("Successfully submitted transaction request with transaction ID: {TransactionItemId}", command.TransactionItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred while submitting transaction request with transaction ID: {TransactionItemId}", command.TransactionItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while submitting transaction request");
            }

            return commandResponse;
        }

        private void SendToHandleTransactionRequest(string transactionItemId)
        {
            var payload = new ProductAccountTransactionRequestPostProcessingEvent
            {
                ProductAccountTransactionRequestItemIds = new List<string> { transactionItemId },
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroTransactionQueue, payload);

            _logger.LogInformation("Transaction post processing initiated for Transaction ID: {TransactionItemId}", transactionItemId);
        }

        private async Task CreateTransactionRequestAsync(
            SubmitTransactionRequestCommand command, 
            BuroEmployee currentEmployee, 
            BuroMember member, 
            BuroProductAccountBase account)
        {
            _logger.LogInformation("Creating transaction request for transaction ID: {TransactionItemId}", command.TransactionItemId);

            var depositRequest = new CreateTransactionRequestDto
            {
                TransactionItemId = Guid.NewGuid().ToString(),
                MemberItemId = member.ItemId,
                Amount = command.Amount,
                TransactionMedium = command.TransactionMedium,
                ProductType = command.ProductType,
                TransactionType = command.TransactionType,
                AccountItemId = account.ItemId,
                MemberScheduleItemId = command.MemberScheduleItemId,
                MessageCorrelationId = command.MessageCorrelationId,
                IsTransactionRequired = true,
            };

            _sharedService.CreateTransactionRequestAsync(depositRequest, currentEmployee, member, account);
        }

        private async Task<BuroProductAccountBase> GetProductAccountByAccountItemIdAsync(string accountItemId, EnumProductType productType)
        {
            // NOSONAR Need to refactor this method to avoid code duplication

            if (string.IsNullOrWhiteSpace(accountItemId))
            {
                return default!;
            }

            return productType switch
            {
                EnumProductType.GeneralSaving => await _finMongoDbRepository.FindOneAsync<BuroGeneralSavingsAccount>(a => a.ItemId == accountItemId),
                EnumProductType.ContractualSaving => await _finMongoDbRepository.FindOneAsync<BuroContractualSavingsAccount>(a => a.ItemId == accountItemId),
                EnumProductType.Loan => await _finMongoDbRepository.FindOneAsync<BuroLoanAccount>(a => a.ItemId == accountItemId),
                _ => throw new NotSupportedException($"Product type {productType} is not supported.")
            };
        }

        private async Task<BuroMember> GetMemberAsync(string memberItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroMember>(m => m.ItemId == memberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "Member"));
        }

        private async Task<BuroEmployee> GetCurrentEmployeeAsync()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            return await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId);
        }
    }
}
