using Infrastructure.Contracts;

namespace Domain.Services.Helper
{
    public class CommonHelperService
    {
        private readonly ISequenceNumberClient _sequenceNumberClient;

        public CommonHelperService(ISequenceNumberClient sequenceNumberClient)
        {
            _sequenceNumberClient = sequenceNumberClient ?? throw new ArgumentNullException(nameof(sequenceNumberClient));
        }

        public async Task<string> GetSequenceNumberAsync(string context, string prefix)
        {
            var sequence = await _sequenceNumberClient.GetSequenceNumber(new SequenceNumberQuery { Context = context });

            if (sequence == null || sequence.CurrentNumber <= 0)
            {
                throw new InvalidOperationException("Invalid sequence number received from the sequence number service.");
            }

            var sequenceNumber = sequence.CurrentNumber.ToString().PadLeft(6, '0');

            return $"{prefix}{sequenceNumber}";
        }

    }

}
