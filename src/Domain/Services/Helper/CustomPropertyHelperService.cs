using Domain.Commands;
using SeliseBlocks.Entities.PrimaryEntities.BURO;


namespace Domain.Services.Helper
{
    public static class CustomPropertyHelperService
    {
        public static CustomProperties[] MapToEntityCustomProperties(CustomPropertiesCommand[] customPropertiesCommands)
        {
            return customPropertiesCommands.Select(cp => new CustomProperties
            {
                ItemId = cp.ItemId,
                PropertyName = cp.PropertyName,
                Value = cp.Value,
                CustomPropertyValueType = cp.CustomPropertyValueType
            }).ToArray();
        }

        public static List<Dictionary<string, object>> ConvertToCustomPropertiesDictionary(IEnumerable<CustomPropertiesCommand> customPropertiesCommands)
        {
            return customPropertiesCommands.Select(cp => new Dictionary<string, object>
            {
                { "ItemId", cp.ItemId },
                { "PropertyName", cp.PropertyName },
                { "Value", cp.Value },
                { "CustomPropertyValueType", (int)cp.CustomPropertyValueType }
            }).ToList();
        }


    }
}
