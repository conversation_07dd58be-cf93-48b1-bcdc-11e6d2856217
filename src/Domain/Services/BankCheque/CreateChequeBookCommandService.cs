using Domain.Commands.BankCheque;
using Domain.Contracts.BankCheque;
using Domain.Contracts.Member;
using Domain.Services.Bank;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.BankCheque;

public class CreateChequeBookCommandService : ICreateChequeBookCommandService
{
    private readonly ILogger<CreateChequeBookCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;

    public CreateChequeBookCommandService(
        ILogger<CreateChequeBookCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        ISharedService sharedService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
    }
    public async Task<CommandResponse> CreateChequeBookAsync(CreateChequeBookCommand command)
    {
        _logger.LogInformation("Creating cheque book for Office : {OfficeItemId}", command.OfficeItemId);
        var commandResponse = new CommandResponse();
        try
        {
            var branch = await _repository.FindWithProjectionAsync<BuroBankBranch, BranchInfoDto>(b => b.ItemId == command.BankBranchItemId,
                b => new BranchInfoDto
                {
                    BankItemId = b.BankItemId,
                    BankName = b.BankName,
                    BranchName = b.BranchName,
                    RoutingNumber = b.RoutingNumber
                });

            if (!branch.Any())
            {
                commandResponse.SetError(BuroErrorMessageKeys.BankBranchNotFound, "No Bank Branch Exists");
                return commandResponse;
            }

            await SubmitChequeBookInfoAsync(command, branch[0]);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error creating cheque book for Office : {OfficeItemId}", command.OfficeItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
        }
        _logger.LogInformation("Cheque book created for Office : {OfficeItemId}", command.OfficeItemId);
        return commandResponse;
    }

    private async Task SubmitChequeBookInfoAsync(CreateChequeBookCommand command, BranchInfoDto branch)
    {
        var bankSwiftCode = await _repository.FindWithProjectionAsync<BuroBank, string>(b => b.ItemId == branch.BankItemId, b => b.SwiftCode);
        var bankCode = GetBankCodeFromSwiftCode(bankSwiftCode);

        var context = _securityContextProvider.GetSecurityContext();
        var employee = await _repository.FindWithProjectionAsync<BuroEmployee, EmployeeIdentityDto>(e => e.UserItemId == context.UserId,
            e => new EmployeeIdentityDto
            {
                EmployeeName = e.EmployeeName,
                EmployeePin = e.EmployeePin,
                DesignationTitle = e.DesignationTitle
            });

        _logger.LogInformation("Inserting cheque book for Office : {OfficeItemId}", command.OfficeItemId);
        var chequeBook = new BuroChequeBook
        {
            ItemId = Guid.NewGuid().ToString(),
            BookSequenceNumber = await _sharedService.GetSequenceNumberAsync(nameof(BuroChequeBook), bankCode),
            OfficeItemId = command.OfficeItemId,
            BankItemId = branch.BankItemId,
            BankName = branch.BankName,
            BranchName = branch.BranchName,
            RoutingNumber = branch.RoutingNumber,
            AccountNumber = command.AccountNumber,
            AccountName = command.AccountName,
            TotalLeaves = command.TotalLeaves,
            ChequeLeaves = GenerateChequeLeaves(command),
            LeavesMinimumThreshold = command.LeavesThreshold,
            LeafPrefix = command.LeafPrefix,
            StartingLeafSerialNumber = command.StartingLeavesSerial,
            BookInitializeDate = command.InitializedDate ?? DateTime.UtcNow,
            AddedByEmployeeDesignation = employee[0].DesignationTitle,
            AddedByEmployeeName = employee[0].EmployeeName,
            AddedByEmployeePin = employee[0].EmployeePin,
            Tags = new[] { Tags.IsAChequeBook }
        };

        _logger.LogInformation("Inserted cheque book created for Office : {OfficeItemId}", command.OfficeItemId);
        await _repository.InsertOneAsync(chequeBook);
    }

    private static string GetBankCodeFromSwiftCode(List<string> bankSwiftCode)
    {
        if (!bankSwiftCode.Any() || string.IsNullOrWhiteSpace(bankSwiftCode[0]))
        {
            return string.Empty;
        }

        var swiftCode = bankSwiftCode[0];
        return swiftCode.Length >= 3 ? swiftCode.Substring(0, 3) : swiftCode;
    }

    private List<ChequeLeaf> GenerateChequeLeaves(CreateChequeBookCommand command)
    {
        return Enumerable.Range(0, command.TotalLeaves)
            .Select(offset => new ChequeLeaf
            {
                ItemId = Guid.NewGuid().ToString(),
                LeafSerialNumber = string.IsNullOrWhiteSpace(command.LeafPrefix) ? (command.StartingLeavesSerial + offset).ToString()
                    : $"{command.LeafPrefix} {command.StartingLeavesSerial + offset}",
                UsageStatus = BuroChequeUsageStatus.Unused,
                UsedDate = null,
                UsedByEmployeeName = null,
                UsedByEmployeePin = null
            })
            .ToList();
    }
}