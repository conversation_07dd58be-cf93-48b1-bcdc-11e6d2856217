using Domain.Commands.Register;
using Domain.Contracts.Member;
using Domain.Contracts.Register;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Register;

public class CreatePostRegisterCommandService : ICreatePostRegisterCommandService
{
    private readonly ILogger<CreatePostRegisterCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;

    public CreatePostRegisterCommandService(
        ILogger<CreatePostRegisterCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        ISharedService sharedService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
    }


    public async Task<CommandResponse> CreatePostRegisterAsync(CreatePostRegisterCommand command)
    {
        _logger.LogInformation("Starting CreatePostRegisterAsync for OfficeItemId: {OfficeItemId}", command.OfficeItemId);
        var commandResponse = new CommandResponse();

        try
        {
            await ProcessPostRegisterAsync(command, commandResponse);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during CreatePostRegisterAsync for OfficeItemId: {OfficeItemId}", command.OfficeItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating post.");
        }

        return commandResponse;
    }

    private async Task ProcessPostRegisterAsync(CreatePostRegisterCommand command, CommandResponse commandResponse)
    {
        try
        {
            _logger.LogInformation("Starting ProcessPostRegisterAsync for OfficeItemId: {OfficeItemId}", command.OfficeItemId);
            var postedItemId = Guid.NewGuid().ToString();
            var securityContext = _securityContextProvider.GetSecurityContext();
            var employeeInfo = await _sharedService.GetEmployeeInfoAsync(securityContext.UserId);
            var taggedPersonInfo = new List<EmployeeIdentityDto>();
            var notificationPayload = new object();
            
            if(command.TaggedEmployeeItemIds.Any() || command.TaggedEmployeeItemIds.Count >0)
            {
                _logger.LogInformation("TaggedEmployeeItemIds: {TaggedEmployeeItemIds}", command.TaggedEmployeeItemIds);
                taggedPersonInfo = await _repository.FindWithProjectionAsync<BuroEmployee, EmployeeIdentityDto>(x => command.TaggedEmployeeItemIds.Contains(x.ItemId),
                    x => new EmployeeIdentityDto
                    {
                        EmployeeName = x.EmployeeName,
                        EmployeePin = x.EmployeePin,
                        DesignationTitle = x.DesignationTitle,
                        UserItemId = x.UserItemId
                    }); 
                notificationPayload = MakeNotificationPayloadForTaggedPerson(postedItemId, employeeInfo);
            }

            var postRegister = PreparePostRegisterEntity(command, employeeInfo, taggedPersonInfo, postedItemId);
            await _repository.InsertOneAsync(postRegister);
            _logger.LogInformation("Post register created with post itemId: {PostedItemId}", postedItemId);
            
            foreach (var taggedPerson in taggedPersonInfo)
            {
                _logger.LogInformation("Sending notification to tagged person: {TaggedPerson}", taggedPerson.EmployeeName);
                await _sharedService.NotifyUserAsync(
                    BuroNotificationKeys.CommentedInRegister,
                    notificationPayload,
                    postedItemId,
                    taggedPerson.UserItemId);
                _logger.LogInformation("Notification sent to tagged person: {TaggedPerson}", taggedPerson.EmployeeName);
            }
            
            
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during ProcessPostRegisterAsync for OfficeItemId: {OfficeItemId}", command.OfficeItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating post.");
        }
        
    }
    private BuroPost PreparePostRegisterEntity(CreatePostRegisterCommand command, EmployeeIdentityDto employeeInfo, List<EmployeeIdentityDto> taggedPersonInfo, string postedItemId)
    {
        return new BuroPost
        {
            ItemId = postedItemId,
            OfficeItemId = command.OfficeItemId,
            OfficeType = command.OfficeType,
            PostText = command.PostText,
            TaggedEmployees = taggedPersonInfo.Select(x => new TaggedEmployeeInfo
            {
                EmployeeName = x.EmployeeName,
                EmployeePin = x.EmployeePin,
                EmployeeItemId = x.UserItemId
            }).ToList(),
            PostMediaFileItemId = command.PostMediaFileItemId,
            Tags = new[] { Tags.IsAPostRegister },
            PostedByEmployeeName = employeeInfo.EmployeeName,
            PostedByEmployeePin = employeeInfo.EmployeePin,
            PostedByEmployeeDesignation = employeeInfo.DesignationTitle,
            PostedByEmployeeItemId = employeeInfo.EmployeeItemId,
            PostDate = DateTime.UtcNow
        };
    }
    
    private static object MakeNotificationPayloadForTaggedPerson(
        string entityItemId,
        EmployeeIdentityDto commentedPerson)
    {
        return new
        {
            CommentRegisterItemId = entityItemId,
            CommentedAt = DateTime.UtcNow,
            CommentedName = commentedPerson.EmployeeName,
            CommentedPin = commentedPerson.EmployeePin,
            CommentedDesignation = commentedPerson.DesignationTitle,
        };
    }
}