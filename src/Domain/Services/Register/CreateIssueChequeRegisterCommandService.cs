using Domain.Commands.Register;
using Domain.Commands.Register.Dtos;
using Domain.Contracts.Member;
using Domain.Contracts.Register;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Events.Voucher;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;


namespace Domain.Services.Register;

public class CreateIssueChequeRegisterCommandService : ICreateIssueChequeRegisterCommandService
{

    private readonly ILogger<CreateIssueChequeRegisterCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;
    private readonly IServiceClient _serviceClient;

    public CreateIssueChequeRegisterCommandService(
        ILogger<CreateIssueChequeRegisterCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        ISharedService sharedService,
        IServiceClient serviceClient)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
        _serviceClient = serviceClient;
    }


    public async Task<CommandResponse> CreateIssueChequeAsync(CreateIssueChequeRegisterCommand command)
    {
        _logger.LogInformation("Starting CreateIssueChequeAsync for ChequeBookItemId: {ChequeBookItemId}", command.ChequeBookItemId);
        var commandResponse = new CommandResponse();

        try
        {
            await ProcessIssueChequeAsync(command, commandResponse);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during CreateIssueChequeAsync for ChequeBookItemId: {ChequeBookItemId}", command.ChequeBookItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating issue cheque.");
        }

        return commandResponse;

    }

    private async Task ProcessIssueChequeAsync(CreateIssueChequeRegisterCommand command,
        CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        try
        {
            var chequeBook = await _repository.FindAsync<BuroChequeBook>(c => c.ItemId == command.ChequeBookItemId);
            if (chequeBook == null)
            {
                _logger.LogError("ChequeBookItemId: {Command}, Cheque book not found.", command.ChequeBookItemId);
                commandResponse.SetError(BuroErrorMessageKeys.ChequeBookNotFound, "Cheque book not found.");
                return;
            }

            var chequeLeafInfo = GetChequeLeafInfo(chequeBook[0], command.ChequeLeafItemId);

            var employeeInfo = await _sharedService.GetEmployeeInfoAsync(securityContext.UserId);

            var transactions = await _repository.FindWithProjectionAsync<BuroTransactionCategoryType, TransactionTypeDto>(t => t.ItemId == command.TransactionTypeItemId,
                t => new TransactionTypeDto
                {
                    TransactionTypeName = t.TransactionTypeName,
                    TransactionTypeTag = t.TransactionTypeTag
                });

            if (!transactions.Any())
            {
                _logger.LogError("TransactionTypeItemId: {Command}, Transaction type not found.", command.TransactionTypeItemId);
                commandResponse.SetError(BuroErrorMessageKeys.TransactionTypeNotFound, "Transaction type not found.");
                return;
            }

            OfficeInfoDto? officeInfo = null;

            if (!string.IsNullOrEmpty(command.OfficeItemId))
            {
                var offices = await _repository.FindWithProjectionAsync<BuroOffice, OfficeInfoDto>(
                    o => o.ItemId == command.OfficeItemId,
                    o => new OfficeInfoDto
                    {
                        ItemId = o.ItemId,
                        OfficeName = o.OfficeName,
                        OfficeCode = o.OfficeCode,
                        ParentOfficeItemId = o.ParentOfficeItemId,
                        OfficeType = o.OfficeType
                    });

                if (offices == null || !offices.Any())
                {
                    _logger.LogError("OfficeItemId: {Command}, Office not found.", command.OfficeItemId);
                    commandResponse.SetError(BuroErrorMessageKeys.OfficeNotFound, "Office not found.");
                    return;
                }

                officeInfo = offices[0];
            }

            await SubmitIssueChequeAsync(command, chequeBook[0], chequeLeafInfo, employeeInfo, transactions[0], officeInfo);
            await PostProcessChequeBookAsync(chequeBook[0], chequeLeafInfo, employeeInfo);
            CreateVoucher(chequeBook[0], chequeLeafInfo, transactions[0], command);
            _logger.LogInformation("Cheque register created with cheque number: {ChequeNumber}", command.ChequeLeafItemId);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during ProcessIssueChequeAsync for ChequeBookItemId: {ChequeBookItemId}", command.ChequeBookItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating issue cheque.");
        }

    }

    private async Task SubmitIssueChequeAsync(CreateIssueChequeRegisterCommand command, BuroChequeBook chequeBook, ChequeLeaf chequeLeafInfo, EmployeeIdentityDto employeeInfo, TransactionTypeDto transactionType, OfficeInfoDto? officeInfo)
    {
        var chequeRegister = new BuroIssueChequeRegister
        {
            ItemId = command.ItemId,
            ChequeBookItemId = chequeBook.ItemId,
            ChequeLeafItemId = chequeLeafInfo.ItemId,
            ChequeLeafSerialNumber = chequeLeafInfo.LeafSerialNumber,
            ChequeBookSequenceNumber = chequeBook.BookSequenceNumber,
            PayeeName = command.PayeeName,
            Amount = command.Amount,
            TransactionTypeName = transactionType.TransactionTypeName,
            TransactionTypeTag = transactionType.TransactionTypeTag,
            IssueDate = command.IssueDate ?? DateTime.UtcNow,
            InstrumentType = command.InstrumentType,
            ChequeIssueType = command.ChequeIssueType,
            OfficeItemId = chequeBook.OfficeItemId,
            PayeeOfficeCode = officeInfo?.OfficeCode,
            PayeeOfficeItemId = officeInfo?.ItemId,
            PayeeOfficeName = officeInfo?.OfficeName,
            ChequeFileStorageItemId = command.ChequeFileStorageItemId,
            Tags = new[] { Tags.IsAIssueChequeRegister },
            IssuedByEmployeeName = employeeInfo.EmployeeName,
            IssuedByEmployeePin = employeeInfo.EmployeePin,
            IssuedByEmployeeDesignation = employeeInfo.DesignationTitle,
        };

        await _repository.InsertOneAsync(chequeRegister);
        _logger.LogInformation("Cheque register created with cheque number: {ChequeNumber}", command.ChequeLeafItemId);
    }

    private ChequeLeaf GetChequeLeafInfo(BuroChequeBook chequeBook, string chequeLeafItemId)
    {
        var chequeLeaf = chequeBook.ChequeLeaves.FirstOrDefault(c => c.ItemId == chequeLeafItemId);

        if (chequeLeaf != null) return chequeLeaf;
        _logger.LogError("ChequeLeafItemId: {Command}, Cheque leaf not found.", chequeLeafItemId);
        throw new ArgumentException("Cheque leaf not found.");

    }

    private async Task PostProcessChequeBookAsync(BuroChequeBook chequeBook, ChequeLeaf chequeLeafInfo, EmployeeIdentityDto employeeInfo)
    {
        _logger.LogInformation("ChequeBookItemId: {Command}, Updating Cheque Book leaf.", chequeBook.ItemId);
        var updatedProperties = PrepareChequeBookProperties(chequeBook, chequeLeafInfo, employeeInfo);
        await _repository.UpdateOneAsync<BuroChequeBook>(c => c.ItemId == chequeBook.ItemId, updatedProperties);
        _logger.LogInformation("ChequeBookItemId: {Command}, Cheque Book leaf updated.", chequeBook.ItemId);
    }

    private Dictionary<string, object> PrepareChequeBookProperties(BuroChequeBook chequeBook, ChequeLeaf chequeLeafInfo, EmployeeIdentityDto employeeInfo)
    {

        var updatedChequeLeaves = chequeBook.ChequeLeaves.Select(leaf =>
        {
            if (leaf.ItemId == chequeLeafInfo.ItemId)
            {
                return new ChequeLeaf
                {
                    ItemId = leaf.ItemId,
                    LeafSerialNumber = leaf.LeafSerialNumber,
                    UsageStatus = BuroChequeUsageStatus.Used,
                    UsedDate = DateTime.UtcNow,
                    UsedByEmployeeName = employeeInfo.EmployeeName,
                    UsedByEmployeePin = employeeInfo.EmployeePin
                };
            }
            return leaf;
        });

        var properties = new Dictionary<string, object>
        {
            { nameof(BuroChequeBook.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroChequeBook.LastUpdatedBy), _securityContextProvider.GetSecurityContext().UserId },
            { nameof(BuroChequeBook.ChequeLeaves), new BsonArray(updatedChequeLeaves.Select(leaf => leaf.ToBsonDocument())) }
        };

        return properties;
    }

    private void CreateVoucher(BuroChequeBook chequeBook, ChequeLeaf chequeLeafInfo, TransactionTypeDto transactionType, CreateIssueChequeRegisterCommand command)
    {
        var payload = new CreateVoucherEvent
        {
            VoucherGroupItemId = Guid.NewGuid().ToString(),
            OfficeItemId = chequeBook.OfficeItemId,
            RelatedEntityName = nameof(BuroIssueChequeRegister),
            RelatedEntityItemId = command.ItemId,
            VoucherScopeCategory = EnumVoucherScopeCategory.IssueCheque,
            Amount = command.Amount,
            Narration = $"Cheque Issued to {command.PayeeName}, Cheque Number: {chequeLeafInfo.LeafSerialNumber}, Bank: {chequeBook.BankName}",
            TransactionTypeTag = transactionType.TransactionTypeTag
        };

        _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);
        _logger.LogInformation("Voucher creation initiated for cheque number: {ChequeNumber}", chequeLeafInfo.LeafSerialNumber);
    }


}