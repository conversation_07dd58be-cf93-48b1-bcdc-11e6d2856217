using Domain.Commands.Register;
using Domain.Contracts.Register;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Register;

public class DeletePostRegisterCommandService : IDeletePostRegisterCommandService
{
    private readonly ILogger<DeletePostRegisterCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DeletePostRegisterCommandService(
        ILogger<DeletePostRegisterCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        
    }
    
    public async Task<CommandResponse> DeletePostRegisterAsync(DeletePostRegisterCommand command)
    {
        _logger.LogInformation("Starting DeletePostRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
        var commandResponse = new CommandResponse();

        try
        {
            await ProcessDeletePostRegisterAsync(command, commandResponse);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during DeletePostRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while deleting post.");
        }

        return commandResponse;
    }
    
  
    private async Task ProcessDeletePostRegisterAsync(DeletePostRegisterCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;
    
        var existingData =
            await _repository.ExistsAsync<BuroPost>(c => c.ItemId == command.PostItemId);

        if (existingData == false)
        {
            _logger.LogWarning("No existing post found for PostItemId: {PostItemId}, skipping post update.", command.PostItemId);
            commandResponse.SetError(BuroErrorMessageKeys.PostNotFound, "Post not found.");
            return;
        }
        
        var updatedProperties = PreparePostRegisterProperties(command, securityContext.UserId, currentDateTime);
        
        var existingComments = await _repository.FindWithProjectionAsync<BuroPostComment, string>(c => c.PostItemId == command.PostItemId,
            c => c.PostItemId);

        _logger.LogInformation("Deleting post register for PostItemId: {PostItemId}", command.PostItemId);
        
        var bulkOperations = new List<Task> { _repository.UpdateOneAsync<BuroPost>(x => x.ItemId == command.PostItemId, updatedProperties) };

        if (existingComments.Any())
        {
            _logger.LogInformation("Deleting comments for PostItemId: {PostItemId}", command.PostItemId);
        
            var updatedCommentProperties = PrepareCommentRegisterProperties(command, securityContext.UserId, currentDateTime);
            bulkOperations.Add(_repository.UpdateManyAsync<BuroPostComment>(x => x.PostItemId == command.PostItemId, updatedCommentProperties));
        }
        
        await Task.WhenAll(bulkOperations);

        _logger.LogInformation("Deleted post register for PostItemId: {PostItemId}", command.PostItemId);
    }
    
    private Dictionary<string, object> PrepareCommentRegisterProperties(DeletePostRegisterCommand command, string userId, DateTime currentDateTime)
    {
        _logger.LogInformation("Preparing delete properties for comments for PostItemId: {PostItemId}", command.PostItemId);

        var properties = new Dictionary<string, object>
        {
            { nameof(BuroPostComment.LastUpdateDate), currentDateTime },
            { nameof(BuroPostComment.LastUpdatedBy), userId },
            { nameof(BuroPostComment.IsMarkedToDelete), true }
        };

        return properties;
    }
    
    private Dictionary<string, object> PreparePostRegisterProperties(DeletePostRegisterCommand command,
        string userId, DateTime currentDateTime)
    {
        _logger.LogInformation("Preparing delete properties for PostItemId: {PostItemId}", command.PostItemId);

        var properties = new Dictionary<string, object>
        {
            { nameof(BuroPost.LastUpdateDate), currentDateTime },
            { nameof(BuroPost.LastUpdatedBy), userId },
            { nameof(BuroPost.IsMarkedToDelete), true }
        };

        return properties;
    }
}