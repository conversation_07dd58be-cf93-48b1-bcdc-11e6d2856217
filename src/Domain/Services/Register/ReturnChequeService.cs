using Domain.Commands.Register;
using Domain.Contracts.Register;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Register;

public class ReturnChequeService : IChequeReturnService
{

    private readonly ILogger<ReturnChequeService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly SharedService _sharedService;

    public ReturnChequeService(
        ILogger<ReturnChequeService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        SharedService sharedService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
    }


    public async Task<CommandResponse> ChequeReturnAsync(ReturnChequeCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, ReturnChequeCommand command received.", command.MessageCorrelationId);
        var commandResponse = new CommandResponse();

        try
        {
            await ProcessChequeReturnAsync(command, commandResponse);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "MessageCorrelationId: {Command}, Error while processing ReturnChequeCommand command.", command.MessageCorrelationId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while processing Return Cheque.");
        }

        return commandResponse;
    }

    private async Task ProcessChequeReturnAsync(ReturnChequeCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var isChequeRegister = await _repository.ExistsAsync<BuroChequeRegister>(c => c.ItemId == command.ChequeRegisterItemId && c.IsEligibleReturn && c.Status == EnumChequeRegisterStatus.Deposit);
        if (!isChequeRegister)
        {
            _logger.LogError("MessageCorrelationId: {Command}, Cheque Register Item not found.", command.ChequeRegisterItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ChequeRegisterNotFound, "Cheque Register Item not found.");
        }

        var employeeInfo = await _sharedService.GetEmployeeInfoAsync(securityContext.UserId);

        var updatedProperties = PrepareChequeRegisterProperties(command, employeeInfo, securityContext.UserId, currentDateTime);

        _logger.LogInformation("MessageCorrelationId: {Command}, Updating Cheque Register Item.", command.ChequeRegisterItemId);

        await _repository.UpdateOneAsync<BuroChequeRegister>(x => x.ItemId == command.ChequeRegisterItemId, updatedProperties);

        _logger.LogInformation("MessageCorrelationId: {Command}, Cheque Register Item updated.", command.ChequeRegisterItemId);

    }

    private static Dictionary<string, object> PrepareChequeRegisterProperties(ReturnChequeCommand command, EmployeeIdentityDto employeeInfo, string userId, DateTime currentDateTime)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroChequeRegister.LastUpdateDate), currentDateTime },
            { nameof(BuroChequeRegister.LastUpdatedBy), userId },
            { nameof(BuroChequeRegister.Status), EnumChequeRegisterStatus.Return },
            {nameof(BuroChequeRegister.ReturnDate), currentDateTime },
            {nameof(BuroChequeRegister.ReturnAckStorageItemId), command.ReturnAckStorageItemId },
            {nameof(BuroChequeRegister.ChequeReturnedByEmployeeName), employeeInfo.EmployeeName },
            {nameof(BuroChequeRegister.ChequeReturnedByEmployeePin), employeeInfo.EmployeePin },
            {nameof(BuroChequeRegister.ChequeReturnedByEmployeeDesignation), employeeInfo.DesignationTitle },

        };

        return properties;
    }

}