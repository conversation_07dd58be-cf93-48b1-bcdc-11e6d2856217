using Domain.Commands.Register;
using Domain.Contracts.Register;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Register;

public class UpdatePostRegisterCommandService : IUpdatePostRegisterCommandService
{
    private readonly ILogger<UpdatePostRegisterCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdatePostRegisterCommandService(
        ILogger<UpdatePostRegisterCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }
    
    
    public async Task<CommandResponse> UpdatePostRegisterAsync(UpdatePostRegisterCommand command)
    {
        _logger.LogInformation("Starting UpdatePostRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
        var commandResponse = new CommandResponse();

        try
        {
            await ProcessUpdatePostRegisterAsync(command, commandResponse);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during UpdatePostRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating post.");
        }

        return commandResponse;
    }
    
    private async Task ProcessUpdatePostRegisterAsync(UpdatePostRegisterCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var existingData =
            await _repository.FindWithProjectionAsync<BuroPost, string>(c => c.ItemId == command.PostItemId,
                c => c.PostText);

        if (existingData == null)
        {
            _logger.LogWarning("No existing post found for PostItemId: {PostItemId}, skipping post update.", command.PostItemId);
            commandResponse.SetError(BuroErrorMessageKeys.PostNotFound, "Post not found.");
            return;
        }

        var updatedProperties = PreparePostRegisterProperties(command, existingData[0], securityContext.UserId, currentDateTime);

        _logger.LogInformation("Updating post register for PostItemId: {PostItemId}", command.PostItemId);

        await _repository.UpdateOneAsync<BuroPost>(x => x.ItemId == command.PostItemId, updatedProperties);

        _logger.LogInformation("Updated post register for PostItemId: {PostItemId}", command.PostItemId);
    }

    private Dictionary<string, object> PreparePostRegisterProperties(UpdatePostRegisterCommand command,
        string postText, string userId, DateTime currentDateTime)
    {
        _logger.LogInformation("Preparing update properties for PostItemId: {PostItemId}", command.PostItemId);

        var properties = new Dictionary<string, object>
        {
            { nameof(BuroPost.LastUpdateDate), currentDateTime },
            { nameof(BuroPost.LastUpdatedBy), userId }
        };

        AddIfChanged(properties, nameof(BuroPost.PostText), command.PostText, postText);

        return properties;
    }
    
    private static void AddIfChanged<T>(
        Dictionary<string, object> properties,
        string propertyName,
        T newValue,
        T oldValue) where T : IComparable 
    {
        if (!Equals(newValue, oldValue))
        {
            properties[propertyName] = newValue;
        }
    }

}