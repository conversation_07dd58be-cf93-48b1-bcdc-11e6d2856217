using Domain.Commands.Register;
using Domain.Contracts.Member;
using Domain.Contracts.Register;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Register;

public class CreateCommentRegisterCommandService : ICreateCommentRegisterCommandService
{
    private readonly ILogger<CreateCommentRegisterCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;

    public CreateCommentRegisterCommandService(
        ILogger<CreateCommentRegisterCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        ISharedService sharedService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
    }
    
    public async Task<CommandResponse> CreateCommentRegisterAsync(CreateCommentRegisterCommand command)
    {
        _logger.LogInformation("Starting CreateCommentRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
        var commandResponse = new CommandResponse();

        try
        {
            await ProcessCommentRegisterAsync(command, commandResponse);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during CreateCommentRegisterAsync for PostItemId: {PostItemId}", command.PostItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating comment.");
        }

        return commandResponse;
    }
    
    private async Task ProcessCommentRegisterAsync(CreateCommentRegisterCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var notifiedPersons = new List<EmployeeIdentityDto>();
        var notificationPayload = new object();
        
        var employeeInfo = await _sharedService.GetEmployeeInfoAsync(securityContext.UserId);
        
        var postInteractedEmployeeIds = await _repository.FindWithProjectionAsync<BuroPost, List<string>>(x => x.ItemId == command.PostItemId,
            x => x.TaggedEmployees.Select(y => y.EmployeeItemId).Concat(new[] { x.PostedByEmployeeItemId }).ToList());
        
        if (!postInteractedEmployeeIds.Any() && !postInteractedEmployeeIds[0].Any())
        {
            _logger.LogError("No post found for PostItemId: {PostItemId}", command.PostItemId);
            commandResponse.SetError(BuroErrorMessageKeys.PostNotFound, "No post found.");
            return;
        }

        var commentedEmployeeIds = await _repository.FindWithProjectionAsync<BuroPostComment, string>(x => x.PostItemId == command.PostItemId,
            x => x.CommentedEmployeeItemId);
        
        var uniqueEmployeeIds = postInteractedEmployeeIds[0].Concat(commentedEmployeeIds).Distinct().ToList();
        
        if(uniqueEmployeeIds.Any() || uniqueEmployeeIds.Count >0)
        {

            notifiedPersons = await _repository.FindWithProjectionAsync<BuroEmployee, EmployeeIdentityDto>(x => uniqueEmployeeIds.Contains(x.ItemId),
                x => new EmployeeIdentityDto
                {
                    EmployeeName = x.EmployeeName,
                    EmployeePin = x.EmployeePin,
                    DesignationTitle = x.DesignationTitle,
                    UserItemId = x.UserItemId
                }); 
            notificationPayload = MakeNotificationPayloadForInteractedPerson(command.PostItemId, employeeInfo);
        }
        
        var commentRegister = PrepareCommentRegisterEntity(command, employeeInfo);
        await _repository.InsertOneAsync(commentRegister);
        _logger.LogInformation("Comment register created with post itemId: {PostItemId}", command.PostItemId);
        
        foreach (var notifiedPerson in notifiedPersons)
        {
            _logger.LogInformation("Sending notification to notified person: {NotifiedPerson}", notifiedPerson.EmployeeName);
            await _sharedService.NotifyUserAsync(
                BuroNotificationKeys.PostedInRegister,
                notificationPayload,
                command.PostItemId,
                notifiedPerson.UserItemId);
            _logger.LogInformation("Notification sent to notified person: {NotifiedPerson}", notifiedPerson.EmployeeName);
        }

    }
    
    private BuroPostComment PrepareCommentRegisterEntity(CreateCommentRegisterCommand command, EmployeeIdentityDto employeeInfo)
    {
        return new BuroPostComment
        {
            ItemId = Guid.NewGuid().ToString(),
            PostItemId = command.PostItemId,
            CommentText = command.CommentText,
            Tags = new[] { Tags.IsACommentRegister },
            CommentedEmployeeItemId = employeeInfo.EmployeeName,
            CommentedEmployeeName = employeeInfo.EmployeePin,
            CommentedEmployeePin = employeeInfo.EmployeeItemId,
            CommentedEmployeeDesignation = employeeInfo.DesignationTitle,
            CommentDate = DateTime.UtcNow
        };
    }
    
    private static object MakeNotificationPayloadForInteractedPerson(
        string entityItemId,
        EmployeeIdentityDto commentedPerson)
    {
        return new
        {
            CommentRegisterItemId = entityItemId,
            CommentedAt = DateTime.UtcNow,
            CommentedName = commentedPerson.EmployeeName,
            CommentedPin = commentedPerson.EmployeePin,
            CommentedDesignation = commentedPerson.DesignationTitle,
        };
    }
}