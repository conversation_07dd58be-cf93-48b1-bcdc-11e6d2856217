using Domain.Commands.Collection;
using Infrastructure.Constants;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Collection
{
    public class GeneralSavingAccountOneShotPaymentHandler : IAccountOneShotPaymentHandler
    {
        private readonly ILogger<GeneralSavingAccountOneShotPaymentHandler> _logger;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;

        public GeneralSavingAccountOneShotPaymentHandler(ILogger<GeneralSavingAccountOneShotPaymentHandler> logger,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _serviceClient = serviceClient;
        }
        public EnumProductType AccountType => EnumProductType.GeneralSaving;

        public async Task<CommandResponse> ProcessPaymentAsync(string memberId, OneShotPaymentDetail paymentDetail)
        {
            var response = new CommandResponse();

            try
            {
                _logger.LogInformation("Processing GS payment for MemberId: {MemberId}, AccountId: {AccountId}", memberId, paymentDetail.Account.ItemId);

                await UpdateGsPaymentAsync(paymentDetail);

                var trackPayementEvent = new TrackAccountPaymentEvent
                {
                    AccountPayments = new List<AccountPayment>
                    {
                        new()
                        {
                            MemberItemId = memberId,
                            AccountItemId = paymentDetail.Account.ItemId,
                            PaidAmount = paymentDetail.RealizedAmount,
                            ProductType = EnumProductType.GeneralSaving
                        }
                    }
                };


                _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, trackPayementEvent);

                _logger.LogInformation("GS Payment processed successfully for MemberId: {MemberId}", memberId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing gs payment for MemberId: {MemberId}", memberId);
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "An unexpected error occurred while processing the payment.");
            }

            return response;
        }

        private async Task UpdateGsPaymentAsync(OneShotPaymentDetail paymentDetail)
        {
            var gsAccount = paymentDetail.Account as BuroGeneralSavingsAccount ?? throw new InvalidCastException($"Expected BuroGeneralSavingsAccount but got {paymentDetail.Account.GetType().Name}");

            var gsAccountUpdates = new Dictionary<string, object>
            {
                { nameof(BuroGeneralSavingsAccount.Balance), gsAccount.Balance + paymentDetail.RealizedAmount }
            };

            await _repository.UpdateAsync<BuroGeneralSavingsAccount>(x => x.ItemId == gsAccount.ItemId, gsAccountUpdates);
        }
    }
}
