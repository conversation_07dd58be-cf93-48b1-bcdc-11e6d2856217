using Domain.Commands.Collection;
using Domain.Contracts.Collection;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Transaction;
using Infrastructure.Extensions;
using Infrastructure.Models;
using Infrastructure.Models.Transaction;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.TEntities;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Collection
{
    public class OneShotPaymentService : IOneShotPaymentService
    {
        private readonly Dictionary<EnumProductType, IAccountOneShotPaymentHandler> _paymentStrategies;
        private readonly ITransactionClientService _transactionClientService;
        private readonly ILogger<OneShotPaymentService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly IInfrastructureSharedService _sharedService;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;

        public OneShotPaymentService(
            IEnumerable<IAccountOneShotPaymentHandler> strategies,
            ITransactionClientService transactionClientService,
            ILogger<OneShotPaymentService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            IInfrastructureSharedService sharedService,
            ISecurityContextProvider securityContextProvider,
            IServiceClient serviceClient)
        {
            _paymentStrategies = strategies.ToDictionary(s => s.AccountType, s => s);
            _transactionClientService = transactionClientService;
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _sharedService = sharedService;
            _securityContextProvider = securityContextProvider;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> ProcessOneShotPaymentAsync(OneShotPaymentCommand command)
        {
            _logger.LogInformation("Processing one-shot payment for MemberItemId: {MemberItemId} with amount: {PaidAmount}", command.MemberItemId, command.PaidAmount);

            var response = new CommandResponse();

            try
            {
                var remainingAmount = command.PaidAmount;

                var (gsAccount, csAccounts, csSchedules, loanAccounts, loanSchedules) = await FetchAccountAndSchedules(command.MemberItemId);

                var transactionCommands = new List<TransactionCommand>();
                var paymentDetails = new List<OneShotPaymentDetail>();

                ApplyLoanPayments(loanSchedules, loanAccounts, ref remainingAmount, transactionCommands, paymentDetails);
                ApplyContractualSavingsPayments(csSchedules, csAccounts, ref remainingAmount, transactionCommands, paymentDetails);
                ApplyGeneralSavingPayment(gsAccount, ref remainingAmount, transactionCommands, paymentDetails);

                await InitiateOneShotPaymentTransactionAsync(command, transactionCommands);

                var transactionRequests = await CreateTransactionRequestsAsync(command, paymentDetails);
                SendToHandleTransactionRequest(transactionRequests.Select(t => t.ItemId).ToList());

                _logger.LogInformation("One-shot payment completed for MemberItemId: {MemberItemId}", command.MemberItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing one-shot payment for MemberItemId: {MemberItemId}", command.MemberItemId);

                response.SetError(BuroErrorMessageKeys.ErrorOccurred, ex.Message);
            }

            return response;
        }

        private void SendToHandleTransactionRequest(List<string> transactionItemIds)
        {
            var payload = new ProductAccountTransactionRequestPostProcessingEvent
            {
                ProductAccountTransactionRequestItemIds = transactionItemIds,
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroTransactionQueue, payload);

            _logger.LogInformation("Transaction post processing initiated for Transaction ID: {TransactionItemId}", string.Join(", ", transactionItemIds));
        }

        private async Task<List<BuroProductAccountTransactionRequest>> CreateTransactionRequestsAsync(OneShotPaymentCommand command, List<OneShotPaymentDetail> paymentDetails)
        {
            _logger.LogInformation("Creating transaction requests for MemberItemId: {MemberItemId}", command.MemberItemId);

            var tractionRequests = new List<BuroProductAccountTransactionRequest>();

            var member = await _finMongoDbRepository.FindOneAsync<BuroMember>(x => x.ItemId == command.MemberItemId);
            var currentContext = _securityContextProvider.GetSecurityContext();
            var currentEmployee = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.UserItemId == currentContext.UserId);

            foreach (var payment in paymentDetails)
            {
                if (payment.Account is BuroLoanAccount loanAccount)
                {
                    var previousUnpaidSchedules = await _finMongoDbRepository
                        .FindAsync<BuroMemberSchedule>(x =>
                            x.AccountItemId == loanAccount.ItemId &&
                            x.PaymentStatus == PaymentStatus.Pending &&
                            x.ProductType == EnumProductType.Loan &&
                            x.OriginalPaymentDate.Date < DateTime.UtcNow.Date);

                    // NOSONAR WIP Need to create transaction for each previous unpaid schedule
                    // Also we need to break down the amount like overdue amount + late fee + payable amount and make transaction for each of them
                    if (previousUnpaidSchedules.Any()) { }
                }

                var requestDto = new CreateTransactionRequestDto
                {
                    TransactionItemId = Guid.NewGuid().ToString(),
                    MemberItemId = command.MemberItemId,
                    Amount = payment.RealizedAmount,
                    TransactionMedium = EnumTransactionMedium.Cash,
                    ProductType = payment.AccountType,
                    TransactionType = payment.AccountType switch 
                    { 
                        EnumProductType.GeneralSaving => EnumBuroTransactionType.GsDeposit,
                        EnumProductType.ContractualSaving => EnumBuroTransactionType.CsInstallment,
                        EnumProductType.Loan => EnumBuroTransactionType.LoanInstallmentRepayment,
                        _ => throw new NotSupportedException($"Product type {payment.AccountType} is not supported.")
                    },
                    AccountItemId = payment.Account.ItemId,
                    MemberScheduleItemId = payment.MemberSchedule.ItemId,
                    MessageCorrelationId = command.MessageCorrelationId,
                    IsTransactionRequired = false,
                };

                var tractionRequest = _sharedService.CreateTransactionRequestAsync(requestDto, currentEmployee, member, payment.Account);

                tractionRequests.Add(tractionRequest);
            }

            await _finMongoDbRepository.InsertManyAsync(tractionRequests);

            _logger.LogInformation("Transaction requests created for MemberItemId: {MemberItemId}, Count: {Count}, Transaction Ids: {TransactionRequestIds}", command.MemberItemId, tractionRequests.Count, string.Join(", ", tractionRequests.Select(t => t.ItemId)));

            return tractionRequests;
        }

        private async Task ProcessPaymentStrategies(string memberItemId, List<OneShotPaymentDetail> paymentDetails)
        {
            foreach (var detail in paymentDetails)
            {
                if (_paymentStrategies.TryGetValue(detail.AccountType, out var strategy))
                {
                    _logger.LogInformation("Processing strategy for AccountType: {AccountType}, AccountItemId: {AccountItemId}", detail.AccountType, detail.Account.ItemId);
                    await strategy.ProcessPaymentAsync(memberItemId, detail);
                }
            }
        }

        private async Task InitiateOneShotPaymentTransactionAsync(OneShotPaymentCommand command, List<TransactionCommand> transactionCommands)
        {
            var result = await _transactionClientService.InitiateTransactionAsync(new InitiateTransactionCommand
            {
                Transactions = transactionCommands,
                IsIndependentTransaction = false,
                MessageCorrelationId = command.MessageCorrelationId
            });

            if (!result.IsSuccess())
            {
                _logger.LogError("One-shot payment trnsaction failed for MemberItemId: {MemberItemId}", command.MemberItemId);

                throw new InvalidOperationException($"Transactions failed for one shot payment");
            }
        }

        private async Task<(
            BuroGeneralSavingsAccount?,
            List<BuroContractualSavingsAccount>,
            List<BuroMemberSchedule>,
            List<BuroLoanAccount>,
            List<BuroMemberSchedule>
        )> FetchAccountAndSchedules(string memberItemId)
        {
            var today = DateTime.UtcNow.Date;
            var startOfMonth = new DateTime(today.Year, today.Month, 1);
            var startOfNextMonth = startOfMonth.AddMonths(1);

            var gsAccount = await _finMongoDbRepository.FindOneAsync<BuroGeneralSavingsAccount>(x => x.MemberItemId == memberItemId);

            var csAccounts = await _finMongoDbRepository.FindAsync<BuroContractualSavingsAccount>(x => x.MemberItemId == memberItemId);
            var csAccountItemIds = csAccounts.Select(x => x.ItemId).ToList();

            var csFilter = BuildContractualScheduleFilter(csAccountItemIds, today, startOfMonth, startOfNextMonth);
            var csSchedules = await _finMongoDbRepository.FindAsync(csFilter);

            var loanAccounts = await _finMongoDbRepository.FindAsync<BuroLoanAccount>(x => x.MemberItemId == memberItemId);
            var loanSchedules = new List<BuroMemberSchedule>();

            foreach (var account in loanAccounts)
            {
                var schedule = await _finMongoDbRepository.FindOneAsync<BuroMemberSchedule>(
                    x => x.MemberItemId == memberItemId &&
                         x.PaymentStatus == PaymentStatus.Pending &&
                         x.ProductType == EnumProductType.Loan &&
                         x.ActualPaymentDate >= today);

                if (schedule != null)
                    loanSchedules.Add(schedule);
            }

            return (gsAccount, csAccounts, csSchedules, loanAccounts, loanSchedules);
        }

        private static FilterDefinition<BuroMemberSchedule> BuildContractualScheduleFilter(List<string> accountItemIds, DateTime today, DateTime startOfMonth, DateTime startOfNextMonth)
        {
            var builder = Builders<BuroMemberSchedule>.Filter;

            var baseFilter = builder.And(
                builder.Eq(x => x.PaymentStatus, PaymentStatus.Pending),
                builder.Eq(x => x.ProductType, EnumProductType.ContractualSaving),
                builder.In(x => x.AccountItemId, accountItemIds)
            );

            var weeklyFilter = builder.And(
                builder.Eq(x => x.PaymentInterval, EnumPaymentInterval.Weekly),
                builder.Eq(x => x.ActualPaymentDate, today)
            );

            var monthlyFilter = builder.And(
                builder.Eq(x => x.PaymentInterval, EnumPaymentInterval.Monthly),
                builder.Gte(x => x.ActualPaymentDate, startOfMonth),
                builder.Lt(x => x.ActualPaymentDate, startOfNextMonth)
            );

            return builder.And(baseFilter, builder.Or(weeklyFilter, monthlyFilter));
        }


        private void ApplyLoanPayments(List<BuroMemberSchedule> loanSchedules, List<BuroLoanAccount> loanAccounts,
            ref double remainingAmount, List<TransactionCommand> transactionRequests, List<OneShotPaymentDetail> paymentDetails)
        {
            foreach (var schedule in loanSchedules)
            {
                var account = loanAccounts.Find(a => a.ItemId == schedule.AccountItemId);
                if (account == null) continue;

                double payable = account.OverDueAmount + account.LateFeeAmount;

                if (DateTime.UtcNow.Date == schedule.OriginalPaymentDate.Date)
                {
                    payable += schedule.PayableAmount;
                }

                if (remainingAmount >= payable)
                {
                    _logger.LogInformation("Applying loan payment for AccountItemId: {AccountItemId}, Amount: {Amount}", account.ItemId, payable);

                    transactionRequests.Add(new TransactionCommand
                    {
                        TransactionId = Guid.NewGuid(),
                        AccountHolderNumber = account.MemberSequenceNumber,
                        AccountNumber = account.AccountSequenceNumber,
                        Amount = payable,
                        TransactionType = TransactionServiceConstants.TransactionTypeConstants.Deposit,
                        AccountType = TransactionServiceConstants.AccountTypeConstants.Loan,
                        Reference = EnumBuroTransactionType.LoanInstallmentRepayment.GetDisplayName(),
                    });

                    paymentDetails.Add(new OneShotPaymentDetail
                    {
                        AccountType = EnumProductType.Loan,
                        Account = account,
                        MemberSchedule = schedule,
                        RealizedAmount = payable
                    });

                    remainingAmount -= payable;
                }
            }
        }

        private void ApplyContractualSavingsPayments(List<BuroMemberSchedule> csSchedules, List<BuroContractualSavingsAccount> csAccounts,
            ref double remainingAmount, List<TransactionCommand> transactionRequests, List<OneShotPaymentDetail> paymentDetails)
        {
            foreach (var schedule in csSchedules)
            {
                var account = csAccounts.FirstOrDefault(a => a.ItemId == schedule.AccountItemId);
                if (account == null) continue;

                double payable = schedule.PayableAmount;

                if (remainingAmount >= payable)
                {
                    _logger.LogInformation("Applying contractual savings payment for AccountItemId: {AccountItemId}, Amount: {Amount}", account.ItemId, payable);

                    transactionRequests.Add(new TransactionCommand
                    {
                        TransactionId = Guid.NewGuid(),
                        AccountHolderNumber = account.MemberSequenceNumber,
                        AccountNumber = account.AccountSequenceNumber,
                        Amount = payable,
                        TransactionType = TransactionServiceConstants.TransactionTypeConstants.Deposit,
                        AccountType = TransactionServiceConstants.AccountTypeConstants.Current,
                        Reference = EnumBuroTransactionType.CsInstallment.GetDisplayName(),
                    });

                    paymentDetails.Add(new OneShotPaymentDetail
                    {
                        AccountType = EnumProductType.ContractualSaving,
                        Account = account,
                        MemberSchedule = schedule,
                        RealizedAmount = payable
                    });

                    remainingAmount -= payable;
                }
            }
        }

        private void ApplyGeneralSavingPayment(BuroGeneralSavingsAccount? gsAccount, ref double remainingAmount,
            List<TransactionCommand> transactionRequests, List<OneShotPaymentDetail> paymentDetails)
        {
            if (remainingAmount > 0 && gsAccount != null)
            {
                _logger.LogInformation("Depositing remaining amount to General Savings. AccountItemId: {AccountItemId}, Amount: {Amount}", gsAccount.ItemId, remainingAmount);

                transactionRequests.Add(new TransactionCommand
                {
                    TransactionId = Guid.NewGuid(),
                    AccountHolderNumber = gsAccount.MemberSequenceNumber,
                    AccountNumber = gsAccount.AccountSequenceNumber,
                    Amount = remainingAmount,
                    TransactionType = TransactionServiceConstants.TransactionTypeConstants.Deposit,
                    AccountType = TransactionServiceConstants.AccountTypeConstants.Savings,
                    Reference = EnumBuroTransactionType.GsDeposit.GetDisplayName(),
                });

                paymentDetails.Add(new OneShotPaymentDetail
                {
                    AccountType = EnumProductType.GeneralSaving,
                    Account = gsAccount,
                    RealizedAmount = remainingAmount
                });
            }
        }
    }
}
