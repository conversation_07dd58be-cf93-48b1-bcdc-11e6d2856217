using Domain.Commands.Collection;
using Infrastructure.Constants;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Collection
{
    public class ContractualSavingAccountOneShotPaymentHanlder : IAccountOneShotPaymentHandler
    {
        private readonly ILogger<ContractualSavingAccountOneShotPaymentHanlder> _logger;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;

        public ContractualSavingAccountOneShotPaymentHanlder(ILogger<ContractualSavingAccountOneShotPaymentHanlder> logger,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public EnumProductType AccountType => EnumProductType.ContractualSaving;

        public async Task<CommandResponse> ProcessPaymentAsync(string memberId, OneShotPaymentDetail paymentDetail)
        {
            var response = new CommandResponse();

            try
            {
                _logger.LogInformation("Processing full payment for MemberId: {MemberId}, AccountId: {AccountId}", memberId, paymentDetail.Account.ItemId);

                await UpdatePaymentAsync(paymentDetail);

                var trackPayementEvent = new TrackAccountPaymentEvent
                {
                    AccountPayments = new List<AccountPayment>
                    {
                        new()
                        {
                            MemberItemId = memberId,
                            AccountItemId = paymentDetail.Account.ItemId,
                            MemberScheduleItemId = paymentDetail.MemberSchedule.ItemId,
                            PaidAmount = paymentDetail.RealizedAmount,
                            ProductType = EnumProductType.ContractualSaving,
                        }
                    }
                };

                _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, trackPayementEvent);

                _logger.LogInformation("Payment processed successfully for MemberId: {MemberId}", memberId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing full payment for MemberId: {MemberId}", memberId);
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "An unexpected error occurred while processing the payment.");
            }

            return response;
        }

        private async Task UpdatePaymentAsync(OneShotPaymentDetail paymentDetail)
        {
            var csAccount = paymentDetail.Account as BuroContractualSavingsAccount ?? throw new InvalidCastException($"Expected BuroContractualSavingsAccount but got {paymentDetail.Account.GetType().Name}");

            var newBalance = csAccount.Balance + paymentDetail.RealizedAmount;

            var csAccountUpdates = new Dictionary<string, object>
            {
                { nameof(BuroContractualSavingsAccount.Balance), newBalance }
            };

            var scheduleUpdates = new Dictionary<string, object>
            {
                { nameof(BuroMemberSchedule.PaidAmount), paymentDetail.RealizedAmount },
                { nameof(BuroMemberSchedule.PaymentStatus), PaymentStatus.Paid }
            };

            await Task.WhenAll(
                _repository.UpdateAsync<BuroContractualSavingsAccount>(x => x.ItemId == paymentDetail.Account.ItemId, csAccountUpdates),
                _repository.UpdateAsync<BuroMemberSchedule>(x => x.ItemId == paymentDetail.MemberSchedule.ItemId, scheduleUpdates)
            );
        }
    }
}
