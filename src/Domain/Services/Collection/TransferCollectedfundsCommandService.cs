using Domain.Commands.Collection;
using Domain.Contracts.Collection;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Events.Collection;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Collection
{
    public class TransferCollectedfundsCommandService : ITransferCollectedfundsCommandService
    {
        private readonly ILogger<TransferCollectedfundsCommandService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;
        private readonly ISharedService _sharedService;

        public TransferCollectedfundsCommandService(
            ILogger<TransferCollectedfundsCommandService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider,
            IServiceClient serviceClient,
            ISharedService sharedService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _serviceClient = serviceClient;
            _sharedService = sharedService;
        }

        public async Task<CommandResponse> TransferCollectedfundsAsync(TransferCollectedfundsCommand command)
        {
            _logger.LogInformation("Start transfering collected funds");

            var commandResponse = new CommandResponse();

            try
            {
                switch (command.Status)
                {
                    case EnumCollectionFundTransferStatus.Submitted:
                        await HandleCollectionTransferSubmissionAsync(command);
                        break;
                    case EnumCollectionFundTransferStatus.Reverted:
                        await HandleCollectionTransferRevertAsync(command);
                        break;
                    default:
                        break;
                }

                SendToHandleCollectionFundTransferEvent(command);

                _logger.LogInformation("Successfully transfered collected Amount with transfer request Id: {CollectionFundTransferItemId}", command.CollectionFundTransferItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during collection fund transfer");

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while transfering collected funds.");
            }

            return commandResponse;
        }

        private async Task HandleCollectionTransferSubmissionAsync(TransferCollectedfundsCommand command)
        {
            var currentDate = DateTime.UtcNow; // NOSONAR Need to change this according to the Brach clock
            var currentEmployee = await GetCurrentEmployeeInformationAsync();
            var schedulePayments = await GetSchedulePaymentsAsync(currentEmployee.EmployeeItemId);

            var branchAccountantEmployeeInformation = await GetDesignationSpecificEmployeeAsyn(
                currentEmployee.EmployeeOfficeItemId, BuroDesignationConstant.BranchAccountantDesignationCode);

            var branchManagerEmployeeInformation = await GetDesignationSpecificEmployeeAsyn(
                currentEmployee.EmployeeOfficeItemId, BuroDesignationConstant.BranchManagerDesignationCode);

            var totalTransferableAmount = schedulePayments.Sum(p => p.PaidAmount);

            var fundTransferRequest = new BuroCollectionFundTransfer
            {
                ItemId = command.CollectionFundTransferItemId,
                CollectedAmount = totalTransferableAmount,
                BranchOfficeItemId = currentEmployee.EmployeeOfficeItemId,
                BranchOfficeCode = currentEmployee.EmployeeOfficeCode,
                BranchOfficeName = currentEmployee.EmployeeOfficeName,
                ProgramOrganizerEmployeeInformation = currentEmployee,
                BranchAccountantEmployeeInformation = branchAccountantEmployeeInformation,
                BranchManagerEmployeeInformation = branchManagerEmployeeInformation,
                Status = command.Status,
                BranchDate = currentDate
            };

            await _finMongoDbRepository.InsertOneAsync(fundTransferRequest);

            _logger.LogInformation("Successfully requested for fund transfer with amount {TotalTransferableAmount}", totalTransferableAmount);
        }

        private async Task HandleCollectionTransferRevertAsync(TransferCollectedfundsCommand command)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCollectionFundTransfer.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroCollectionFundTransfer.LastUpdatedBy), securityContext.UserId },
                {nameof(BuroCollectionFundTransfer.Status), command.Status}
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroCollectionFundTransfer>(t => t.ItemId == command.CollectionFundTransferItemId, properties);
        }

        private void SendToHandleCollectionFundTransferEvent(TransferCollectedfundsCommand command)
        {
            var payload = new CollectionFundTransferPostProcessingEvent
            {
                CollectionFundTransferItemId = command.CollectionFundTransferItemId,
                Status = command.Status,
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("Collection fund transfer post processing initiated for Transfer Item ID: {CollectionFundTransferItemId}", command.CollectionFundTransferItemId);
        }

        private async Task<BuroActionByEmployeeInformation> GetCurrentEmployeeInformationAsync()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var employees = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, BuroActionByEmployeeInformation>(
                dataFilters: e => e.UserItemId == securityContext.UserId,
                projectionExpression: e => new BuroActionByEmployeeInformation
                {
                    EmployeeItemId = e.ItemId,
                    EmployeePin = e.EmployeePin,
                    EmployeeName = e.EmployeeName,
                    EmployeeDesginationItemId = e.DesignationItemId,
                    EmployeeDesginationTitle = e.DesignationTitle,
                    EmployeeOfficeItemId = e.CurrentOfficeItemId,
                    EmployeeOfficeName = e.CurrentOfficeTitle,
                    EmployeeOfficeCode = e.CurrentOfficeCode
                });

            return employees.FirstOrDefault() ?? default!;
        }

        private async Task<BuroActionByEmployeeInformation> GetDesignationSpecificEmployeeAsyn(string currentOfficeItemId, string designationCode)
        {
            var employee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(currentOfficeItemId, designationCode);

            return new BuroActionByEmployeeInformation
            {
                EmployeeItemId = employee.ItemId,
                EmployeePin = employee.EmployeePin,
                EmployeeName = employee.EmployeeName,
                EmployeeDesginationItemId = employee.DesignationItemId,
                EmployeeDesginationTitle = employee.DesignationTitle,
                EmployeeOfficeItemId = employee.CurrentOfficeItemId,
                EmployeeOfficeName = employee.CurrentOfficeTitle,
                EmployeeOfficeCode = employee.CurrentOfficeCode
            };
        }

        private async Task<List<BuroMemberAccountPayment>> GetSchedulePaymentsAsync(string currentEmployeeItemId)
        {
            return await _finMongoDbRepository.FindAsync<BuroMemberAccountPayment>(
                p => p.ProgramOrganizerEmployeeItemId == currentEmployeeItemId
                  && p.CreateDate.Date == DateTime.UtcNow.Date);
        }
    }
}
