using Domain.Commands.Collection;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Collection
{
    public class LoanAccountOneShotPaymentHandler : IAccountOneShotPaymentHandler
    {
        private readonly ILogger<LoanAccountOneShotPaymentHandler> _logger;
        private readonly IRepository _repository;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly IServiceClient _serviceClient;

        public LoanAccountOneShotPaymentHandler(ILogger<LoanAccountOneShotPaymentHandler> logger,
            IRepository repository,
            IFinMongoDbRepository finMongoDbRepository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _finMongoDbRepository = finMongoDbRepository;
            _serviceClient = serviceClient;
        }
        public EnumProductType AccountType => EnumProductType.Loan;

        public async Task<CommandResponse> ProcessPaymentAsync(string memberId, OneShotPaymentDetail paymentDetail)
        {
            var response = new CommandResponse();

            try
            {
                _logger.LogInformation("Processing loan payment for MemberId: {MemberId}, AccountId: {AccountId}", memberId, paymentDetail.Account.ItemId);

                await UpdatePaymentAsync(paymentDetail);

                _logger.LogInformation("loan payment processed successfully for MemberId: {MemberId}, AccountId: {AccountId}", memberId, paymentDetail.Account.ItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing loan payment for MemberId: {MemberId}, AccountId: {AccountId}", memberId, paymentDetail.Account.ItemId);
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "An unexpected error occurred while processing the payment.");
            }

            return response;
        }

        private async Task UpdatePaymentAsync(OneShotPaymentDetail paymentDetail)
        {
            var loanAccount = paymentDetail.Account as BuroLoanAccount
                ?? throw new InvalidCastException($"Expected BuroLoanAccount but got {paymentDetail.Account.GetType().Name}");

            var memberSchedule = paymentDetail.MemberSchedule;

            var previousUnpaidSchedules = await _finMongoDbRepository
                .FindAsync<BuroMemberSchedule>(x =>
                    x.AccountItemId == loanAccount.ItemId &&
                    x.PaymentStatus == PaymentStatus.Pending &&
                    x.AccountCode == "LOAN" &&
                    x.OriginalPaymentDate.Date < DateTime.UtcNow.Date);

            var accountPayments = new List<AccountPayment>();
            var previousScheduleUpdateTasks = previousUnpaidSchedules.Select(schedule =>
            {
                accountPayments.Add(new AccountPayment
                {
                    MemberItemId = schedule.MemberItemId,
                    AccountItemId = schedule.AccountItemId,
                    MemberScheduleItemId = schedule.ItemId,
                    PaidAmount = schedule.PayableAmount,
                    ProductType = EnumProductType.Loan
                });

                var updates = new Dictionary<string, object>
                {
                    { nameof(BuroMemberSchedule.PaidAmount), schedule.PayableAmount },
                    { nameof(BuroMemberSchedule.PaymentStatus), PaymentStatus.Paid }
                };

                return _repository.UpdateAsync<BuroMemberSchedule>(x => x.ItemId == schedule.ItemId, updates);
            });

            var loanAccountUpdates = new Dictionary<string, object>
            {
                { nameof(BuroLoanAccount.OverDueAmount), 0 },
                { nameof(BuroLoanAccount.LateFeeAmount), 0 },
            };

            var scheduleUpdates = new Dictionary<string, object>
            {
                { nameof(BuroMemberSchedule.PaidAmount), memberSchedule.PayableAmount },
                { nameof(BuroMemberSchedule.PaymentStatus), PaymentStatus.Paid }
            };

            var updateTasks = previousScheduleUpdateTasks
                .Append(_repository.UpdateAsync<BuroLoanAccount>(x => x.ItemId == loanAccount.ItemId, loanAccountUpdates))
                .Append(_repository.UpdateAsync<BuroMemberSchedule>(x => x.ItemId == memberSchedule.ItemId, scheduleUpdates));

            await Task.WhenAll(updateTasks);

            var trackPayementEvent = new TrackAccountPaymentEvent
            {
                AccountPayments = accountPayments
            };

            _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, trackPayementEvent);
        }

    }
}
