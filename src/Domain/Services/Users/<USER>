using Domain.Commands.User;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Users;

public class CreateUserPermissionService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ILogger<CreateUserPermissionService> _logger;

    public CreateUserPermissionService(IRepository repository, ISecurityContextProvider securityContextProvider,
        ILogger<CreateUserPermissionService> logger)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
        _logger = logger;
    }

    public async Task<CommandResponse> CreateUserPermissionAsync(CreateUserPermissionCommand command)
    {
        _logger.LogInformation("Inside CreateUserPermission");
        var commandResponse = new CommandResponse();
        try
        {
            await DeleteExistingUserPermission(command.UserItemId);
            var userPermission = CreateUserPermissionData(command);
            await _repository.SaveAsync(userPermission);
            _logger.LogInformation("Finished CreateUserPermission");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error in CreateUserPermission");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }
        return commandResponse;

    }

    private async Task DeleteExistingUserPermission(string userItemId)
    {
        if (!userItemId.IsNullOrEmptyOrWhiteSpace())
        {
            await _repository.DeleteAsync<BuroUserPermission>(x => x.UserItemId == userItemId);
        }
    }

    private BuroUserPermission CreateUserPermissionData(CreateUserPermissionCommand command)
    {
        var loggedInUserId = _securityContextProvider.GetSecurityContext().UserId;
        var userPermission = new BuroUserPermission
        {
            ItemId = Guid.NewGuid().ToString(),
            UserItemId = command.UserItemId,
            FeatureRoleItemId = command.FeatureRoleItemId,
            Features = command.Features.Select(x => new FeatureForPermission
            {
                ItemId = x.FeatureItemId,
                FeatureName = x.FeatureName,
                FeaturePath = x.FeaturePath,
                ApiPath = x.ApiPath,
                IconName = x.IconName,
            }).ToList(),
            CreatedBy = loggedInUserId,
            CreateDate = DateTime.UtcNow
        };
        return userPermission;
    }
}