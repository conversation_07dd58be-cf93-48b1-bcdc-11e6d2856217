using Domain.Contracts.User;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Selise.Ecap.Entities.PrimaryEntities.PlatformDataService;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.Uam.Commands;

namespace Domain.Services.Users;

public class UserService : IUserService
{
    private readonly ILogger<UserService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IUamAdapter _uamAdapter;

    public UserService(IBusinessConfig businessConfig,
        IUamAdapter uamAdapter,
        ISecurityContextProvider securityContextProvider,
        ILogger<UserService> logger)
    {
        _uamAdapter = uamAdapter;
        _securityContextProvider = securityContextProvider;
        _logger = logger;
    }


    public async Task<CommandResponse> CreateUserFromEmployee(BuroEmployee employee, string userId)
    {
        var commandResponse = new CommandResponse();
        _logger.LogInformation("Creating user for employee with EmployeePin: {EmployeePin}", employee.EmployeePin);
        var context = _securityContextProvider.GetSecurityContext();
        var userCommand = PrepareCreateUserCommand(employee, userId, context);
        var response = await _uamAdapter.CreateUser(userCommand, null);
        if (!response)
        {
            _logger.LogInformation("Failed to create user for employee with pin: {EmployeePin}", employee.EmployeePin);
            commandResponse.SetError(BuroErrorMessageKeys.UserCreationFailed, "Failed to create user");
            return commandResponse;
        }
        _logger.LogInformation("User creation response: {Response}", response);
        return commandResponse;
    }


    private CreateUserCommand PrepareCreateUserCommand(BuroEmployee employee, string userId, SecurityContext context)
    {
        return new CreateUserCommand
        {
            ItemId = userId,
            UserName = employee.EmployeePin,
            PhoneNumber = employee.WorkPhone.IsNullOrEmptyOrWhiteSpace() ? employee.PersonalPhone : employee.WorkPhone,
            CountryCode = "BD",
            Email = $"{employee.EmployeePin}@buroBd.com",
            Password = Guid.NewGuid().ToString(),
            FirstName = employee.EmployeeName,
            LastName = employee.EmployeeName,
            DisplayName = employee.EmployeeName,
            RegisteredBy = RegisteredBy.AdminCommand,
            CreateDate = DateTime.UtcNow,
            LastUpdateDate = DateTime.UtcNow,
            CreatedBy = context.UserId,
            LastUpdatedBy = context.UserId,
            HostDomain = context.RequestOrigin,
            Roles = new[] { UserRoles.AppUser },
            Tags = new[] { Tags.IsAEmployee },
            TenantId = BuroConstants.TenantId,
            Language = BuroConstants.DefaultLanguage,
            MailPurpose = Guid.NewGuid().ToString(),
        };
    }
    private CreateUserCommand PrepareCreateUserCommand(string userId, Person person, BuroEmployee employee, SecurityContext context)
    {
        return new CreateUserCommand
        {
            ItemId = userId,
            UserName = employee.EmployeePin,
            PhoneNumber = employee.WorkPhone.IsNullOrEmptyOrWhiteSpace() ? employee.PersonalPhone : employee.WorkPhone,
            CountryCode = "BD",
            Email = $"{employee.EmployeePin}@burobd.com",
            Password = Guid.NewGuid().ToString(),
            FirstName = employee.EmployeeName,
            LastName = employee.EmployeeName,
            DisplayName = employee.EmployeeName,
            RegisteredBy = RegisteredBy.CreateFromProposedUserId,
            CreateDate = DateTime.UtcNow,
            LastUpdateDate = DateTime.UtcNow,
            CreatedBy = context.UserId,
            LastUpdatedBy = context.UserId,
            HostDomain = context.RequestOrigin,
            Roles = person.Roles,
            Tags = new[] { Tags.IsAEmployee },
            TenantId = BuroConstants.TenantId,
            Language = BuroConstants.DefaultLanguage,
            MailPurpose = Guid.NewGuid().ToString()
        };
    }

    public async Task<CommandResponse> CreateUserFromPerson(string userId, Person person, BuroEmployee employee)
    {
        var commandResponse = new CommandResponse();
        _logger.LogInformation("UserService->CreateUserFromPerson->START", person.ItemId);
        var context = _securityContextProvider.GetSecurityContext();
        var userCommand = PrepareCreateUserCommand(userId, person, employee, context);
        var response = await _uamAdapter.CreateUser(userCommand, null);
        if (!response)
        {
            _logger.LogError("UserService->CreateUserFromPerson-> Failed to create user for employee with pin: " + employee.EmployeePin);
            commandResponse.SetError(BuroErrorMessageKeys.UserCreationFailed, "Failed to create user");
            return commandResponse;
        }
        _logger.LogInformation("User creation response: {Response}", response);
        return commandResponse;
    }



}