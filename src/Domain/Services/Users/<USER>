using Domain.Commands.User;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Users;

public class UpdateUserPermissionService
{
    private readonly IBusinessRepository _businessRepository;
    private readonly ILogger<UpdateUserPermissionService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateUserPermissionService(ILogger<UpdateUserPermissionService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository,
        IBusinessRepository businessRepository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _businessRepository = businessRepository;
    }

    public async Task<CommandResponse> UpdateUserPermissionAsync(UpdateUserPermissionCommand command)
    {
        _logger.LogInformation("Inside UpdateUserPermissionAsync");
        var commandResponse = new CommandResponse();
        try
        {
            if (!await ValidateUserPermissionExists(command.UserPermissionItemId, commandResponse))
                return commandResponse;

            var updateFeaturesModel = await GetFeaturesToUpdate(command);

            await UpdateUserPermissionAsync(command, updateFeaturesModel);
            _logger.LogInformation("Finished UpdateUserPermissionAsync");
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error in UpdateUserPermissionAsync");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error Occurred");
        }

        return commandResponse;
    }

    private async Task<bool> ValidateUserPermissionExists(string userPermissionItemId, CommandResponse response)
    {
        var exists = await _repository.ExistsAsync<BuroUserPermission>(x => x.ItemId == userPermissionItemId);
        if (!exists)
        {
            _logger.LogWarning("User permission with ID {ItemId} does not exist.", userPermissionItemId);
            response.SetError(BuroErrorMessageKeys.UserPermissionDoesntExist, "User permission does not exist");
        }

        return exists;
    }

    private async Task<UpdateFeaturesModel> GetFeaturesToUpdate(UpdateUserPermissionCommand command)
    {
        var featuresModel = new UpdateFeaturesModel();
        var user = await GetUserByItemIdAsync(command.UserItemId);
        if (user == null || user.Roles.Length == 0)
        {
            _logger.LogWarning("User or roles not found in PrepareFeaturesToUpdate for userId {UserId}",
                command.UserItemId);
            return featuresModel;
        }

        var mappedFeatures = await GetMappedFeaturesAsync(user);
        var mappedFeatureItemIds = new HashSet<string>(mappedFeatures.Select(f => f.ItemId));

        var defaultFeatures = ExtractDefaultFeatures(command, mappedFeatures);

        var missingRequiredFeatures = FindMissingRequiredFeatures(command, mappedFeatures);

        var customFeatures = ExtractCustomFeaturesFromCommand(command, mappedFeatures);

        var featuresToUpdate = new List<FeatureForPermission>(defaultFeatures);
        featuresToUpdate.AddRange(missingRequiredFeatures);
        featuresToUpdate.AddRange(customFeatures);

        var isCustom = featuresToUpdate.Any(f => !mappedFeatureItemIds.Contains(f.ItemId));

        featuresModel.Features = featuresToUpdate.Distinct().ToList();
        featuresModel.HasCustomFeatures = isCustom;

        return featuresModel;
    }

    private async Task UpdateUserPermissionAsync(UpdateUserPermissionCommand command,
        UpdateFeaturesModel updateFeaturesModel)
    {
        var updatedProperties = UpdatedProperties(command, updateFeaturesModel);
        await _repository.UpdateAsync<BuroUserPermission>(
            x => x.ItemId == command.UserPermissionItemId,
            updatedProperties
        );
    }

    private async Task<User?> GetUserByItemIdAsync(string userItemId)
    {
        return await _repository.GetItemAsync<User>(item => item.ItemId == userItemId);
    }

    private async Task<List<FeatureForRoleMap>> GetMappedFeaturesAsync(User user)
    {
        var collection = _businessRepository.GetCollection<BuroFeatureRoleMap>();
        var filter = Builders<BuroFeatureRoleMap>.Filter.In(x => x.RoleKey, user.Roles);
        var featureRoleMaps = await collection.Find(filter).ToListAsync();
        return featureRoleMaps.SelectMany(x => x.Features).Distinct().ToList();
    }

    private List<FeatureForPermission> ExtractDefaultFeatures(UpdateUserPermissionCommand command,
        List<FeatureForRoleMap> featuresFromRoleMap)
    {
        return command.Features
            .Where(featureCommand => IsFeatureAllowed(featureCommand, featuresFromRoleMap))
            .Select(featureCommand => ExtractFeature(featureCommand, false))
            .ToList();
    }

    private bool IsFeatureAllowed(FeatureCommand featureCommand, List<FeatureForRoleMap> featuresFromRoleMap)
    {
        return featuresFromRoleMap.Any(featureRoleMap =>
            featureRoleMap.ItemId == featureCommand.FeatureItemId &&
            featureRoleMap.FeatureStatus != EnumFeatureStatus.Restricted);
    }


    private List<FeatureForPermission> ExtractCustomFeaturesFromCommand(UpdateUserPermissionCommand command,
        List<FeatureForRoleMap> featuresFromRoleMap)
    {
        var result = command.Features
            .Where(featureCommand =>
                featuresFromRoleMap.All(featureRoleMap => featureRoleMap.ItemId != featureCommand.FeatureItemId))
            .Select(featureCommand => ExtractFeature(featureCommand, true));
        return result.ToList();
    }

    private List<FeatureForPermission> FindMissingRequiredFeatures(
        UpdateUserPermissionCommand command,
        List<FeatureForRoleMap> featuresFromRoleMap)
    {
        return featuresFromRoleMap
            .Where(feature => IsFeatureRequiredAndMissingInCommand(feature, command))
            .Select(feature => ExtractFeature(feature, false))
            .ToList();
    }

    private bool IsFeatureRequiredAndMissingInCommand(FeatureForRoleMap feature, UpdateUserPermissionCommand command)
    {
        return command.Features.All(f => f.FeatureItemId != feature.ItemId)
               && feature.FeatureStatus == EnumFeatureStatus.Required;
    }

    private FeatureForPermission ExtractFeature(FeatureForRoleMap feature, bool isCustom)
    {
        return new FeatureForPermission
        {
            ItemId = feature.ItemId,
            FeatureName = feature.FeatureName,
            FeaturePath = feature.FeaturePath,
            ApiPath = feature.ApiPath,
            IconName = feature.IconName,
            IsCustomFeature = isCustom
        };
    }

    private FeatureForPermission ExtractFeature(FeatureCommand featureCommand, bool isCustom)
    {
        return new FeatureForPermission
        {
            ItemId = featureCommand.FeatureItemId,
            FeatureName = featureCommand.FeatureName,
            FeaturePath = featureCommand.FeaturePath,
            ApiPath = featureCommand.ApiPath,
            IconName = featureCommand.IconName,
            IsCustomFeature = isCustom
        };
    }

    private Dictionary<string, object> UpdatedProperties(UpdateUserPermissionCommand command,
        UpdateFeaturesModel featuresModel)
    {
        var loggedInUserId = _securityContextProvider.GetSecurityContext().UserId;
        var features = featuresModel.Features.Select(x => new BsonDocument
        {
            { nameof(FeatureForPermission.ItemId), x.ItemId },
            { nameof(FeatureForPermission.FeatureName), x.FeatureName },
            { nameof(FeatureForPermission.FeaturePath), x.FeaturePath },
            { nameof(FeatureForPermission.ApiPath), x.ApiPath },
            { nameof(FeatureForPermission.IsCustomFeature), x.IsCustomFeature }
        }).ToList();

        var properties = new Dictionary<string, object>
        {
            { nameof(BuroUserPermission.UserItemId), command.UserItemId },
            { nameof(BuroUserPermission.IsCustom), featuresModel.HasCustomFeatures },
            { nameof(BuroUserPermission.FeatureRoleItemId), command.FeatureRoleItemId },
            { nameof(BuroUserPermission.Features), features },
            { nameof(BuroUserPermission.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroUserPermission.LastUpdatedBy), loggedInUserId }
        };

        return properties;
    }
}

public class UpdateFeaturesModel
{
    public List<FeatureForPermission> Features { get; set; } = new();
    public bool HasCustomFeatures { get; set; }
}