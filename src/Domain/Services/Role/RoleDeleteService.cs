using Domain.Commands.Role;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Roles;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Role;

public class RoleDeleteService
{
    private readonly IBusinessRepository _businessRepository;
    private readonly IServiceClient _serviceClient;
    public RoleDeleteService(IBusinessRepository businessRepository,
        IServiceClient serviceClient)
    {
        _businessRepository = businessRepository;
        _serviceClient = serviceClient;
    }

    public async Task<CommandResponse> DeleteRoleAsync(RoleDeleteCommand deleteRoleCommand)
    {
        var response = new CommandResponse();
        var role = await _businessRepository.GetItemAsync<BuroRole>(item => item.ItemId == deleteRoleCommand.RoleItemId);
        if (role == null)
        {
            response.SetError(BuroErrorMessageKeys.RoleNotFound, "Role not found");
            return response;
        }
        DeleteRoleAndRelatedData(role.RoleKey);
        return response;
    }

    private void DeleteRoleAndRelatedData(string roleKey)
    {
        var roleAndRelatedDataDelete = new RoleAndRelatedDataDeleteEvent
        {
            RoleKey = roleKey
        };

        _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, roleAndRelatedDataDelete);
    }
}