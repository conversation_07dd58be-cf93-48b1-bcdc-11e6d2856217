using Domain.Commands;
using Domain.Commands.Role;
using Domain.Contracts.Role;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Role;

public class CreateRoleService : RoleBaseService
{
    private readonly IRepository _repository;
    private readonly ICreateFeatureRoleMapService _createFeatureRoleMapService;

    public CreateRoleService(IRepository repository, ICreateFeatureRoleMapService createFeatureRoleMapService)
    {
        _repository = repository;
        _createFeatureRoleMapService = createFeatureRoleMapService;
    }

    public async Task CreateRole(CreateRoleCommand createRoleCommand)
    {
        var role = PopulateRole(createRoleCommand);
        await _repository.SaveAsync(role);
        await CreateFeatureRoleMapAsync(role);
        await CreateRoleInBlocks(role.RoleKey);
    }

    private BuroRole PopulateRole(CreateRoleCommand createRoleCommand)
    {
        return new BuroRole
        {
            ItemId = Guid.NewGuid().ToString(),
            RoleName = createRoleCommand.RoleName,
            RoleKey = GenerateRoleKey(createRoleCommand.RoleName),
            DesignationItemIds = new List<string>()
        };
    }

    private async Task CreateFeatureRoleMapAsync(BuroRole role)
    {
        var createFeatureRoleMapCommand = new CreateFeatureRoleMapCommand
        {
            RoleItemId = role.ItemId,
            RoleName = role.RoleName,
            RoleKey = role.RoleKey,
            FeatureRequests = new List<FeatureRequestWithStatus>()
        };

        await _createFeatureRoleMapService.CreateFeatureRoleMapAsync(createFeatureRoleMapCommand);
    }

    private async Task CreateRoleInBlocks(string roleKey)
    {
        var role = new Selise.Ecap.Entities.PrimaryEntities.Security.Role()
        {
            ItemId = Guid.NewGuid().ToString(),
            RoleName = roleKey

        };

        await _repository.SaveAsync(role);
    }

}