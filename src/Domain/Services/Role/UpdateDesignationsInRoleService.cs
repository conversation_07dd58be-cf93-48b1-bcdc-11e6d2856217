using Domain.Commands.Role;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Roles;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Role;

public class UpdateDesignationsInRoleService
{
    private readonly ILogger<UpdateDesignationsInRoleService> _logger;
    private readonly IRepository _repository;
    private readonly IServiceClient _serviceClient;
    private readonly IBusinessRepository _businessRepository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateDesignationsInRoleService(ILogger<UpdateDesignationsInRoleService> logger,
        IRepository repository,
        IServiceClient serviceClient,
        IBusinessRepository businessRepository,
        ISecurityContextProvider securityContextProvider)
    {
        _logger = logger;
        _repository = repository;
        _serviceClient = serviceClient;
        _businessRepository = businessRepository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task<CommandResponse> UpdateDesignationRoleMap(
        UpdateDesignationsInRoleCommand command)
    {
        _logger.LogInformation("Inside UpdateDesignationRoleMap with RoleItemId: {DesignationItemId}", command.RoleItemId);
        var response = new CommandResponse();
        var role = await _businessRepository.GetItemAsync<BuroRole>(item => item.ItemId == command.RoleItemId);
        var designationsToBeRemoved = role.DesignationItemIds.Except(command.DesignationItemIds);
        await UpdateDesignationsInRoles(command);

        UpdateUserRolesAndPermission(command.RoleItemId, designationsToBeRemoved.ToList());

        return response;
    }

    private async Task UpdateDesignationsInRoles(UpdateDesignationsInRoleCommand updateDesignationsInRoleCommand)
    {
        var context = _securityContextProvider.GetSecurityContext();
        var updatedProperties = new Dictionary<string, object>
        {
            {nameof(BuroRole.DesignationItemIds), updateDesignationsInRoleCommand.DesignationItemIds},
            {nameof(BuroRole.LastUpdatedBy), context.UserId},
            {nameof(BuroRole.LastUpdateDate), DateTime.UtcNow}
        };

        await _repository.UpdateAsync<BuroRole>(item => item.ItemId == updateDesignationsInRoleCommand.RoleItemId, updatedProperties);
    }

    private void UpdateUserRolesAndPermission(string roleItemId, List<string> designationItemIds)
    {
        var updateUserRoleEvent = new UserRolesUpdateEvent
        {
            RoleItemId = roleItemId,
            RemovedDesignationIds = designationItemIds
        };

        _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, updateUserRoleEvent);
    }
}