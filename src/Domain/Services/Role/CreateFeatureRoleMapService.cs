using Domain.Commands.Role;
using Domain.Contracts.Role;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Role
{
    public class CreateFeatureRoleMapService : ICreateFeatureRoleMapService
    {
        private readonly ILogger<CreateFeatureRoleMapService> _logger;
        private readonly IRepository _repository;

        public CreateFeatureRoleMapService(
            ILogger<CreateFeatureRoleMapService> logger,
            IRepository repository)
        {
            _logger = logger;
            _repository = repository;
        }

        public async Task<CommandResponse> CreateFeatureRoleMapAsync(CreateFeatureRoleMapCommand command)
        {
            _logger.LogInformation("Starting CreateFeatureRoleMapAsync for Role ID: {RoleItemId}", command.RoleItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var newFeatureRoleMap = CreateFeatureRoleMapData(command);

                await _repository.SaveAsync(newFeatureRoleMap);

                _logger.LogInformation("Successfully created FeatureRoleMap with Role ID: {RoleItemId}", command.RoleItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during FeatureRoleMap Creation for role ID: {RoleItemId}", command.RoleItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the role.");
            }

            return commandResponse;
        }

        private static BuroFeatureRoleMap CreateFeatureRoleMapData(CreateFeatureRoleMapCommand command)
        {
            var newRoleMap = new BuroFeatureRoleMap
            {
                RoleItemId = command.RoleItemId,
                RoleName = command.RoleName,
                RoleKey = command.RoleKey,
                CreateDate = DateTime.UtcNow,
                Features = CreateFeatureData(command.FeatureRequests)
            };

            newRoleMap.AddEntityBasicInfo();

            return newRoleMap;
        }

        private static List<FeatureForRoleMap> CreateFeatureData(List<FeatureRequestWithStatus> features)
        {
            return features.Select(f => new FeatureForRoleMap
            {
                ItemId = f.FeatureItemId,
                FeatureName = f.FeatureName,
                FeaturePath = f.FeaturePath,
                ApiPath = f.ApiPath,
                FeatureStatus = f.FeatureStatus,
                IconName = f.IconName
            }).ToList();
        }
    }
}
