using Domain.Commands.Role;
using Domain.Contracts.Role;
using Infrastructure.Constants;
using Infrastructure.Events.UserPermission;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Role;

public class UpdateFeatureRoleMapService : IUpdateFeatureRoleMapService
{
    private readonly ILogger<UpdateFeatureRoleMapService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IServiceClient _serviceClient;

    public UpdateFeatureRoleMapService(
        ILogger<UpdateFeatureRoleMapService> logger,
        ISecurityContextProvider securityContextProvider,
        IServiceClient serviceClient,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _serviceClient = serviceClient;
        _repository = repository;
    }

    public async Task<CommandResponse> UpdateFeatureRoleMapAsync(UpdateFeatureRoleMapCommand command)
    {
        _logger.LogInformation("Starting UpdateFeatureRoleMapAsync for Role ID: {RoleItemId}", command.RoleItemId);

        var commandResponse = new CommandResponse();

        try
        {
            var properties = await PrepareFeatureRoleMapPropertiesToUpdateAsync(command);

            await _repository.UpdateAsync<BuroFeatureRoleMap>(f => f.ItemId == command.FeatureRoleMapItemId,
                properties);

            UpdateRoleRelatedUserPermission(command.RoleKey);

            _logger.LogInformation("Successfully updated FeatureRoleMap with Role ID: {RoleItemId}",
                command.RoleItemId);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during FeatureRoleMap Update for role ID: {RoleItemId}",
                command.RoleItemId);

            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the role.");
        }

        return commandResponse;
    }

    private void UpdateRoleRelatedUserPermission(string roleKey)
    {
        var command = new SyncUserPermissionByRolesEvent
        {
            RoleKeys = new List<string>()
            {
                roleKey
            }
        };

        _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, command);
    }

    private string GetCurrentUserId()
    {
        var securityContext = _securityContextProvider.GetSecurityContext();

        return securityContext.UserId;
    }

    private async Task<Dictionary<string, object>> PrepareFeatureRoleMapPropertiesToUpdateAsync(
        UpdateFeatureRoleMapCommand command)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroFeatureRoleMap.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroFeatureRoleMap.LastUpdatedBy), GetCurrentUserId() }
        };

        if (!command.RoleItemId.IsNullOrEmptyOrWhiteSpace())
            properties.Add(nameof(BuroFeatureRoleMap.RoleItemId), command.RoleItemId);

        if (!command.RoleName.IsNullOrEmptyOrWhiteSpace())
            properties.Add(nameof(BuroFeatureRoleMap.RoleName), command.RoleName);

        if (!command.RoleKey.IsNullOrEmptyOrWhiteSpace())
            properties.Add(nameof(BuroFeatureRoleMap.RoleKey), command.RoleKey);

        await PrepareFeaturesToUpdateAsync(properties, command);

        return properties;
    }

    private async Task PrepareFeaturesToUpdateAsync(
        Dictionary<string, object> properties,
        UpdateFeatureRoleMapCommand command)
    {
        var featureRequestsBsonData = await GetBuroFeatureBsonDataAsync(command);
        properties.Add(nameof(BuroFeatureRoleMap.Features), featureRequestsBsonData);
    }

    private async Task<BsonArray> GetBuroFeatureBsonDataAsync(UpdateFeatureRoleMapCommand command)
    {
        var featureRequests = command is UpdateFeatureRoleMapWithStatusCommand commandWithStatus
            ? CreateFeatures(commandWithStatus.FeatureRequests)
            : await CreateFeaturesWithExistingStatusAsync(command);

        return new BsonArray(featureRequests.Select(f => f.ToBsonDocument()));
    }

    private async Task<List<FeatureForRoleMap>> CreateFeaturesWithExistingStatusAsync(
        UpdateFeatureRoleMapCommand command)
    {
        var features = new List<FeatureForRoleMap>();

        var featureRoleMappings = command.FeatureRequests.Any()
            ? await _repository.GetItemAsync<BuroFeatureRoleMap>(f => f.ItemId == command.FeatureRoleMapItemId)
            : null;

        if (featureRoleMappings == null) return features;

        foreach (var feature in command.FeatureRequests)
        {
            var currentFeatureStatus = featureRoleMappings.Features
                .Where(frm => frm.ItemId == feature.FeatureItemId)
                .Select(frm => frm.FeatureStatus)
                .FirstOrDefault();

            features.Add(new FeatureForRoleMap
            {
                ItemId = feature.FeatureItemId,
                FeatureName = feature.FeatureName,
                FeaturePath = feature.FeaturePath,
                ApiPath = feature.ApiPath,
                IconName = feature.IconName,
                FeatureStatus = currentFeatureStatus
            });
        }

        return features;
    }

    private static List<FeatureForRoleMap> CreateFeatures(List<FeatureRequestWithStatus> features)
    {
        return features.Select(f => new FeatureForRoleMap
        {
            ItemId = f.FeatureItemId,
            FeatureName = f.FeatureName,
            FeaturePath = f.FeaturePath,
            ApiPath = f.ApiPath,
            FeatureStatus = f.FeatureStatus,
            IconName = f.IconName
        }).ToList();
    }
}