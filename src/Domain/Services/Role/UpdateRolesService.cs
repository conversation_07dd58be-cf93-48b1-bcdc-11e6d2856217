using Domain.Commands.Role;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Role
{
    public class UpdateRolesService : RoleBaseService
    {
        private readonly ILogger<UpdateRolesService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IRepository _repository;

        public UpdateRolesService(
            ILogger<UpdateRolesService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
        }

        public async Task<CommandResponse> UpdateRolesAsync(UpdateRolesCommand command)
        {
            _logger.LogInformation("Starting Update Role for Role ID: {RoleItemId}", command.RoleItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var securityContext = _securityContextProvider.GetSecurityContext();
                var updateProperties = GetUpdatedRoleProperties(command, securityContext.UserId);

                await _repository.UpdateAsync<BuroRole>(r => r.ItemId == command.RoleItemId, updateProperties);

                _logger.LogInformation("Successfully Updated Role with Role ID: {RoleItemId}", command.RoleItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during role update for role ID: {RoleItemId}", command.RoleItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the role.");
            }

            return commandResponse;
        }

        private Dictionary<string, object> GetUpdatedRoleProperties(
            UpdateRolesCommand command,
            string userId)
        {
            var properties = new Dictionary<string, object>()
            {
                { nameof(BuroRole.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroRole.LastUpdatedBy), userId },
            };

            if (command.RoleName.IsNullOrEmptyOrWhiteSpace())
                return properties;

            properties.Add(nameof(BuroRole.RoleName), command.RoleName);
            properties.Add(nameof(BuroRole.RoleKey), GenerateRoleKey(command.RoleName));

            return properties;
        }
    }
}
