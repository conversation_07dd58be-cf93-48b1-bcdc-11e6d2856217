using Domain.Commands.Center;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Events;
using Infrastructure.Events.Center;
using Infrastructure.Events.EmployeeHistory;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Center
{
    public class CenterCommandService
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<CenterCommandService> _logger;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;
        private readonly ISequenceNumberClient _sequenceNumberClient;

        public CenterCommandService(
            ISecurityContextProvider securityContextProvider,
            ILogger<CenterCommandService> logger,
            IRepository repository,
            ISequenceNumberClient sequenceNumberClient,
            IServiceClient serviceClient)
        {
            _securityContextProvider = securityContextProvider;
            _logger = logger;
            _repository = repository;
            _serviceClient = serviceClient;
            _sequenceNumberClient = sequenceNumberClient;
        }

        public async Task<CommandResponse> AddCenter(AddCenterCommand command)
        {
            _logger.LogInformation("Inside AddCenter");

            var commandResponse = new CommandResponse();

            try
            {
                var securityContext = _securityContextProvider.GetSecurityContext();
                var employee = await GetEmployeeByUserId(securityContext.UserId);
                var programOrganizerInfo = await GetEmployeeByItemId(command.ProgramOrganizerId);

                if (employee == null)
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.InvalidEmployeeId);
                }
                else if (employee.DesignationTitle == BuroEmployeeDesignationConstant.BuroBranchManager)
                {
                    var buroCenter = await CreateBuroCenterDataAsync(command, programOrganizerInfo, securityContext.UserId, true, employee.CurrentOfficeItemId);
                    await SaveCenterAsync(buroCenter);
                    await SendToQueueForCenterCountSync(buroCenter.ItemId);
                    if (programOrganizerInfo != null)
                    {
                        await UpdateCenterInfoOfEmployee(buroCenter.Name, buroCenter.ItemId, programOrganizerInfo.ItemId);
                        CreateEmployeeAssignmentHistory(buroCenter, programOrganizerInfo);
                    }

                }
                else if (employee.DesignationTitle == BuroEmployeeDesignationConstant.BuroBranchAccountant)
                {
                    var buroCenter = await CreateBuroCenterDataAsync(command, programOrganizerInfo, securityContext.UserId, false, employee.CurrentOfficeItemId);
                    await SaveCenterAsync(buroCenter);
                    await SendToQueueForApproval(buroCenter.ItemId);
                }
                else
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.UserPermissionDoesntExist);
                }

                _logger.LogInformation("Finished AddCenter");
            }
            catch (Exception ex)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, ex.Message);
            }

            return commandResponse;
        }

        private void CreateEmployeeAssignmentHistory(BuroCenter buroCenter, BuroEmployee programOrganizer)
        {
            var history = PopulateEmployeeAssignmentHistoryEvent(buroCenter, programOrganizer);
            _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, history);
        }

        private CreateEmployeeAssignmentHistoryEvent PopulateEmployeeAssignmentHistoryEvent(BuroCenter buroCenter,
            BuroEmployee programOrganizer)
        {
            return new CreateEmployeeAssignmentHistoryEvent
            {
                EmployeeItemId = programOrganizer.ItemId,
                UserItemId = programOrganizer.UserItemId,
                CenterItemId = buroCenter.ItemId,
                CenterTitle = buroCenter.Name,
                MemberCount = buroCenter.MemberCount,
                StartDate = DateTime.UtcNow
            };
        }

        private async Task SaveCenterAsync(BuroCenter buroCenter)
        {
            _logger.LogInformation("Inside Save Center Async");
            await _repository.SaveAsync(buroCenter);
            _logger.LogInformation("Finished Save Center Async");
        }

        private async Task<BuroCenter> CreateBuroCenterDataAsync(
            AddCenterCommand command, BuroEmployee? programOrganizerInfo, string userId, bool isApproved, string branchItemId)
        {
            _logger.LogInformation("Inside CreateBuroCenterData with UserId {UserId}", userId);
            var centerId = await GetSequenceNumber(BuroConstants.BuroCenterIDIdentifier);
            var buroCenter = new BuroCenter
            {
                ItemId = Guid.NewGuid().ToString(),
                CreatedBy = userId,
                LastUpdatedBy = userId,
                RolesAllowedToRead = new[] { UserRoles.Admin },
                IdsAllowedToRead = new[] { userId },
                IdsAllowedToDelete = new[] { userId },
                IdsAllowedToUpdate = new[] { userId },
                IdsAllowedToWrite = new[] { userId },
                Name = command.Name,
                CenterId = centerId,
                Type = command.Type,
                Address = command.Address,
                BranchId = branchItemId,
                CenterDay = command.CenterDay,
                CenterTime = command.CenterTime,
                ProgramOrganizerEmployeeItemId = command.ProgramOrganizerId,
                ProgramOrganizerEmployeeName = programOrganizerInfo?.EmployeeName,
                GPSLocation = command.GPSLocation,
                DivisionId = command.DivisionId,
                DivisionName = command.DivisionName,
                DistrictId = command.DistrictId,
                DistrictName = command.DistrictName,
                UpazilaId = command.UpazilaId,
                UpazilaName = command.UpazilaName,
                UnionId = command.UnionId,
                UnionName = command.UnionName,
                PostOfficeId = command.PostOfficeId,
                PostOfficeName = command.PostOfficeName,
                PostCode = command.PostCode,
                WardId = command.WardId,
                WardName = command.WardName,
                IsApproved = isApproved
            };
            buroCenter.AddEntityBasicInfo();
            _logger.LogInformation("End CreateBuroCenterData with UserId {UserId}", userId);

            return buroCenter;
        }

        private Task SendToQueueForApproval(string buroCenterId)
        {
            var centerApprovalPermissionEvent = new CenterApprovalPermissionEvent
            {
                BuroCenterItemId = buroCenterId,
                ProcessOperationType = EnumOperationTypes.ADD
            };
            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, centerApprovalPermissionEvent);

            return Task.CompletedTask;
        }

        private Task SendToQueueForCenterCountSync(string buroCenterId)
        {
            var syncNumberOfCenterEvent = new SyncNumberOfCenterEvent
            {
                CenterId = buroCenterId,
                Action = EnumOperationTypes.ADD
            };
            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, syncNumberOfCenterEvent);
            return Task.CompletedTask;
        }
        //TODO: refactor to merge three function

        private Task<BuroEmployee> GetEmployeeByUserId(string userId)
        {
            return _repository.GetItemAsync<BuroEmployee>(x => x.UserItemId == userId);
        }

        private Task<BuroEmployee> GetEmployeeByItemId(string employeeId)
        {
            return _repository.GetItemAsync<BuroEmployee>(x => x.ItemId == employeeId);
        }

        private async Task<string> GetSequenceNumber(string context)
        {
            var sequence = await _sequenceNumberClient.GetSequenceNumber(new SequenceNumberQuery { Context = context });
            if (sequence == null || sequence.CurrentNumber <= 0)
            {
                throw new InvalidOperationException("Invalid sequence number received from the sequence number service.");
            }

            return sequence.CurrentNumber.ToString().PadLeft(6, '0');
        }

        private async Task UpdateCenterInfoOfEmployee(string centerTitle, string centerItemId, string employeeId)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroEmployee.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroEmployee.LastUpdatedBy), securityContext.UserId},
                {nameof(BuroEmployee.CurrentCenterItemId), centerItemId },
                {nameof(BuroEmployee.CurrentCenterTitle), centerTitle}
            };
            await _repository.UpdateAsync<BuroEmployee>(x => x.ItemId == employeeId, properties);
        }
    }
}
