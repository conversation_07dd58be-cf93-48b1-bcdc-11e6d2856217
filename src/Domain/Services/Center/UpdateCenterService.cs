using Domain.Commands.Center;
using Infrastructure.Constants;
using Infrastructure.Enums;
using Infrastructure.Events;
using Infrastructure.Events.Center;
using Infrastructure.Events.EmployeeHistory;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Center
{
    public class UpdateCenterService
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<UpdateCenterService> _logger;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;

        public UpdateCenterService(
            ISecurityContextProvider securityContextProvider,
            ILogger<UpdateCenterService> logger,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _securityContextProvider = securityContextProvider;
            _logger = logger;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> EditCenter(EditCenterCommand command)
        {
            _logger.LogInformation("Inside EditCenter");

            var commandResponse = new CommandResponse();

            try
            {
                var securityContext = _securityContextProvider.GetSecurityContext();
                var employee = await GetEmployeeByUserId(securityContext.UserId);
                if (employee == null)
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.InvalidEmployeeId);
                }

                var existingCenter = await GetCenterById(command.ItemId);
                if (existingCenter == null)
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.InvalidCenterId);
                }

                if (employee.DesignationTitle == BuroEmployeeDesignationConstant.BuroBranchManager
                    && employee.CurrentOfficeItemId == existingCenter.BranchId)
                {
                    var updatedProperties = await PreparePropertiesForCenterUpdate(command, existingCenter, false);
                    await UpdateCenterAsync(command.ItemId, updatedProperties);
                    if (updatedProperties.ContainsKey(nameof(BuroCenter.ProgramOrganizerEmployeeItemId)))
                    {
                        await UpdateCenterInfoOfEmployee(existingCenter.Name, existingCenter.ItemId, command.ProgramOrganizerId);
                    }
                }
                else if (employee.DesignationTitle == BuroEmployeeDesignationConstant.BuroBranchAccountant
                    && employee.CurrentOfficeItemId == existingCenter.BranchId)
                {
                    var updatedProperties = await PreparePropertiesForCenterUpdate(command, existingCenter, true);
                    await UpdateCenterAsync(command.ItemId, PreparePendingProperties(updatedProperties));
                    await SendToQueue(PreparePermissionEvent(command.ItemId, EnumOperationTypes.EDIT));
                }
                else
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.UserPermissionDoesntExist);
                }

                await HandleProgramOrganizerChangeAsync(command, existingCenter);

                _logger.LogInformation("Finished EditCenter");
            }
            catch (Exception ex)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, ex.Message);
            }

            return commandResponse;
        }

        private async Task HandleProgramOrganizerChangeAsync(EditCenterCommand command, BuroCenter existingCenter)
        {
            if (command.ProgramOrganizerId.IsNullOrEmptyOrWhiteSpace())
            {
                _logger.LogInformation("In HandleProgramOrganizerChangeAsync, No Program Organizer is selected. So no need to update employee assignment history");
                return;
            }
            var isProgramOrganizerChanged = existingCenter.ProgramOrganizerEmployeeItemId != command.ProgramOrganizerId;

            var existingProgramOrganizer = await GetEmployeeById(existingCenter.ProgramOrganizerEmployeeItemId);

            DateTime? endDateForProgramOrganizer = null;

            var office = await GetOfficeByItemId(existingCenter.BranchId);

            if (isProgramOrganizerChanged)
            {
                var newProgramOrganizer = await GetEmployeeById(command.ProgramOrganizerId);
                endDateForProgramOrganizer = DateTime.UtcNow;
                CreateEmployeeAssignmentHistory(existingCenter, newProgramOrganizer, office);
            }

            UpdateEmployeeAssignmentHistory(existingCenter, existingProgramOrganizer, endDateForProgramOrganizer);
        }

        private async Task<BuroOffice> GetOfficeByItemId(string itemId)
        {
            return await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == itemId);
        }

        private void CreateEmployeeAssignmentHistory(BuroCenter buroCenter, BuroEmployee programOrganizer, BuroOffice office)
        {
            var history = PopulateEmployeeAssignmentHistoryEvent(buroCenter, programOrganizer, office);
            _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, history);
        }

        private CreateEmployeeAssignmentHistoryEvent PopulateEmployeeAssignmentHistoryEvent(BuroCenter buroCenter,
            BuroEmployee programOrganizer, BuroOffice office)
        {
            return new CreateEmployeeAssignmentHistoryEvent
            {
                EmployeeItemId = programOrganizer.ItemId,
                UserItemId = programOrganizer.UserItemId,
                CenterItemId = buroCenter.ItemId,
                CenterTitle = buroCenter.Name,
                OfficeItemId = office.ItemId,
                OfficeTitle = office.OfficeName,
                OfficeCode = office.OfficeCode,
                MemberCount = buroCenter.MemberCount,
                StartDate = DateTime.UtcNow
            };
        }

        private void UpdateEmployeeAssignmentHistory(BuroCenter buroCenter, BuroEmployee programOrganizer, DateTime? endDateForProgramOrganizer)
        {
            var history = PopulateUpdateEmployeeAssignmentHistoryEvent(buroCenter, programOrganizer, endDateForProgramOrganizer);
            _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, history);
        }

        private UpdateEmployeeAssignmentHistoryEvent PopulateUpdateEmployeeAssignmentHistoryEvent(BuroCenter buroCenter,
            BuroEmployee programOrganizer, DateTime? endDateForProgramOrganizer)
        {
            return new UpdateEmployeeAssignmentHistoryEvent
            {
                EmployeeItemId = programOrganizer.ItemId,
                CenterItemId = buroCenter.ItemId,
                EndDate = endDateForProgramOrganizer
            };
        }

        private Task<BuroEmployee> GetEmployeeByUserId(string userId)
        {
            return _repository.GetItemAsync<BuroEmployee>(x => x.UserItemId == userId);
        }

        private Task<BuroCenter> GetCenterById(string id)
        {
            return _repository.GetItemAsync<BuroCenter>(x => x.ItemId == id);
        }

        private async Task UpdateCenterAsync(string id, Dictionary<string, object> updatedProperties)
        {
            _logger.LogInformation("Inside UpdateCenterAsync for ItemID: {ItemId}", id);
            await _repository.UpdateAsync<BuroCenter>(c => c.ItemId == id, updatedProperties);
            _logger.LogInformation("Finished UpdateCenterAsync for ItemID: {ItemId}", id);
        }

        private async Task<Dictionary<string, object>> PreparePropertiesForCenterUpdate(
            EditCenterCommand command,
            BuroCenter existingCenter,
            bool isApprovalNeeded)
        {
            _logger.LogInformation("Preparing properties of Center for Update ID: {ItemId}", command.ItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCenter.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroCenter.LastUpdatedBy), securityContext.UserId}
            };

            if (!string.IsNullOrEmpty(command.Name) && existingCenter.Name != command.Name)
            {
                properties.Add(nameof(BuroCenter.Name), command.Name);
            }

            if (command.Type != existingCenter.Type)
            {
                properties.Add(nameof(BuroCenter.Type), command.Type);
            }

            if (!string.IsNullOrEmpty(command.Address) && existingCenter.Address != command.Address)
            {
                properties.Add(nameof(BuroCenter.Address), command.Address);
            }

            if (command.CenterDay != existingCenter.CenterDay)
            {
                properties.Add(nameof(BuroCenter.CenterDay), command.CenterDay);
            }

            if (!string.IsNullOrEmpty(command.CenterTime) && existingCenter.CenterTime != command.CenterTime)
            {
                properties.Add(nameof(BuroCenter.CenterTime), command.CenterTime);
            }

            if (!string.IsNullOrEmpty(command.ProgramOrganizerId) && existingCenter.ProgramOrganizerEmployeeItemId != command.ProgramOrganizerId)
            {
                var programOrganizerDetails = await CheckValidityOfProgramOrganizer(command.ProgramOrganizerId, existingCenter.BranchId);
                if (programOrganizerDetails != null)
                {
                    properties.Add(nameof(BuroCenter.ProgramOrganizerEmployeeItemId), command.ProgramOrganizerId);
                    properties.Add(nameof(BuroCenter.ProgramOrganizerEmployeeName), programOrganizerDetails.EmployeeName);
                }

            }

            if (command.GPSLocation != null && !command.GPSLocation.Equals(existingCenter.GPSLocation))
            {
                properties.Add(nameof(BuroCenter.GPSLocation), new BsonDocument
                {
                    { nameof(LocationCoordinates.Latitude), command.GPSLocation.Latitude ?? 0.0 },
                    { nameof(LocationCoordinates.Longitude), command.GPSLocation.Longitude ?? 0.0 }
                });
            }

            if (!string.IsNullOrEmpty(command.DivisionId) && existingCenter.DivisionId != command.DivisionId)
            {
                properties.Add(nameof(BuroCenter.DivisionId), command.DivisionId);
            }

            if (!string.IsNullOrEmpty(command.DivisionName) && existingCenter.DivisionName != command.DivisionName)
            {
                properties.Add(nameof(BuroCenter.DivisionName), command.DivisionName);
            }

            if (!string.IsNullOrEmpty(command.DistrictId) && existingCenter.DistrictId != command.DistrictId)
            {
                properties.Add(nameof(BuroCenter.DistrictId), command.DistrictId);
            }

            if (!string.IsNullOrEmpty(command.DistrictName) && existingCenter.DistrictName != command.DistrictName)
            {
                properties.Add(nameof(BuroCenter.DistrictName), command.DistrictName);
            }

            if (!string.IsNullOrEmpty(command.UpazilaId) && existingCenter.UpazilaId != command.UpazilaId)
            {
                properties.Add(nameof(BuroCenter.UpazilaId), command.UpazilaId);
            }

            if (!string.IsNullOrEmpty(command.UpazilaName) && existingCenter.UpazilaName != command.UpazilaName)
            {
                properties.Add(nameof(BuroCenter.UpazilaName), command.UpazilaName);
            }

            if (!string.IsNullOrEmpty(command.UnionId) && existingCenter.UnionId != command.UnionId)
            {
                properties.Add(nameof(BuroCenter.UnionId), command.UnionId);
            }

            if (!string.IsNullOrEmpty(command.UnionName) && existingCenter.UnionName != command.UnionName)
            {
                properties.Add(nameof(BuroCenter.UnionName), command.UnionName);
            }

            if (!string.IsNullOrEmpty(command.PostOfficeId) && existingCenter.PostOfficeId != command.PostOfficeId)
            {
                properties.Add(nameof(BuroCenter.PostOfficeId), command.PostOfficeId);
            }

            if (!string.IsNullOrEmpty(command.PostOfficeName) && existingCenter.PostOfficeName != command.PostOfficeName)
            {
                properties.Add(nameof(BuroCenter.PostOfficeName), command.PostOfficeName);
            }

            if (!string.IsNullOrEmpty(command.PostCode) && existingCenter.PostCode != command.PostCode)
            {
                properties.Add(nameof(BuroCenter.PostCode), command.PostCode);
            }

            if (!string.IsNullOrEmpty(command.WardId) && existingCenter.WardId != command.WardId)
            {
                properties.Add(nameof(BuroCenter.WardId), command.WardId);
            }

            if (!string.IsNullOrEmpty(command.WardName) && existingCenter.WardName != command.WardName)
            {
                properties.Add(nameof(BuroCenter.WardName), command.WardName);
            }

            _logger.LogInformation("Finished Preparing properties of Center for Update ID: {ItemId}", command.ItemId);

            return properties;
        }


        public async Task<CommandResponse> DeleteCenter(DeleteCenterCommand command)
        {
            _logger.LogInformation("Inside DeleteCenter");

            var commandResponse = new CommandResponse();

            try
            {
                var securityContext = _securityContextProvider.GetSecurityContext();
                var employee = await GetEmployeeByUserId(securityContext.UserId);

                if (employee == null)
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.InvalidEmployeeId);
                }
                var existingCenter = await GetCenterById(command.ItemId);
                if (existingCenter == null || existingCenter.IsMarkedToDelete)
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.InvalidCenterId);
                }


                if (employee.DesignationTitle == BuroEmployeeDesignationConstant.BuroBranchManager)
                {
                    var updatedProperties = PreparePropertiesForCenterDelete(command, false);
                    await UpdateCenterAsync(command.ItemId, updatedProperties);
                    await UpdateCenterInfoOfEmployee(null, null, existingCenter.ProgramOrganizerEmployeeItemId);
                    await SendToQueueForCenterCountSync(command.ItemId);
                }
                else if (employee.DesignationTitle == BuroEmployeeDesignationConstant.BuroBranchAccountant)
                {
                    var updatedProperties = PreparePropertiesForCenterDelete(command, true);
                    await UpdateCenterAsync(command.ItemId, PreparePendingProperties(updatedProperties));
                    await SendToQueue(PreparePermissionEvent(command.ItemId, EnumOperationTypes.REMOVE));
                }
                else
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.UserPermissionDoesntExist);
                }


                _logger.LogInformation("Finished DeleteCenter");
            }
            catch (Exception ex)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, ex.Message);
            }

            return commandResponse;
        }

        private Dictionary<string, object> PreparePropertiesForCenterDelete(
            DeleteCenterCommand command, bool isApprovalNeeded)
        {
            _logger.LogInformation("Preparing properties of Center for Delete ID: {ItemId}", command.ItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCenter.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroCenter.LastUpdatedBy), securityContext.UserId},
                {nameof(BuroCenter.IsMarkedToDelete), true}
            };

            _logger.LogInformation("Finished Preparing properties of Center for Delete ID: {ItemId}", command.ItemId);

            return properties;
        }

        private static CenterApprovalPermissionEvent PreparePermissionEvent(string centerId, EnumOperationTypes type)
        {
            return new CenterApprovalPermissionEvent
            {
                BuroCenterItemId = centerId,
                ProcessOperationType = type,
            };
        }

        private ChangePOrForMembersEvent PrepareChangePOrForMemberEvent(string centerId, string programOrgranizerId)
        {
            return new ChangePOrForMembersEvent
            {
                CenterItemId = centerId,
                ProgramOrganizerItemId = programOrgranizerId,
            };
        }

        private Task SendToQueueForUpdatingPOForMember(string centerId, string programOrganizerId)
        {
            _logger.LogInformation("Inside SendToQueue For Updating Members");
            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, PrepareChangePOrForMemberEvent(centerId, programOrganizerId));
            _logger.LogInformation("Finished SendToQueue  For Updating Members");
            return Task.CompletedTask;
        }

        private async Task<BuroEmployee?> CheckValidityOfProgramOrganizer(string programOrganizerId, string existingBranchId)
        {
            var newProgramOrganizerInfo = await GetEmployeeById(programOrganizerId);
            if (newProgramOrganizerInfo.CurrentOfficeItemId == existingBranchId && newProgramOrganizerInfo.DesignationTitle == BuroCenterConstants.ProgramOrganizerDesignationTitle)
            {
                return newProgramOrganizerInfo;
            }
            else
            {
                return null;
            }
        }

        private Task SendToQueueForCenterCountSync(string buroCenterId)
        {
            var syncNumberOfCenterEvent = new SyncNumberOfCenterEvent
            {
                CenterId = buroCenterId,
                Action = EnumOperationTypes.REMOVE
            };
            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, syncNumberOfCenterEvent);
            return Task.CompletedTask;
        }

        private Task SendToQueue(CenterApprovalPermissionEvent centerApprovalPermissionEvent)
        {
            _logger.LogInformation("Inside SendToQueue");
            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, centerApprovalPermissionEvent);
            _logger.LogInformation("Finished SendToQueue");
            return Task.CompletedTask;
        }

        private Dictionary<string, object> PreparePendingProperties(Dictionary<string, object> requestedChanges)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroCenter.PendingChanges), requestedChanges},
                {nameof(BuroCenter.IsApproved), false},
            };

            return properties;
        }

        private Task<BuroEmployee> GetEmployeeById(string itemId)
        {
            return _repository.GetItemAsync<BuroEmployee>(x => x.ItemId == itemId);
        }

        private async Task UpdateCenterInfoOfEmployee(string centerTitle, string centerItemId, string employeeId)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroEmployee.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroEmployee.LastUpdatedBy), securityContext.UserId},
                {nameof(BuroEmployee.CurrentCenterItemId), centerItemId },
                {nameof(BuroEmployee.CurrentCenterTitle), centerTitle}
            };
            await _repository.UpdateAsync<BuroEmployee>(x => x.ItemId == employeeId, properties);
        }
    }
}
