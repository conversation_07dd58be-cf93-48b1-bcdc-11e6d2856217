using Domain.Commands.Approval;
using Infrastructure.Constants;
using Infrastructure.Events;
using Infrastructure.Events.Account;
using Infrastructure.Events.Collection;
using Infrastructure.Events.ConsentForm;
using Infrastructure.Events.Inventory;
using Infrastructure.Events.MemberSurvey;
using Infrastructure.Events.OfficeSurvey;
using Infrastructure.Events.Transaction;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.Genesis.Framework.PDS.Entity;

namespace Domain.Services.Approval
{
    public class ApprovalService
    {
        private readonly ILogger<ApprovalService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;

        public ApprovalService(
            ILogger<ApprovalService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository,
            IServiceClient serviceClient
        )
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> UpdateApprovalStatus(ApprovalRequestStatusUpdateCommand command)
        {
            _logger.LogInformation("Updating approval status for Approval ID: {ApprovalId}", command.ApprovalId);

            var commandResponse = new CommandResponse();
            try
            {
                await ProcessApproval(command);
                _logger.LogInformation("Updated approval status for Approval ID: {ApprovalId}", command.ApprovalId);
            }
            catch (Exception ex)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
                _logger.LogError(ex, "Failed to update approval status for Approval ID: {ApprovalId}", command.ApprovalId);
            }

            return commandResponse;
        }

        private async Task ProcessApproval(ApprovalRequestStatusUpdateCommand command)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var loggedInUserId = securityContext.UserId;
            var requestedApproval = await _repository.GetItemAsync<BuroApproval>(x => x.ItemId == command.ApprovalId && x.Status == EnumApprovalStatusType.Pending);
            var authorizedUser = await _repository.GetItemAsync<Connection>(x => x.ParentEntityID == loggedInUserId);

            ValidateApprovalRequest(requestedApproval, authorizedUser);

            await UpdateApprovalStatus(command, loggedInUserId, requestedApproval.ItemId);

            var eventAndQueue = GetEventAndQueueForApprovalCategory(command, requestedApproval);

            if (eventAndQueue is not null)
            {
                var (eventObject, queueName) = eventAndQueue.Value;
                _serviceClient.SendToQueue<bool>(queueName, eventObject);
            }
        }

        private static void ValidateApprovalRequest(BuroApproval? approval, Connection? authorizedUser)
        {
            if (approval == null) throw new InvalidOperationException("Requested approval not found.");
            if (approval.ActionedByPersonItemId != authorizedUser?.ChildEntityID)
                throw new UnauthorizedAccessException("User unauthorized for this approval action.");
        }

        private async Task UpdateApprovalStatus(ApprovalRequestStatusUpdateCommand command, string userId, string approvalId)
        {
            var updatedData = new Dictionary<string, object>
            {
                { nameof(BuroApproval.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroApproval.LastUpdatedBy), userId },
                { nameof(BuroApproval.ActionDate), DateTime.UtcNow },
                { nameof(BuroApproval.Status), command.Status },
                { nameof(BuroApproval.Remarks), command.Remarks ?? string.Empty }
            };

            if (!string.IsNullOrEmpty(command.Remarks))
            {
                updatedData[nameof(BuroApproval.Remarks)] = command.Remarks;
            }

            await _repository.UpdateAsync<BuroApproval>(x => x.ItemId == approvalId, updatedData);
            _logger.LogInformation("Updated status for Approval ID: {ApprovalId} to {Status}", approvalId, command.Status);
        }

        private static (object? Event, string QueueName)? GetEventAndQueueForApprovalCategory(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return approval.Category switch
            {
                EnumApprovalCategoryType.EmployeeTransferIn => GetStaffingRequestApprovalStatusUpdateForEmployeeTransferEvent(command, approval),
                EnumApprovalCategoryType.MemberTransferIn => GetStaffingRequestApprovalStatusUpdateForMemberTransferEvent(command, approval),
                EnumApprovalCategoryType.ZMAssignesSurveyToAM => GetOfficeSurveyTeamLeadRespondToInvitationEvent(command, approval),
                EnumApprovalCategoryType.AMAddsBMToSurvey => GetOfficeSurveyTeamMemberRespondToInvitationEvent(command, approval),
                EnumApprovalCategoryType.AMSubmitsSurveyToZM => GetZoneManagerAcknowledgeOfficeSurveySubmissionEvent(command, approval),
                EnumApprovalCategoryType.ZMSubmitsSurveyToPD => GetManagementOfficeSurveyApprovalEvent(command, approval),
                EnumApprovalCategoryType.SurveyToMemberApproval => GetMemberSurveyRequestApprovalEvent(command, approval),
                EnumApprovalCategoryType.MemberPhoneNumberChangeApproval => GetMemberPhoneNumberChangeApprovalEvent(command, approval),
                EnumApprovalCategoryType.MemberAddressChangeApproval => GetMemberAddressChangeApprovalEvent(command, approval),
                EnumApprovalCategoryType.NewCSAccountApproval => GetContractualAccountApprovalEvent(command, approval),
                EnumApprovalCategoryType.LoanApplicationApproval => GetLoanApplicationPostProcessingEvent(command, approval),
                EnumApprovalCategoryType.CollectionFundTransferRevertApproval => GetCollectionFundTransferRevertApprovalEvent(command, approval),
                EnumApprovalCategoryType.InventoryOutApproval => GetInventoryOutApprovalEvent(command, approval),
                EnumApprovalCategoryType.ConsentFormApproval => GetConsentFormApprovalEvent(command, approval),
                EnumApprovalCategoryType.GSWithdrawalApproval => GetGSWithdrawalApprovalEvent(command, approval),
                EnumApprovalCategoryType.InventoryTransferRecipientApproval => GetInventoryTransferRecipientApprovalEvent(command, approval),
                EnumApprovalCategoryType.InventoryTransferSourceApproval => GetInventoryTransferSourceApprovalEvent(command, approval),
                EnumApprovalCategoryType.InventoryDisposeApproval => GetInventoryDisposeApprovalEvent(command, approval),
                EnumApprovalCategoryType.GSChangeApproval => GetGSChangeApprovalEvent(command, approval),
                _ => null
            };
        }

        private static (object? Event, string QueueName) GetGSWithdrawalApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new GSWithdrawalApprovalEvent
            {
                ProductAccountTransactionRequestItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetInventoryOutApprovalEvent(
            ApprovalRequestStatusUpdateCommand command, BuroApproval approval)
        {
            return (new InventoryOutItemApprovalEvent
            {
                InventoryMovementPlanItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetInventoryTransferRecipientApprovalEvent(
            ApprovalRequestStatusUpdateCommand command, BuroApproval approval)
        {
            return (new InventoryTransferRecipientApprovalEvent
            {
                InventoryMovementPlanItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetInventoryTransferSourceApprovalEvent(
            ApprovalRequestStatusUpdateCommand command, BuroApproval approval)
        {
            return (new InventoryTransferSourceApprovalEvent
            {
                InventoryMovementPlanItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetInventoryDisposeApprovalEvent(
            ApprovalRequestStatusUpdateCommand command, BuroApproval approval)
        {
            return (new InventoryDisposeApprovalEvent
            {
                InventoryMovementPlanItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetGSChangeApprovalEvent(
            ApprovalRequestStatusUpdateCommand command, BuroApproval approval)
        {
            return (new GSChangeApprovalEvent
            {
                ApprovalItemId = approval.ItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetCollectionFundTransferRevertApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new CollectionFundTransferRevertApprovalEvent
            {
                CollectionFundTransferItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetLoanApplicationPostProcessingEvent(ApprovalRequestStatusUpdateCommand command, BuroApproval approval)
        {
            var loanStatus = command.Status == SubmissionStatus.Approved
                ? EnumProductApplicationStatus.Approved
                : EnumProductApplicationStatus.Rejected;

            return (new LoanApplicationPostProcessingEvent
            {
                LoanApplicationItemId = approval.RelatedEntityItemId,
                Status = loanStatus,
                Remarks = command.Remarks
            }, QueueNames.BuroProductAccountQueue);
        }

        private static (object? Event, string QueueName) GetContractualAccountApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new ContractualAccountApprovalEvent
            {
                ApplicationItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetStaffingRequestApprovalStatusUpdateForEmployeeTransferEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new StaffingRequestApprovalStatusUpdateEvent
            {
                ApprovalItemId = approval.ItemId,
                Status = command.Status
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetStaffingRequestApprovalStatusUpdateForMemberTransferEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new StaffingRequestApprovalStatusUpdateEvent
            {
                ApprovalItemId = approval.ItemId,
                Status = command.Status
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetOfficeSurveyTeamLeadRespondToInvitationEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new OfficeSurveyTeamLeadRespondToInvitationEvent
            {
                SurveyItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroSurveyQueue);
        }

        private static (object? Event, string QueueName) GetOfficeSurveyTeamMemberRespondToInvitationEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new OfficeSurveyTeamMemberRespondToInvitationEvent
            {
                SurveyItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroSurveyQueue);
        }

        private static (object? Event, string QueueName) GetZoneManagerAcknowledgeOfficeSurveySubmissionEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new ZoneManagerAcknowledgeOfficeSurveySubmissionEvent
            {
                SurveyItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroSurveyQueue);
        }

        private static (object? Event, string QueueName) GetManagementOfficeSurveyApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new ManagementOfficeSurveyApprovalEvent
            {
                SurveyItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroSurveyQueue);
        }

        private static (object? Event, string QueueName) GetMemberSurveyRequestApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            var requestedData = command.AdditionalRequestedData?.ToObject<MemberSurveyAdditionalRequest>() ?? default!;

            return (new MemberSurveyRequestApprovalEvent
            {
                SurveyItemId = approval.RelatedEntityItemId,
                Status = command.Status,
                Remarks = approval.Remarks,
                RequestedData = requestedData
            }, QueueNames.BuroSurveyQueue);
        }

        private static (object? Event, string QueueName) GetMemberPhoneNumberChangeApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new MemberPhoneNumberChangeApprovalEvent
            {
                ChangeRequestItemId = approval.RelatedEntityItemId,
                Remarks = approval.Remarks,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetMemberAddressChangeApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new MemberAddressChangeApprovalEvent
            {
                ChangeRequestItemId = approval.RelatedEntityItemId,
                Remarks = approval.Remarks,
                IsAccepted = command.Status == SubmissionStatus.Approved
            }, QueueNames.BuroCommandQueue);
        }

        private static (object? Event, string QueueName) GetConsentFormApprovalEvent(
            ApprovalRequestStatusUpdateCommand command,
            BuroApproval approval)
        {
            return (new ConsentFormApprovalEvent
            {
                ConsentFormItemId = approval.RelatedEntityItemId,
                IsAccepted = command.Status == SubmissionStatus.Approved,
                Remarks = approval.Remarks
            }, QueueNames.BuroCommandQueue);
        }
    }
}
