using Domain.Commands.Vendor;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Vendor;

public class UpdateVendorStatusService
{
    private readonly IRepository _repository;

    public UpdateVendorStatusService(IRepository repository)
    {
        _repository = repository;
    }

    public async Task UpdateVendorStatus(UpdateVendorStatusCommand command)
    {
        await _repository.UpdateAsync<BuroVendor>(
            item => item.ItemId == command.VendorItemId,
            GetUpdatedProperties(command));
    }

    private Dictionary<string, object> GetUpdatedProperties(UpdateVendorStatusCommand command)
    {
        return new Dictionary<string, object>
        {
            { nameof(BuroVendor.VendorStatus), command.VendorStatus }
        };
    }
}