using Domain.Commands.Vendor;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Vendor;

public class CreateVendorService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISequenceNumberClient _sequenceNumberClient;

    public CreateVendorService(IRepository repository, ISecurityContextProvider securityContextProvider,
        ISequenceNumberClient sequenceNumberClient)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
        _sequenceNumberClient = sequenceNumberClient;
    }

    public async Task CreateVendor(CreateVendorCommand command)
    {
        var context = _securityContextProvider.GetSecurityContext();
        var vendorId = await GenerateVendorId(command);
        var buroVendor = PopulateVendor(command, vendorId, context);
        await _repository.SaveAsync(buroVendor);
    }

    private async Task<string> GenerateVendorId(CreateVendorCommand command)
    {
        var sequenceNumberResponse = await _sequenceNumberClient.GetSequenceNumber(
            new SequenceNumberQuery { Context = BuroConstants.BuroInventoryVendorIdentifier });

        var sequenceNumber =
            sequenceNumberResponse.CurrentNumber.ToString("D4");
        var year = command.BusinessStartDate?.Year.ToString();

        return $"{command.PostCode}{year}{sequenceNumber}";
    }

    private BuroVendor PopulateVendor(CreateVendorCommand command, string vendorId, SecurityContext context)
    {
        var vendor = new BuroVendor
        {
            ItemId = Guid.NewGuid().ToString(),
            VendorId = vendorId,
            VendorType = command.VendorType,
            CategoryItemId = command.VendorDomain == EnumVendorDomain.Inventory ? command.CategoryItemId : string.Empty,
            CategoryName = command.VendorDomain == EnumVendorDomain.Inventory ? command.CategoryName : string.Empty,
            SubCategoryItemId = command.VendorDomain == EnumVendorDomain.Inventory
                ? command.SubCategoryItemId
                : string.Empty,
            SubCategoryName = command.VendorDomain == EnumVendorDomain.Inventory
                ? command.SubCategoryName
                : string.Empty,
            GroupItemId = command.VendorDomain == EnumVendorDomain.Asset ? command.GroupItemId : string.Empty,
            GroupName = command.VendorDomain == EnumVendorDomain.Asset ? command.GroupName : string.Empty,
            SubGroupItemId = command.VendorDomain == EnumVendorDomain.Asset ? command.SubGroupItemId : string.Empty,
            SubGroupName = command.VendorDomain == EnumVendorDomain.Asset ? command.SubGroupName : string.Empty,
            VendorLegalName = command.VendorLegalName,
            BusinessStartDate = command.BusinessStartDate,
            OfficePhoneNo = command.OfficePhoneNo,
            Email = command.Email,
            TradeLicenseNo = command.TradeLicenseNo,
            EtinNumber = command.EtinNumber,
            VatRegistrationNumber = command.VatRegistrationNumber,
            Remarks = command.Remarks,
            HouseRoad = command.HouseRoad,
            Ward = command.Ward,
            WardItemId = command.WardItemId,
            PostOffice = command.PostOffice,
            PostOfficeItemId = command.PostOfficeItemId,
            PostCode = command.PostCode,
            Union = command.Union,
            UnionItemId = command.UnionItemId,
            Upazila = command.Upazila,
            UpazilaItemId = command.UpazilaItemId,
            District = command.District,
            DistrictItemId = command.DistrictItemId,
            Country = command.Country,
            VendorContactPersons = PopulateVendorContactPerson(command.VendorContactPersons),
            CorporateStatus = command.CorporateStatus,
            VendorStatus = command.VendorStatus,
            TransactionMediums = command.TransactionMediums,
            VendorDomain = command.VendorDomain,
            CreatedBy = context.UserId,
            CreateDate = DateTime.UtcNow
        };

        return vendor;
    }

    private List<BuroVendorContactPerson> PopulateVendorContactPerson(List<VendorContactPerson> contactPerson)
    {
        return contactPerson.Select(item => new BuroVendorContactPerson
        {
            Name = item.Name,
            PhoneNo = item.PhoneNo,
            SecondPhoneNo = item.SecondPhoneNo,
            Email = item.Email
        }).ToList();
    }
}