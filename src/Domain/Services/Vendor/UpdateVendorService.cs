using Domain.Commands.Vendor;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Vendor;

public class UpdateVendorService
{
    private readonly IRepository _repository;

    public UpdateVendorService(IRepository repository)
    {
        _repository = repository;
    }

    public async Task UpdateVendorAsync(UpdateVendorCommand command)
    {
        var existingVendor = await GetExistingVendor(command);
        var propertiesToUpdate = GetPropertiesToUpdate(command, existingVendor);
        await _repository.UpdateAsync<BuroVendor>(item => item.ItemId == command.ItemId, propertiesToUpdate);
    }

    private async Task<BuroVendor> GetExistingVendor(UpdateVendorCommand command)
    {
        return await _repository.GetItemAsync<BuroVendor>(item => item.ItemId == command.ItemId);
    }

    private Dictionary<string, object> GetPropertiesToUpdate(UpdateVendorCommand command, BuroVendor existingVendor)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroVendor.VendorType), command.VendorType },
            {
                nameof(BuroVendor.CategoryItemId),
                existingVendor.VendorDomain == EnumVendorDomain.Inventory ? command.CategoryItemId : string.Empty
            },
            {
                nameof(BuroVendor.CategoryName),
                existingVendor.VendorDomain == EnumVendorDomain.Inventory ? command.CategoryName : string.Empty
            },
            {
                nameof(BuroVendor.SubCategoryItemId),
                existingVendor.VendorDomain == EnumVendorDomain.Inventory ? command.SubCategoryItemId : string.Empty
            },
            {
                nameof(BuroVendor.SubCategoryName),
                existingVendor.VendorDomain == EnumVendorDomain.Inventory ? command.SubCategoryName : string.Empty
            },

            {
                nameof(BuroVendor.GroupItemId),
                existingVendor.VendorDomain == EnumVendorDomain.Asset ? command.GroupItemId : string.Empty
            },
            {
                nameof(BuroVendor.GroupName),
                existingVendor.VendorDomain == EnumVendorDomain.Asset ? command.GroupName : string.Empty
            },
            {
                nameof(BuroVendor.SubGroupItemId),
                existingVendor.VendorDomain == EnumVendorDomain.Asset ? command.SubGroupItemId : string.Empty
            },
            {
                nameof(BuroVendor.SubGroupName),
                existingVendor.VendorDomain == EnumVendorDomain.Asset ? command.SubGroupName : string.Empty
            },
            { nameof(BuroVendor.VendorLegalName), command.VendorLegalName },
            { nameof(BuroVendor.OfficePhoneNo), command.OfficePhoneNo },
            { nameof(BuroVendor.Email), command.Email },
            { nameof(BuroVendor.BusinessStartDate), command.BusinessStartDate },
            { nameof(BuroVendor.TradeLicenseNo), command.TradeLicenseNo },
            { nameof(BuroVendor.EtinNumber), command.EtinNumber },
            { nameof(BuroVendor.VatRegistrationNumber), command.VatRegistrationNumber },
            { nameof(BuroVendor.Remarks), command.Remarks },
            { nameof(BuroVendor.HouseRoad), command.HouseRoad },
            { nameof(BuroVendor.Ward), command.Ward },
            { nameof(BuroVendor.WardItemId), command.WardItemId },
            { nameof(BuroVendor.PostOffice), command.PostOffice },
            { nameof(BuroVendor.PostOfficeItemId), command.PostOfficeItemId },
            { nameof(BuroVendor.PostCode), command.PostCode },
            { nameof(BuroVendor.Union), command.Union },
            { nameof(BuroVendor.UnionItemId), command.UnionItemId },
            { nameof(BuroVendor.Upazila), command.Upazila },
            { nameof(BuroVendor.UpazilaItemId), command.UpazilaItemId },
            { nameof(BuroVendor.District), command.District },
            { nameof(BuroVendor.DistrictItemId), command.DistrictItemId },
            { nameof(BuroVendor.Country), command.Country },
            { nameof(BuroVendor.CorporateStatus), command.CorporateStatus },
            { nameof(BuroVendor.VendorStatus), command.VendorStatus },
            { nameof(BuroVendor.TransactionMediums), command.TransactionMediums }
        };
        PrepareContactPersonsToUpdate(properties, command);

        return properties;
    }

    private void PrepareContactPersonsToUpdate(Dictionary<string, object> properties,
        UpdateVendorCommand command)
    {
        var contactPersons = CreateVendorContactPersons(command);
        var contactPersonsBsonData = new BsonArray(contactPersons.Select(x => x.ToBsonDocument()));
        properties.Add(nameof(BuroVendor.VendorContactPersons), contactPersonsBsonData);
    }

    private List<BuroVendorContactPerson> CreateVendorContactPersons(UpdateVendorCommand command)
    {
        return command.VendorContactPersons.Select(item => new BuroVendorContactPerson
        {
            Email = item.Email,
            Name = item.Name,
            PhoneNo = item.PhoneNo,
            SecondPhoneNo = item.SecondPhoneNo
        }).ToList();
    }
}