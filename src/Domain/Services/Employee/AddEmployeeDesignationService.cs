using Domain.Commands.Employee;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Employee
{
    public class AddEmployeeDesignationService
    {
        private readonly ILogger<AddEmployeeDesignationService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;

        public AddEmployeeDesignationService(
            ILogger<AddEmployeeDesignationService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository
        )
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
        }

        public async Task<CommandResponse> AddEmployeeDesignation(AddEmployeeDesignationCommand command)
        {
            _logger.LogInformation("Inside AddEmployeeDesignation");

            var commandResponse = new CommandResponse();

            try
            {
                await ProcessAddEmployeeDesignation(command, commandResponse);
                _logger.LogInformation("Finished AddEmployeeDesignation");
            }
            catch (Exception e)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
            }



            return commandResponse;
        }

        private async Task ProcessAddEmployeeDesignation(AddEmployeeDesignationCommand command, CommandResponse commandResponse)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var userId = securityContext.UserId;

            var employee = await _repository.GetItemAsync<BuroEmployee>(x => x.ItemId == command.EmployeeItemId);

            if (employee == null)
            {
                throw new InvalidOperationException("employee not found.");
            }

            var currentDesignation = employee.DesignationItemId;


            var previousDesignation = await _repository.GetItemAsync<BuroDesignation>(x => x.ItemId == currentDesignation);
            var newDesignation = await _repository.GetItemAsync<BuroDesignation>(x => x.ItemId == command.NewDesignationItemId);

            var employeeDesignation = CreateEmployeeDesignation(command, employee, securityContext, userId, previousDesignation.Title, newDesignation.Title);
            await _repository.SaveAsync(employeeDesignation);
        }

        private static BuroEmployeeDesignationAssignment CreateEmployeeDesignation(AddEmployeeDesignationCommand command, BuroEmployee employee, SecurityContext securityContext, string userId, string previousDesignationTitle, string newDesignationTitle)
        {

            var buroEmployeeDesignationAssignment = new BuroEmployeeDesignationAssignment
            {
                ItemId = command.ItemId ?? Guid.NewGuid().ToString(),
                CreatedBy = userId,
                LastUpdatedBy = userId,
                RolesAllowedToRead = new[] { UserRoles.Admin },
                IdsAllowedToRead = new[] { userId },
                IdsAllowedToDelete = new[] { userId },
                IdsAllowedToUpdate = new[] { userId },
                IdsAllowedToWrite = new[] { userId },
                Tags = new[] { Tags.IsAEmployeeDesignation },
                EmployeeName = employee.EmployeeName,
                EmployeeItemId = command.EmployeeItemId,
                EmployeePin = employee.EmployeePin,
                PreviousDesignationTitle = previousDesignationTitle,
                NewDesignationTitle = newDesignationTitle,
                EffectiveDate = command.EffectiveDate,
                ChangedByUserId = userId,
                ChangedByUserName = securityContext.DisplayName,
                FileIds = command.FileIds ?? Array.Empty<string>(),
                Status = EnumEmployeeDesignationStatus.Success,
                Remark = command.Remark
            };
            buroEmployeeDesignationAssignment.AddEntityBasicInfo();

            return buroEmployeeDesignationAssignment;
        }
    }
}
