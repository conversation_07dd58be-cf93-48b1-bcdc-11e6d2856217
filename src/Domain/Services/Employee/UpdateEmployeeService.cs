using Domain.Commands.Employee;
using Domain.Services.Helper;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Employee
{
    public class UpdateEmployeeService
    {
        private readonly ILogger<UpdateEmployeeService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;

        public UpdateEmployeeService(IRepository repository,
            ILogger<UpdateEmployeeService> logger, ISecurityContextProvider securityContextProvider)
        {
            _repository = repository;
            _logger = logger;
            _securityContextProvider = securityContextProvider;
        }

        public async Task<CommandResponse> EditEmployeeAsync(EditEmployeeCommand command)
        {
            _logger.LogInformation("Inside EditEmployeeAsync");

            var commandResponse = new CommandResponse();

            try
            {
                var existingEmployee = await _repository.GetItemAsync<BuroEmployee>(c => c.ItemId == command.ItemId)
                    ?? throw new InvalidOperationException("Given Employee does not exist");

                var newEmployeeData = PreparePropertiesForEmployeeUpdate(command, existingEmployee);

                await _repository.UpdateAsync<BuroEmployee>(c => c.ItemId == command.ItemId, newEmployeeData);

                _logger.LogInformation("Finished EditEmployeeAsync");
            }
            catch (Exception)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            }

            return commandResponse;
        }

        private Dictionary<string, object> PreparePropertiesForEmployeeUpdate(
            EditEmployeeCommand command,
            BuroEmployee existingEmployee)
        {
            _logger.LogInformation("Preparing properties of Employee for Update ID: {ItemId}", command.ItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroEmployee.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroEmployee.LastUpdatedBy), securityContext.UserId}
            };

            if (!string.IsNullOrEmpty(command.NidNo) && existingEmployee.EmployeeNid != command.NidNo)
            {
                properties.Add(nameof(BuroEmployee.EmployeeNid), command.NidNo);
            }
            if (!string.IsNullOrEmpty(command.EmployeeName) && existingEmployee.EmployeeName != command.EmployeeName)
            {
                properties.Add(nameof(BuroEmployee.EmployeeName), command.EmployeeName);
            }
            if (!string.IsNullOrEmpty(command.FatherName) && existingEmployee.FatherName != command.FatherName)
            {
                properties.Add(nameof(BuroEmployee.FatherName), command.FatherName);
            }
            if (!string.IsNullOrEmpty(command.MotherName) && existingEmployee.MotherName != command.MotherName)
            {
                properties.Add(nameof(BuroEmployee.MotherName), command.MotherName);
            }
            if (!string.IsNullOrEmpty(command.WorkPhone) && existingEmployee.WorkPhone != command.WorkPhone)
            {
                properties.Add(nameof(BuroEmployee.WorkPhone), command.WorkPhone);
            }
            if (!string.IsNullOrEmpty(command.PersonalPhone) && existingEmployee.PersonalPhone != command.PersonalPhone)
            {
                properties.Add(nameof(BuroEmployee.PersonalPhone), command.PersonalPhone);
            }
            if (!string.IsNullOrEmpty(command.CountryId) && existingEmployee.CountryId != command.CountryId)
            {
                properties.Add(nameof(BuroEmployee.CountryId), command.CountryId);
            }
            if (!string.IsNullOrEmpty(command.CountryName) && existingEmployee.CountryName != command.CountryName)
            {
                properties.Add(nameof(BuroEmployee.CountryName), command.CountryName);
            }
            if (!string.IsNullOrEmpty(command.CountryLangKey) && existingEmployee.CountryLangKey != command.CountryLangKey)
            {
                properties.Add(nameof(BuroEmployee.CountryLangKey), command.CountryLangKey);
            }
            if (!string.IsNullOrEmpty(command.DistrictId) && existingEmployee.DistrictId != command.DistrictId)
            {
                properties.Add(nameof(BuroEmployee.DistrictId), command.DistrictId);
            }
            if (!string.IsNullOrEmpty(command.DistrictLangKey) && existingEmployee.DistrictLangKey != command.DistrictLangKey)
            {
                properties.Add(nameof(BuroEmployee.DistrictLangKey), command.DistrictLangKey);
            }
            if (!string.IsNullOrEmpty(command.Address) && existingEmployee.Address != command.Address)
            {
                properties.Add(nameof(BuroEmployee.Address), command.Address);
            }
            if (!string.IsNullOrEmpty(command.PostCode) && existingEmployee.PostCode != command.PostCode)
            {
                properties.Add(nameof(BuroEmployee.PostCode), command.PostCode);
            }
            if (!string.IsNullOrEmpty(command.PostOfficeId) && existingEmployee.PostOfficeId != command.PostOfficeId)
            {
                properties.Add(nameof(BuroEmployee.PostOfficeId), command.PostOfficeId);
            }
            if (!string.IsNullOrEmpty(command.PostOfficeName) && existingEmployee.PostOfficeName != command.PostOfficeName)
            {
                properties.Add(nameof(BuroEmployee.PostOfficeName), command.PostOfficeName);
            }
            if (!string.IsNullOrEmpty(command.PostOfficeLangKey) && existingEmployee.PostOfficeLangKey != command.PostOfficeLangKey)
            {
                properties.Add(nameof(BuroEmployee.PostOfficeLangKey), command.PostOfficeLangKey);
            }
            if (!string.IsNullOrEmpty(command.UnionId) && existingEmployee.UnionId != command.UnionId)
            {
                properties.Add(nameof(BuroEmployee.UnionId), command.UnionId);
            }
            if (!string.IsNullOrEmpty(command.UnionName) && existingEmployee.UnionName != command.UnionName)
            {
                properties.Add(nameof(BuroEmployee.UnionName), command.UnionName);
            }
            if (!string.IsNullOrEmpty(command.UnionLangKey) && existingEmployee.UnionLangKey != command.UnionLangKey)
            {
                properties.Add(nameof(BuroEmployee.UnionLangKey), command.UnionLangKey);
            }
            if (!string.IsNullOrEmpty(command.UpazilaId) && existingEmployee.UpazilaId != command.UpazilaId)
            {
                properties.Add(nameof(BuroEmployee.UpazilaId), command.UpazilaId);
            }
            if (!string.IsNullOrEmpty(command.UpazilaName) && existingEmployee.UpazilaName != command.UpazilaName)
            {
                properties.Add(nameof(BuroEmployee.UpazilaName), command.UpazilaName);
            }
            if (existingEmployee.UpazilaLangKey != command.UpazilaLangKey)
            {
                properties.Add(nameof(BuroEmployee.UpazilaLangKey), command.UpazilaLangKey);
            }
            if (!string.IsNullOrEmpty(command.WardId) && existingEmployee.WardId != command.WardId)
            {
                properties.Add(nameof(BuroEmployee.WardId), command.WardId);
            }
            if (!string.IsNullOrEmpty(command.WardName) && existingEmployee.WardName != command.WardName)
            {
                properties.Add(nameof(BuroEmployee.WardName), command.WardName);
            }
            if (!string.IsNullOrEmpty(command.WardLangKey) && existingEmployee.WardLangKey != command.WardLangKey)
            {
                properties.Add(nameof(BuroEmployee.WardLangKey), command.WardLangKey);
            }

            if (command.CustomProperties.Any())
            {
                var customPropertiesArray = CustomPropertyHelperService.ConvertToCustomPropertiesDictionary(command.CustomProperties);
                properties.Add(nameof(BuroOffice.CustomProperties), customPropertiesArray);
            }

            return properties;
        }
    }
}
