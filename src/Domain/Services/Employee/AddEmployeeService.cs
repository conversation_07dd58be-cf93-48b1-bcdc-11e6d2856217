using Domain.Commands.Employee;
using Domain.Contracts.Employee;
using Domain.Contracts.User;
using Domain.Services.Helper;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.EmployeeHistory;
using Infrastructure.Events.UserPermission;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Selise.Ecap.Entities.PrimaryEntities.PlatformDataService;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Employee;

public class AddEmployeeService : IAddEmployeeService
{
    private readonly ILogger<AddEmployeeService> _logger;
    private readonly ISequenceNumberClient _sequenceNumberClient;
    private readonly IServiceClient _serviceClient;
    private readonly IRepository _repository;
    private readonly IUserService _userService;

    public AddEmployeeService(IRepository repository, IUserService userService,
        ILogger<AddEmployeeService> logger, ISequenceNumberClient sequenceNumberClient, IServiceClient serviceClient)
    {
        _repository = repository;
        _userService = userService;
        _logger = logger;
        _sequenceNumberClient = sequenceNumberClient;
        _serviceClient = serviceClient;
    }

    public async Task<CommandResponse> SaveEmployee(AddEmployeeCommand command)
    {
        _logger.LogInformation("AddEmployeeService->SaveEmployee-START");
        var response = new CommandResponse();
        try
        {
            var designation = await _repository.GetItemAsync<BuroDesignation>(x => x.ItemId == command.DesignationId);
            if (designation == null)
            {
                _logger.LogError("AddEmployeeService->SaveEmployee->Failed to create employee -> No designation Found: " + command.DesignationId);
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "No designation Found");
                return response;
            }

            var designationRelatedRole = await _repository.GetItemAsync<BuroRole>(x => x.DesignationItemIds.Contains(command.DesignationId));
            if (designationRelatedRole == null)
            {
                _logger.LogError("AddEmployeeService->SaveEmployee->Failed to create employee -> No Role Found related to designation: " + command.DesignationId);
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "No Role Found related to designation");
                return response;
            }

            string userId = Guid.NewGuid().ToString();
            string personId = Guid.NewGuid().ToString();
            string employeePin = await GetSequenceNumber(BuroConstants.BuroEmployeePinIdentifier);

            var employeeModel = CreateEmployeeDataModel(command, employeePin, personId, userId, designation);
            var personModel = CreatePersonDataModel(employeeModel, designationRelatedRole.RoleKey, personId, userId);
            await _repository.SaveAsync(personModel);

            var userResponse = await _userService.CreateUserFromPerson(userId, personModel, employeeModel);
            response.ExtendCommandResponse(userResponse);
            if (!response.Errors.IsValid)
            {
                await _repository.DeleteAsync<Person>(x => x.ItemId == personModel.ItemId);
                _logger.LogError("AddEmployeeService->SaveEmployee->Failed to create employee USER");
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "No Role Found related to designation");
                return response;
            }

            await _repository.SaveAsync(employeeModel);

            CreateEmployeeTransferHistory(employeeModel);

            var updateModel = new Dictionary<string, object>
            {
                {nameof(User.Active), true},
                {nameof(User.EmailVarified), true},
                {nameof(User.IsSystemBlocked), false},
                {nameof(User.UserName), employeePin }
            };
            await _repository.UpdateAsync<User>(x => x.ItemId == userId, updateModel);
            AssignUserPermission(userId);
            _logger.LogInformation("AddEmployeeService->SaveEmployee-END");
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return response;
    }

    private void CreateEmployeeTransferHistory(BuroEmployee employee)
    {
        var transferEvent = PopulateTransferEvent(employee);

        _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, transferEvent);
    }

    private CreateEmployeeTransferHistoryEvent PopulateTransferEvent(BuroEmployee employee)
    {
        return new CreateEmployeeTransferHistoryEvent
        {
            EmployeeItemId = employee.ItemId,
            UserItemId = employee.UserItemId,
            DesignationItemId = employee.DesignationItemId,
            DesignationTitle = employee.DesignationTitle,
            DesignationCode = employee.DesignationCode,
            OfficeTitle = employee.CurrentOfficeTitle,
            OfficeItemId = employee.CurrentOfficeItemId,
            OfficeCode = employee.CurrentOfficeCode,
            StartDate = DateTime.UtcNow,
            TransferHistoryType = EnumEmployeeTransferHistoryType.DesignationChange
        };
    }

    private Person CreatePersonDataModel(BuroEmployee employee, string designationRole, string personId, string userId)
    {
        var person = new Person
        {
            ItemId = personId,
            FirstName = employee.EmployeeName,
            LastName = "",
            DisplayName = employee.EmployeeName,
            Email = $"{employee.EmployeePin}@burobd.com",
            PhoneNumber = !string.IsNullOrEmpty(employee.WorkPhone) ? employee.WorkPhone : employee.PersonalPhone,
            Roles = new string[] { BuiltInRoles.Appuser, BuiltInRoles.Anonymous, designationRole },
            Tags = new string[] { Tags.Person },
            ProposedUserId = userId,
            Designation = employee.DesignationItemId
        };

        person.AddEntityBasicInfo();

        return person;
    }

    private void AssignUserPermission(string userId)
    {
        var assignUserPermissionEvent = new AssignUserPermissionEvent
        {
            UserItemIds = new List<string>() { userId }
        };

        _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, assignUserPermissionEvent);
    }

    private BuroEmployee CreateEmployeeDataModel(AddEmployeeCommand command, string employeePin, string personId, string userId, BuroDesignation designation)
    {
        //var employeeId = await GetSequenceNumber(BuroConstants.BuroCenterIDIdentifier);
        var employee = new BuroEmployee
        {
            ItemId = personId,
            EmployeePin = employeePin,
            // EmployeeId = employeeId,
            EmployeeName = command.EmployeeName,
            FatherName = command.FatherName,
            MotherName = command.MotherName,
            EmployeeNid = command.NidNo,
            WorkPhone = command.WorkPhone,
            PersonalPhone = command.PersonalPhone,
            DesignationItemId = command.DesignationId,
            DesignationTitle = designation.Title,
            DesignationCode = designation.DesignationCode,
            DistrictId = command.DistrictId,
            DistrictName = command.DistrictName,
            DistrictLangKey = command.DistrictLangKey,
            Address = command.Address,
            PostCode = command.PostCode,
            PostOfficeId = command.PostOfficeId,
            PostOfficeName = command.PostOfficeName,
            PostOfficeLangKey = command.PostOfficeLangKey,
            UnionId = command.UnionId,
            UnionName = command.UnionName,
            UnionLangKey = command.UnionLangKey,
            UpazilaId = command.UpazilaId,
            UpazilaName = command.UpazilaName,
            UpazilaLangKey = command.UpazilaLangKey,
            WardId = command.WardId,
            WardName = command.WardName,
            WardLangKey = command.WardLangKey,
            CustomProperties = CustomPropertyHelperService.MapToEntityCustomProperties(command.CustomProperties),
            PersonItemId = personId,
            UserItemId = userId
        };

        employee.AddEntityBasicInfo();

        return employee;
    }

    private async Task<string> GetSequenceNumber(string context)
    {
        var sequence = await _sequenceNumberClient.GetSequenceNumber(new SequenceNumberQuery { Context = context });
        if (sequence == null || sequence.CurrentNumber <= 0)
        {
            throw new InvalidOperationException("Invalid sequence number received from the sequence number service.");
        }

        if (context == BuroConstants.BuroEmployeeIDIdentifier)
        {
            return sequence.CurrentNumber.ToString("D6");
        }
        else
        {
            return sequence.CurrentNumber.ToString("D5");
        }
    }

}