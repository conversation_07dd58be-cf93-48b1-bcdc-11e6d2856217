using Domain.Commands.Employee;
using Infrastructure.Constants;
using Infrastructure.Events.Employee;
using Infrastructure.Events.EmployeeHistory;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Employee
{
    public class EmployeeStaffingRequestService
    {
        private readonly ILogger<EmployeeStaffingRequestService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;
        public EmployeeStaffingRequestService(
            ILogger<EmployeeStaffingRequestService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> AddStaffingRequest(AddStaffingRequestCommand command)
        {
            _logger.LogInformation("Inside  AddStaffingRequest");

            var commandResponse = new CommandResponse();
            try
            {
                await ProcessAddStaffingRequest(command);
                _logger.LogInformation("Finished  AddStaffingRequest");
            }
            catch (Exception e)
            {
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
                _logger.LogError(e, "Staffing Request Creation Failed");
            }

            return commandResponse;
        }

        private async Task ProcessAddStaffingRequest(AddStaffingRequestCommand command)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var userId = securityContext.UserId;

            var employee = await GetEmployeeInfoById(command.EmployeeItemId);
            var requestor = await GetEmployeeByUserId(userId);

            if (employee == null)
            {
                _logger.LogError("EmployeeStaffingRequestService->ProcessAddStaffingRequest-> {ERROR}", BuroErrorMessageKeys.EmployeeNotFound);
                throw new InvalidOperationException(BuroErrorMessageKeys.EmployeeNotFound);
            }

            if (!string.IsNullOrEmpty(employee.CurrentOfficeItemId))
            {
                _logger.LogError("EmployeeStaffingRequestService->ProcessAddStaffingRequest-> {ERROR}", BuroErrorMessageKeys.ExistingEmployeeCantBeMoved);
                throw new InvalidOperationException(BuroErrorMessageKeys.ExistingEmployeeCantBeMoved);
            }

            var movingToOfficeInfo = await GetOfficeInfoById(command.MovingToOfficeItemId);
            var staffingRequest = CreateStaffingRequest(command, employee, userId, requestor.ItemId, movingToOfficeInfo.OfficeName);
            await _repository.SaveAsync(staffingRequest);

            var properties = PrepareDataToUpdateEmployee(movingToOfficeInfo);
            await UpdateBuroEmployee(properties, employee.ItemId);
            await SendToQueueForEmployeeSyncInOffice(movingToOfficeInfo.ItemId);

            CreateEmployeeTransferHistory(employee, movingToOfficeInfo, command);

            //Temp: Directly Updating Employee Info as Stafffing will be done from HRM module
            //SendToQueue(staffingRequest.ItemId);
        }

        private void CreateEmployeeTransferHistory(BuroEmployee employee, BuroOffice office, AddStaffingRequestCommand command)
        {
            var transferEvent = PopulateTransferEvent(employee, office, command);

            _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, transferEvent);
        }

        private CreateEmployeeTransferHistoryEvent PopulateTransferEvent(BuroEmployee employee, BuroOffice office, AddStaffingRequestCommand command)
        {
            return new CreateEmployeeTransferHistoryEvent
            {
                EmployeeItemId = employee.ItemId,
                UserItemId = employee.UserItemId,
                DesignationItemId = employee.DesignationItemId,
                DesignationTitle = employee.DesignationTitle,
                DesignationCode = employee.DesignationCode,
                OfficeTitle = office.OfficeName,
                OfficeItemId = office.ItemId,
                OfficeCode = office.OfficeCode,
                StartDate = command.TransferStartDate,
                EndDate = command.TransferEndDate,
                TransferHistoryType = EnumEmployeeTransferHistoryType.OfficeChange
            };
        }

        private static BuroStaffingRequest CreateStaffingRequest(
            AddStaffingRequestCommand command,
            BuroEmployee employee,
            string userId,
            string requestorEmployeeId,
            string movingToOfficeName)
        {
            var staffingRequest = new BuroStaffingRequest
            {
                ItemId = Guid.NewGuid().ToString(),
                CreatedBy = userId,
                LastUpdatedBy = userId,
                RolesAllowedToRead = new[] { UserRoles.Admin },
                IdsAllowedToRead = new[] { userId },
                IdsAllowedToDelete = new[] { userId },
                IdsAllowedToUpdate = new[] { userId },
                IdsAllowedToWrite = new[] { userId },
                EmployeeItemId = employee.ItemId,
                EmployeeName = employee.EmployeeName,
                EmployeePin = employee.EmployeePin,
                MovingToOfficeName = movingToOfficeName,
                MovingToOfficeId = command.MovingToOfficeItemId,
                TransferType = command.TransferType,
                TransferStartDate = command.TransferStartDate,
                TransferEndDate = command.TransferEndDate,
                TransferEffectiveDate = command.TransferStartDate,
                TransferRequestedByEmployeeId = requestorEmployeeId,
                Status = EnumStaffingRequestStatus.Completed,   //Temp: Directly Updating Employee Info as Stafffing will be done from HRM module
                TransferReason = command.TransferReason,
                ApprovalRequiredFromEmployeeIds = Array.Empty<string>(), //TODO: add needed approval ids
            };
            staffingRequest.AddEntityBasicInfo();

            return staffingRequest;
        }

        private Task SendToQueueForEmployeeSyncInOffice(string officeId)
        {
            var syncNumberOfEmployeeEvent = new SyncNumberOfEmployeesEvent
            {
                OfficeId = officeId
            };
            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, syncNumberOfEmployeeEvent);
            return Task.CompletedTask;
        }

        private Task<BuroEmployee> GetEmployeeInfoById(string employeeId)
        {
            return _repository.GetItemAsync<BuroEmployee>(x => x.ItemId == employeeId);
        }

        private Task<BuroEmployee> GetEmployeeByUserId(string userId)
        {
            return _repository.GetItemAsync<BuroEmployee>(x => x.UserItemId == userId);
        }

        private async Task<BuroOffice> GetOfficeInfoById(string officeId)
        {
            return await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == officeId);
        }

        private static Dictionary<string, object> PrepareDataToUpdateEmployee(BuroOffice movingToOfficeInfo)
        {
            return new Dictionary<string, object>
            {
                {nameof(BuroEmployee.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroEmployee.LastUpdatedBy), BuroConstants.SuperAdmin },
                {nameof(BuroEmployee.CurrentOfficeTitle), movingToOfficeInfo.OfficeName},
                {nameof(BuroEmployee.CurrentOfficeItemId), movingToOfficeInfo.ItemId},
                {nameof(BuroEmployee.CurrentOfficeCode), movingToOfficeInfo.OfficeCode},
                {nameof(BuroEmployee.Hid), movingToOfficeInfo.Hid},
                {nameof(BuroEmployee.Depth), movingToOfficeInfo.Depth},
                {nameof(BuroEmployee.Path), movingToOfficeInfo.Path},
            };
        }

        private async Task UpdateBuroEmployee(Dictionary<string, object> properties, string employeeId)
        {
            await _repository.UpdateAsync<BuroEmployee>(x => x.ItemId == employeeId, properties);
        }


    }
}
