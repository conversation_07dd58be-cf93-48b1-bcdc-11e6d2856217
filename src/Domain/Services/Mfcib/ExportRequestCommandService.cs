using Domain.Commands.Mfcib;
using Domain.Contracts.Member;
using Domain.Contracts.Mfcib;
using Infrastructure.Constants;
using Infrastructure.Events.Mfcib;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Mfcib;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Mfcib
{
    public class ExportRequestCommandService : IExportRequestCommandService
    {
        private readonly ILogger<ExportRequestCommandService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ISharedService _sharedService;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;

        public ExportRequestCommandService(
            ILogger<ExportRequestCommandService> logger,
            ISecurityContextProvider securityContextProvider,
            ISharedService sharedService,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _sharedService = sharedService;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> CreateRequestAsync(ExportRequestCommand command)
        {
            _logger.LogInformation("Start placing request for CIB data for member Id: {MemberItemId}", command.MemberItemIds);

            var commandResponse = new CommandResponse();

            try
            {
                var currentEmployee = await GetCurrentEmployeeAsync();

                var newRequest = await CreateRequestForCibDataAsync(command, currentEmployee);

                await SaveRequestForCibHistoryDataAsync(newRequest, currentEmployee);

                SendToHandleCIBDataSubmission(newRequest.ItemId);

                _logger.LogInformation("Request for CIB data successfully submitted for member Id: {MemberItemId}", command.MemberItemIds);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during CIB data request submission for member Id: {MemberItemId}", command.MemberItemIds);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while requesting for CIB data.");
            }

            return commandResponse;
        }

        private void SendToHandleCIBDataSubmission(string MfcibRequestItemId)
        {
            var payload = new MfcibRequestPostProcessingEvent
            {
                MfcibRequestItemId = MfcibRequestItemId
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("CIB export request submission post processing initiated for Request Id: {MfcibRequestItemId}", MfcibRequestItemId);
        }

        private async Task SaveRequestForCibHistoryDataAsync(BuroMfcibRequest newRequest, BuroEmployee currentEmployee)
        {
            var history = new BuroMfcibRequestHistory
            {
                MfcibRequestItemId = newRequest.ItemId,
                Status = newRequest.Status,
                ActionByEmployeeItemId = currentEmployee.ItemId,
                ActionByEmployeePin = currentEmployee.EmployeePin,
                ActionByEmployeeName = currentEmployee.EmployeeName,
                ActionByEmployeeDesginationItemId = currentEmployee.DesignationItemId,
                ActionByEmployeeDesginationTitle = currentEmployee.DesignationTitle,
                ActionByEmployeeOfficeItemId = currentEmployee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = currentEmployee.CurrentOfficeCode
            };

            history.AddEntityBasicInfo();

            await _repository.SaveAsync(history);
        }

        private async Task<BuroEmployee> GetCurrentEmployeeAsync()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var employee = await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId);

            return employee;
        }

        private async Task<BuroMfcibRequest> CreateRequestForCibDataAsync(ExportRequestCommand command, BuroEmployee currentEmployee)
        {
            var sequenceNumber = await _sharedService.GetSequenceNumberAsync(BuroConstants.BuroMfcibRequestIdentifier, "MFCIB");

            var newRequest = new BuroMfcibRequest
            {
                SequenceNumber = sequenceNumber,
                MemberItemIds = command.MemberItemIds,
                Status = EnumMfCibRequestStatus.Pending,
                ActionByEmployeeItemId = currentEmployee.ItemId,
                ActionByEmployeeName = currentEmployee.EmployeeName,
                ActionByEmployeePin = currentEmployee.EmployeePin
            };

            newRequest.AddEntityBasicInfo();

            await _repository.SaveAsync(newRequest);

            return newRequest;
        }
    }
}
