using Domain.Commands.Asset;
using FinMongoDbRepositories;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Assets;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Asset;

public class CreateAssetItemService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateAssetItemService(IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task CreateAssetItemAsync(CreateAssetItemCommand command)
    {
        var loggedInEmployee = await GetLoggedInEmployeeAsync();
        var assetItem = PopulateAssetItem(command, loggedInEmployee);
        await _repository.InsertOneAsync(assetItem);
    }

    private async Task<BuroEmployee> GetLoggedInEmployeeAsync()
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return await _repository.FindOneAsync<BuroEmployee>(x => x.UserItemId == userId);
    }

    private BuroAssetItem PopulateAssetItem(CreateAssetItemCommand command, BuroEmployee loggedInEmployee)
    {
        return new BuroAssetItem
        {
            ItemId = Guid.NewGuid().ToString(),
            GroupItemId = command.GroupItemId,
            GroupName = command.GroupName,
            SubGroupItemId = command.SubGroupItemId,
            SubGroupName = command.SubGroupName,
            AssetName = command.AssetName,
            CreatedBy = loggedInEmployee.UserItemId,
            CreateDate = DateTime.UtcNow,
            CreatedByEmployeeName = loggedInEmployee.EmployeeName,
            CreatedByEmployeeDesignationItemId = loggedInEmployee.DesignationItemId,
            CreatedByEmployeeDesignationTitle = loggedInEmployee.DesignationTitle,
            CreatedByEmployeePin = loggedInEmployee.EmployeePin
        };
    }
}