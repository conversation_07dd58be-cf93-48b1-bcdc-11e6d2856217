using Domain.Commands.Asset;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Assets;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Asset;

public class CreateAssetSubGroupService
{
    private readonly IFinMongoDbRepository _repository;

    public CreateAssetSubGroupService(IFinMongoDbRepository repository)
    {
        _repository = repository;
    }

    public async Task<CommandResponse> CreateAssetSubGroupAsync(CreateAssetSubGroupCommand command)
    {
        var response = new CommandResponse();
        var assetGroup = await FetchAssetGroupAsync(command.GroupItemId);
        if (assetGroup == null)
        {
            response.SetError(BuroErrorMessageKeys.AssetGroupDoesntExists, "Asset group doesn't exist.");
            return response;
        }
        var assetSubGroup = PopulateAssetSubGroup(command, assetGroup);
        await _repository.InsertOneAsync(assetSubGroup);
        return response;
    }

    private async Task<BuroAssetGroup?> FetchAssetGroupAsync(string commandGroupItemId)
    {
        return await _repository.FindOneAsync<BuroAssetGroup>(item => item.ItemId == commandGroupItemId);
    }

    private BuroAssetSubGroup PopulateAssetSubGroup(CreateAssetSubGroupCommand command, BuroAssetGroup assetGroup)
    {
        var additionalProperties = command.AdditionalProperties.Select(x =>
        {
            x.ItemId = Guid.NewGuid().ToString();
            return x;
        }).ToList();
        
        return new BuroAssetSubGroup
        {
            ItemId = Guid.NewGuid().ToString(),
            GroupItemId = assetGroup.ItemId,
            GroupCode = assetGroup.GroupCode,
            GroupName = assetGroup.GroupName,
            GroupDescription = assetGroup.GroupDescription,
            SubGroupCode = command.SubGroupCode,
            SubGroupName = command.SubGroupName,
            SubGroupDescription = command.SubGroupDescription,
            DepreciationRate = command.DepreciationRate,
            PartialSale = command.PartialSale,
            AdditionalProperties = additionalProperties
        };
    }
}