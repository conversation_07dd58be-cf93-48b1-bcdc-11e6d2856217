using Domain.Commands.Asset;
using FinMongoDbRepositories;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Assets;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Asset;

public class UpdateAssetGroupService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateAssetGroupService(IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task UpdateAssetGroupAsync(UpdateAssetGroupCommand command)
    {
        var updatedSubGroupProperties = GetUpdatedSubGroupProperties(command);
        await _repository.UpdateManyAsync<BuroAssetSubGroup>(x => x.GroupItemId == command.GroupItemId, updatedSubGroupProperties);

        var updatedAssetItemProperties = GetUpdatedAssetItemProperties(command);
        await _repository.UpdateManyAsync<BuroAssetItem>(x => x.GroupItemId == command.GroupItemId, updatedAssetItemProperties);

        var updatedGroupProperties = GetUpdatedGroupProperties(command);
        await _repository.UpdateOneAsync<BuroAssetGroup>(x => x.ItemId == command.GroupItemId, updatedGroupProperties);
    }

    private Dictionary<string, object> GetUpdatedGroupProperties(UpdateAssetGroupCommand command)
    {
        return new Dictionary<string, object>()
        {
            { nameof(BuroAssetGroup.GroupCode), command.GroupCode },
            { nameof(BuroAssetGroup.GroupName), command.GroupName },
            { nameof(BuroAssetGroup.GroupDescription), command.GroupDescription },
            { nameof(BuroAssetGroup.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroAssetGroup.LastUpdatedBy), _securityContextProvider.GetSecurityContext().UserId }
        };
    }

    private Dictionary<string, object> GetUpdatedSubGroupProperties(UpdateAssetGroupCommand command)
    {
        return new Dictionary<string, object>()
        {
            { nameof(BuroAssetSubGroup.GroupCode), command.GroupCode },
            { nameof(BuroAssetSubGroup.GroupName), command.GroupName },
            { nameof(BuroAssetSubGroup.GroupDescription), command.GroupDescription },
            { nameof(BuroAssetSubGroup.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroAssetSubGroup.LastUpdatedBy), _securityContextProvider.GetSecurityContext().UserId }
        };
    }

    private Dictionary<string, object> GetUpdatedAssetItemProperties(UpdateAssetGroupCommand command)
    {
        return new Dictionary<string, object>()
        {
            { nameof(BuroAssetItem.GroupName), command.GroupName },
            { nameof(BuroAssetItem.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroAssetItem.LastUpdatedBy), _securityContextProvider.GetSecurityContext().UserId }
        };
    }
}