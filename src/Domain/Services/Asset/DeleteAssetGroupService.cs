using Domain.Commands.Asset;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Assets;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Asset;

public class DeleteAssetGroupService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DeleteAssetGroupService(IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task<CommandResponse> DeleteAssetGroupAsync(DeleteAssetGroupCommand command)
    {
        var commandResponse = new CommandResponse();
        var hasAssociatedAssetItems =
            await _repository.ExistsAsync<BuroAssetItem>(x => x.GroupItemId == command.GroupItemId);


        if (hasAssociatedAssetItems)
        {
            commandResponse.SetError(BuroErrorMessageKeys.AssetGroupCannotBeDeleted, "Asset group cannot be deleted.");
            return commandResponse;
        }

        var subGroupUpdatedProperties = GetSubGroupUpdatedProperties(command);
        await _repository.UpdateManyAsync<BuroAssetSubGroup>(x => x.GroupItemId == command.GroupItemId, subGroupUpdatedProperties);

        var groupUpdatedProperties = GetGroupUpdatedProperties(command);
        await _repository.UpdateOneAsync<BuroAssetGroup>(x => x.ItemId == command.GroupItemId, groupUpdatedProperties);

        return commandResponse;
    }

    private Dictionary<string, object> GetGroupUpdatedProperties(DeleteAssetGroupCommand command)
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return new Dictionary<string, object>
        {
            { nameof(BuroAssetGroup.IsArchived), true },
            { nameof(BuroAssetGroup.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroAssetGroup.LastUpdatedBy), userId }
        };
    }

    private Dictionary<string, object> GetSubGroupUpdatedProperties(DeleteAssetGroupCommand command)
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return new Dictionary<string, object>
        {
            { nameof(BuroAssetSubGroup.IsArchived), true },
            { nameof(BuroAssetSubGroup.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroAssetSubGroup.LastUpdatedBy), userId }
        };
    }
}