using Domain.Commands.Asset;
using FinMongoDbRepositories;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Assets;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Asset;

public class CreateAssetGroupService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateAssetGroupService(IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task CreateAssetGroupAsync(CreateAssetGroupCommand command)
    {
        var loggedInEmployee = await GetLoggedInEmployeeAsync();
        var assetGroup = PopulateAssetGroup(command, loggedInEmployee);
        await _repository.InsertOneAsync(assetGroup);
    }

    private async Task<BuroEmployee> GetLoggedInEmployeeAsync()
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return await _repository.FindOneAsync<BuroEmployee>(x => x.UserItemId == userId);
    }

    private BuroAssetGroup PopulateAssetGroup(CreateAssetGroupCommand command, BuroEmployee loggedInEmployee)
    {
        return new BuroAssetGroup
        {
            ItemId = Guid.NewGuid().ToString(),
            GroupCode = command.GroupCode,
            GroupName = command.GroupName,
            GroupDescription = command.GroupDescription,
            CreatedBy = loggedInEmployee.UserItemId,
            CreateDate = DateTime.UtcNow,
            CreatedByEmployeeName = loggedInEmployee.EmployeeName,
            CreatedByEmployeeDesignationItemId = loggedInEmployee.DesignationItemId,
            CreatedByEmployeeDesignationTitle = loggedInEmployee.DesignationTitle,
            CreatedByEmployeePin = loggedInEmployee.EmployeePin
        };
    }
}