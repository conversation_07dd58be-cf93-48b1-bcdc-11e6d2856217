using Domain.Commands.VoucherTransaction;
using Domain.Contracts.VoucherTransaction;
using FinMongoDbRepositories;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.VoucherTransaction;

public class VoucherTransactionSetupCommandService : IVoucherTransactionSetupCommandService
{
    private readonly ILogger<VoucherTransactionSetupCommandService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IFinMongoDbRepository _repository;

    public VoucherTransactionSetupCommandService(
        ILogger<VoucherTransactionSetupCommandService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> SetupVoucherTransactionAsync(VoucherTransactionSetupCommand command)
    {
        
    }
}