using Domain.Commands.Account;
using Domain.Contracts.Account;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class SubmitDisbursementCommandService : ISubmitDisbursementCommandService
    {
        private readonly ILogger<SubmitDisbursementCommandService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ISharedService _sharedService;
        private readonly IServiceClient _serviceClient;

        public SubmitDisbursementCommandService(
            ILogger<SubmitDisbursementCommandService> logger,
            IRepository repository,
            ISecurityContextProvider securityContextProvider,
            ISharedService sharedService,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> SubmitDisbursementAsync(SubmitDisbursementCommand command)
        {
            _logger.LogInformation("Start submitting loan Application disbursement with ID: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var securityContext = _securityContextProvider.GetSecurityContext();
                var currentEmployee = await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == securityContext.UserId);

                await SubmitDisbursementDataAsync(command, currentEmployee);
                await SaveDisbursementHistoryAsync(command, currentEmployee);

                SendToHandleLoanDisbursement(command);

                _logger.LogInformation("Successfully submitted loan application disbursement with ID: {LoanApplicationItemId}", command.LoanApplicationItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred while submitting loan application disbursement with ID: {LoanApplicationItemId}", command.LoanApplicationItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while submitting loan application disbursement");
            }

            return commandResponse;
        }

        private void SendToHandleLoanDisbursement(SubmitDisbursementCommand command)
        {
            var payload = new LoanDisbursementPostProcessingEvent
            {
                LoanApplicationDisbursementItemId = command.LoanApplicationDisbursementItemId,
                LoanApplicationItemId = command.LoanApplicationItemId,
                Status = command.Status,
                FinalMicroInsuranceFee = command.FinalMicroInsuranceFee
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("Loan disbursement post processing initiated for Application ID: {LoanApplicationItemId}", command.LoanApplicationItemId);
        }

        private async Task SubmitDisbursementDataAsync(SubmitDisbursementCommand command, BuroEmployee currentEmployee)
        {
            var loanApplication = await GetLoanApplicationAsync(command);
            var existingDisbursement = await GetLoanDisbursementAsync(command);

            var disbursement = existingDisbursement ?? new BuroLoanApplicationDisbursement
            {
                ProposedLoanAccountItemId = loanApplication.ProposedLoanAccountItemId,
            };

            switch (command.Status)
            {
                case EnumLoanApplicationDisbursementStatus.NegotiationSubmited:
                    await HandleNegotiationSubmitionAsync(command, loanApplication, existingDisbursement, disbursement);
                    break;
                case EnumLoanApplicationDisbursementStatus.LoanScheduleSubmited:
                    await HandleLoanScheduleSubmitionAsync(command, currentEmployee, existingDisbursement, disbursement);
                    break;
                case EnumLoanApplicationDisbursementStatus.CustomerSecurityFundCollected:
                    await HandleCustomerSecurityFundCollectionAsync(command, currentEmployee, existingDisbursement, disbursement);
                    break;
                case EnumLoanApplicationDisbursementStatus.DisbursementAcknowledged:
                    await HandleDisbursementAcknowledgementAsync(command, currentEmployee, existingDisbursement, disbursement);
                    break;
                default:
                    break;
            }
        }

        private async Task HandleDisbursementAcknowledgementAsync(
            SubmitDisbursementCommand command,
            BuroEmployee currentEmployee,
            BuroLoanApplicationDisbursement? existingDisbursement,
            BuroLoanApplicationDisbursement disbursement)
        {
            if (existingDisbursement == null)
            {
                return;
            }

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanApplicationDisbursement.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroLoanApplicationDisbursement.LastUpdatedBy), currentEmployee.UserItemId },
                {nameof(BuroLoanApplicationDisbursement.Status), command.Status},
                {nameof(BuroLoanApplicationDisbursement.DisbursementAcknowledgementDetails), command.DisbursementAcknowledgementDetails.ToBsonDocument() }
            };

            await _repository.UpdateAsync<BuroLoanApplicationDisbursement>(d => d.ItemId == disbursement.ItemId, properties);
        }

        private async Task HandleCustomerSecurityFundCollectionAsync(
            SubmitDisbursementCommand command,
            BuroEmployee currentEmployee,
            BuroLoanApplicationDisbursement? existingDisbursement,
            BuroLoanApplicationDisbursement disbursement)
        {
            if (existingDisbursement == null)
            {
                return;
            }

            // NOSONAR need to set this in configuration
            var loanApplicationFee = 25;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanApplicationDisbursement.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroLoanApplicationDisbursement.LastUpdatedBy), currentEmployee.UserItemId },
                {nameof(BuroLoanApplicationDisbursement.Status), command.Status},
                {nameof(BuroLoanApplicationDisbursement.FinalMicroInsuranceFee), command.FinalMicroInsuranceFee},
                {nameof(BuroLoanApplicationDisbursement.LoanApplicationFee), loanApplicationFee}
            };

            await _repository.UpdateAsync<BuroLoanApplicationDisbursement>(d => d.ItemId == disbursement.ItemId, properties);
        }

        private async Task HandleLoanScheduleSubmitionAsync(
            SubmitDisbursementCommand command,
            BuroEmployee currentEmployee,
            BuroLoanApplicationDisbursement? existingDisbursement,
            BuroLoanApplicationDisbursement disbursement)
        {
            if (existingDisbursement == null)
            {
                return;
            }

            // NOSONAR generate loan schedule (here or winservice ?)
            var loanSchedules = new List<LoanSchedule>();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanApplicationDisbursement.LastUpdateDate), DateTime.UtcNow },
                {nameof(BuroLoanApplicationDisbursement.LastUpdatedBy), currentEmployee.UserItemId },
                {nameof(BuroLoanApplicationDisbursement.Status), command.Status},
                {nameof(BuroLoanApplicationDisbursement.LoanSchedules), new BsonArray(loanSchedules.Select(s => s.ToBsonDocument())) }
            };

            await _repository.UpdateAsync<BuroLoanApplicationDisbursement>(d => d.ItemId == disbursement.ItemId, properties);
        }

        private async Task HandleNegotiationSubmitionAsync(
            SubmitDisbursementCommand command,
            BuroLoanApplication loanApplication,
            BuroLoanApplicationDisbursement? existingDisbursement,
            BuroLoanApplicationDisbursement disbursement)
        {
            if (existingDisbursement != null)
            {
                return;
            }

            var productLine = await GetProductLineAsync(loanApplication);
            var sequenceNumebr = await _sharedService.GetSequenceNumberAsync(BuroConstants.BuroLoanDisbursementIdentifier);

            disbursement.LoanApplicationItemId = command.LoanApplicationItemId;
            disbursement.ProductLineItemId = loanApplication.ProductLineItemId;
            disbursement.SequenceNumber = sequenceNumebr;
            disbursement.Status = command.Status;
            disbursement.NegotiationInformation = new LoanDisbursementNegotiationInformation
            {
                MinGracePeriodInDays = productLine.MinGracePeriodInDays,
                MaxGracePeriodInDays = productLine.MaxGracePeriodInDays,
                PaymentInterval = loanApplication.PaymentInterval,
                FirstInstallmentDate = command.FirstInstallmentDate,
                ChequeInformation = command.ChequeInformation,
                LoanAgreementFormFileItemId = command.LoanAgreementFormFileItemId,
                SecurityChequeFileItemId = command.SecurityChequeFileItemId
            };

            disbursement.AddEntityBasicInfo();

            await _repository.SaveAsync(disbursement);
        }

        private async Task<BuroLoanApplicationDisbursement> GetLoanDisbursementAsync(SubmitDisbursementCommand command)
        {
            return await _repository.GetItemAsync<BuroLoanApplicationDisbursement>(
                d => d.ItemId == command.LoanApplicationDisbursementItemId
                  || d.LoanApplicationItemId == command.LoanApplicationItemId);
        }

        private async Task<BuroLoanApplication> GetLoanApplicationAsync(SubmitDisbursementCommand command)
        {
            return await _repository.GetItemAsync<BuroLoanApplication>(a => a.ItemId == command.LoanApplicationItemId);
        }

        private async Task<LoanProductLine> GetProductLineAsync(BuroLoanApplication loanApplication)
        {
            return await _repository.GetItemAsync<LoanProductLine>(l => l.ItemId == loanApplication.ProductLineItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(LoanProductLine)));
        }

        private async Task SaveDisbursementHistoryAsync(SubmitDisbursementCommand command, BuroEmployee currentEmployee)
        {
            var history = new BuroLoanApplicationDisbursementHistory
            {
                LoanApplicationDisbursementItemId = command.LoanApplicationDisbursementItemId,
                Status = command.Status,
                ActionByEmployeeItemId = currentEmployee.ItemId,
                ActionByEmployeePin = currentEmployee.EmployeePin,
                ActionByEmployeeName = currentEmployee.EmployeeName,
                ActionByEmployeeDesginationItemId = currentEmployee.DesignationItemId,
                ActionByEmployeeDesginationTitle = currentEmployee.DesignationTitle,
                ActionByEmployeeOfficeItemId = currentEmployee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = currentEmployee.CurrentOfficeCode,
                Remarks = command.Remarks
            };

            history.AddEntityBasicInfo();

            await _repository.SaveAsync(history);
        }
    }
}
