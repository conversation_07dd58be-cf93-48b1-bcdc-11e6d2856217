using Domain.Commands.Account;
using Domain.Contracts.Account;
using Infrastructure.Constants;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class ApplyLoanCommandService : IApplyLoanCommandService
    {
        private readonly ILogger<ApplyLoanCommandService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILoanCommonService _loanCommonService;
        private readonly IServiceClient _serviceClient;

        public ApplyLoanCommandService(
            ILogger<ApplyLoanCommandService> logger,
            IRepository repository,
            ISecurityContextProvider securityContextProvider,
            ILoanCommonService loanCommonService,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _securityContextProvider = securityContextProvider;
            _loanCommonService = loanCommonService;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> ApplyForLoanAsync(ApplyLoanCommand command)
        {
            _logger.LogInformation("Starting Loan Application for member ID: {MemberItemId}", command.MemberItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var currentEmployee = await GetCurrentEmployeeAsync();
                var loanApplication = await CreateLoanApplicationAsync(command, currentEmployee);

                await SaveLoanApplicaitonHistoryAsync(command, loanApplication.ItemId, currentEmployee);

                SendToHandleLoanApplication(loanApplication.ItemId, loanApplication.Status);

                _logger.LogInformation("Successfully applied for loan for member ID: {MemberItemId}", command.MemberItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during loan application for member ID: {MemberItemId}", command.MemberItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while applying for loan.");
            }

            return commandResponse;
        }

        private void SendToHandleLoanApplication(string loanApplicationItemId, EnumProductApplicationStatus status)
        {
            var payload = new LoanApplicationPostProcessingEvent
            {
                LoanApplicationItemId = loanApplicationItemId,
                Status = status
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroProductAccountQueue, payload);

            _logger.LogInformation("Loan application post processing initiated for Application ID: {LoanApplicationItemId}", loanApplicationItemId);
        }

        private async Task SaveLoanApplicaitonHistoryAsync(ApplyLoanCommand command, string loanApplicationItemId, BuroEmployee employee)
        {
            _logger.LogInformation("Saving Loan application history for loan ID: {LoanApplicationItemId}", loanApplicationItemId);

            var history = new BuroProductApplicationHistory
            {
                ApplicationItemId = loanApplicationItemId,
                ActionByEmployeeItemId = employee.ItemId,
                ActionByEmployeePin = employee.EmployeePin,
                ActionByEmployeeName = employee.EmployeeName,
                ActionByEmployeeDesginationItemId = employee.DesignationItemId,
                ActionByEmployeeDesginationTitle = employee.DesignationTitle,
                ActionByEmployeeOfficeItemId = employee.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = employee.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = employee.CurrentOfficeCode,
                Status = command.Status,
                ProductType = EnumProductType.Loan,
                MetaData = GetMetaDataForLoanApplication(command)
            };

            history.AddEntityBasicInfo();

            await _repository.SaveAsync(history);
        }

        private async Task<BuroEmployee> GetCurrentEmployeeAsync()
        {
            var context = _securityContextProvider.GetSecurityContext();
            return await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == context.UserId);
        }

        private static List<MetaData> GetMetaDataForLoanApplication(ApplyLoanCommand command)
        {
            return new List<MetaData>
            {
                new()
                {
                    Key = nameof(BuroLoanApplication.MemberItemId),
                    Value = command.MemberItemId
                },
                new()
                {
                    Key = nameof(BuroLoanApplication.ProductLineItemId),
                    Value = command.ProductLineItemId
                }
            };
        }

        private async Task<BuroLoanApplication> CreateLoanApplicationAsync(ApplyLoanCommand command, BuroEmployee currentEmployee)
        {
            var proposedLoanAccountItemId = Guid.NewGuid().ToString();
            var productLine = await _loanCommonService.GetProductLineByItemIdAsync(command.ProductLineItemId);
            var sectorInfo = productLine.AllowedSectors.Find(s => s.SectorItemId == command.LoanSectorItemId);
            var totalNumberOfInstallment = _loanCommonService.CalculateTotalNumberOfInstallment(command.PaymentInterval, command.TenureDetails.Duration);
            var existingLoanApplication = await _loanCommonService.GetLoanApplicationByItemId(command.LoanApplicationItemId);

            var loanApplication = existingLoanApplication ?? new BuroLoanApplication();
            var reviewer = await _loanCommonService.GetEmployeeInfoByDesignationForAnOfficeAsync(
                currentEmployee.CurrentOfficeItemId,
                BuroDesignationConstant.BranchAccountantDesignationCode);

            loanApplication.Status = command.Status;
            loanApplication.RequestedLoanAmount = command.LoanAmount;
            loanApplication.LoanRequiredByDate = command.LoanRequiredByDate.GetValueOrDefault().ToUniversalTime();
            loanApplication.ProductLineItemId = command.ProductLineItemId;
            loanApplication.LoanSectorItemId = sectorInfo?.SectorItemId;
            loanApplication.LoanSectorName = sectorInfo?.SectorName;
            loanApplication.LoanSectorCode = sectorInfo?.BuroSectorCode;
            loanApplication.ProductLineName = productLine.LineName;
            loanApplication.TenureDetails = command.TenureDetails;
            loanApplication.PaymentInterval = command.PaymentInterval;
            loanApplication.FatherOrSpouseName = command.FatherOrSpouseName;
            loanApplication.MotherName = command.MotherName;
            loanApplication.Investment = command.Investment;
            loanApplication.BusinessInformation = command.BusinessInformation;
            loanApplication.SelfNetIncome = command.SelfNetIncome;
            loanApplication.FamilyNetIncome = command.FamilyNetIncome;
            loanApplication.Guarantors = command.Guarantors;
            loanApplication.AdditionalDocumentIds = command.AdditionalDocumentIds;
            loanApplication.Reviewer = reviewer;
            loanApplication.Reason = command.Reason;
            loanApplication.InitialMicroInsuranceFee = command.InitialMicroInsuranceFee;
            loanApplication.InterestRate = productLine.InterestRate;
            loanApplication.TotalNumberOfInstallment = totalNumberOfInstallment;

            if (existingLoanApplication == null)
            {
                var member = await _loanCommonService.GetMemberByItemIdAsync(command.MemberItemId);
                var center = await _loanCommonService.GetCenterByItemIdAsync(member.CenterItemId);
                var newLoanApplicationItemId = string.IsNullOrWhiteSpace(command.LoanApplicationItemId) ? Guid.NewGuid().ToString() : command.LoanApplicationItemId;
                var sequenceNumber = await _loanCommonService.GetLoanApplicationSequenceNumberAsync();

                loanApplication.ItemId = newLoanApplicationItemId;
                loanApplication.SequenceNumber = sequenceNumber;
                loanApplication.MemberItemId = command.MemberItemId;
                loanApplication.MemberName = member.MemberName;
                loanApplication.MemberNIDNumber = member.NIDNumber;
                loanApplication.MemberSequenceNumber = member.MemberSequenceNumber;
                loanApplication.PersonItemId = member.PersonItemId;
                loanApplication.BranchItemId = center?.BranchId;
                loanApplication.CenterItemId = center?.ItemId;
                loanApplication.CenterName = center?.Name;
                loanApplication.CenterSequenceNumber = center?.CenterId;
                loanApplication.ProgramOrganizerEmployeeItemId = center?.ProgramOrganizerEmployeeItemId ?? string.Empty;
                loanApplication.ProgramOrganizerEmployeeName = center?.ProgramOrganizerEmployeeName ?? string.Empty;
                loanApplication.Initiator = _loanCommonService.MakeEmployeeInfo(currentEmployee);
                loanApplication.ProposedLoanAccountItemId = proposedLoanAccountItemId;

                loanApplication.AddEntityBasicInfo();

                await _repository.SaveAsync(loanApplication);
            }
            else
            {
                await _repository.UpdateAsync(l => l.ItemId == command.LoanApplicationItemId, loanApplication);
            }

            return loanApplication;
        }
    }
}
