using Domain.Contracts.Account;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class LoanCommonService : ILoanCommonService
    {
        private readonly ILogger<LoanCommonService> _logger;
        private readonly ISharedService _sharedService;
        private readonly IRepository _repository;

        public LoanCommonService(
            ILogger<LoanCommonService> logger,
            ISharedService sharedService,
            IRepository repository)
        {
            _logger = logger;
            _sharedService = sharedService;
            _repository = repository;
        }

        public async Task<EmployeeInfo> GetEmployeeInfoByDesignationForAnOfficeAsync(string officeItemId, string designationCode)
        {
            _logger.LogInformation("Inside GetEmployeeInfoByDesignationForAnOfficeAsync");

            if (officeItemId.IsNullOrEmptyOrWhiteSpace() || designationCode.IsNullOrEmptyOrWhiteSpace())
            {
                return default!;
            }

            var employee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(officeItemId, designationCode);

            _logger.LogInformation("Found employee with ID: {ItemId}, office Id: {OfficeItemId}, Designation {DesignationCode}", employee.ItemId, officeItemId, designationCode);

            return MakeEmployeeInfo(employee);
        }

        public EmployeeInfo MakeEmployeeInfo(BuroEmployee employee)
        {
            if (employee == null)
            {
                return default!;
            }

            return new EmployeeInfo
            {
                ItemId = employee.ItemId,
                Name = employee.EmployeeName,
                SequenceNumber = employee.EmployeePin,
                DesignationItemId = employee.DesignationItemId,
                DesignationTitle = employee.DesignationTitle
            };
        }

        public async Task<string> GetLoanApplicationSequenceNumberAsync()
        {
            _logger.LogInformation("Inside GetLoanApplicationSequenceNumberAsync");

            var accountCode = _sharedService.GenerateAbbreviation(nameof(EnumProductType.Loan));
            var sequenceNumber = await _sharedService.GetSequenceNumberAsync(BuroConstants.BuroLoanApplicationIdentifier, accountCode);

            return sequenceNumber;
        }

        public int CalculateTotalNumberOfInstallment(EnumPaymentInterval paymentInterval, Period? duration)
        {
            var intervalInDays = GetIntervalInDays(paymentInterval);
            var tenureInDays = GetTenureInDays(duration);

            if (intervalInDays == 0 || tenureInDays == 0)
            {
                return default;
            }

            var totalNumberOfInstallments = (double)tenureInDays / intervalInDays;

            return (int)Math.Ceiling(totalNumberOfInstallments);
        }

        private static int GetIntervalInDays(EnumPaymentInterval paymentInterval)
        {
            return paymentInterval switch
            {
                EnumPaymentInterval.Weekly => BuroProductConstants.DaysInWeek,
                EnumPaymentInterval.Monthly => BuroProductConstants.DaysInMonth,
                _ => default
            };
        }

        private static int GetTenureInDays(Period? tenureDuration)
        {
            if (tenureDuration == null)
            {
                return default;
            }

            return tenureDuration.Unit switch
            {
                EnumTenureUnit.Week => tenureDuration.Value * BuroProductConstants.DaysInWeek,
                EnumTenureUnit.Month => tenureDuration.Value * BuroProductConstants.DaysInMonth,
                EnumTenureUnit.Year => tenureDuration.Value * BuroProductConstants.DaysInYear,
                _ => default
            };
        }

        public async Task<BuroLoanApplication?> GetLoanApplicationByItemId(string loanApplicationItemId)
        {
            return await _repository.GetItemAsync<BuroLoanApplication>(l => l.ItemId == loanApplicationItemId);
        }

        public async Task<BuroCenter> GetCenterByItemIdAsync(string centerItemId)
        {
            return await _repository.GetItemAsync<BuroCenter>(c => c.ItemId == centerItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "Center"));
        }

        public async Task<BuroMember> GetMemberByItemIdAsync(string memberItemId)
        {
            return await _repository.GetItemAsync<BuroMember>(m => m.ItemId == memberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "MEMBER"));
        }

        public async Task<LoanProductLine> GetProductLineByItemIdAsync(string productLineItemId)
        {
            return await _repository.GetItemAsync<LoanProductLine>(lp => lp.ItemId == productLineItemId);
        }
    }
}
