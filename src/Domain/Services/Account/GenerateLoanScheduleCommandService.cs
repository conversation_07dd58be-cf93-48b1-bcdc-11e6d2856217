using Domain.Commands.Account;
using Domain.Contracts.Account;
using Infrastructure.Constants;
using Infrastructure.Events.Account;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class GenerateLoanScheduleCommandService : IGenerateLoanScheduleCommandService
    {
        private readonly ILogger<GenerateLoanScheduleCommandService> _logger;
        private readonly IServiceClient _serviceClient;

        public GenerateLoanScheduleCommandService(
            ILogger<GenerateLoanScheduleCommandService> logger,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _serviceClient = serviceClient;
        }
        public Task<CommandResponse> GenerateLoanSchedule(GenerateLoanScheduleCommand command)
        {
            _logger.LogInformation("Initiating loan schedule generation for Application ItemId : {ApplicationItemId}", command.ApplicationItemId);
            var response = new CommandResponse();

            try
            {
                var scheduleGenerationEvent = new LoanAccountScheduleGenerationEvent
                {
                    LoanApplicationItemId = command.ApplicationItemId,
                    SubscriptionId = command.SubscriptionId,
                };

                _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, scheduleGenerationEvent);

                _logger.LogInformation("loan schedule generation submitted for Application ItemId: {ApplicationItemId}", command.ApplicationItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate loan schedule for: {ApplicationItemId}", command.ApplicationItemId);
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while generating loan schedule");
            }

            return Task.FromResult(response);
        }
    }
}
