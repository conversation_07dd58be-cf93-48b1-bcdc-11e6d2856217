using Domain.Commands.Account;
using Domain.Contracts.Account;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class CloneLoanCommandService : ICloneLoanCommandService
    {
        private readonly ILogger<CloneLoanCommandService> _logger;
        private readonly IRepository _repository;
        private readonly ILoanCommonService _loanCommonService;
        private readonly ISecurityContextProvider _securityContextProvider;

        public CloneLoanCommandService(
            ILogger<CloneLoanCommandService> logger,
            IRepository repository,
            ILoanCommonService loanCommonService,
            ISecurityContextProvider securityContextProvider)
        {
            _logger = logger;
            _repository = repository;
            _loanCommonService = loanCommonService;
            _securityContextProvider = securityContextProvider;
        }

        public async Task<CommandResponse> CloneLoanAsync(CloneLoanCommand command)
        {
            _logger.LogInformation("Start cloning Loan application with Id: {LoanApplicationItemId}", command.LoanApplicationItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var currentEmployee = await GetCurrentEmployeeAsync();
                var initiator = _loanCommonService.MakeEmployeeInfo(currentEmployee);
                var existingLoanApplication = await _repository.GetItemAsync<BuroLoanApplication>(a => a.ItemId == command.LoanApplicationItemId);

                var reviewer = await _loanCommonService.GetEmployeeInfoByDesignationForAnOfficeAsync(
                    currentEmployee.CurrentOfficeItemId,
                    BuroDesignationConstant.BranchAccountantDesignationCode);

                await CreateNewDraftLoanApplicationAsync(existingLoanApplication, initiator, reviewer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during cloning of loan application with ID: {MemberItemId}", command.LoanApplicationItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while cloning for loan application.");
            }

            return commandResponse;
        }

        private async Task CreateNewDraftLoanApplicationAsync(BuroLoanApplication existingLoanApplication, EmployeeInfo initiator, EmployeeInfo reviewer)
        {
            var sequenceNumber = await _loanCommonService.GetLoanApplicationSequenceNumberAsync();
            var member = await _loanCommonService.GetMemberByItemIdAsync(existingLoanApplication.MemberItemId);
            var center = await _loanCommonService.GetCenterByItemIdAsync(member.CenterItemId);
            var productLine = await _loanCommonService.GetProductLineByItemIdAsync(existingLoanApplication.ProductLineItemId);
            var tenure = GetTenureDetails(existingLoanApplication.TenureDetails, productLine);
            var totalNumberOfInstallment = _loanCommonService.CalculateTotalNumberOfInstallment(
                    existingLoanApplication.PaymentInterval,
                    existingLoanApplication.TenureDetails?.Duration);

            var newloanApplication = new BuroLoanApplication
            {
                SequenceNumber = sequenceNumber,
                MemberItemId = member.ItemId,
                MemberName = member.MemberName,
                MemberNIDNumber = member.NIDNumber,
                MemberSequenceNumber = member.MemberSequenceNumber,
                PersonItemId = member.PersonItemId,
                BranchItemId = center?.BranchId,
                CenterItemId = center?.ItemId,
                CenterName = center?.Name,
                CenterSequenceNumber = center?.CenterId,
                ProgramOrganizerEmployeeItemId = center?.ProgramOrganizerEmployeeItemId,
                ProgramOrganizerEmployeeName = center?.ProgramOrganizerEmployeeName,
                Status = EnumProductApplicationStatus.Draft,
                RequestedLoanAmount = existingLoanApplication.RequestedLoanAmount,
                LoanRequiredByDate = existingLoanApplication.LoanRequiredByDate,
                ProductLineItemId = productLine?.ItemId,
                ProductLineName = productLine?.ProductName,
                InterestRate = productLine?.InterestRate ?? 0,
                TenureDetails = tenure,
                PaymentInterval = existingLoanApplication.PaymentInterval,
                FatherOrSpouseName = existingLoanApplication.FatherOrSpouseName,
                MotherName = existingLoanApplication.MotherName,
                Investment = existingLoanApplication.Investment,
                BusinessInformation = existingLoanApplication.BusinessInformation,
                SelfNetIncome = existingLoanApplication.SelfNetIncome,
                FamilyNetIncome = existingLoanApplication.FamilyNetIncome,
                Initiator = initiator,
                ProposedLoanAccountItemId = Guid.NewGuid().ToString(),
                AdditionalDocumentIds = existingLoanApplication.AdditionalDocumentIds,
                TotalNumberOfInstallment = totalNumberOfInstallment,
                Reviewer = reviewer,
                Guarantors = GetValidGurantors(existingLoanApplication.Guarantors),
                LoanSectorItemId = existingLoanApplication.LoanSectorItemId,
                LoanSectorName = existingLoanApplication.LoanSectorName,
                LoanSectorCode = existingLoanApplication.LoanSectorCode
            };

            newloanApplication.AddEntityBasicInfo();

            await _repository.SaveAsync(newloanApplication);
        }

        private List<Guarantor> GetValidGurantors(List<Guarantor> guarantors)
        {
            var existingBuroGuarantors = guarantors.Where(g => g.IsGuarantorExistingBuroMember).ToList();

            if (!existingBuroGuarantors.Exists(g => g.IsGuarantorExistingBuroMember))
            {
                return guarantors;
            }

            var memberItemIds = existingBuroGuarantors.Where(g => g.IsGuarantorExistingBuroMember)
                .Select(g => g.MemberItemId)
                .ToList();

            var buroMemberItemIds = _repository.GetItems<BuroMember>(m => memberItemIds.Contains(m.ItemId))
                .Select(m => m.ItemId)
                .ToList();

            var validGuarantors = existingBuroGuarantors
                .Where(g => buroMemberItemIds.Contains(g.MemberItemId))
                .ToList();

            // NOSONAR Make sure the guarantors has no association with other approved/disbursed loan application

            return validGuarantors;
        }

        private async Task<BuroEmployee> GetCurrentEmployeeAsync()
        {
            var context = _securityContextProvider.GetSecurityContext();
            return await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == context.UserId);
        }

        private static LoanLineTenure? GetTenureDetails(LoanLineTenure tenureDetails, LoanProductLine? productLine)
        {
            if (productLine == null)
            {
                return default!;
            }

            var tenure = productLine.AllowedTenures.Find(
                t => t.TenureId == tenureDetails.TenureId
                  && t.Duration.Unit == tenureDetails.Duration.Unit
                  && t.Duration.Value == tenureDetails.Duration.Value);

            return tenure;
        }
    }
}
