using Domain.Commands.Account;
using Domain.Contracts.Account;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class ChangeGSAccountCommandService : IChangeGSAccountCommandService
    {
        private readonly ILogger<ChangeGSAccountCommandService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IRepository _repository;
        private readonly ISharedService _sharedService;

        public ChangeGSAccountCommandService(
            ILogger<ChangeGSAccountCommandService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository,
            ISharedService sharedService)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
            _sharedService = sharedService;
        }

        public async Task<CommandResponse> ChangeGSAccount(ChangeGSAccountCommand command)
        {
            _logger.LogInformation("Initiating GS account change for member ID: {MemberItemId}", command.MemberItemId);
            var response = new CommandResponse();

            try
            {
                var contextData = await GetContextDataAsync(command);

                var approval = CreateApprovalEntity(command, contextData);

                await _repository.SaveAsync(approval);

                await NotifyApproverAsync(contextData, approval);

                _logger.LogInformation("GS account change request submitted for member ID: {MemberItemId}", command.MemberItemId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to change GS account for member ID: {MemberItemId}", command.MemberItemId);
                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while applying for GS account change.");
            }

            return response;
        }

        private async Task<(
            BuroGeneralSavingsAccount CurrentGSAccount,
            BuroMember Member,
            BuroEmployee Initiator,
            BuroEmployee Approver,
            GeneralSavingProductLine OldProductLine,
            GeneralSavingProductLine NewProductLine,
            SecurityContext Context)> GetContextDataAsync(ChangeGSAccountCommand command)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var currentGSAccount = await _repository.GetItemAsync<BuroGeneralSavingsAccount>(
                x => x.MemberItemId == command.MemberItemId && x.AccountStatus == EnumProductAccountStatus.Active);

            var oldProductLine = await _repository.GetItemAsync<GeneralSavingProductLine>(x => x.ItemId == currentGSAccount.ProductLineItemId);
            var newProductLine = await _repository.GetItemAsync<GeneralSavingProductLine>(x => x.ItemId == command.ProductLineItemId);

            var member = await _repository.GetItemAsync<BuroMember>(x => x.ItemId == command.MemberItemId);

            var initiator = await _repository.GetItemAsync<BuroEmployee>(x => x.UserItemId == securityContext.UserId);

            var approver = await _repository.GetItemAsync<BuroEmployee>(
                x => x.CurrentOfficeItemId == member.BranchOfficeItemId &&
                     x.DesignationCode == BuroDesignationConstant.BranchManagerDesignationCode);

            return (currentGSAccount, member, initiator, approver, oldProductLine, newProductLine, securityContext);
        }

        private BuroApproval CreateApprovalEntity(ChangeGSAccountCommand command,
            (
                BuroGeneralSavingsAccount CurrentGSAccount,
                BuroMember Member,
                BuroEmployee Initiator,
                BuroEmployee Approver,
                GeneralSavingProductLine OldProductLine,
                GeneralSavingProductLine NewProductLine,
                SecurityContext securityContext) data)
        {
            var (gsAccount, member, initiator, approver, oldProductLine, newProductLine, securityContext) = data;

            var approval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsAGSChangeApproval },
                CreatedBy = securityContext.UserId,
                LastUpdatedBy = securityContext.UserId,
                RelatedEntityItemId = gsAccount.ItemId,
                RelatedEntityName = nameof(BuroGeneralSavingsAccount),
                RequestedFromEmployeeItemId = initiator.ItemId,
                RequestedFromPersonItemId = initiator.PersonItemId,
                RequestedFromEmployeePIN = initiator.EmployeePin,
                RequestedFromEmployeeName = initiator.EmployeeName,
                RequestedFromEmployeeDesignation = approver.DesignationTitle,
                ActionedByEmployeeItemId = approver.ItemId,
                ActionedByPersonItemId = approver.PersonItemId,
                ActionedByEmployeePIN = approver.EmployeePin,
                ActionedByEmployeeName = approver.EmployeeName,
                ActionedByEmployeeDesignation = approver.DesignationTitle,
                Status = EnumApprovalStatusType.Pending,
                Category = EnumApprovalCategoryType.GSChangeApproval,
                MetaData = new List<MetaData>
                {
                    new() { Key = "MemberItemId", Value = member.ItemId },
                    new() { Key = "MemberName", Value = member.MemberName },
                    new() { Key = "CenterItemId", Value = member.CenterItemId },
                    new() { Key = "CenterName", Value = member.CenterName },
                    new() { Key = "InitiatorEmployeeEmployeeName", Value = initiator.EmployeeName },
                    new() { Key = "CurrentGSBalance", Value = gsAccount.Balance.ToString() },
                    new() { Key = "CurrentIterestRate", Value = gsAccount.InterestRate.ToString() },
                    new() { Key = "OldProductLineItemId", Value = oldProductLine.ItemId },
                    new() { Key = "OldProductLineName", Value = oldProductLine.LineName },
                    new() { Key = "OldProductLineInterestRate", Value = oldProductLine.InterestRate.ToString() },
                    new() { Key = "OldProductLineCalculationInterval", Value = oldProductLine.InterestCalculationInterval.ToString() },
                    new() { Key = "NewProductLineItemId", Value = newProductLine.ItemId },
                    new() { Key = "NewProductLineName", Value = newProductLine.LineName },
                    new() { Key = "NewProductLineInterestRate", Value = newProductLine.InterestRate.ToString() },
                    new() { Key = "NewProductLineItemId", Value = command.ProductLineItemId },
                    new() { Key = "NewProductLineCalculationInterval", Value = newProductLine.InterestCalculationInterval.ToString() },
                },
                IdsAllowedToRead = new[] { securityContext.UserId },
                IdsAllowedToWrite = new[] { securityContext.UserId },
                IdsAllowedToUpdate = new[] { securityContext.UserId },
                IdsAllowedToDelete = new[] { securityContext.UserId }
            };

            approval.AddEntityBasicInfo();
            return approval;
        }

        private async Task NotifyApproverAsync(
            (
                BuroGeneralSavingsAccount CurrentGSAccount,
                BuroMember Member,
                BuroEmployee Initiator,
                BuroEmployee Approver,
                GeneralSavingProductLine OldProductLine,
                GeneralSavingProductLine NewProductLine,
                SecurityContext Context) data,
            BuroApproval approval)
        {
            var (currentGSAccount, member, initiator, approver, _, _, _) = data;

            var notificationPayload = new
            {
                member.ItemId,
                member.MemberName,
                member.CenterItemId,
                member.CenterName,
                AssignedAt = DateTime.UtcNow,
                AssignedByEmployeeItemId = initiator.ItemId,
                AssignedByDisplayName = initiator.EmployeeName,
                AssignedByEmployeePin = initiator.EmployeePin,
                ApprovalItemId = approval.ItemId,
            };

            await _sharedService.NotifyUserAsync(
                BuroNotificationKeys.GSAccountChangeBranchManagerInvited,
                notificationPayload,
                currentGSAccount.ItemId,
                approver.UserItemId);
        }
    }
}