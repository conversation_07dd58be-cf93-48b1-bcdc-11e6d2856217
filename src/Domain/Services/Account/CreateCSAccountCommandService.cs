using Domain.Commands.Account;
using Domain.Contracts.Account;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Infrastructure.Events.Account;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Account
{
    public class CreateCSAccountCommandService : ICreateCSAccountCommandService
    {
        private readonly ILogger<CreateCSAccountCommandService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ISharedService _sharedService;
        private readonly IServiceClient _serviceClient;

        public CreateCSAccountCommandService(
            ILogger<CreateCSAccountCommandService> logger,
            IRepository repository,
            ISecurityContextProvider securityContextProvider,
            ISharedService sharedService,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _repository = repository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> ApplyForCSAccountAsync(CreateCSAccountCommand command)
        {
            _logger.LogInformation("Starting CS account application for member ID: {MemberItemId}", command.MemberItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var context = _securityContextProvider.GetSecurityContext();
                var initiator = await GetEmployeeAsync(context.UserId);

                await SaveCSApplicationAsync(command, context, initiator);

                if (command.Status == EnumProductApplicationStatus.Pending)
                {
                    await HandleCSPendingApplication(command, initiator, context);

                    SendToCreateCSApplicationVoucherAsync(command);
                }

                _logger.LogInformation("Successfully created CS application for member ID: {MemberItemId}", command.MemberItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during CS application creation for member ID: {MemberItemId}", command.MemberItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating CS application.");
            }

            return commandResponse;
        }

        private void SendToCreateCSApplicationVoucherAsync(CreateCSAccountCommand command)
        {
            var payload = new CreateCSApplicationVoucherEvent
            {
                ApplicationName = nameof(BuroContractualSavingsApplication),
                ApplicationItemId = command.ApplicationItemId,
                ProductLineItemId = command.ProductLineItemId,
                Amount = command.InstalmentAmount,
                IsDebited = false,
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("CS application voucher creation initiated for member: {MemberItemId}", command.MemberItemId);
        }

        private async Task HandleCSPendingApplication(CreateCSAccountCommand command, BuroEmployee initiator, SecurityContext context)
        {
            _logger.LogInformation("Starting HandleCSPendingApplication for member ID: {MemberItemId}", command.MemberItemId);

            var approver = await GetApprover(initiator.CurrentOfficeItemId);

            if (initiator.ItemId == approver.ItemId)
            {
                SendToCreateCSAccountAsync(command);
            }
            else
            {
                await SendApprovalAsync(command, initiator, approver, context);
            }
        }

        private void SendToCreateCSAccountAsync(CreateCSAccountCommand command)
        {
            var payload = new ContractualAccountApprovalEvent
            {
                ApplicationItemId = command.ApplicationItemId,
                IsAccepted = true
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("CS account creation initiated for member: {MemberItemId}", command.MemberItemId);
        }

        private async Task SaveCSApplicationAsync(
            CreateCSAccountCommand command,
            SecurityContext context,
            BuroEmployee initiator)
        {
            var isExistingCSApplication = await _repository.ExistsAsync<BuroContractualSavingsApplication>(c => c.ItemId == command.ApplicationItemId);

            if (isExistingCSApplication)
            {
                await UpdateCSApplicationAsync(command, context);
            }
            else
            {
                await CreateCSApplicationAsync(command);
            }

            await SaveProductApplicationHistoryAsync(command, initiator);
        }

        private async Task UpdateCSApplicationAsync(
            CreateCSAccountCommand command,
            SecurityContext context)
        {
            var nominees = new BsonArray(command.Nominees.Select(n => n.ToBsonDocument()));

            var properties = new Dictionary<string, object>
            {
                { nameof(BuroContractualSavingsApplication.LastUpdateDate), DateTime.UtcNow },
                { nameof(BuroContractualSavingsApplication.LastUpdatedBy), context.UserId },
                { nameof(BuroContractualSavingsApplication.ProductLineItemId), command.ProductLineItemId },
                { nameof(BuroContractualSavingsApplication.InstalmentAmount), command.InstalmentAmount },
                { nameof(BuroContractualSavingsApplication.MemberSignatureId), command.MemberSignatureId },
                { nameof(BuroContractualSavingsApplication.Nominees), nominees },
                { nameof(BuroContractualSavingsApplication.TenureDetails), command.TenureDetails },
                { nameof(BuroContractualSavingsApplication.PaymentInterval), command.PaymentInterval }
            };

            await _repository.UpdateAsync<BuroContractualSavingsApplication>(c => c.ItemId == command.ApplicationItemId, properties);
        }

        private async Task<BuroEmployee> GetEmployeeAsync(string userItemId)
        {
            return await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == userItemId);
        }

        private async Task<BuroMember> GetMemberAsync(string memberItemId)
        {
            return await _repository.GetItemAsync<BuroMember>(m => m.ItemId == memberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "MEMBER"));
        }

        private async Task<BuroCenter> GetCenterAsync(string centerItemId)
        {
            return await _repository.GetItemAsync<BuroCenter>(m => m.ItemId == centerItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "CENTER"));
        }

        private async Task SaveProductApplicationHistoryAsync(CreateCSAccountCommand command, BuroEmployee initiator)
        {
            var history = new BuroProductApplicationHistory
            {
                ApplicationItemId = command.ApplicationItemId,
                ActionByEmployeeItemId = initiator.ItemId,
                ActionByEmployeePin = initiator.EmployeePin,
                ActionByEmployeeName = initiator.EmployeeName,
                ActionByEmployeeDesginationItemId = initiator.DesignationItemId,
                ActionByEmployeeDesginationTitle = initiator.DesignationTitle,
                ActionByEmployeeOfficeItemId = initiator.CurrentOfficeItemId,
                ActionByEmployeeOfficeName = initiator.CurrentOfficeTitle,
                ActionByEmployeeOfficeCode = initiator.CurrentOfficeCode,
                Status = command.Status,
                ProductType = EnumProductType.ContractualSaving,
                MetaData = new List<MetaData>
                {
                    new()
                    {
                        Key = nameof(command.MemberItemId),
                        Value = command.MemberItemId
                    },
                    new()
                    {
                        Key = nameof(command.ProductLineItemId),
                        Value = command.ProductLineItemId
                    }
                }
            };

            history.AddEntityBasicInfo();

            await _repository.SaveAsync(history);
        }

        private async Task SendApprovalAsync(
            CreateCSAccountCommand command,
            BuroEmployee initiator,
            BuroEmployee approver,
            SecurityContext securityContext)
        {
            _logger.LogInformation("Starting SendApprovalAsync");

            var approvalItemId = await CreateApprovalAsync(command, initiator, approver, securityContext.UserId);

            var notificationPayload = new
            {
                command.ApplicationItemId,
                command.MemberItemId,
                command.ProductLineItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                ApprovalItemId = approvalItemId,
                ApproverName = approver.EmployeeName,
                ApproverPin = approver.EmployeePin,
                ApproverDesignation = approver.DesignationTitle,
            };

            await _sharedService.NotifyUserAsync(
                BuroNotificationKeys.ContractualAccountBranchManagerInvited,
                notificationPayload,
                command.ApplicationItemId,
                approver.UserItemId);
        }

        private async Task<BuroEmployee> GetApprover(string currentOfficeItemId)
        {
            return await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(
                currentOfficeItemId, BuroDesignationConstant.BranchManagerDesignationCode)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "APPROVER"));
        }

        private async Task<string> CreateApprovalAsync(
            CreateCSAccountCommand command,
            BuroEmployee initiator,
            BuroEmployee approver,
            string currentUserId)
        {
            _logger.LogInformation("Starting CreateApprovalAsync");

            var approval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsAContractualAccountApproval },
                CreatedBy = currentUserId,
                LastUpdatedBy = currentUserId,
                RelatedEntityItemId = command.ApplicationItemId,
                RelatedEntityName = nameof(BuroContractualSavingsApplication),
                RequestedFromPersonItemId = initiator.PersonItemId,
                RequestedFromEmployeeItemId = initiator.ItemId,
                RequestedFromEmployeePIN = initiator.EmployeePin,
                RequestedFromEmployeeName = initiator.EmployeeName,
                ActionedByPersonItemId = approver.PersonItemId,
                ActionedByEmployeeItemId = approver.ItemId,
                ActionedByEmployeePIN = approver.EmployeePin,
                ActionedByEmployeeName = approver.EmployeeName,
                Status = EnumApprovalStatusType.Pending,
                Category = EnumApprovalCategoryType.NewCSAccountApproval,
                ActionedByEmployeeDesignation = approver.DesignationTitle,
                MetaData = new List<MetaData>
                {
                    new() { Key = "ApplicationItemId", Value = command.ApplicationItemId },
                    new() { Key = nameof(command.MemberItemId), Value = command.MemberItemId },
                    new() { Key = nameof(command.ProductLineItemId), Value = command.ProductLineItemId }
                },
                IdsAllowedToRead = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToUpdate = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToDelete = new[] { currentUserId },
                IdsAllowedToWrite = new[] { currentUserId }
            };

            approval.AddEntityBasicInfo();

            await _repository.SaveAsync(approval);

            _logger.LogInformation("CS Account approval created with approval Id: {ItemId}", approval.ItemId);

            return approval.ItemId;
        }

        private async Task CreateCSApplicationAsync(CreateCSAccountCommand command)
        {
            var member = await GetMemberAsync(command.MemberItemId);
            var center = await GetCenterAsync(member.CenterItemId);
            var branch = await GetBranchOfficeAsync(center?.BranchId ?? string.Empty);
            var productLine = await GetProductLineAsync(command.ProductLineItemId);
            var sequenceNumber = await GenerateSequenceNumberForCSApplicationAsync();

            var application = new BuroContractualSavingsApplication
            {
                ItemId = command.ApplicationItemId,
                SequenceNumber = sequenceNumber,
                Status = command.Status,
                MemberItemId = member.ItemId,
                MemberName = member.MemberName,
                MemberSequenceNumber = member.MemberSequenceNumber,
                PersonItemId = member.PersonItemId,
                BranchItemId = branch?.ItemId,
                BranchName = branch?.OfficeName,
                BranchSequenceNumber = branch?.OfficeId,
                ProductLineItemId = command.ProductLineItemId,
                ProductLineName = productLine?.LineName,
                InterestRate = productLine?.InterestRate ?? 0,
                CenterItemId = center?.ItemId,
                CenterName = center?.Name,
                CenterSequenceNumber = center?.CenterId,
                ProgramOrganizerEmployeeItemId = center?.ProgramOrganizerEmployeeItemId,
                ProgramOrganizerEmployeeName = center?.ProgramOrganizerEmployeeName,
                InstalmentAmount = command.InstalmentAmount,
                MemberSignatureId = command.MemberSignatureId,
                Nominees = command.Nominees,
                TenureDetails = command.TenureDetails,
                PaymentInterval = command.PaymentInterval
            };

            application.AddEntityBasicInfo();

            await _repository.SaveAsync(application);
        }

        private async Task<ContractualSavingProductLine> GetProductLineAsync(string productLineItemId)
        {
            return await _repository.GetItemAsync<ContractualSavingProductLine>(cp => cp.ItemId == productLineItemId);
        }

        private async Task<BuroOffice> GetBranchOfficeAsync(string branchId)
        {
            if (branchId.IsNullOrEmptyOrWhiteSpace())
            {
                return default!;
            }

            return await _repository.GetItemAsync<BuroOffice>(b => b.ItemId == branchId);
        }

        private async Task<string> GenerateSequenceNumberForCSApplicationAsync()
        {
            var accountCode = _sharedService.GenerateAbbreviation(nameof(EnumProductType.ContractualSaving));
            var sequenceNumber = await _sharedService.GetSequenceNumberAsync(BuroConstants.BuroContractualSavingsApplicationIdentifier, accountCode);

            return sequenceNumber;
        }
    }
}