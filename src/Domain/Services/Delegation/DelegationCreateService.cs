using Domain.Commands.Delegation;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Delegation;

public class DelegationCreateService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DelegationCreateService(IRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task<CommandResponse> CreateAsync(DelegationCreateCommand command)
    {
        var commandResponse = new CommandResponse();
        var sourceEmployee =
            await _repository.GetItemAsync<BuroEmployee>(item => item.ItemId == command.SourceEmployeeItemId);
        var delegateeEmployee =
            await _repository.GetItemAsync<BuroEmployee>(item => item.ItemId == command.DelegateeEmployeeItemId);
        var delegationRequest = PopulateDelegationRequest(command, sourceEmployee, delegateeEmployee);
        await _repository.SaveAsync(delegationRequest);
        return commandResponse;
    }

    private BuroDelegationRequest PopulateDelegationRequest(DelegationCreateCommand command,
        BuroEmployee sourceEmployee, BuroEmployee delegateeEmployee)
    {
        var context = _securityContextProvider.GetSecurityContext();

        return new BuroDelegationRequest
        {
            ItemId = Guid.NewGuid().ToString(),
            SourceEmployeeItemId = command.SourceEmployeeItemId,
            SourceName = sourceEmployee.EmployeeName,
            SourceDesignationItemId = sourceEmployee.DesignationItemId,
            SourceDesignationTitle = sourceEmployee.DesignationTitle,
            SourceEmployeeId = sourceEmployee.EmployeeId,
            SourceEmployeePin = sourceEmployee.EmployeePin,
            SourceCenterTitle = sourceEmployee.CurrentCenterTitle,
            SourceUserItemId = sourceEmployee.UserItemId,
            DelegateeEmployeeItemId = command.DelegateeEmployeeItemId,
            DelegateeName = delegateeEmployee.EmployeeName,
            DelegateeDesignationItemId = delegateeEmployee.DesignationItemId,
            DelegateeDesignationTitle = delegateeEmployee.DesignationTitle,
            DelegateeEmployeeId = delegateeEmployee.EmployeeId,
            DelegateeEmployeePin = delegateeEmployee.EmployeePin,
            DelegateeCenterTitle = delegateeEmployee.CurrentCenterTitle,
            DelegateeUserItemId = delegateeEmployee.UserItemId,
            StartDate = command.StartDate,
            EndDate = command.EndDate,
            IsLifeTimeDelegation = command.IsLifeTimeDelegation,
            DelegationStatus = EnumDelegationStatus.Delegated,
            Features = command.FeatureList.Select(x => new FeatureForDelegation
            {
                ItemId = x.FeatureItemId,
                FeatureName = x.FeatureName,
                FeaturePath = x.FeaturePath,
                ApiPath = x.ApiPath,
                IconName = x.IconName
            }).ToList(),
            CreatedBy = context.UserId,
            CreatorName = context.DisplayName,
            CreateDate = DateTime.UtcNow
        };
    }
}