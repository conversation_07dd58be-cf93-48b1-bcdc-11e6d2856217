using Domain.Commands.Delegation;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Delegation;

public class DelegationUpdateService
{
    private readonly IRepository _repository;
    private readonly IBuroKeyStore _keyStore;

    public DelegationUpdateService(IRepository repository,
        IBuroKeyStore keyStore)
    {
        _repository = repository;
        _keyStore = keyStore;
    }

    public async Task<CommandResponse> UpdateDelegationEndDate(DelegationEndDateUpdateCommand command)
    {
        var commandResponse = new CommandResponse();

        var updatedProperties = GetPropertiesToUpdate(command);
        await _repository.UpdateAsync<BuroDelegationRequest>(item => item.ItemId == command.DelegationItemId,
            updatedProperties);

        await HandleDelegationKeyRemoval(command);

        return commandResponse;
    }

    private async Task HandleDelegationKeyRemoval(DelegationEndDateUpdateCommand command)
    {
        var delegation = await _repository.GetItemAsync<BuroDelegationRequest>(item => item.ItemId == command.DelegationItemId);

        var key = $"{delegation.DelegateeUserItemId}_{delegation.SourceUserItemId}{BuroConstants.DelegatedPermissionSuffix}";

        await _keyStore.RemoveKeyAsync(key);
    }


    private Dictionary<string, object> GetPropertiesToUpdate(DelegationEndDateUpdateCommand command)
    {
        return command.EndEarly
            ? new Dictionary<string, object>
            {
                { nameof(BuroDelegationRequest.DelegationStatus), EnumDelegationStatus.Terminated }
            }
            : new Dictionary<string, object>
            {
                { nameof(BuroDelegationRequest.EndDate), command.EndDate! }
            };
    }
}