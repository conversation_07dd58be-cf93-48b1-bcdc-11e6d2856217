using Domain.Commands.Dms;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using Selise.Ecap.Entities.PrimaryEntities.Arc.ArcDms;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Dms;

public class UpdateDmsArtifactTagService
{
    private readonly ILogger<UpdateDmsArtifactTagService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateDmsArtifactTagService(
        ILogger<UpdateDmsArtifactTagService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }


    public async Task<CommandResponse> UpdateDmsArtifactTag(UpdateDmsArtifactTagCommand command)
    {
        _logger.LogInformation("UpdateDmsArtifactTag process initiated");

        var commandResponse = new CommandResponse();
        try
        {
            await ProcessDmsArtifactTagAsync(command);
            _logger.LogInformation("UpdateDmsArtifactTag process completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during UpdateDmsArtifactTag process");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred,
                "An error occurred while updating the artifacts.");
        }

        return commandResponse;
    }

    private async Task ProcessDmsArtifactTagAsync(UpdateDmsArtifactTagCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();

        var existingData =
            await _repository.GetItemAsync<DmsArtifact>(c => c.ItemId == command.ItemId);
        if (existingData != null)
        {
            var updatedProperties = PrepareArtifactProperties(securityContext.UserId, command);
            await _repository.UpdateManyAsync<DmsArtifact>(x => x.ItemId == existingData.ItemId, updatedProperties);
            _logger.LogInformation("update dms artifact successfully for Item ID: {ItemId}", existingData.ItemId);
        }
        else
        {
            _logger.LogWarning("No existing artifact found for Item ID: {ItemId}, skipping update dms artifact.", command.ItemId);
            throw new ArgumentException("Item not found.");
        }

    }

    private static Dictionary<string, object> PrepareArtifactProperties(string userId, UpdateDmsArtifactTagCommand command)
    {

        var currentDateTime = DateTime.UtcNow;
        var updatedProperties = new Dictionary<string, object>
        {
            { nameof(DmsArtifact.LastUpdateDate), currentDateTime },
            { nameof(DmsArtifact.LastUpdatedBy), userId },
            { "DocumentTags", command.DocumentTags }
        };

        return updatedProperties;
    }




}