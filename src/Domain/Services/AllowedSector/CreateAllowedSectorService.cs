using Domain.Commands.AllowedSector;
using Infrastructure.Contracts;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.AllowedSector;

public class CreateAllowedSectorService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISequenceNumberClient _sequenceNumberClient;

    public CreateAllowedSectorService(IRepository repository, ISecurityContextProvider securityContextProvider,
        ISequenceNumberClient sequenceNumberClient)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
        _sequenceNumberClient = sequenceNumberClient;
    }

    public async Task CreateAllowedSector(CreateAllowedSectorCommand command)
    {
        var context = _securityContextProvider.GetSecurityContext();
        var buroSector = PopulateSector(command, context);
        await _repository.SaveAsync(buroSector);
    }

    private static BuroSector PopulateSector(CreateAllowedSectorCommand command, SecurityContext context)
    {
        var sector = new BuroSector
        {
            ItemId = Guid.NewGuid().ToString(),
            ParentItemId = command.ParentItemId,
            Name = command.Name,
            CategoryType = command.CategoryType,
            BuroSectorCode = command.BuroSectorCode,
            MFCIBCode = command.MFCIBCode,
            IsActive = command.IsActive,
            CreateDate = DateTime.UtcNow,
            CreatedBy = context.UserId,
        };

        return sector;
    }
}