using Domain.Commands.AllowedSector;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.AllowedSector;
public class UpdateAllowedSectorService
{
    private readonly IRepository _repository;

    public UpdateAllowedSectorService(IRepository repository)
    {
        _repository = repository;
    }

    public async Task UpdateAllowedSectorAsync(UpdateAllowedSectorCommand command)
    {
        var existingSector = await GetExistingSector(command);
        var propertiesToUpdate = GetPropertiesToUpdate(command, existingSector);
        await _repository.UpdateAsync<BuroSector>(item => item.ItemId == command.ItemId, propertiesToUpdate);
    }

    private async Task<BuroSector> GetExistingSector(UpdateAllowedSectorCommand command)
    {
        return await _repository.GetItemAsync<BuroSector>(item => item.ItemId == command.ItemId);
    }

    private Dictionary<string, object> GetPropertiesToUpdate(UpdateAllowedSectorCommand command, BuroSector existingVendor)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroSector.ParentItemId), command.ParentItemId },
            { nameof(BuroSector.Name), command.Name },
            { nameof(BuroSector.CategoryType), command.CategoryType },
            { nameof(BuroSector.BuroSectorCode), command.BuroSectorCode },
            { nameof(BuroSector.MFCIBCode), command.MFCIBCode },
            { nameof(BuroSector.IsActive), command.IsActive },
        };

        return properties;
    }
}