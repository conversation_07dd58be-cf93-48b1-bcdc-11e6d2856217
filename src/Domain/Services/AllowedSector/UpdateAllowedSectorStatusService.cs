using Domain.Commands.AllowedSector;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.AllowedSector;

public class UpdateAllowedSectorStatusService
{
    private readonly ILogger<UpdateAllowedSectorStatusService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    public UpdateAllowedSectorStatusService(
         ILogger<UpdateAllowedSectorStatusService> logger,
         ISecurityContextProvider securityContextProvider,
         IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> UpdateAllowedSectorStatus(UpdateAllowedSectorStatusCommand command)
    {
        _logger.LogInformation("UpdateAllowedSectorStatus process initiated. Sector ItemId: {SectorItemId}", command.SectorItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessUpdateAllowedSectorStatus(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during UpdateAllowedSectorStatus process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while sector status change.");
        }

        return commandResponse;
    }

    private async Task ProcessUpdateAllowedSectorStatus(UpdateAllowedSectorStatusCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var isExistingData =
            await _repository.ExistsAsync<BuroSector>(c => c.ItemId == command.SectorItemId);
        if (!isExistingData)
        {
            _logger.LogWarning("No existing sector found : {ItemID}. Skipping status change.", command.SectorItemId);
            commandResponse.SetError(BuroErrorMessageKeys.SectorNotFound, "No existing sector found.");
        }

        var updatedProperties = PrepareSectorProperties(command);

        _logger.LogInformation("Status change for sector Item ID: {ItemId}", command.SectorItemId);

        await _repository.UpdateOneAsync<BuroSector>(x => x.ItemId == command.SectorItemId, updatedProperties);
        _logger.LogInformation("Status change for sector Item ID: {ItemId}", command.SectorItemId);
    }

    private static Dictionary<string, object> PrepareSectorProperties(UpdateAllowedSectorStatusCommand command)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroSector.IsActive), command?.IsActive ?? false }
        };

        return properties;
    }
}

