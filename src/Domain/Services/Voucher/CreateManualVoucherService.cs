using Domain.Commands.Voucher;
using Domain.Contracts.Member;
using Domain.Contracts.Voucher;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher;

public class CreateManualVoucherService : ICreateManualVoucherCommandService
{

    private readonly ILogger<CreateManualVoucherService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISharedService _sharedService;

    public CreateManualVoucherService(
        ILogger<CreateManualVoucherService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository,
        ISharedService sharedService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sharedService = sharedService;
    }

    public async Task<CommandResponse> CreateManualVoucherAsync(CreateManualVoucherCommand command)
    {
        _logger.LogInformation("Starting Manual Voucher creation for the VoucherConfigItemId: {VoucherConfigItemId}", command.VoucherConfigItemId);

        var commandResponse = new CommandResponse();

        try
        {
            var voucherConfig = await
                _repository.FindAsync<BuroStandardVoucherConfiguration>(v => v.ItemId == command.VoucherConfigItemId);

            if (!voucherConfig.Any())
            {
                throw new ArgumentException("There is no Voucher configuration with this ID");
            }

            var securityContext = _securityContextProvider.GetSecurityContext();

            await ProcessManualVoucherEntry(command, voucherConfig[0], securityContext);
            _logger.LogInformation("CreateManualVoucherAsync process completed successfully.");

        }

        catch (Exception e)
        {
            _logger.LogError(e, "Error occurred during Manual Voucher creation for VoucherConfigItemId: {VoucherConfigItemId}", command.VoucherConfigItemId);

            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating manual voucher.");
        }

        return commandResponse;

    }

    private async Task ProcessManualVoucherEntry(CreateManualVoucherCommand command,
        BuroStandardVoucherConfiguration voucherConfig, SecurityContext securityContext)
    {
        _logger.LogInformation("Preparing to insert manual voucher... for TransactionType: {TransactionName}", voucherConfig.TransactionTypeName);

        var createdByEmployeePin = await _repository.FindWithProjectionAsync<BuroEmployee, string>(x => x.UserItemId == securityContext.UserId,
            x => x.EmployeePin);

        var voucherCode = await _sharedService.GetSequenceNumberAsync(
            BuroConstants.BuroVoucherIdentifier, "WM");

        var vouchers = voucherConfig.LedgerEntries
            .Select(ledger => new BuroVoucher
            {
                ItemId = Guid.NewGuid().ToString(),
                VoucherCode = voucherCode,
                VoucherGroupItemId = command.VoucherGroupItemId,
                ChartOfAccountItemId = ledger.ChartOfAccountItemId,
                OfficeItemId = command.OfficeItemId,
                ChartOfAccountCode = ledger.ChartOfAccountCode,
                ChartOfAccountName = ledger.ChartOfAccountName,
                VoucherCreationType = EnumVoucherCreationType.Manual,
                VoucherScopeCategory = voucherConfig.VoucherScopeCategory,
                TransactionTypeItemId = voucherConfig.TransactionTypeItemId,
                TransactionTypeName = voucherConfig.TransactionTypeName,
                VoucherType = ledger.VoucherType,
                DebitAmount = ledger.VoucherType == EnumBuroVoucherType.Debit
                    ? command.Amount : 0,
                CreditAmount = ledger.VoucherType == EnumBuroVoucherType.Credit
                    ? command.Amount : 0,
                Status = EnumVoucherStatus.Approved,
                IssueDate = DateTime.UtcNow,
                Narration = command.Narration,
                VoucherCreatedEmployeeName = securityContext.DisplayName,
                VoucherCreatedEmployeePin = createdByEmployeePin.FirstOrDefault() ?? string.Empty,
                Tags = new[] { Tags.IsAVoucher },
                CreatedBy = securityContext.UserId,
                LastUpdatedBy = securityContext.UserId,

            })
            .ToList();

        await _repository.InsertManyAsync(vouchers);
        _logger.LogInformation("Successfully inserted voucher with TransactionType: {TransactionName}", voucherConfig.TransactionTypeName);

    }


}