using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class UpdateAutoVoucherConfigurationStrategy : IUpdateVoucherConfigurationStrategy
{
    private readonly UpdateProductLineVoucherConfigurationService _updateProductLineVoucherService;

    public UpdateAutoVoucherConfigurationStrategy(UpdateProductLineVoucherConfigurationService updateProductLineVoucherService)
    {
        _updateProductLineVoucherService = updateProductLineVoucherService;
    }

    public async Task<CommandResponse> Execute(UpdateVoucherConfigCommand command)
    {
        return await _updateProductLineVoucherService.UpdateAutoVoucherConfiguration(command);
    }
}