using Domain.Commands.Voucher.Configuration;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace Domain.Services.Voucher.Configuration;

public class UpdateStandardVoucherConfigurationService
{
    private readonly ILogger<UpdateStandardVoucherConfigurationService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateStandardVoucherConfigurationService(
        ILogger<UpdateStandardVoucherConfigurationService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> UpdateManualVoucherConfiguration(UpdateVoucherConfigCommand command)
    {

        _logger.LogInformation("UpdateManualVoucherConfiguration process initiated. ItemId {ItemId}", command.ItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessManualUpdateVoucherConfig(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during UpdateManualVoucherConfiguration process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the manual voucher configuration.");
        }

        return commandResponse;
    }


    private async Task ProcessManualUpdateVoucherConfig(UpdateVoucherConfigCommand command, CommandResponse commandResponse)
    {

        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var isExistingData =
            await _repository.ExistsAsync<BuroStandardVoucherConfiguration>(c => c.ItemId == command.ItemId);
        if (!isExistingData)
        {
            _logger.LogWarning("No existing manual voucher configuration found for Voucher Config Item ID: {ItemID}. Skipping update.", command.ItemId);
            commandResponse.SetError(BuroErrorMessageKeys.VoucherNotFound, "No existing manual voucher configuration found.");
        }

        var updatedProperties = PrepareVoucherConfigProperties(command, securityContext.UserId, currentDateTime);

        _logger.LogInformation("Updating manual voucher config accounts heads for Item ID: {ItemId}", command.ItemId);

        await _repository.UpdateManyAsync<BuroStandardVoucherConfiguration>(x => x.ItemId == command.ItemId, updatedProperties);
        _logger.LogInformation("Updated manual voucher configuration successfully for Item ID: {ItemId}", command.ItemId);
    }

    private Dictionary<string, object> PrepareVoucherConfigProperties(UpdateVoucherConfigCommand command, string userId,
        DateTime currentDateTime)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroStandardVoucherConfiguration.LastUpdateDate), currentDateTime },
            { nameof(BuroStandardVoucherConfiguration.LastUpdatedBy), userId },
        };

        var charOfAccIds = command.LedgerEntries.Select(c => c.ChartOfAccountItemId).ToList();

        var accountHeads = _repository.GetItems<BuroChartOfAccount>(x => charOfAccIds.Contains(x.ItemId))
            .ToDictionary(c => c.ItemId, c => new { c.Code, c.Name });

        var ledgerEntries = command.LedgerEntries.Select(entry =>
        {
            accountHeads.TryGetValue(entry.ChartOfAccountItemId, out var acc);

            return new LedgerEntry
            {
                ItemId = Guid.NewGuid().ToString(),
                ChartOfAccountItemId = entry.ChartOfAccountItemId,
                ChartOfAccountCode = acc?.Code,
                ChartOfAccountName = acc?.Name,
                VoucherType = entry.VoucherType
            };
        }).ToList();

        properties.Add(nameof(BuroStandardVoucherConfiguration.LedgerEntries),
            new BsonArray(ledgerEntries.Select(le => le.ToBsonDocument())));

        return properties;
    }
}