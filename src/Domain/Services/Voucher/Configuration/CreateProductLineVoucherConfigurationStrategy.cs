using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class CreateProductLineVoucherConfigurationStrategy : ICreateVoucherConfigurationStrategy
{
    private readonly CreateProductLineVoucherConfigurationService _createProductLineVoucherService;

    public CreateProductLineVoucherConfigurationStrategy(CreateProductLineVoucherConfigurationService createProductLineVoucherService)
    {
        _createProductLineVoucherService = createProductLineVoucherService;
    }

    public async Task<CommandResponse> Execute(CreateVoucherConfigCommand command)
    {
        return await _createProductLineVoucherService.CreateProductLineVoucherConfiguration(command);
    }
}