using Domain.Commands.Voucher.Configuration;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class DeleteProductLineVoucherConfigurationService
{
    private readonly ILogger<DeleteProductLineVoucherConfigurationService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DeleteProductLineVoucherConfigurationService(
        ILogger<DeleteProductLineVoucherConfigurationService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> DeleteAutoVoucherConfiguration(DeleteVoucherConfigCommand command)
    {

        _logger.LogInformation("DeleteAutoVoucherConfiguration process initiated. ItemId {ItemId}", command.ItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessDeletionAutoVoucherConfig(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during DeleteAutoVoucherConfiguration process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while deleting the auto voucher configuration.");
        }

        return commandResponse;
    }

    private async Task ProcessDeletionAutoVoucherConfig(DeleteVoucherConfigCommand command, CommandResponse commandResponse)
    {

        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var isExistingData =
            await _repository.ExistsAsync<BuroProductLineVoucherConfiguration>(c => c.ItemId == command.ItemId);
        if (!isExistingData)
        {
            _logger.LogWarning("No existing auto voucher configuration found for Voucher Config Item ID: {ItemID}. Skipping delete.", command.ItemId);
            commandResponse.SetError(BuroErrorMessageKeys.VoucherNotFound, "No existing auto voucher configuration found.");
        }

        var updatedProperties = PrepareVoucherConfigProperties(securityContext.UserId, currentDateTime);

        _logger.LogInformation("Deleting auto voucher config accounts heads for Item ID: {ItemId}", command.ItemId);

        await _repository.UpdateManyAsync<BuroProductLineVoucherConfiguration>(x => x.ItemId == command.ItemId, updatedProperties);
        _logger.LogInformation("Deleted auto voucher configuration successfully for Item ID: {ItemId}", command.ItemId);
    }


    private static Dictionary<string, object> PrepareVoucherConfigProperties(string userId, DateTime currentDateTime)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroProductLineVoucherConfiguration.LastUpdateDate), currentDateTime },
            { nameof(BuroProductLineVoucherConfiguration.LastUpdatedBy), userId },
            { nameof(BuroProductLineVoucherConfiguration.IsMarkedToDelete), true }
        };

        return properties;
    }


}