using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class CreateStandardVoucherConfigurationStrategy : ICreateVoucherConfigurationStrategy
{
    private readonly CreateStandardVoucherConfigurationService _createStandardVoucherService;

    public CreateStandardVoucherConfigurationStrategy(CreateStandardVoucherConfigurationService createStandardVoucherService)
    {
        _createStandardVoucherService = createStandardVoucherService;
    }

    public async Task<CommandResponse> Execute(CreateVoucherConfigCommand command)
    {
        return await _createStandardVoucherService.CreateStandardVoucherConfiguration(command);
    }
}