
using Domain.Commands.Voucher.Configuration;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace Domain.Services.Voucher.Configuration;

public class CreateStandardVoucherConfigurationService
{
    private readonly ILogger<CreateStandardVoucherConfigurationService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateStandardVoucherConfigurationService(
        ILogger<CreateStandardVoucherConfigurationService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> CreateStandardVoucherConfiguration(CreateVoucherConfigCommand command)
    {
        _logger.LogInformation("CreateStandardVoucherConfiguration process initiated. ItemId {ItemId}", command.ItemId);

        var commandResponse = new CommandResponse();
        try
        {
            var transactionSetup =
                await _repository.FindAsync<BuroTransactionCategoryType>(x => x.ItemId == command.TransactionTypeItemId);
            if (!transactionSetup.Any())
            {
                _logger.LogWarning("TransactionCategoryType not found for ItemId {ItemId}", command.TransactionTypeItemId);
                return commandResponse.SetError(BuroErrorMessageKeys.TransactionCategoryTypeNotFound,
                    "TransactionCategoryType not found for the provided ItemId.");
            }

            var isExistVoucherConfig = await _repository.ExistsAsync<BuroStandardVoucherConfiguration>(v => v.TransactionTypeItemId == command.TransactionTypeItemId);

            if (isExistVoucherConfig)
            {
                _logger.LogWarning("Voucher configuration already exists for TransactionType: {TransactionTypeName}",
                    transactionSetup[0].TransactionTypeName);
                return commandResponse.SetError(BuroErrorMessageKeys.VoucherExists,
                    "A voucher configuration for this transaction type already exists with the same TransactionTypeName.");
            }

            await ProcessConfigurationCreationAsync(command, transactionSetup[0]);
            _logger.LogInformation("CreateStandardVoucherConfiguration process completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during CreateManualVoucherConfiguration process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating the manual voucher configuration.");
        }

        return commandResponse;
    }

    private async Task ProcessConfigurationCreationAsync(CreateVoucherConfigCommand command, BuroTransactionCategoryType transactionSetup)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();

        _logger.LogInformation("Preparing to insert manual voucher configuration...");


        var voucherConfig = await PrepareVoucherConfigurationEntity(command, transactionSetup, securityContext);

        await _repository.InsertOneAsync(voucherConfig);

        _logger.LogInformation("Successfully inserted manual voucher configuration with ItemId: {ItemId}", voucherConfig.ItemId);

    }

    private async Task<BuroStandardVoucherConfiguration> PrepareVoucherConfigurationEntity(CreateVoucherConfigCommand command,
        BuroTransactionCategoryType transactionSetup, SecurityContext securityContext)
    {

        var charOfAccIds = command.LedgerEntries.Select(c => c.ChartOfAccountItemId).ToList();

        var accountItems = await _repository.FindAsync<BuroChartOfAccount>(
            x => charOfAccIds.Contains(x.ItemId));

        var accountHeads = accountItems.ToDictionary(
            c => c.ItemId,
            c => new { c.Code, c.Name });

        var ledgerEntries = command.LedgerEntries.Select(entry =>
        {
            accountHeads.TryGetValue(entry.ChartOfAccountItemId, out var acc);

            return new LedgerEntry
            {
                ItemId = Guid.NewGuid().ToString(),
                ChartOfAccountItemId = entry.ChartOfAccountItemId,
                ChartOfAccountCode = acc?.Code,
                ChartOfAccountName = acc?.Name,
                VoucherType = entry.VoucherType
            };
        }).ToList();
        if (!ledgerEntries.Any())
        {
            _logger.LogWarning("No valid LedgerEntries found for the provided ChartOfAccountItemIds.");
        }

        var voucherConfigEntity = new BuroStandardVoucherConfiguration
        {
            ItemId = command.ItemId,
            VoucherCreationType = transactionSetup.VoucherCreationType,
            VoucherScopeCategory = transactionSetup.VoucherScopeCategory,
            TransactionTypeItemId = transactionSetup.ItemId,
            TransactionTypeName = transactionSetup.TransactionTypeName,
            TransactionTypeTag = transactionSetup.TransactionTypeTag,
            LedgerEntries = ledgerEntries,
            Tags = new[] { Tags.IsAStandardVoucherConfiguration },
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
        };

        return voucherConfigEntity;
    }

}