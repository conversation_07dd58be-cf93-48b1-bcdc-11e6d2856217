using Domain.Commands.Voucher.Configuration;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class DeleteStandardVoucherConfigurationService
{
    private readonly ILogger<DeleteStandardVoucherConfigurationService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DeleteStandardVoucherConfigurationService(
        ILogger<DeleteStandardVoucherConfigurationService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> DeleteManualVoucherConfiguration(DeleteVoucherConfigCommand command)
    {

        _logger.LogInformation("DeleteManualVoucherConfiguration process initiated. ItemId {ItemId}", command.ItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessDeletionManualVoucherConfig(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during DeleteManualVoucherConfiguration process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while deleting the manual voucher configuration.");
        }

        return commandResponse;
    }

    private async Task ProcessDeletionManualVoucherConfig(DeleteVoucherConfigCommand command, CommandResponse commandResponse)
    {

        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var isExistingData =
            await _repository.ExistsAsync<BuroStandardVoucherConfiguration>(c => c.ItemId == command.ItemId);
        if (!isExistingData)
        {
            _logger.LogWarning("No existing manual voucher configuration found for Voucher Config Item ID: {ItemID}. Skipping delete.", command.ItemId);
            commandResponse.SetError(BuroErrorMessageKeys.VoucherNotFound, "No existing manual voucher configuration found.");
        }

        var updatedProperties = PrepareVoucherConfigProperties(securityContext.UserId, currentDateTime);

        _logger.LogInformation("Deleting manual voucher config accounts heads for Item ID: {ItemId}", command.ItemId);

        await _repository.UpdateManyAsync<BuroStandardVoucherConfiguration>(x => x.ItemId == command.ItemId, updatedProperties);
        _logger.LogInformation("Deleted manual voucher configuration successfully for Item ID: {ItemId}", command.ItemId);
    }


    private static Dictionary<string, object> PrepareVoucherConfigProperties(string userId, DateTime currentDateTime)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroStandardVoucherConfiguration.LastUpdateDate), currentDateTime },
            { nameof(BuroStandardVoucherConfiguration.LastUpdatedBy), userId },
            { nameof(BuroStandardVoucherConfiguration.IsMarkedToDelete), true }
        };

        return properties;
    }
}