using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class DeleteStandardVoucherConfigurationStrategy : IDeleteVoucherConfigurationStrategy
{
    private readonly DeleteStandardVoucherConfigurationService _deleteStandardVoucherService;
    public DeleteStandardVoucherConfigurationStrategy(DeleteStandardVoucherConfigurationService deleteStandardVoucherService)
    {
        _deleteStandardVoucherService = deleteStandardVoucherService;
    }

    public async Task<CommandResponse> Execute(DeleteVoucherConfigCommand command)
    {
        return await _deleteStandardVoucherService.DeleteManualVoucherConfiguration(command);
    }
}