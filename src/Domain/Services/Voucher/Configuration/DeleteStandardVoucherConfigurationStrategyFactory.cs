using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class DeleteStandardVoucherConfigurationStrategyFactory : IDeleteVoucherConfigurationStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;
    public DeleteStandardVoucherConfigurationStrategyFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }


    public async Task<CommandResponse> ExecuteStrategyAsync(DeleteVoucherConfigCommand command)
    {
        var strategies = _serviceProvider.GetServices<IDeleteVoucherConfigurationStrategy>();
        var strategy = command.VoucherCreationType switch
        {
            EnumVoucherCreationType.Manual => strategies.FirstOrDefault(s => s is DeleteStandardVoucherConfigurationStrategy),
            EnumVoucherCreationType.Auto => strategies.FirstOrDefault(s => s is DeleteProductLineVoucherConfigurationStrategy),
            _ => throw new ArgumentException("Unsupported voucher type")
        } ?? throw new InvalidOperationException($"Strategy for type {command.VoucherCreationType} not found");

        return await strategy.Execute(command);

    }
}