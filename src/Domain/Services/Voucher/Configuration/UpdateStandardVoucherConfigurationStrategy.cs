using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class UpdateStandardVoucherConfigurationStrategy : IUpdateVoucherConfigurationStrategy
{
    private readonly UpdateStandardVoucherConfigurationService _updateStandardService;

    public UpdateStandardVoucherConfigurationStrategy(UpdateStandardVoucherConfigurationService updateStandardService)
    {
        _updateStandardService = updateStandardService;
    }

    public async Task<CommandResponse> Execute(UpdateVoucherConfigCommand command)
    {
        return await _updateStandardService.UpdateManualVoucherConfiguration(command);
    }
}