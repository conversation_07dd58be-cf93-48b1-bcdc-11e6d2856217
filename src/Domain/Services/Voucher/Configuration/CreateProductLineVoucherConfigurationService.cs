using Domain.Commands.Voucher.Configuration;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace Domain.Services.Voucher.Configuration;

public class CreateProductLineVoucherConfigurationService
{
    private readonly ILogger<CreateProductLineVoucherConfigurationService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateProductLineVoucherConfigurationService(
        ILogger<CreateProductLineVoucherConfigurationService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> CreateProductLineVoucherConfiguration(CreateVoucherConfigCommand command)
    {
        _logger.LogInformation("CreateProductLineVoucherConfiguration process initiated. ProductLineItemId {ItemId}", command.ProductLineItemId);

        var commandResponse = new CommandResponse();
        try
        {
            var isExistAutoVoucherConfig = await _repository.ExistsAsync<BuroProductLineVoucherConfiguration>(v => v.ProductLineItemId == command.ProductLineItemId && v.TransactionType == command.TransactionType);

            if (isExistAutoVoucherConfig)
            {
                _logger.LogWarning("Voucher configuration already exists for TransactionType: {TransactionType}",
                    command.TransactionType);
                return commandResponse.SetError(BuroErrorMessageKeys.VoucherExists,
                    "A voucher configuration for this transaction type already exists with the same ProductLineItemId.");
            }

            await ProcessConfigurationCreationAsync(command);
            _logger.LogInformation("CreateProductLineVoucherConfiguration process completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during CreateProductLineVoucherConfiguration process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating the auto voucher configuration.");
        }

        return commandResponse;
    }

    private async Task ProcessConfigurationCreationAsync(CreateVoucherConfigCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();

        _logger.LogInformation("Preparing to insert auto voucher configuration...");

        var voucherConfig = await PrepareAutoVoucherConfigurationEntity(command, securityContext);

        await _repository.InsertOneAsync(voucherConfig);

        _logger.LogInformation("Successfully inserted auto voucher configuration with ItemId: {ItemId}", voucherConfig.ItemId);

    }

    private Task<BuroProductLineVoucherConfiguration> PrepareAutoVoucherConfigurationEntity(CreateVoucherConfigCommand command,
        SecurityContext securityContext)
    {

        var charOfAccIds = command.LedgerEntries.Select(c => c.ChartOfAccountItemId).ToList();

        var accountHeads = _repository.FindAsync<BuroChartOfAccount>(x => charOfAccIds.Contains(x.ItemId))
            .ToDictionaryAsync(c => c.ItemId, c => new { c.Code, c.Name });

        var ledgerEntries = command.LedgerEntries.Select(entry =>
        {
            accountHeads.TryGetValue(entry.ChartOfAccountItemId, out var acc);

            return new LedgerEntry
            {
                ItemId = Guid.NewGuid().ToString(),
                ChartOfAccountItemId = entry.ChartOfAccountItemId,
                ChartOfAccountCode = acc?.Code,
                ChartOfAccountName = acc?.Name,
                VoucherType = entry.VoucherType
            };
        }).ToList();

        if (!ledgerEntries.Any())
        {
            _logger.LogWarning("No valid LedgerEntries found for the provided ChartOfAccountItemIds.");
        }

        var voucherConfigEntity = new BuroProductLineVoucherConfiguration
        {
            ProductLineItemId = command.ProductLineItemId,
            VoucherCreationType = command.VoucherCreationType,
            VoucherScopeCategory = command.VoucherScopeCategory,
            LedgerEntries = ledgerEntries,
            Tags = new[] { Tags.IsProductLineVoucherConfiguration },
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
        };

        return Task.FromResult(voucherConfigEntity);
    }



}