using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class DeleteProductLineVoucherConfigurationStrategy : IDeleteVoucherConfigurationStrategy
{
    private readonly DeleteProductLineVoucherConfigurationService _deleteProductLineVoucherService;

    public DeleteProductLineVoucherConfigurationStrategy(DeleteProductLineVoucherConfigurationService deleteProductLineVoucherService)
    {
        _deleteProductLineVoucherService = deleteProductLineVoucherService;
    }

    public async Task<CommandResponse> Execute(DeleteVoucherConfigCommand command)
    {
        return await _deleteProductLineVoucherService.DeleteAutoVoucherConfiguration(command);
    }
}