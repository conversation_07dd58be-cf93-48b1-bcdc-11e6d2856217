using Domain.Commands.Voucher.Configuration;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace Domain.Services.Voucher.Configuration;

public class UpdateProductLineVoucherConfigurationService
{
    private readonly ILogger<UpdateProductLineVoucherConfigurationService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateProductLineVoucherConfigurationService(
        ILogger<UpdateProductLineVoucherConfigurationService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> UpdateAutoVoucherConfiguration(UpdateVoucherConfigCommand command)
    {

        _logger.LogInformation("UpdateAutoVoucherConfiguration process initiated. ItemId {ItemId}", command.ItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessAutoUpdateVoucherConfig(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during UpdateAutoVoucherConfiguration process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the auto voucher configuration.");
        }

        return commandResponse;
    }

    private async Task ProcessAutoUpdateVoucherConfig(UpdateVoucherConfigCommand command, CommandResponse commandResponse)
    {

        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var isExistingData =
            await _repository.ExistsAsync<BuroProductLineVoucherConfiguration>(c => c.ItemId == command.ItemId);
        if (!isExistingData)
        {
            _logger.LogWarning("No existing auto voucher configuration found for Voucher Config Item ID: {ItemID}. Skipping update.", command.ItemId);
            commandResponse.SetError(BuroErrorMessageKeys.VoucherNotFound, "No existing auto voucher configuration found.");
        }

        var updatedProperties = PrepareVoucherConfigProperties(command, securityContext.UserId, currentDateTime);

        _logger.LogInformation("Updating auto voucher config accounts heads for Item ID: {ItemId}", command.ItemId);

        await _repository.UpdateManyAsync<BuroProductLineVoucherConfiguration>(x => x.ItemId == command.ItemId, updatedProperties);
        _logger.LogInformation("Updated auto voucher configuration successfully for Item ID: {ItemId}", command.ItemId);
    }

    private Dictionary<string, object> PrepareVoucherConfigProperties(UpdateVoucherConfigCommand command, string userId,
        DateTime currentDateTime)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroProductLineVoucherConfiguration.LastUpdateDate), currentDateTime },
            { nameof(BuroProductLineVoucherConfiguration.LastUpdatedBy), userId },
        };

        var charOfAccIds = command.LedgerEntries.Select(c => c.ChartOfAccountItemId).ToList();

        var accountHeads = _repository.GetItems<BuroChartOfAccount>(x => charOfAccIds.Contains(x.ItemId))
            .ToDictionary(c => c.ItemId, c => new { c.Code, c.Name });

        var ledgerEntries = command.LedgerEntries.Select(entry =>
        {
            accountHeads.TryGetValue(entry.ChartOfAccountItemId, out var acc);

            return new LedgerEntry
            {
                ItemId = Guid.NewGuid().ToString(),
                ChartOfAccountItemId = entry.ChartOfAccountItemId,
                ChartOfAccountCode = acc?.Code,
                ChartOfAccountName = acc?.Name,
                VoucherType = entry.VoucherType
            };
        }).ToList();

        properties.Add(nameof(BuroProductLineVoucherConfiguration.LedgerEntries),
            new BsonArray(ledgerEntries.Select(le => le.ToBsonDocument())));

        return properties;
    }


}