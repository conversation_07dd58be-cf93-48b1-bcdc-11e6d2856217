using Domain.Commands.Voucher.Configuration;
using Domain.Contracts.Voucher.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher.Configuration;

public class CreateStandardVoucherConfigurationStrategyFactory : ICreateVoucherConfigurationStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;

    public CreateStandardVoucherConfigurationStrategyFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task<CommandResponse> ExecuteStrategyAsync(CreateVoucherConfigCommand command)
    {
        var strategies = _serviceProvider.GetServices<ICreateVoucherConfigurationStrategy>();
        var strategy = command.VoucherScopeCategory switch
        {
            EnumVoucherScopeCategory.Product => strategies.FirstOrDefault(s => s is CreateProductLineVoucherConfigurationStrategy),
            _ => strategies.FirstOrDefault(s => s is CreateStandardVoucherConfigurationStrategy)
        } ?? throw new InvalidOperationException($"Strategy for type {command.VoucherScopeCategory} not found");

        return await strategy.Execute(command);
    }
}