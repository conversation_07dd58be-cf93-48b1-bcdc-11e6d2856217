using Domain.Commands.Voucher;
using Domain.Contracts.Voucher;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Voucher;

public class UpdateManualVoucherService : IUpdateManualVoucherCommandService
{
    private readonly ILogger<UpdateManualVoucherService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public UpdateManualVoucherService(
        ILogger<UpdateManualVoucherService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }


    public async Task<CommandResponse> UpdateManualVoucherAsync(UpdateManualVoucherCommand command)
    {
        _logger.LogInformation("UpdateManualVoucherAsync process initiated. VoucherGroupItemId {GroupItemId}", command.VoucherGroupItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessUpdateManualVoucher(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during UpdateManualVoucherAsync process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the manual voucher.");
        }

        return commandResponse;
    }


    private async Task ProcessUpdateManualVoucher(UpdateManualVoucherCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var existingVouchers = _repository.GetItems<BuroVoucher>(
            c => c.VoucherGroupItemId == command.VoucherGroupItemId && c.VoucherCreationType == EnumVoucherCreationType.Manual).ToList();

        if (!existingVouchers.Any())
        {
            _logger.LogWarning("No vouchers found for group: {VoucherGroupItemId} and type: {VoucherCreationType}", command.VoucherGroupItemId, EnumVoucherCreationType.Manual);
            commandResponse.SetError(BuroErrorMessageKeys.ManualVoucherNotFound, "No vouchers found for this group");
            return;
        }

        var voucherConfig = await _repository.GetItemAsync<BuroStandardVoucherConfiguration>(
            v => v.ItemId == command.VoucherConfigItemId);

        if (voucherConfig == null)
        {
            _logger.LogWarning("Voucher config not found: {VoucherConfigItemId}", command.VoucherConfigItemId);
            commandResponse.SetError(BuroErrorMessageKeys.VoucherConfigNotFound, "Voucher configuration not found");
            return;
        }

        var bulkOperations = new List<Task>();

        foreach (var voucher in existingVouchers)
        {
            var matchingLedger = voucherConfig.LedgerEntries?
                .FirstOrDefault(le => le.ChartOfAccountItemId == voucher.ChartOfAccountItemId);

            if (matchingLedger == null) continue;

            var updates = new Dictionary<string, object>
        {
            { nameof(BuroVoucher.LastUpdateDate), currentDateTime },
            { nameof(BuroVoucher.LastUpdatedBy), securityContext.UserId },
            { nameof(BuroVoucher.Narration), command.Narration },
            {
                nameof(BuroVoucher.DebitAmount),
                matchingLedger.VoucherType == EnumBuroVoucherType.Debit ? command.Amount : 0
            },
            {
                nameof(BuroVoucher.CreditAmount),
                matchingLedger.VoucherType == EnumBuroVoucherType.Credit ? command.Amount : 0
            }
        };

            bulkOperations.Add(
                _repository.UpdateAsync<BuroVoucher>(
                    x => x.ItemId == voucher.ItemId,
                    updates
                )
            );
        }

        await Task.WhenAll(bulkOperations);

        _logger.LogInformation("Updated {Count} vouchers in group: {VoucherGroupItemId}",
            existingVouchers.Count, command.VoucherGroupItemId);
    }




}