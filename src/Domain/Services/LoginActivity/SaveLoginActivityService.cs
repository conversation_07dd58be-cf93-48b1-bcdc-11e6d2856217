using Domain.Commands.LoginActivity;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Models.LoginActivity;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.LoginActivity;

public class SaveLoginActivityService
{
    private readonly ISecurityContextProvider _contextProvider;
    private readonly IBuroKeyStore _keyStore;
    private readonly IRepository _repository;

    public SaveLoginActivityService(IRepository repository,
        IBuroKeyStore keyStore,
        ISecurityContextProvider contextProvider)
    {
        _repository = repository;
        _keyStore = keyStore;
        _contextProvider = contextProvider;
    }

    public async Task<CommandResponse> SaveLoginActivity(SaveLoginActivityCommand saveLoginActivityCommand)
    {
        var response = new CommandResponse();
        var userId = _contextProvider.GetSecurityContext().UserId;
        var key = GetKeyForLoginActivity(userId);

        if (string.IsNullOrEmpty(saveLoginActivityCommand.DelegationRequestItemId))
        {
            await HandleNewLoginActivity(key, userId, userId, DateTime.UtcNow, DateTime.UtcNow.AddDays(1));
            return response;
        }

        var delegationRequest = await ValidateDelegationRequestAndRetrieve(
            saveLoginActivityCommand, response, userId);

        if (delegationRequest == null) return response;

        var loginActivityInfo = await GetLoginActivityInfo(key);
        if (loginActivityInfo == null)
        {
            await HandleNewLoginActivity(key, delegationRequest.SourceUserItemId, delegationRequest.DelegateeUserItemId,
                delegationRequest.StartDate, delegationRequest.EndDate);
            return response;
        }

        if (loginActivityInfo.LoggedInAsUserId == delegationRequest.DelegateeUserItemId) return response;

        await HandleExistingLoginActivity(key, delegationRequest.SourceUserItemId,
            delegationRequest.DelegateeUserItemId, delegationRequest.StartDate,
            delegationRequest.EndDate, loginActivityInfo);

        return response;
    }

    private async Task<BuroDelegationRequest?> ValidateDelegationRequestAndRetrieve(
        SaveLoginActivityCommand saveLoginActivityCommand, CommandResponse response, string userId)
    {
        var delegationRequest = await _repository.GetItemAsync<BuroDelegationRequest>(item =>
            item.ItemId == saveLoginActivityCommand.DelegationRequestItemId &&
            item.SourceUserItemId == userId &&
            item.StartDate <= DateTime.UtcNow &&
            item.EndDate >= DateTime.UtcNow &&
            item.DelegationStatus == EnumDelegationStatus.Delegated);

        if (delegationRequest == null)
        {
            response.SetError(BuroErrorMessageKeys.InvalidDelegation, "Invalid delegation request");
            return null;
        }

        return delegationRequest;
    }

    private async Task HandleNewLoginActivity(string key, string userId, string delegateeUserId, DateTime startDate,
        DateTime endDate)
    {
        var loginActivityInfoModel = PopulateLoginActivityInfo(userId, delegateeUserId, startDate, endDate);
        await SetLoginActivityInfo(key, loginActivityInfoModel);

        var loginActivityExists =
            await _repository.ExistsAsync<BuroLoginActivity>(item => item.LoggedInUserId == userId);
        if (!loginActivityExists)
            await SaveNewLoginActivityAsync(userId, delegateeUserId, startDate, endDate);
        else
            await UpdateLoginActivityAsync(userId, delegateeUserId, startDate, endDate);
    }


    private async Task SaveNewLoginActivityAsync(string userId, string delegateeUserId, DateTime startDate,
        DateTime endDate)
    {
        var loginActivity = PopulateLoginActivity(userId, delegateeUserId, startDate, endDate);
        await _repository.SaveAsync(loginActivity);
    }

    private async Task HandleExistingLoginActivity(
        string key,
        string userId, string delegateeUserId, DateTime startDate, DateTime endDate,
        LoginActivityModel loginActivityInfo)
    {
        loginActivityInfo.LoggedInAsUserId = delegateeUserId;
        loginActivityInfo.StartDate = startDate;
        loginActivityInfo.EndDate = endDate;

        await SetLoginActivityInfo(key, loginActivityInfo);

        await UpdateLoginActivityAsync(userId, delegateeUserId, startDate, endDate);
    }

    private async Task UpdateLoginActivityAsync(string userId, string delegateeUserId, DateTime startDate,
        DateTime endDate)
    {
        var properties = PrepareLoginActivityPropertiesToUpdateAsync(delegateeUserId, startDate, endDate);
        await _repository.UpdateAsync<BuroLoginActivity>(l => l.LoggedInUserId == userId, properties);
    }

    private BuroLoginActivity PopulateLoginActivity(string userId, string delegateeUserId, DateTime startDate,
        DateTime endDate)
    {
        return new BuroLoginActivity
        {
            ItemId = Guid.NewGuid().ToString(),
            LoggedInUserId = userId,
            LoggedInAsUserId = delegateeUserId,
            StartDate = startDate,
            EndDate = endDate,
            CreateDate = DateTime.UtcNow,
            LastUpdateDate = DateTime.UtcNow
        };
    }

    private LoginActivityModel PopulateLoginActivityInfo(string userId, string delegateeUserId, DateTime startDate,
        DateTime endDate)
    {
        return new LoginActivityModel
        {
            LoggedInUserId = userId,
            LoggedInAsUserId = delegateeUserId,
            StartDate = startDate,
            EndDate = endDate
        };
    }


    private Dictionary<string, object> PrepareLoginActivityPropertiesToUpdateAsync(
        string delegateeUserId, DateTime startDate, DateTime endDate)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroLoginActivity.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroLoginActivity.LoggedInAsUserId), delegateeUserId },
            { nameof(BuroLoginActivity.StartDate), startDate },
            { nameof(BuroLoginActivity.EndDate), endDate }
        };

        return properties;
    }


    private string GetKeyForLoginActivity(string userId)
    {
        return $"{userId}_{BuroConstants.LoginActivitySuffix}";
    }

    private async Task<LoginActivityModel?> GetLoginActivityInfo(string key)
    {
        var loginActivity = await _keyStore.GetValueAsync<LoginActivityModel>(key);
        return loginActivity;
    }

    private async Task SetLoginActivityInfo(string key, LoginActivityModel loginActivityInfo)
    {
        var loginActivityInfoStr = JsonConvert.SerializeObject(loginActivityInfo);
        await _keyStore.AddKeyWithExpiryAsync(key, loginActivityInfoStr, 36000);
    }
}