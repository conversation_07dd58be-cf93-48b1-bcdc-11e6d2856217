using Domain.Commands.Notice;
using FinMongoDbRepositories;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Notice
{
    public class NoticeService
    {
        private readonly ILogger<NoticeService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _contextProvider;
        private readonly string ButtonTypePrimary = "PRIMARY";
        public NoticeService(ILogger<NoticeService> logger, IFinMongoDbRepository finMongoDbRepository, ISecurityContextProvider contextProvider)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _finMongoDbRepository = finMongoDbRepository ?? throw new ArgumentNullException(nameof(finMongoDbRepository));
            _contextProvider = contextProvider ?? throw new ArgumentNullException(nameof(contextProvider));
        }

        public async Task<bool> SaveNotice(BuroNotice notice)
        {
            _logger.LogInformation("NoticeService->SaveNotice-START");
            await _finMongoDbRepository.InsertOneAsync(notice);
            return true;
        }

        public async Task<bool> SaveNoticeRicipientAction(CreateNoticeRecipientActionCommand command)
        {
            var userId = _contextProvider.GetSecurityContext().UserId;
            var employee = await _finMongoDbRepository.FindOneAsync<BuroEmployee>(x => x.UserItemId == userId);
            if (employee == null)
            {
                throw new InvalidOperationException("employee not found.");
            }

            var notice = await _finMongoDbRepository.FindOneAsync<BuroNotice>(x => x.ItemId == command.NoticeItemId);
            if (notice == null)
            {
                throw new InvalidOperationException("notice not found.");
            }

            var noticeRecipient = await _finMongoDbRepository.FindOneAsync<BuroNoticeRecipient>(x => x.NoticeItemId == command.NoticeItemId);

            string clickedButtonLabel = command.ClickedButtonType == EnumButtonType.PRIMARY ? notice.PrimaryButtonTitle : command.ClickedButtonType == EnumButtonType.SECONDARY ? notice.SecondaryButtonTitle : "Remind Me Later";
            string clickedButtonType = command.ClickedButtonType == EnumButtonType.PRIMARY ? "PRIMARY" : command.ClickedButtonType == EnumButtonType.SECONDARY ? "SECONDARY" : "REMINDLATER";
            var noticeRecipientAction = new NoticeRecipientAction
            {
                ActionDateTime = DateTime.UtcNow,
                ClickedButtonLabel = clickedButtonLabel,
                ClickedButtonType = clickedButtonType,
            };
            //check if has previous entry
            if (noticeRecipient == null)
            {
                var newNoticeRecipient = new BuroNoticeRecipient
                {
                    ItemId = Guid.NewGuid().ToString(),
                    NoticeItemId = command.NoticeItemId,
                    EmployeeName = employee.EmployeeName,
                    EmployeeItemId = employee.ItemId,
                    EmployeePin = employee.EmployeePin,
                    IsActionPerformed = true,
                    IsAcknowledged = command.ClickedButtonType == EnumButtonType.PRIMARY ? true : false,
                    RecipientActionList = new List<NoticeRecipientAction>() { noticeRecipientAction }
                };

                await _finMongoDbRepository.InsertOneAsync(newNoticeRecipient);

                return true;
            }


            if (command.ClickedButtonType == EnumButtonType.PRIMARY)
            {
                noticeRecipient.IsAcknowledged = true;
            }

            noticeRecipient.RecipientActionList.Add(noticeRecipientAction);

            await _finMongoDbRepository.UpdateOneAsync<BuroNoticeRecipient>(t => t.ItemId == noticeRecipient.ItemId, noticeRecipient);

            return true;
        }
    }
}
