using Domain.Commands.DayEnd;
using Domain.Contracts.DayEnd;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.DayEnd;

public class DayEndProcessService
{
    private readonly ILogger<DayEndProcessService> _logger;
    private readonly IDayEndProcessFactoryService _dayEndProcessFactoryService;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DayEndProcessService(
        ILogger<DayEndProcessService> logger,
        IDayEndProcessFactoryService dayEndProcessFactoryService,
        ISecurityContextProvider securityContextProvider,
        IRepository repository)
    {
        _logger = logger;
        _dayEndProcessFactoryService = dayEndProcessFactoryService;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> UpdateDayEndStatus(UpdateDayEndProcessCommand command)
    {
        _logger.LogInformation("UpdateDayEndStatus process initiated");

        var commandResponse = new CommandResponse();
        try
        {
            await ProcessUpdateDayEndStatusAsync(command);

            _logger.LogInformation("UpdateDayEndStatus process completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during UpdateDayEndStatus process");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred,
                "An error occurred while updating the date end process.");
        }

        return commandResponse;
    }

    private async Task ProcessUpdateDayEndStatusAsync(UpdateDayEndProcessCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();

        var existingData =
            await _repository.GetItemAsync<BuroDayEndActivityLog>(d => d.BranchItemId == command.BranchItemId &&
                                                                      d.CreateDate == command.BranchOpeningDate);

        if (existingData == null)
        {
            _logger.LogWarning("No existing DayEnd found for Item ID: {ItemId} and BranchItemId {BranchItemId}",
                command.ItemId, command.BranchItemId);
            throw new ArgumentException("Item not found.");
        }

        var executionResults = new List<DayEndExecutionResponse>();

        foreach (var service in command.ActivityTypes.Select(activityType => _dayEndProcessFactoryService.GetService(activityType)))
        {
            var result = await service.ExecuteDayEndAsync(command, existingData);
            executionResults.Add(result);
        }

        var updatedProperties = PrepareActivityLogUpdates(
            securityContext.UserId,
            executionResults,
            existingData.Activities
        );

        await _repository.UpdateManyAsync<BuroDayEndActivityLog>(x => x.ItemId == existingData.ItemId, updatedProperties);

        _logger.LogInformation("Updated DayEnd status for Item ID: {ItemId}", existingData.ItemId);


    }

    private Dictionary<string, object> PrepareActivityLogUpdates(
        string userId,
        List<DayEndExecutionResponse> executionResults,
        List<DayEndActivity> existingActivities)
    {
        var currentDateTime = DateTime.UtcNow;
        var updatedProperties = new Dictionary<string, object>
        {
            [nameof(BuroDayEndActivityLog.LastUpdateDate)] = currentDateTime,
            [nameof(BuroDayEndActivityLog.LastUpdatedBy)] = userId
        };

        // Create lookup dictionary for results
        var resultLookup = executionResults.ToDictionary(r => r.ItemId);

        // Update only status and entity data for matching activities
        var updatedActivities = existingActivities.Select(activity =>
        {
            if (resultLookup.TryGetValue(activity.ItemId, out var result))
            {
                return new DayEndActivity
                {

                    ItemId = activity.ItemId,
                    ActivityType = activity.ActivityType,
                    Status = result.ActivityStatus,
                    EntityData = result.EntityData
                };
            }
            return activity;
        }).ToList();

        updatedProperties[nameof(BuroDayEndActivityLog.Activities)] = updatedActivities;

        return updatedProperties;
    }



}