using Domain.Contracts.DayEnd;
using Infrastructure.Constants;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Services.DayEnd;

public class DayEndProcessFactoryService : IDayEndProcessFactoryService
{
    private readonly IServiceProvider _serviceProvider;

    public DayEndProcessFactoryService(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }


    public IDayEndExecuteService GetService(EnumDayEndActivityType activityType)
    {
        return activityType switch
        {
            EnumDayEndActivityType.NewCsWithoutDeposit => _serviceProvider.GetRequiredService<CheckNewCsAccountWithoutDepositsService>(),
            _ => throw new ArgumentException(string.Format(BuroErrorMessageKeys.InvalidProperty, nameof(activityType).ToUpper()))
        };
    }



}