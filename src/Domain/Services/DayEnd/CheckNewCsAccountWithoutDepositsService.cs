using Domain.Commands.DayEnd;
using Domain.Contracts.DayEnd;
using FinMongoDbRepositories;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Domain.Services.DayEnd;

public class CheckNewCsAccountWithoutDepositsService : IDayEndExecuteService
{

    private readonly ILogger<CheckNewCsAccountWithoutDepositsService> _logger;
    private readonly IFinMongoDbRepository _finMongoDbRepository;

    public CheckNewCsAccountWithoutDepositsService(
        ILogger<CheckNewCsAccountWithoutDepositsService> logger,
        IFinMongoDbRepository finMongoDbRepository)
    {
        _logger = logger;
        _finMongoDbRepository = finMongoDbRepository;

    }

    public async Task<DayEndExecutionResponse> ExecuteDayEndAsync(
        UpdateDayEndProcessCommand command,
        BuroDayEndActivityLog activityLog)
    {
        _logger.LogInformation("Start Checking New Cs Accounts Without Deposits with Id: {BranchItemId}", command.BranchItemId);

        try
        {
            var filter = BuildCsAccountsFilter(command);
            var totalCount = await _finMongoDbRepository.GetCountByFilterDefinitionAsync(filter);

            var response = totalCount == 0
                ? new DayEndExecutionResponse
                {
                    ItemId = activityLog.ItemId,
                    ActivityStatus = EnumDayEndActivityStatus.Clear,
                    EntityData = new MetaData()
                }
                : new DayEndExecutionResponse
                {
                    ItemId = activityLog.ItemId,
                    ActivityStatus = EnumDayEndActivityStatus.Issue,
                    EntityData = new MetaData()
                };

            _logger.LogInformation("End Checking New Cs Accounts Without Deposits with Id: {BranchItemId}", command.BranchItemId);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception in GetNewCsAccountsWithoutDepositsDataAsync");
            _logger.LogInformation("End Checking New Cs Accounts Without Deposits with Id: {BranchItemId}", command.BranchItemId);

            return new DayEndExecutionResponse
            {
                ItemId = activityLog.ItemId,
                ActivityStatus = EnumDayEndActivityStatus.NotInitiated,
                EntityData = new MetaData()
            };
        }
    }

    private static FilterDefinition<BuroContractualSavingsApplication> BuildCsAccountsFilter(
        UpdateDayEndProcessCommand command)
    {
        var builder = Builders<BuroContractualSavingsApplication>.Filter;

        return builder.And(
            builder.Eq(x => x.BranchItemId, command.BranchItemId),
            builder.Eq(x => x.Status, EnumProductApplicationStatus.Pending),
            builder.Gte(x => x.LastUpdateDate, command.BranchOpeningDate),
            builder.Lt(x => x.LastUpdateDate, command.BranchOpeningDate.Date.AddDays(1))
        );
    }
}