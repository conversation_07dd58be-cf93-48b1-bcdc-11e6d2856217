using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.ExternalSync
{
    public class ExternalSyncService
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IRepository _repository;
        public ExternalSyncService(
            ISecurityContextProvider securityContextProvider,
            IRepository repository)
        {
            _securityContextProvider = securityContextProvider ?? throw new ArgumentNullException(nameof(securityContextProvider));
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        public async Task<bool> AddExternalRequestEntry(string requestItemId, string requestNumber, EnumExternalSyncType syncType)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var loggedInUserId = securityContext.UserId;

            var request = new BuroExternalSyncRequest()
            {
                ItemId = requestItemId,
                RequestNumber = requestNumber,
                DataSyncType = syncType,
                DataSyncStatus = EnumnExternalSyncStatus.Requested,
                CreateDate = DateTime.UtcNow,
                CreatedBy = loggedInUserId,
            };

            await _repository.SaveAsync<BuroExternalSyncRequest>(request);
            return true;
        }
    }
}
