using Domain.Commands.ConsentForm;
using Domain.Contracts.ConsentForm;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.ConsentForm;

public class RevokeConsentFormService : IRevokeConsentFormCommandService
{
    private readonly ILogger<RevokeConsentFormService> _logger;
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public RevokeConsentFormService(
        ILogger<RevokeConsentFormService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }


    public async Task<CommandResponse> RevokeConsentFormAsync(RevokeConsentFormCommand command)
    {
        _logger.LogInformation("RevokeConsentFormAsync process initiated. Consent Form ItemId: {ConsentFormItemId}", command.ConsentFormItemId);

        var commandResponse = new CommandResponse();

        try
        {
            await ProcessRevokeConsentFormAsync(command, commandResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during RevokeConsentFormAsync process.");
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while revoking the consent form.");
        }

        return commandResponse;
    }

    private async Task ProcessRevokeConsentFormAsync(RevokeConsentFormCommand command, CommandResponse commandResponse)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();
        var currentDateTime = DateTime.UtcNow;

        var isExistingData =
            await _repository.ExistsAsync<BuroConsentForm>(c => c.ItemId == command.ConsentFormItemId);
        if (!isExistingData)
        {
            _logger.LogWarning("No existing consent form found for consent form Item ID: {ItemID}. Skipping revoke.", command.ConsentFormItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ConsentFormNotFound, "No existing consent form found.");
        }

        var updatedProperties = PrepareConsentFormProperties(securityContext.UserId, currentDateTime);

        _logger.LogInformation("Revoking consent form Item ID: {ItemId}", command.ConsentFormItemId);

        await _repository.UpdateOneAsync<BuroConsentForm>(x => x.ItemId == command.ConsentFormItemId, updatedProperties);
        _logger.LogInformation("Revoked consent form for Item ID: {ItemId}", command.ConsentFormItemId);
    }

    private static Dictionary<string, object> PrepareConsentFormProperties(string userId, DateTime currentDateTime)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroConsentForm.LastUpdateDate), currentDateTime },
            { nameof(BuroConsentForm.LastUpdatedBy), userId },
            { nameof(BuroConsentForm.IsMarkedToDelete), true }
        };

        return properties;
    }

}

