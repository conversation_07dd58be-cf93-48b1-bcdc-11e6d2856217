using Domain.Commands.Inventory;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class CreateInventoryRequestService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateInventoryRequestService(IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task CreateInventoryRequestAsync(CreateInventoryRequestCommand command)
    {
        var loggedInEmployee = await GetLoggedInEmployeeAsync();
        var inventoryItem = await GetInventoryItemAsync(command.InventoryItemId);
        var requestingOffice = await GetRequestingOfficeAsync(loggedInEmployee);
        var recipientOffice = await GetRecipientOfficeAsync(requestingOffice);
        var inventoryRequest = PopulateInventoryRequest(command, inventoryItem, requestingOffice, recipientOffice);

        await _repository.InsertOneAsync(inventoryRequest);

    }

    private async Task<BuroInventoryItem> GetInventoryItemAsync(string inventoryItemId)
    {
        return await _repository.FindOneAsync<BuroInventoryItem>(item => item.ItemId == inventoryItemId);
    }

    private BuroInventoryRequest PopulateInventoryRequest(CreateInventoryRequestCommand command,
        BuroInventoryItem inventoryItem, BuroOffice requestingOffice, BuroOffice recipientOffice)
    {
        var context = _securityContextProvider.GetSecurityContext();
        return new BuroInventoryRequest
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = command.InventoryItemId,
            InventoryName = command.InventoryName,
            InventoryItemDefinition = inventoryItem.ItemDefinition,
            CategoryItemId = inventoryItem.CategoryItemId,
            Category = inventoryItem.CategoryName,
            SubCategoryItemId = inventoryItem.SubCategoryItemId,
            SubCategory = inventoryItem.SubCategoryName,
            HasSerialNumber = inventoryItem.HasSerialNumber,
            RequestDate = DateTime.UtcNow,
            RequestedQuantity = command.RequestedQuantity,
            RequestingOfficeId = requestingOffice.ItemId,
            RequestingOfficeName = requestingOffice.OfficeName,
            RequestingOfficeDepth = requestingOffice.Depth,
            RequestingOfficeHid = requestingOffice.Hid,
            RecipientOfficeId = recipientOffice.ItemId,
            RecipientOfficeName = recipientOffice.OfficeName,
            RecipientOfficeDepth = recipientOffice.Depth,
            RecipientOfficeHid = recipientOffice.Hid,
            CreatedBy = context.UserId,
            CreateDate = DateTime.UtcNow
        };
    }

    private async Task<BuroOffice> GetRecipientOfficeAsync(BuroOffice requestingOffice)
    {
        var recipientOfficeDepth = 0.0;
        switch (requestingOffice.Depth)
        {
            case BuroHierarchyDepthConfig.BranchOffice:
                recipientOfficeDepth = BuroHierarchyDepthConfig.ZoneOffice;
                break;
            case BuroHierarchyDepthConfig.ZoneOffice:
            case BuroHierarchyDepthConfig.HeadOffice:
                recipientOfficeDepth = BuroHierarchyDepthConfig.HeadOffice;
                break;
        }
        return await _repository.FindOneAsync<BuroOffice>(item => item.Depth == recipientOfficeDepth
        && item.Path.Contains(requestingOffice.Hid), null, false);
    }

    private async Task<BuroOffice> GetRequestingOfficeAsync(BuroEmployee loggedInEmployee)
    {
        return await _repository.FindOneAsync<BuroOffice>(item => item.ItemId == loggedInEmployee.CurrentOfficeItemId);
    }

    private async Task<BuroEmployee> GetLoggedInEmployeeAsync()
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return await _repository.FindOneAsync<BuroEmployee>(x => x.UserItemId == userId);
    }
}