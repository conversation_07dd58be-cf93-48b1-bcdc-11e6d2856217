using Domain.Commands.Inventory;
using Infrastructure.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class UpdateInventoryItemService
{
    private readonly IRepository _repository;

    public UpdateInventoryItemService(IRepository repository)
    {
        _repository = repository;
    }

    public async Task UpdateInventoryItem(UpdateInventoryItemCommand command)
    {
        var updatedProperties = PrepareUpdateCommand(command);
        await _repository.UpdateAsync<BuroInventoryItem>(item => item.ItemId == command.InventoryItemItemId,
            updatedProperties);
    }

    private Dictionary<string, object> PrepareUpdateCommand(UpdateInventoryItemCommand command)
    {
        return new Dictionary<string, object>
        {
            { nameof(BuroInventoryItem.ItemDefinition), command.ItemDefinition },
            { nameof(BuroInventoryItem.InventoryItemName), command.InventoryItemName },
            { nameof(BuroInventoryItem.NormalizedInventoryItemName), command.InventoryItemName.NormalizeString() },
            { nameof(BuroInventoryItem.CategoryItemId), command.CategoryItemId },
            { nameof(BuroInventoryItem.CategoryName), command.CategoryName },
            { nameof(BuroInventoryItem.SubCategoryItemId), command.SubCategoryItemId },
            { nameof(BuroInventoryItem.SubCategoryName), command.SubCategoryName },
            { nameof(BuroInventoryItem.VendorItemId), command.VendorItemId },
            { nameof(BuroInventoryItem.VendorName), command.VendorName },
            { nameof(BuroInventoryItem.ChartOfAccountItemId), command.ChartOfAccountItemId },
            { nameof(BuroInventoryItem.ChartOfAccount), command.ChartOfAccount },
            { nameof(BuroInventoryItem.PriceType), command.PriceType },
            { nameof(BuroInventoryItem.MinPrice), command.MinPrice },
            { nameof(BuroInventoryItem.MaxPrice), command.MaxPrice },
            { nameof(BuroInventoryItem.FixedPrice), command.FixedPrice },
            { nameof(BuroInventoryItem.HasSerialNumber), command.HasSerialNumber },
            { nameof(BuroInventoryItem.RequiresExpiryDate), command.RequiresExpiryDate },
            { nameof(BuroInventoryItem.LastUpdateDate), DateTime.UtcNow }
        };
    }
}