using Domain.Commands.Inventory;
using Domain.Contracts.Inventory;
using Infrastructure.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class AddInventoryMovementService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IInventoryStockManagementService _inventoryStockManagementService;

    public AddInventoryMovementService(IRepository repository, ISecurityContextProvider securityContextProvider,
        IInventoryStockManagementService inventoryStockManagementService)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
        _inventoryStockManagementService = inventoryStockManagementService;
    }

    public async Task AddInventoryManagement(AddInventoryMovementCommand command)
    {
        var inventoryItem = await GetInventoryItemAsync(command);
        var loggedInEmployee = await GetLoggedInEmployeeAsync();
        var inventoryMovement = await PrepareInventoryMovement(command, inventoryItem, loggedInEmployee);
        await _repository.SaveAsync(inventoryMovement);
        await _inventoryStockManagementService.ManageInventoryStock(inventoryMovement);
    }
    private async Task<BuroEmployee> GetLoggedInEmployeeAsync()
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return await _repository.GetItemAsync<BuroEmployee>(item => item.UserItemId == userId);
    }

    private async Task<BuroInventoryItem> GetInventoryItemAsync(AddInventoryMovementCommand command)
    {
        return await _repository.GetItemAsync<BuroInventoryItem>(item => item.ItemId == command.InventoryItemId);
    }

    private async Task<BuroInventoryMovement> PrepareInventoryMovement(AddInventoryMovementCommand command,
        BuroInventoryItem inventoryItem, BuroEmployee loggedInEmployee)
    {
        var context = _securityContextProvider.GetSecurityContext();
        var employee = await _repository.GetItemAsync<BuroEmployee>(item => item.UserItemId == context.UserId);
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            SourceOfficeItemId = employee.CurrentOfficeItemId,
            SourceOfficeName = employee.CurrentOfficeTitle,
            InventoryItemId = inventoryItem.ItemId,
            InventoryName = inventoryItem.InventoryItemName,
            InventoryItemDefinition = inventoryItem.ItemDefinition,
            CategoryItemId = inventoryItem.CategoryItemId,
            Category = inventoryItem.CategoryName,
            SubCategoryItemId = inventoryItem.SubCategoryItemId,
            SubCategory = inventoryItem.SubCategoryName,
            VendorItemId = command.VendorItemId,
            VendorName = command.VendorName,
            Quantity = command.Quantity,
            HasSerialNumber = inventoryItem.HasSerialNumber,
            SerialNumber = command.SerialNumber,
            RequiresExpiryDate = inventoryItem.RequiresExpiryDate,
            ExpiryDate = command.ExpiryDate,
            UnitPrice = command.Price,
            TotalPrice = command.Price * command.Quantity,
            MovementType = EnumInventoryMovementType.In,
            CreatedBy = context.UserId,
            CreateDate = DateTime.UtcNow,
            CreatedByEmployeeName = loggedInEmployee.EmployeeName,
            CreatedByEmployeePin = loggedInEmployee.EmployeePin,
            CreatedByEmployeeDesignationTitle = loggedInEmployee.DesignationTitle,
            CreatedByEmployeeDesignationItemId = loggedInEmployee.DesignationItemId
        };
        inventoryMovement.AddEntityBasicInfo();
        return inventoryMovement;
    }
}