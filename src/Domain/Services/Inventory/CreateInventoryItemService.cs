using Domain.Commands.Inventory;
using Infrastructure.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class CreateInventoryItemService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public CreateInventoryItemService(IRepository repository, ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task CreateInventoryItem(CreateInventoryItemCommand command)
    {
        var context = _securityContextProvider.GetSecurityContext();
        var employee = await _repository.GetItemAsync<BuroEmployee>(item => item.UserItemId == context.UserId);
        var inventoryItem = PopulateInventoryItem(command, employee);

        await _repository.SaveAsync(inventoryItem);
    }

    private BuroInventoryItem PopulateInventoryItem(CreateInventoryItemCommand command, BuroEmployee? employee)
    {
        return new BuroInventoryItem
        {
            ItemId = Guid.NewGuid().ToString(),
            ItemDefinition = command.ItemDefinition,
            InventoryItemName = command.InventoryItemName,
            NormalizedInventoryItemName = command.InventoryItemName.NormalizeString(),
            CategoryItemId = command.CategoryItemId,
            CategoryName = command.CategoryName,
            SubCategoryItemId = command.SubCategoryItemId,
            SubCategoryName = command.SubCategoryName,
            VendorItemId = command.VendorItemId,
            VendorName = command.VendorName,
            ChartOfAccountItemId = command.ChartOfAccountItemId,
            ChartOfAccount = command.ChartOfAccount,
            PriceType = command.PriceType,
            MinPrice = command.MinPrice,
            MaxPrice = command.MaxPrice,
            FixedPrice = command.FixedPrice,
            HasSerialNumber = command.HasSerialNumber,
            RequiresExpiryDate = command.RequiresExpiryDate,
            CreatedBy = employee?.UserItemId,
            CreatedByEmployeeName = employee?.EmployeeName,
            CreatedByEmployeePin = employee?.EmployeePin,
            CreatedByEmployeeDesignation = employee?.DesignationTitle,
            CreateDate = DateTime.UtcNow
        };
    }
}