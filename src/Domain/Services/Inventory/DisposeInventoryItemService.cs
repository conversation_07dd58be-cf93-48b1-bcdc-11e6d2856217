using Domain.Commands.Inventory;
using Domain.Contracts.Inventory;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class DisposeInventoryItemService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly IInventoryStockManagementService _stockManagementService;
    private readonly ISharedService _sharedService;
    private readonly ISecurityContextProvider _securityContextProvider;

    public DisposeInventoryItemService(IFinMongoDbRepository repository,
        IInventoryStockManagementService stockManagementService,
        ISharedService sharedService,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _stockManagementService = stockManagementService;
        _sharedService = sharedService;
        _securityContextProvider = securityContextProvider;
    }

    public async Task DisposeInventoryItemAsync(DisposeInventoryItemCommand command)
    {
        var loggedInEmployee = await GetLoggedInEmployeeAsync();
        var inventoryItem = await GetInventoryItemByIdAsync(command.InventoryItemId);
        var (totalQuantity, totalPrice) = CalculateTotals(command);

        var inventoryMovementPlan = PopulateInventoryMovementPlan(command, inventoryItem, loggedInEmployee);
        var inventoryMovement = PopulateInventoryMovement(command, inventoryItem, loggedInEmployee, totalQuantity, totalPrice);

        await ProcessInventoryDispose(inventoryMovementPlan, inventoryMovement, command, loggedInEmployee);
    }

    private async Task<BuroInventoryItem> GetInventoryItemByIdAsync(string inventoryItemId)
    {
        return await _repository.FindOneAsync<BuroInventoryItem>(item => item.ItemId == inventoryItemId);
    }

    private async Task ProcessInventoryDispose(
        BuroInventoryMovementPlan inventoryMovementPlan,
        BuroInventoryMovement inventoryMovement,
        DisposeInventoryItemCommand command,
        BuroEmployee loggedInEmployee)
    {
        if (IsBranchManager(loggedInEmployee))
            await ProcessBranchManagerInventoryDispose(inventoryMovementPlan, inventoryMovement, command);
        else
            await ProcessRegularEmployeeInventoryDispose(inventoryMovementPlan, inventoryMovement, command,
                loggedInEmployee);
    }

    private async Task ProcessBranchManagerInventoryDispose(
        BuroInventoryMovementPlan plan,
        BuroInventoryMovement movement,
        DisposeInventoryItemCommand command)
    {
        plan.ApprovalStatus = EnumInventoryApprovalStatus.Approved;
        await _repository.InsertOneAsync(movement);
        await _repository.InsertOneAsync(plan);
        await UpdateInventoryStockAsync(movement, EnumStockUpdateType.BothActualAndAvailable);
        await UpdateInventoryStockBreakdownAsync(movement, command, EnumStockUpdateType.BothActualAndAvailable);
    }

    private async Task ProcessRegularEmployeeInventoryDispose(
        BuroInventoryMovementPlan plan,
        BuroInventoryMovement movement,
        DisposeInventoryItemCommand command,
        BuroEmployee employee)
    {
        var approvalItemId = await AskApprovalForInventoryDisposeAsync(plan, employee);
        plan.DisposeApprovalItemId = approvalItemId;
        await _repository.InsertOneAsync(plan);

        await UpdateInventoryStockAsync(movement, EnumStockUpdateType.AvailableOnly);
        await UpdateInventoryStockBreakdownAsync(movement, command, EnumStockUpdateType.AvailableOnly);
    }

    private async Task<string> AskApprovalForInventoryDisposeAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee loggedInEmployee)
    {
        var approverEmployee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(
            loggedInEmployee.CurrentOfficeItemId,
            BuroDesignationConstant.BranchManagerDesignationCode);
        var approvalItemId =
            await CreateInventoryDisposeItemApprovalAsync(inventoryMovementPlan, loggedInEmployee, approverEmployee);

        var notificationPayload = MakeNotificationPayloadForBranchManger(inventoryMovementPlan,
            loggedInEmployee.EmployeeName, approvalItemId, approverEmployee);

        await _sharedService.NotifyUserAsync(BuroNotificationKeys.InventoryOutItemBranchManagerInvited,
            notificationPayload, inventoryMovementPlan.ItemId, approverEmployee.UserItemId);
        return approvalItemId;
    }

    private static object MakeNotificationPayloadForBranchManger(
        BuroInventoryMovementPlan inventoryMovementPlan,
        string addedBy,
        string approvalItemId,
        BuroEmployee manager)
    {
        return new
        {
            InventoryMovementPlanItemId = inventoryMovementPlan.ItemId,
            AssignedAt = DateTime.UtcNow,
            AssignedBy = addedBy,
            ApprovalItemId = approvalItemId,
            ApproverName = manager.EmployeeName,
            ApproverPin = manager.EmployeePin,
            ApproverDesignation = manager.DesignationTitle
        };
    }

    private bool IsBranchManager(BuroEmployee employee)
    {
        return employee.DesignationCode == BuroDesignationConstant.BranchManagerDesignationCode;
    }

    private async Task<string> CreateInventoryDisposeItemApprovalAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroEmployee loggedInEmployee, BuroEmployee approverEmployee)
    {
        var approval = new BuroApproval
        {
            ItemId = Guid.NewGuid().ToString(),
            Tags = new[] { Tags.IsADisposeInventoryApproval },
            CreatedBy = loggedInEmployee.UserItemId,
            LastUpdatedBy = loggedInEmployee.UserItemId,
            RelatedEntityItemId = inventoryMovementPlan.ItemId,
            RelatedEntityName = nameof(BuroInventoryMovementPlan),
            RequestedFromPersonItemId = loggedInEmployee.PersonItemId,
            RequestedFromEmployeeItemId = loggedInEmployee.ItemId,
            RequestedFromEmployeePIN = loggedInEmployee.EmployeePin,
            RequestedFromEmployeeName = loggedInEmployee.EmployeeName,
            ActionedByPersonItemId = approverEmployee.PersonItemId,
            ActionedByEmployeeItemId = approverEmployee.ItemId,
            ActionedByEmployeePIN = approverEmployee.EmployeePin,
            ActionedByEmployeeName = approverEmployee.EmployeeName,
            ActionedByEmployeeDesignation = approverEmployee.DesignationTitle,
            Status = EnumApprovalStatusType.Pending,
            Category = EnumApprovalCategoryType.InventoryDisposeApproval
        };
        await _repository.InsertOneAsync(approval);

        return approval.ItemId;
    }

    private async Task UpdateInventoryStockBreakdownAsync(BuroInventoryMovement inventoryMovement,
        DisposeInventoryItemCommand command, EnumStockUpdateType updateType)
    {
        var stockBreakdownItemIds =
            command.InventoryItemDetails.Select(item => item.InventoryStockBreakdownItemId).ToList();
        var stockBreakdownList =
            await _stockManagementService.GetInventoryStockBreakdownList(stockBreakdownItemIds);

        var tasks = command.InventoryItemDetails
            .Select(inventoryDetail =>
            {
                var breakdown = stockBreakdownList.FirstOrDefault(item =>
                    item.ItemId == inventoryDetail.InventoryStockBreakdownItemId);
                if (breakdown == null) return null;
                return _stockManagementService.UpdateStocksInventoryStockBreakdown(
                    inventoryMovement.MovementType,
                    breakdown, inventoryDetail.Quantity, inventoryMovement.CreatedBy, updateType);
            })
            .Where(task => task != null)
            .ToList();

        await Task.WhenAll(tasks);
    }

    private async Task UpdateInventoryStockAsync(BuroInventoryMovement inventoryMovement,
        EnumStockUpdateType updateType)
    {
        var currentStock = await _stockManagementService.GetInventoryStock(inventoryMovement.InventoryItemId,
            inventoryMovement.SourceOfficeItemId);

        await _stockManagementService.UpdateInventoryStock(inventoryMovement.MovementType,
            inventoryMovement.Quantity, inventoryMovement.UnitPrice, currentStock, inventoryMovement.CreatedBy,
            updateType);
    }

    private BuroInventoryMovementPlan PopulateInventoryMovementPlan(DisposeInventoryItemCommand command,
        BuroInventoryItem inventoryItem,
        BuroEmployee employee)
    {
        return new BuroInventoryMovementPlan
        {
            ItemId = Guid.NewGuid().ToString(),
            InventoryItemId = inventoryItem.ItemId,
            InventoryName = inventoryItem.InventoryItemName,
            CategoryItemId = inventoryItem.CategoryItemId,
            Category = inventoryItem.CategoryName,
            SubCategoryItemId = inventoryItem.SubCategoryItemId,
            SubCategory = inventoryItem.SubCategoryName,
            VendorItemId = command.VendorItemId,
            VendorName = command.VendorName,
            Reason = command.Reason,
            ApprovalStatus = EnumInventoryApprovalStatus.Pending,
            HasSerialNumber = inventoryItem.HasSerialNumber,
            RequiresExpiryDate = inventoryItem.RequiresExpiryDate,
            SourceOfficeItemId = employee.CurrentOfficeItemId,
            SourceOfficeName = employee.CurrentOfficeTitle,
            PlanType = EnumInventoryMovementPlanType.Dispose,
            InventoryItemDetails = command.InventoryItemDetails.Select(detail => new InventoryItemDetail
            {
                InventoryStockBreakdownItemId = detail.InventoryStockBreakdownItemId,
                Quantity = detail.Quantity,
                SerialNumber = detail.SerialNumber,
                ExpiryDate = detail.ExpiryDate,
                Price = detail.Price
            }).ToList(),
            CreatedBy = employee.UserItemId,
            CreateDate = DateTime.UtcNow
        };
    }

    private BuroInventoryMovement PopulateInventoryMovement(DisposeInventoryItemCommand command,
        BuroInventoryItem inventoryItem, BuroEmployee employee,
        int totalQuantity, double totalPrice)
    {
        var inventoryMovement = new BuroInventoryMovement
        {
            ItemId = Guid.NewGuid().ToString(),
            SourceOfficeItemId = employee.CurrentOfficeItemId,
            SourceOfficeName = employee.CurrentOfficeTitle,
            InventoryItemId = inventoryItem.ItemId,
            InventoryName = inventoryItem.InventoryItemName,
            CategoryItemId = inventoryItem.CategoryItemId,
            Category = inventoryItem.CategoryName,
            SubCategoryItemId = inventoryItem.SubCategoryItemId,
            SubCategory = inventoryItem.SubCategoryName,
            VendorItemId = command.VendorItemId,
            VendorName = command.VendorName,
            Quantity = totalQuantity,
            HasSerialNumber = inventoryItem.HasSerialNumber,
            SerialNumber = string.Join(',', command.InventoryItemDetails.Select(item => item.SerialNumber)),
            RequiresExpiryDate = inventoryItem.RequiresExpiryDate,
            ExpiryDate = command.InventoryItemDetails.Max(item => item.ExpiryDate),
            TotalPrice = totalPrice,
            MovementType = EnumInventoryMovementType.Out,
            Reason = command.Reason,
            CreatedBy = employee.UserItemId,
            CreateDate = DateTime.UtcNow
        };
        return inventoryMovement;
    }

    private (int TotalQuantity, double TotalPrice) CalculateTotals(DisposeInventoryItemCommand command)
    {
        var totalQuantity = command.InventoryItemDetails.Sum(item => item.Quantity);
        var totalPrice = command.InventoryItemDetails.Sum(item => item.Price * item.Quantity);
        return (totalQuantity, totalPrice);
    }

    private async Task<BuroEmployee> GetLoggedInEmployeeAsync()
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return await _repository.FindOneAsync<BuroEmployee>(item => item.UserItemId == userId);
    }
}