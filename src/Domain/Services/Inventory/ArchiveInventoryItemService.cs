using Domain.Commands.Inventory;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class ArchiveInventoryItemService
{
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public ArchiveInventoryItemService(IRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
    }

    public async Task ArchiveInventoryItem(ArchiveInventoryItemCommand command)
    {
        var updatedProperties = PrepareUpdateCommand(command);
        await _repository.UpdateAsync<BuroInventoryItem>(item => item.ItemId == command.InventoryItemItemId,
            updatedProperties);
    }

    private Dictionary<string, object> PrepareUpdateCommand(ArchiveInventoryItemCommand command)
    {
        var userId = _securityContextProvider.GetSecurityContext().UserId;
        return new Dictionary<string, object>
        {
            { nameof(BuroInventoryItem.IsArchived), command.IsArchived },
            { nameof(BuroInventoryItem.LastUpdateDate), DateTime.UtcNow },
            {nameof(BuroInventoryItem.LastUpdatedBy), userId}
        };
    }
}