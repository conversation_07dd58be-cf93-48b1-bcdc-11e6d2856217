using Domain.Contracts.Inventory;
using FinMongoDbRepositories;
using Infrastructure.Enums;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace Domain.Services.Inventory;

public class InventoryStockManagementService : IInventoryStockManagementService
{
    private readonly ILogger<InventoryStockManagementService> _logger;
    private readonly IFinMongoDbRepository _repository;

    public InventoryStockManagementService(ILogger<InventoryStockManagementService> logger,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _repository = repository;
    }

    public async Task<List<BuroInventoryStockBreakdown>> GetInventoryStockBreakdownList(
        List<string> inventoryStockBreakdownItemIds)
    {
        return await _repository.FindAsync<BuroInventoryStockBreakdown>(item =>
            inventoryStockBreakdownItemIds.Contains(item.ItemId));
    }

    public async Task ManageInventoryStock(BuroInventoryMovement inventoryMovement)
    {
        await CreateOrUpdateInventoryStock(inventoryMovement, EnumStockUpdateType.BothActualAndAvailable);

        await CreateOrUpdateInventoryStockBreakdownAsync(inventoryMovement, EnumStockUpdateType.BothActualAndAvailable);
    }

    public async Task<BuroInventoryStock> GetInventoryStock(string inventoryItemId, string officeItemId)
    {
        return await _repository.FindOneAsync<BuroInventoryStock>(item => item.InventoryItemId == inventoryItemId &&
                                                                          item.OfficeItemId == officeItemId);
    }

    public async Task UpdateStocksInventoryStockBreakdown(EnumInventoryMovementType movementType,
        BuroInventoryStockBreakdown stockBreakdown,
        int quantity,
        string createdByUserId,
        EnumStockUpdateType updateType)
    {
        var updatedProperties = GetStockUpdatedPropertiesForStockBreakdown(
            movementType, stockBreakdown, quantity, createdByUserId, updateType);

        await _repository.UpdateOneAsync<BuroInventoryStockBreakdown>(
            item => item.ItemId == stockBreakdown.ItemId, updatedProperties);
    }

    public async Task UpdateInventoryStock(EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice,
        BuroInventoryStock currentInventoryStock,
        string createdByUserId,
        EnumStockUpdateType updateType)
    {
        var updatedProperties = GetPropertiesToUpdateInventoryStock(movementType,
            quantity, unitPrice, currentInventoryStock, createdByUserId, updateType);
        await _repository.UpdateOneAsync<BuroInventoryStock>(
            item => item.ItemId == currentInventoryStock.ItemId, updatedProperties);
    }

    public async Task CreateOrUpdateInventoryStock(BuroInventoryMovement inventoryMovement, EnumStockUpdateType updateType)
    {
        var currentStock = await GetInventoryStock(inventoryMovement.InventoryItemId, inventoryMovement.SourceOfficeItemId);
        if (currentStock == null)
            await AddNewInventoryStock(inventoryMovement);
        else
            await UpdateInventoryStock(inventoryMovement.MovementType, inventoryMovement.Quantity,
                inventoryMovement.UnitPrice, currentStock, inventoryMovement.CreatedBy, updateType);
    }

    public async Task CreateOrUpdateInventoryStockBreakdownAsync(BuroInventoryMovement inventoryMovement, EnumStockUpdateType updateType)
    {
        var stockBreakdown = await _repository.FindOneAsync<BuroInventoryStockBreakdown>(item =>
            item.InventoryItemId == inventoryMovement.InventoryItemId &&
            item.OfficeItemId == inventoryMovement.SourceOfficeItemId &&
            item.UnitPrice == inventoryMovement.UnitPrice);

        if (stockBreakdown == null)
            await AddNewInventoryStockBreakdown(inventoryMovement);
        else
            await UpdateStocksInventoryStockBreakdown(inventoryMovement.MovementType, stockBreakdown,
                inventoryMovement.Quantity, inventoryMovement.CreatedBy, updateType);
    }

    public async Task CreateOrUpdateInventoryStockBreakdownAsync(BuroInventoryMovementPlan inventoryMovementPlan,
        BuroInventoryMovement inventoryMovement,
        EnumStockUpdateType updateType)
    {
        var unitPriceList = inventoryMovementPlan.InventoryItemDetails.Select(x => x.Price).ToList();

        var stockBreakdownList = await _repository.FindAsync<BuroInventoryStockBreakdown>(item =>
            item.InventoryItemId == inventoryMovement.InventoryItemId &&
            item.OfficeItemId == inventoryMovement.SourceOfficeItemId &&
            unitPriceList.Contains(item.UnitPrice));

        if (stockBreakdownList == null || stockBreakdownList.Count == 0)
        {
            await AddNewInventoryStockBreakdownList(inventoryMovementPlan, inventoryMovement);
            return;
        }

        var tasks = inventoryMovementPlan.InventoryItemDetails.Select(async inventoryDetail =>
        {
            var existingStockBreakdown = stockBreakdownList
                .FirstOrDefault(x => x.UnitPrice == inventoryDetail.Price);

            if (existingStockBreakdown == null)
            {
                await AddNewInventoryStockBreakdown(inventoryMovement, inventoryDetail.Price,
                    inventoryDetail.Quantity);
            }
            else
            {
                await UpdateStocksInventoryStockBreakdown(
                    inventoryMovement.MovementType,
                    existingStockBreakdown,
                    inventoryDetail.Quantity,
                    inventoryMovementPlan.CreatedBy,
                    updateType);
            }
        }).ToList();

        await Task.WhenAll(tasks);
    }

    private async Task AddNewInventoryStockBreakdown(BuroInventoryMovement inventoryMovement)
    {
        var inventoryStockBreakdown = PrepareInventoryStockBreakdown(inventoryMovement);
        await _repository.InsertOneAsync(inventoryStockBreakdown);
    }

    private async Task AddNewInventoryStockBreakdownList(BuroInventoryMovementPlan inventoryMovementPlan, BuroInventoryMovement inventoryMovement)
    {
        var inventoryStockBreakdownList = PrepareInventoryStockBreakdownList(inventoryMovementPlan, inventoryMovement);
        await _repository.InsertManyAsync(inventoryStockBreakdownList);
    }

    private async Task AddNewInventoryStockBreakdown(BuroInventoryMovement inventoryMovement, double price, int quantity)
    {
        var inventoryStockBreakdown = PrepareInventoryStockBreakdown(inventoryMovement, price, quantity);
        await _repository.InsertOneAsync(inventoryStockBreakdown);
    }

    private List<BuroInventoryStockBreakdown> PrepareInventoryStockBreakdownList(BuroInventoryMovementPlan inventoryMovementPlan, BuroInventoryMovement inventoryMovement)
    {
        return inventoryMovementPlan.InventoryItemDetails
            .Select(itemDetail => new BuroInventoryStockBreakdown
            {
                ItemId = Guid.NewGuid().ToString(),
                OfficeItemId = inventoryMovement.SourceOfficeItemId,
                OfficeName = inventoryMovement.SourceOfficeName,
                InventoryItemId = inventoryMovement.InventoryItemId,
                InventoryName = inventoryMovement.InventoryName,
                CategoryItemId = inventoryMovement.CategoryItemId,
                Category = inventoryMovement.Category,
                InventoryItemDefinition = inventoryMovement.InventoryItemDefinition,
                SubCategoryItemId = inventoryMovement.SubCategoryItemId,
                SubCategory = inventoryMovement.SubCategory,
                VendorItemId = inventoryMovement.VendorItemId,
                VendorName = inventoryMovement.VendorName,
                UnitPrice = itemDetail.Price,
                AvailableStock = itemDetail.Quantity
            }).ToList();
    }

    private BuroInventoryStockBreakdown PrepareInventoryStockBreakdown(BuroInventoryMovement inventoryMovement)
    {
        return new BuroInventoryStockBreakdown
        {
            ItemId = Guid.NewGuid().ToString(),
            OfficeItemId = inventoryMovement.SourceOfficeItemId,
            OfficeName = inventoryMovement.SourceOfficeName,
            InventoryItemId = inventoryMovement.InventoryItemId,
            InventoryName = inventoryMovement.InventoryName,
            InventoryItemDefinition = inventoryMovement.InventoryItemDefinition,
            CategoryItemId = inventoryMovement.CategoryItemId,
            Category = inventoryMovement.Category,
            SubCategoryItemId = inventoryMovement.SubCategoryItemId,
            SubCategory = inventoryMovement.SubCategory,
            VendorItemId = inventoryMovement.VendorItemId,
            VendorName = inventoryMovement.VendorName,
            UnitPrice = inventoryMovement.UnitPrice,
            AvailableStock = inventoryMovement.Quantity,
            ActualStock = inventoryMovement.Quantity,
        };
    }

    private BuroInventoryStockBreakdown PrepareInventoryStockBreakdown(BuroInventoryMovement inventoryMovement, double price, int quantity)
    {
        return new BuroInventoryStockBreakdown
        {
            ItemId = Guid.NewGuid().ToString(),
            OfficeItemId = inventoryMovement.SourceOfficeItemId,
            OfficeName = inventoryMovement.SourceOfficeName,
            InventoryItemId = inventoryMovement.InventoryItemId,
            InventoryName = inventoryMovement.InventoryName,
            CategoryItemId = inventoryMovement.CategoryItemId,
            Category = inventoryMovement.Category,
            SubCategoryItemId = inventoryMovement.SubCategoryItemId,
            SubCategory = inventoryMovement.SubCategory,
            VendorItemId = inventoryMovement.VendorItemId,
            VendorName = inventoryMovement.VendorName,
            UnitPrice = price,
            AvailableStock = quantity
        };
    }

    private Dictionary<string, object> GetStockUpdatedPropertiesForStockBreakdown(
        EnumInventoryMovementType movementType,
        BuroInventoryStockBreakdown stockBreakdown,
        int quantity,
        string createdByUserId,
        EnumStockUpdateType updateType)
    {
        var (updatedActualStock, updatedAvailableStock) = CalculateStockQuantities(stockBreakdown.ActualStock,
            stockBreakdown.AvailableStock, quantity, movementType);

        var updatedProperties = new Dictionary<string, object>();
        switch (updateType)
        {
            case EnumStockUpdateType.BothActualAndAvailable:
                updatedProperties.Add(nameof(BuroInventoryStockBreakdown.ActualStock), updatedActualStock);
                updatedProperties.Add(nameof(BuroInventoryStockBreakdown.AvailableStock), updatedAvailableStock);
                break;
            case EnumStockUpdateType.ActualOnly:
                updatedProperties.Add(nameof(BuroInventoryStockBreakdown.ActualStock), updatedActualStock);
                break;
            case EnumStockUpdateType.AvailableOnly:
                updatedProperties.Add(nameof(BuroInventoryStockBreakdown.AvailableStock), updatedAvailableStock);
                break;
        }

        updatedProperties.Add(nameof(BuroInventoryStockBreakdown.LastUpdateDate), DateTime.UtcNow);
        updatedProperties.Add(nameof(BuroInventoryStockBreakdown.LastUpdatedBy), createdByUserId);
        return updatedProperties;
    }

    private async Task AddNewInventoryStock(BuroInventoryMovement inventoryMovement)
    {
        var newInventoryStock = PrepareInventoryStock(inventoryMovement);
        await _repository.InsertOneAsync(newInventoryStock);
    }

    private BuroInventoryStock PrepareInventoryStock(BuroInventoryMovement inventoryMovement)
    {
        var inventoryStock = new BuroInventoryStock
        {
            ItemId = Guid.NewGuid().ToString(),
            OfficeItemId = inventoryMovement.SourceOfficeItemId,
            OfficeName = inventoryMovement.SourceOfficeName,
            InventoryItemId = inventoryMovement.InventoryItemId,
            InventoryName = inventoryMovement.InventoryName,
            InventoryItemDefinition = inventoryMovement.InventoryItemDefinition,
            CategoryItemId = inventoryMovement.CategoryItemId,
            Category = inventoryMovement.Category,
            SubCategoryItemId = inventoryMovement.SubCategoryItemId,
            SubCategory = inventoryMovement.SubCategory,
            ActualStock = inventoryMovement.Quantity,
            AvailableStock = inventoryMovement.Quantity,
            ActualStockPrice = inventoryMovement.TotalPrice,
            AvailableStockPrice = inventoryMovement.TotalPrice,
            CreatedBy = inventoryMovement.CreatedBy,
            CreateDate = DateTime.UtcNow
        };
        return inventoryStock;
    }

    private Dictionary<string, object> GetPropertiesToUpdateInventoryStock(EnumInventoryMovementType movementType,
        int quantity,
        double unitPrice,
        BuroInventoryStock currentInventoryStock,
        string createdByUserId,
        EnumStockUpdateType updateType)
    {
        var (actualStock, availableStock) = CalculateStockQuantities(
            currentInventoryStock.ActualStock,
            currentInventoryStock.AvailableStock,
            quantity,
            movementType);

        var (actualStockPrice, availableStockPrice) = CalculateStockPrices(
            currentInventoryStock.ActualStockPrice,
            currentInventoryStock.AvailableStockPrice,
            unitPrice,
            quantity,
            movementType);

        var updatedProperties = new Dictionary<string, object>();

        switch (updateType)
        {
            case EnumStockUpdateType.BothActualAndAvailable:
                updatedProperties.Add(nameof(BuroInventoryStock.ActualStock), actualStock);
                updatedProperties.Add(nameof(BuroInventoryStock.ActualStockPrice), actualStockPrice);
                updatedProperties.Add(nameof(BuroInventoryStock.AvailableStock), availableStock);
                updatedProperties.Add(nameof(BuroInventoryStock.AvailableStockPrice), availableStockPrice);
                break;
            case EnumStockUpdateType.ActualOnly:
                updatedProperties.Add(nameof(BuroInventoryStock.ActualStock), actualStock);
                updatedProperties.Add(nameof(BuroInventoryStock.ActualStockPrice), actualStockPrice);
                break;
            case EnumStockUpdateType.AvailableOnly:
                updatedProperties.Add(nameof(BuroInventoryStock.AvailableStock), availableStock);
                updatedProperties.Add(nameof(BuroInventoryStock.AvailableStockPrice), availableStockPrice);
                break;
        }

        updatedProperties.Add(nameof(BuroInventoryStock.LastUpdateDate), DateTime.UtcNow);
        updatedProperties.Add(nameof(BuroInventoryStock.LastUpdatedBy), createdByUserId);
        return updatedProperties;
    }

    private (int ActualStock, int AvailableStock) CalculateStockQuantities(
        int currentActual,
        int currentAvailable,
        int quantity,
        EnumInventoryMovementType movementType)
    {
        var actualStock = CalculateStockQuantity(currentActual, quantity, movementType);
        var availableStock = CalculateStockQuantity(currentAvailable, quantity, movementType);

        return (actualStock, availableStock);
    }

    private int CalculateStockQuantity(
        int currentQuantity,
        int changeQuantity,
        EnumInventoryMovementType movementType)
    {
        return movementType switch
        {
            EnumInventoryMovementType.In => currentQuantity + changeQuantity,
            EnumInventoryMovementType.Out => currentQuantity - changeQuantity,
            _ => throw new ArgumentException("Invalid movement type", nameof(movementType))
        };
    }

    private (double ActualStockPrice, double AvailableStockPrice) CalculateStockPrices(
        double currentActualStockPrice,
        double currentAvailableStockPrice,
        double unitPrice,
        int quantity,
        EnumInventoryMovementType movementType)
    {
        var actualStockPrice = CalculateStockPrice(currentActualStockPrice, unitPrice, quantity, movementType);
        var availableStockPrice = CalculateStockPrice(currentAvailableStockPrice, unitPrice, quantity, movementType);

        return (actualStockPrice, availableStockPrice);
    }

    private double CalculateStockPrice(
        double currentStockPrice,
        double unitPrice,
        int changeQuantity,
        EnumInventoryMovementType movementType)
    {
        return movementType switch
        {
            EnumInventoryMovementType.In => currentStockPrice + changeQuantity * unitPrice,
            EnumInventoryMovementType.Out => currentStockPrice - changeQuantity * unitPrice,
            _ => throw new ArgumentException("Invalid movement type", nameof(movementType))
        };
    }
}