using Domain.Commands.Inventory;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Inventory;

public class RejectInventoryRequestService
{
    private readonly IFinMongoDbRepository _repository;
    private readonly ISharedService _sharedService;
    private readonly ISecurityContextProvider _securityContextProvider;

    public RejectInventoryRequestService(
        IFinMongoDbRepository repository,
        ISharedService sharedService,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _sharedService = sharedService;
        _securityContextProvider = securityContextProvider;
    }

    public async Task<CommandResponse> RejectInventoryRequestAsync(RejectInventoryRequestCommand command)
    {
        var inventoryRequest = await FetchInventoryRequestAsync(command.InventoryRequestItemId);
        if (!IsValidRequest(inventoryRequest)) return CreateInvalidRequestResponse();

        await UpdateInventoryRequestAsync(command.InventoryRequestItemId);
        await SendNotificationAsync(inventoryRequest!);
        return new CommandResponse();
    }

    private async Task SendNotificationAsync(BuroInventoryRequest inventoryRequest)
    {
        var context = _securityContextProvider.GetSecurityContext();
        var payload = new
        {
            RejectedAt = DateTime.UtcNow,
            RejectedBy = context.UserName,
            InventoryRequestItemId = inventoryRequest.ItemId,
        };
        await _sharedService.NotifyUserAsync(BuroNotificationKeys.InventoryRequestRejected, payload, inventoryRequest.ItemId, inventoryRequest.CreatedBy);
    }

    private async Task<BuroInventoryRequest?> FetchInventoryRequestAsync(string inventoryRequestItemId)
    {
        return await _repository.FindOneAsync<BuroInventoryRequest>(item => item.ItemId == inventoryRequestItemId);
    }

    private bool IsValidRequest(BuroInventoryRequest? inventoryRequest)
    {
        return inventoryRequest is { DisburseStatus: EnumInventoryRequestDisburseStatus.Pending };
    }

    private CommandResponse CreateInvalidRequestResponse()
    {
        var response = new CommandResponse();
        response.SetError(BuroErrorMessageKeys.InvalidRequest, "Invalid request");
        return response;
    }

    private async Task UpdateInventoryRequestAsync(string inventoryRequestItemId)
    {
        var updatedProperties = BuildUpdatedProperties();
        await _repository.UpdateOneAsync<BuroInventoryRequest>(
            item => item.ItemId == inventoryRequestItemId,
            updatedProperties);
    }

    private Dictionary<string, object> BuildUpdatedProperties()
    {
        var context = _securityContextProvider.GetSecurityContext();
        return new Dictionary<string, object>
        {
            { nameof(BuroInventoryRequest.DisburseStatus), EnumInventoryRequestDisburseStatus.Rejected },
            { nameof(BuroInventoryRequest.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroInventoryRequest.LastUpdatedBy), context.UserId }
        };
    }
}