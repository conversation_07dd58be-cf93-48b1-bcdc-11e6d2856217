using Domain.Commands.Bank;
using Domain.Contracts.Bank;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Bank;

public class CreateBankBranchCommandService : ICreateBankBranchCommandService
{
    private readonly ILogger<CreateBankBranchCommandService> _logger;
    private readonly IFinMongoDbRepository _repository;

    public CreateBankBranchCommandService(
        ILogger<CreateBankBranchCommandService> logger,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _repository = repository;
    }


    public async Task<CommandResponse> CreateBranchAsync(CreateBankBranchCommand command)
    {
        _logger.LogInformation("Creating bank branch {BankItemId}", command.BankItemId);
        var commandResponse = new CommandResponse();

        try
        {
            var bankNames = await _repository.FindWithProjectionAsync<BuroBank, string>(b => b.ItemId == command.BankItemId, b => b.BankName);
            if (!bankNames.Any())
            {
                commandResponse.SetError(BuroErrorMessageKeys.BankNotFound, "No Bank Exists with {BankItemId}");
                return commandResponse;
            }

            await SubmitBankBranchInfoAsync(command, bankNames[0]);


        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error creating bank branch {BankItemId}", command.BankItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
        }
        return commandResponse;
    }

    private async Task SubmitBankBranchInfoAsync(CreateBankBranchCommand command, string bankName)
    {
        _logger.LogInformation("Starting CreateBranchAsync");
        var bankBranch = new BuroBankBranch
        {
            ItemId = Guid.NewGuid().ToString(),
            BankName = bankName,
            BankItemId = command.BankItemId,
            RoutingNumber = command.RoutingNumber,
            BranchName = command.BranchName,
            Address = command.Address,
            PhoneNumbers = command.PhoneNumbers,
            Tags = new[] { Tags.IsABankBranch }
        };

        _logger.LogInformation("Ending CreateBranchAsync");
        await _repository.InsertOneAsync(bankBranch);

    }
}