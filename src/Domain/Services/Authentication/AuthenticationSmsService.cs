using Domain.Contracts.Authentication;
using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;

namespace Domain.Services.Authentication;

public class AuthenticationSmsService : IAuthenticationSmsService
{
    private readonly ISmsServiceClient _smsServiceClient;
    private readonly ILogger<AuthenticationSmsService> _logger;

    public AuthenticationSmsService(ISmsServiceClient smsServiceClient, ILogger<AuthenticationSmsService> logger)
    {
        _smsServiceClient = smsServiceClient;
        _logger = logger;
    }

    public async Task<bool> SendOtpViaSmsForForgetPassword(string phoneNumber, string otp)
    {
        _logger.LogInformation("Inside SendOtpViaSmsForForgetPassword for {PhoneNumber}", phoneNumber);
        var smsCommand = new BuroSmsCommand
        {
            PhoneNumber = phoneNumber,
            Message = $"Your OTP for password reset is {otp}",
            Purpose = "ForgetPasswordOtp"
        };

        var response = await _smsServiceClient.SendShortMessage(smsCommand);
        if (!response)
        {
            _logger.LogError("Error SendOtpViaSmsForForgetPassword for {PhoneNumber}", phoneNumber);
            return false;
        }
        _logger.LogInformation("Finished SendOtpViaSmsForForgetPassword for {PhoneNumber}", phoneNumber);
        return true;
    }
}