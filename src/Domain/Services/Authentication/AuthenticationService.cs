using Domain.Commands.Authentication;
using Domain.Contracts.Authentication;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Extensions;
using Infrastructure.Models;
using Infrastructure.Models.ForgotPassword;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Selise.Ecap.Entities.PrimaryEntities.Security;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Authentication;

public class AuthenticationService : IAuthenticationService
{
    private readonly IAuthenticationEmailService _authenticationEmailService;
    private readonly IAuthenticationSmsService _authenticationSmsService;
    private readonly AccessTokenProvider _accessTokenProvider;
    private readonly IBuroKeyStore _buroKeyStore;
    private readonly ILogger<AuthenticationService> _logger;
    private readonly Random _random = new();
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IUamAdapter _uamAdapter;

    public AuthenticationService(IRepository repository, ILogger<AuthenticationService> logger,
        ISecurityContextProvider securityContextProvider, IBuroKeyStore buroKeyStore,
        IAuthenticationEmailService authenticationEmailService, IUamAdapter uamAdapter,
        IAuthenticationSmsService authenticationSmsService,
        AccessTokenProvider accessTokenProvider)
    {
        _repository = repository;
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _buroKeyStore = buroKeyStore;
        _authenticationEmailService = authenticationEmailService;
        _uamAdapter = uamAdapter;
        _authenticationSmsService = authenticationSmsService;
        _accessTokenProvider = accessTokenProvider;
    }

    public async Task<CommandResponse> VerifyEmployeePinForForgetPassword(string employeePin)
    {
        _logger.LogInformation("Inside VerifyEmployeePinForForgetPassword");
        var response = new CommandResponse();
        try
        {
            var sessionId = GetSessionIdForForgetPassword();
            var user = await _repository.GetItemAsync<User>(x => x.UserName == employeePin);
            var isValidUser = ValidateUserForForgetPassword(user);
            if (!isValidUser)
            {
                response.SetError(BuroErrorMessageKeys.UserInvalid, "User is not valid");
                return response;
            }

            var userInfo = user.AsUserInfoForForgetPassword();
            await SetUserInfo(sessionId, userInfo);

            _logger.LogInformation("Finished VerifyEmployeePinForForgetPassword");
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
        }

        return response;
    }

    public async Task<CommandResponse> SendOtpForForgetPassword(OtpChannel otpChannel)
    {
        _logger.LogInformation("Inside SendOtpForForgetPassword");
        var response = new CommandResponse();
        try
        {
            var sessionId = GetSessionIdForForgetPassword();
            var userInfo = await GetUserInfo(sessionId);

            if (userInfo == null)
            {
                response.SetError(BuroErrorMessageKeys.UserInvalid, "User is not valid");
                return response;
            }

            var otp = GenerateOtpCode();
            userInfo.Otp = otp;
            await SetUserInfo(sessionId, userInfo);

            var token = await GetAdminToken();

            if (otpChannel == OtpChannel.Email)
            {
                var mailResponse =
                    await _authenticationEmailService.SendOtpViaEmailForForgetPassword(userInfo, token);

                if (!mailResponse.Errors.IsValid)
                {
                    response.SetError(BuroErrorMessageKeys.EmailSendingFailed, "Email sending failed");
                    return response;
                }
            }
            else
            {
                var isSmsSent =
                    await _authenticationSmsService.SendOtpViaSmsForForgetPassword(userInfo.PhoneNumber, otp);
                if (!isSmsSent)
                {
                    response.SetError(BuroErrorMessageKeys.SmsSendingFailed, "Sms sending failed");
                    return response;
                }
            }

            _logger.LogInformation("Finished SendOtpForForgetPassword");
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return response;
    }

    public async Task<CommandResponse> MatchOtpForForgetPassword(MatchOtpForForgetPasswordCommand command)
    {
        _logger.LogInformation("Inside MatchOtpForForgetPassword");
        var response = new CommandResponse();

        try
        {
            var sessionId = GetSessionIdForForgetPassword();
            var userInfo = await GetUserInfo(sessionId);

            if (userInfo == null)
            {
                response.SetError(BuroErrorMessageKeys.OtpExpired, "Otp is expired");
                return response;
            }

            if (userInfo.Otp.IsNullOrEmptyOrWhiteSpace())
            {
                response.SetError(BuroErrorMessageKeys.OtpExpired, "Otp is expired");
                return response;
            }

            if (userInfo.Otp != command.OtpToMatch)
            {
                response.SetError(BuroErrorMessageKeys.OtpNotMatched, "Otp is not matched");
                return response;
            }

            _logger.LogInformation("Finished MatchOtpForForgetPassword");
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return response;
    }

    public async Task<CommandResponse> ResetPassword(ResetPasswordCommand command)
    {
        _logger.LogInformation("Inside ResetPassword");
        var response = new CommandResponse();
        try
        {
            var sessionId = GetSessionIdForForgetPassword();
            var userInfo = await GetUserInfo(sessionId);

            if (userInfo == null)
            {
                response.SetError(BuroErrorMessageKeys.UserInvalid, "User is not valid");
                return response;
            }

            var user = await _repository.GetItemAsync<User>(x => x.ItemId == userInfo.ItemId);
            var recoverAccountCode = Guid.NewGuid().ToString();
            await SetUser(recoverAccountCode, user);

            var payLoad = new ResetPasswordPayload
            {
                NewPassword = command.NewPassword,
                RecoverAccountCode = recoverAccountCode
            };

            var isPasswordReset = await _uamAdapter.ResetPassword(payLoad);
            if (!isPasswordReset)
            {
                response.SetError(BuroErrorMessageKeys.PasswordResetFailed, "Password reset failed");
                _logger.LogError("Password reset failed for User Id: {UserId}", userInfo.ItemId);
                return response;
            }

            _logger.LogInformation("Finished ResetPassword");
        }
        catch (Exception e)
        {
            response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
        }

        return response;
    }

    private string GetSessionIdForForgetPassword()
    {
        return $"{_securityContextProvider.GetSecurityContext().SessionId!}_{BuroConstants.ForgetPasswordSuffix}";
    }

    private async Task<string> GetAdminToken()
    {
        var tenantId = _securityContextProvider.GetSecurityContext().TenantId;
        return await _accessTokenProvider.CreateForTenantAdminAsync(tenantId);
    }

    private string GenerateOtpCode()
    {
        return _random.Next(100000, 999999).ToString();
    }

    private bool ValidateUserForForgetPassword(User user)
    {
        return user is { Active: true, EmailVarified: true };
    }

    private async Task SetUserInfo(string key, UserInfoForForgetPassword userInfo)
    {
        var userInfoStr = JsonConvert.SerializeObject(userInfo);
        await _buroKeyStore.AddKeyWithExpiryAsync(key, userInfoStr, 300);
    }

    private async Task SetUser(string key, User user)
    {
        var userStr = JsonConvert.SerializeObject(user);
        await _buroKeyStore.AddKeyWithExpiryAsync(key, userStr, 300);
    }

    private async Task<UserInfoForForgetPassword?> GetUserInfo(string key)
    {
        var userInfo = await _buroKeyStore.GetValueAsync<UserInfoForForgetPassword>(key);
        return userInfo;
    }
}