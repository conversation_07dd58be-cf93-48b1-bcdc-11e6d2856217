using Domain.Contracts.Authentication;
using Infrastructure.Constants;
using Infrastructure.Models.ForgotPassword;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.MailService.Driver;
using SeliseBlocks.MailService.Services.Commands;

namespace Domain.Services.Authentication;

public class AuthenticationEmailService : IAuthenticationEmailService
{
    private readonly IMailServiceClient _mailServiceClient;
    private readonly ILogger<AuthenticationEmailService> _logger;

    public AuthenticationEmailService(IMailServiceClient mailServiceClient, ILogger<AuthenticationEmailService> logger)
    {
        _mailServiceClient = mailServiceClient;
        _logger = logger;
    }

    public async Task<CommandResponse> SendOtpViaEmailForForgetPassword(UserInfoForForgetPassword userInfo, string token)
    {
        _logger.LogInformation("Inside SendOtpViaEmailForForgetPassword");
        var templateName = EmailPurposeConstants.OtpForForgetPassword;
        var dataContext = new Dictionary<string, string>
        {
            { "otp", userInfo.Otp },
            { "name", userInfo.Name },
            { "employeePin", userInfo.EmployeePin }
        };

        var emailCommand = new SendMailToEmailCommand
        {
            Bcc = new string[] { },
            Cc = new string[] { },
            DataContext = dataContext,
            Language = "en-US",
            Purpose = templateName,
            To = new[] { userInfo.Email }
        };

        var mailResponse = await _mailServiceClient.EnqueueMail(emailCommand, token);

        if (mailResponse.Errors != null && mailResponse.Errors.Errors.Count > 0)
        {
            mailResponse.SetError(BuroErrorMessageKeys.EmailSendingFailed, "Email sending failed");
            _logger.LogError("Error sending email. Reason: {0}", mailResponse.Errors);
        }

        _logger.LogInformation("Finished SendOtpViaEmailForForgetPassword");

        return mailResponse;
    }
}