using Domain.Commands.Tasks;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Tasks
{
    public class TasksCommandService
    {
        private readonly ILogger<TasksCommandService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;

        public TasksCommandService(
            ILogger<TasksCommandService> logger,
            IRepository repository,
            ISecurityContextProvider securityContextProvider)
        {
            _logger = logger;
            _repository = repository;
            _securityContextProvider = securityContextProvider;
        }

        public async Task<CommandResponse> UpdateManualAssignment(UpdateManualAssignmentCommand command)
        {
            _logger.LogInformation("Inside AddCenter");

            var commandResponse = new CommandResponse();
            try
            {
                var filteredTask = await GetTaskById(command.TaskItemId);
                if (filteredTask == null)
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.InvalidTaskId);
                }
                var employee = await GetEmployeeByItemId(command.EmployeeItemId);
                if (employee == null)
                {
                    throw new InvalidOperationException(BuroErrorMessageKeys.InvalidEmployeeId);
                }
                await UpdateTaskManuallyAsync(command, employee);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "ERROR_OCCURED");
            }
            return commandResponse;
        }

        private async Task UpdateTaskManuallyAsync(UpdateManualAssignmentCommand command, BuroEmployee employeeDetails)
        {
            _logger.LogInformation("UpdateTaskManuallyAsync -> START");
            var updatedProperties = PreparePropertiesForUpdatingTask(command.TaskItemId, employeeDetails);
            await UpdateTaskAsync(updatedProperties, command.TaskItemId);
            _logger.LogInformation("UpdateTaskManuallyAsync -> END");
        }

        private async Task UpdateTaskAsync(Dictionary<string, object> updatedProperties, string taskItemId)
        {
            _logger.LogInformation("UpdateTask -> START");
            await _repository.UpdateAsync<BuroTask>(t => t.ItemId == taskItemId, updatedProperties);
            _logger.LogInformation("UpdateTask -> END");
        }

        private Dictionary<string, object> PreparePropertiesForUpdatingTask(
            string taskItemId,
            BuroEmployee employee)
        {
            _logger.LogInformation("Preparing properties of Task for Manual Assignment ID: {ItemId}", taskItemId);

            var securityContext = _securityContextProvider.GetSecurityContext();

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroTask.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroTask.LastUpdatedBy), securityContext.UserId},
                {nameof(BuroTask.RequestedToEmployeeItemId), employee.ItemId },
                {nameof(BuroTask.RequestedToEmployeeName), employee.EmployeeName },
                {nameof(BuroTask.RequestedToEmployeePIN), employee.EmployeePin },
                {nameof(BuroTask.RequestedToEmployeeDesignation), employee.DesignationTitle },
                {nameof(BuroTask.RequestedToEmployeeOfficeItemId), employee.CurrentOfficeItemId },
                {nameof(BuroTask.RequestedToEmployeeOfficeName), employee.CurrentOfficeTitle },
                {nameof(BuroTask.RequestedToEmployeeCenterItemId), employee.CurrentCenterTitle },
                {nameof(BuroTask.RequestedToEmployeeCenterName), employee.CurrentCenterTitle },

            };

            _logger.LogInformation("Finished Preparing properties of Task for Manual Assignment ID: {ItemId}", taskItemId);

            return properties;
        }

        private Task<BuroTask> GetTaskById(string taskItemId)
        {
            return _repository.GetItemAsync<BuroTask>(t => t.ItemId == taskItemId);
        }

        private Task<BuroEmployee> GetEmployeeByItemId(string employeeId)
        {
            return _repository.GetItemAsync<BuroEmployee>(e => e.ItemId == employeeId);
        }
    }
}
