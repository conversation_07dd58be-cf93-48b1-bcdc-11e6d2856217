using Domain.Commands.Office;
using Domain.Services.Helper;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Office
{
    public class UpdateOfficeService
    {
        private readonly ILogger<UpdateOfficeService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;

        public UpdateOfficeService(
            ILogger<UpdateOfficeService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> UpdateOffice(UpdateOfficeCommand command)
        {
            _logger.LogInformation("Starting UpdateOffice for Office Item ID: {Command}", command.OfficeItemId);

            var commandResponse = new CommandResponse();

            try
            {
                await ProcessUpdateOffice(command, commandResponse);
                _logger.LogInformation("Successfully updated office with Office Item ID: {Command}", command.OfficeItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during office update for Office Item ID: {Command}", command.OfficeItemId);
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the office.");
            }

            return commandResponse;
        }

        private async Task ProcessUpdateOffice(UpdateOfficeCommand command, CommandResponse commandResponse)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var currentDateTime = DateTime.UtcNow;

            var existingData =
                await _repository.GetItemAsync<BuroOfficeLocationHistory>(c => c.OfficeItemId == command.OfficeItemId);

            var updatedProperties = PrepareOfficeProperties(command, securityContext.UserId, currentDateTime);

            _logger.LogInformation("Updating office properties for Office Item ID: {OfficeItemId}", command.OfficeItemId);

            await _repository.UpdateManyAsync<BuroOffice>(x => x.ItemId == command.OfficeItemId, updatedProperties);

            if (existingData != null)
            {
                await CreateLocationHistory(existingData, command, securityContext);
            }
            else
            {
                _logger.LogWarning("No existing location history found for Office Item ID: {OfficeItemId}, skipping location history update.", command.OfficeItemId);
            }

            _logger.LogInformation("Updated office successfully for Office Item ID: {OfficeItemId}", command.OfficeItemId);
        }


        private Dictionary<string, object> PrepareOfficeProperties(UpdateOfficeCommand command, string userId, DateTime currentDateTime)
        {
            _logger.LogInformation($"Preparing update properties for Office Item ID: {command.OfficeItemId}");

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroOffice.LastUpdateDate), currentDateTime},
                {nameof(BuroOffice.LastUpdatedBy), userId}
            };

            if (!string.IsNullOrEmpty(command.Address))
                properties.Add(nameof(BuroOffice.Address), command.Address);

            if (!string.IsNullOrEmpty(command.DistrictId) && !string.IsNullOrEmpty(command.DistrictName))
            {
                properties.Add(nameof(BuroOffice.DistrictId), command.DistrictId);
                properties.Add(nameof(BuroOffice.DistrictName), command.DistrictName);
            }

            if (!string.IsNullOrEmpty(command.UpazilaId) && !string.IsNullOrEmpty(command.UpazilaName))
            {
                properties.Add(nameof(BuroOffice.UpazilaId), command.UpazilaId);
                properties.Add(nameof(BuroOffice.UpazilaName), command.UpazilaName);
            }

            if (!string.IsNullOrEmpty(command.UnionId) && !string.IsNullOrEmpty(command.UnionName))
            {
                properties.Add(nameof(BuroOffice.UnionId), command.UnionId);
                properties.Add(nameof(BuroOffice.UnionName), command.UnionName);
            }

            if (!string.IsNullOrEmpty(command.PostOfficeId) && !string.IsNullOrEmpty(command.PostOfficeName) && !string.IsNullOrEmpty(command.PostOfficeCode))
            {
                properties.Add(nameof(BuroOffice.PostOfficeId), command.PostOfficeId);
                properties.Add(nameof(BuroOffice.PostOfficeName), command.PostOfficeName);
                properties.Add(nameof(BuroOffice.PostCode), command.PostOfficeCode);
            }

            if (!string.IsNullOrEmpty(command.WardId) && !string.IsNullOrEmpty(command.WardName))
            {
                properties.Add(nameof(BuroOffice.WardId), command.WardId);
                properties.Add(nameof(BuroOffice.WardName), command.WardName);
            }

            if (command.Latitude.HasValue)
            {
                properties.Add($"{nameof(BuroOffice.GPSLocation)}.{nameof(BuroOffice.GPSLocation.Latitude)}", command.Latitude.Value);
            }

            if (command.Longitude.HasValue)
            {
                properties.Add($"{nameof(BuroOffice.GPSLocation)}.{nameof(BuroOffice.GPSLocation.Longitude)}", command.Longitude.Value);
            }

            if (command.CustomProperties.Any())
            {
                var customPropertiesArray = CustomPropertyHelperService.ConvertToCustomPropertiesDictionary(command.CustomProperties);
                properties.Add(nameof(BuroOffice.CustomProperties), customPropertiesArray);
            }


            return properties;
        }


        private static BuroOfficeLocationHistory PrepareEntityDataForLocHistory(UpdateOfficeCommand command, BuroOfficeLocationHistory existingData,
            SecurityContext securityContext)
        {
            var userId = securityContext.UserId;

            var officeLocationEntity = new BuroOfficeLocationHistory
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsAOffice },
                CreatedBy = userId,
                LastUpdatedBy = userId,
                OfficeName = existingData.OfficeName,
                OfficeItemId = existingData.OfficeItemId,
                OfficeCode = existingData.ParentOfficeCode,
                OpeningDate = existingData.OpeningDate,
                ClosingDate = DateTime.UtcNow,
                Address = command.Address,
                GPSLocation = CreateLocationCoordinates(command.Latitude, command.Longitude),
                ParentOfficeItemId = existingData.ParentOfficeItemId,
                ParentOfficeName = existingData.ParentOfficeName,
                ParentOfficeCode = existingData.ParentOfficeCode,
                DistrictName = command.DistrictName,
                UpazilaName = command.UpazilaName,
                UnionName = command.UnionName,
                PostOfficeName = command.PostOfficeName,
                WardName = command.WardName,
                LocationType = existingData.LocationType,

            };

            officeLocationEntity.AddEntityBasicInfo();

            return officeLocationEntity;

        }

        private static LocationCoordinates CreateLocationCoordinates(double? latitude, double? longitude)
        {
            return new LocationCoordinates
            {
                Latitude = latitude,
                Longitude = longitude
            };
        }

        private async Task CreateLocationHistory(BuroOfficeLocationHistory existingData, UpdateOfficeCommand command, SecurityContext securityContext)
        {

            var properties = new Dictionary<string, Tuple<string, string>>();

            AddIfChanged(properties, nameof(command.Address), command.Address, existingData.Address);
            AddIfChanged(properties, nameof(command.Latitude), command.Latitude.ToString(), existingData.GPSLocation.Latitude.ToString());
            AddIfChanged(properties, nameof(command.DistrictName), command.DistrictName, existingData.DistrictName);
            AddIfChanged(properties, nameof(command.UpazilaName), command.UpazilaName, existingData.UpazilaName);
            AddIfChanged(properties, nameof(command.UnionName), command.UnionName, existingData.UnionName);
            AddIfChanged(properties, nameof(command.WardName), command.WardName, existingData.WardName);
            AddIfChanged(properties, nameof(command.PostOfficeName), command.PostOfficeName, existingData.PostOfficeName);

            if (properties.Count > 0)
            {
                var officeLocHistory = PrepareEntityDataForLocHistory(command, existingData, securityContext);
                await _repository.SaveAsync(officeLocHistory);

            }


        }
        private static void AddIfChanged<T>(
            Dictionary<string, Tuple<string, string>> properties,
            string propertyName,
            T newValue,
            T oldValue) where T : IComparable?
        {

            if (!Equals(newValue, oldValue))
            {
                properties[propertyName] = new Tuple<string, string>(newValue.ToString(), oldValue.ToString());
            }

        }


    }
}
