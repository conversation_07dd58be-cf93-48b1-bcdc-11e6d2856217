using Domain.Commands.Office;
using Domain.Services.Helper;
using HierarchyManager;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Events.Office;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services;
public class CreateOfficeService
{
    private readonly ILogger<CreateOfficeService> _logger;
    private readonly IRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly ISequenceNumberClient _sequenceNumberClient;
    private readonly IServiceClient _serviceClient;
    private readonly IHierarchyService _hierarchyService;

    public CreateOfficeService(
        ILogger<CreateOfficeService> logger,
        ISecurityContextProvider securityContextProvider,
        IRepository repository,
        ISequenceNumberClient sequenceNumberClient,
        IServiceClient serviceClient,
        IHierarchyService hierarchyService)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
        _sequenceNumberClient = sequenceNumberClient;
        _serviceClient = serviceClient;
        _hierarchyService = hierarchyService;
    }

    public async Task<CommandResponse> CreateOffice(CreateOfficeCommand command)
    {
        _logger.LogInformation("CreateOffice process initiated. Office ItemI: {ItemId}", command.ItemId);

        var response = new CommandResponse();
        try
        {
            await ProcessOfficeCreationAsync(command);
            _logger.LogInformation("CreateOffice process completed successfully with ItemId: {ItemId}", command.ItemId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error occurred during CreateOffice process with ItemId: {ItemId}", command.ItemId);
        }

        return response;
    }

    private async Task ProcessOfficeCreationAsync(CreateOfficeCommand command)
    {
        var securityContext = _securityContextProvider.GetSecurityContext();

        _logger.LogDebug("Generating sequence number...");
        var officeId = await GetSequenceNumberAsync();
        _logger.LogInformation("Generated Office ID: {OfficeId}", officeId);

        var parentOffice = await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == command.ParentOfficeItemId);
        if (parentOffice != null)
        {
            var officeEntity = PrepareOfficeEntity(command, parentOffice, securityContext, officeId);

            var officeLocationHistory = PrepareOfficeLocationHistory(command, securityContext, officeId, parentOffice);

            await _hierarchyService.AddHierarchyInfo(officeEntity, officeEntity.OfficeType.ToString(), parentOffice.Path);

            _logger.LogInformation("Inserting office entity and location history...");
            await _repository.SaveAsync(officeEntity);

            await CreateBalanceEntryForABranchOfficeAsync(command, officeEntity);

            await SendToQueue(officeEntity.ItemId);
            await _repository.SaveAsync(officeLocationHistory);
            _logger.LogInformation("Successfully inserted office entity and location history...");

            if (IsBranchOfficeWithSurvey(command))
            {
                _logger.LogInformation("Branch office with survey detected. Updating survey status...");
                await UpdateSurveyStatusAsync(command.SurveyItemId, securityContext.UserId);
                _logger.LogInformation("Survey status updated for SurveyItemId: {SurveyItemId}", command.SurveyItemId);
            }
        }
        else
        {
            _logger.LogInformation("Parent Office not found with ItemId {ParentOfficeItemId}", command.ParentOfficeItemId);
            throw new InvalidOperationException("Parent office not found.");
        }
    }

    private async Task CreateBalanceEntryForABranchOfficeAsync(CreateOfficeCommand command, BuroOffice officeEntity)
    {
        if (command.OfficeType == EnumOfficeType.BranchOffice)
        {
            var newOfficeBalance = new BuroBranchBalance
            {
                BranchOfficeItemId = officeEntity.ItemId,
                BranchOfficeCode = officeEntity.OfficeCode,
                BranchOfficeName = officeEntity.BranchOfficeName,
                CashInHandAmount = 0,
                CashInBankAmount = 0,
                TotalBalance = 0,
            };
            newOfficeBalance.AddEntityBasicInfo();
            await _repository.SaveAsync(newOfficeBalance);
        }
    }

    private async Task<string> GetSequenceNumberAsync()
    {
        var sequence = await _sequenceNumberClient.GetSequenceNumber(new SequenceNumberQuery { Context = BuroConstants.BuroOfficeIdentifier });
        if (sequence == null || sequence.CurrentNumber <= 0)
        {
            throw new InvalidOperationException("Invalid sequence number received from the service.");
        }
        return sequence.CurrentNumber.ToString("D6");
    }

    private static bool IsBranchOfficeWithSurvey(CreateOfficeCommand command)
        => !string.IsNullOrEmpty(command.SurveyItemId) && command.OfficeType == EnumOfficeType.BranchOffice;

    private async Task UpdateSurveyStatusAsync(string? surveyItemId, string userId)
    {
        var properties = new Dictionary<string, object>
        {
            { nameof(BuroOfficeSurvey.LastUpdateDate), DateTime.UtcNow },
            { nameof(BuroOfficeSurvey.LastUpdatedBy), userId },
            { nameof(BuroOfficeSurvey.Status), EnumOfficeSurveyStatus.Converted }
        };

        await _repository.UpdateAsync<BuroOfficeSurvey>(x => x.ItemId == surveyItemId, properties);
    }

    private static BuroOffice PrepareOfficeEntity(CreateOfficeCommand command, BuroOffice? parentOffice, SecurityContext securityContext, string officeId)
    {
        var userId = securityContext.UserId;
        var officeEntity = new BuroOffice
        {
            ItemId = command.ItemId,
            Tags = new[] { Tags.IsAOffice },
            CreatedBy = userId,
            LastUpdatedBy = userId,
            OfficeName = command.OfficeName,
            OfficeNameLangKey = GenerateOfficeNameLangKey(command.OfficeName),
            OfficeId = officeId,
            OfficeCode = $"OC{officeId}",
            OfficeType = command.OfficeType,
            LocationType = command.LocationType,
            OpeningDate = command.OpeningDate,
            SurveyItemId = command.SurveyItemId,
            AuthorizationLetterFileIds = command.AuthorizationLetterFileIds,
            ParentOfficeItemId = command.ParentOfficeItemId,
            ParentOfficeName = command.ParentOfficeName,
            DivisionId = command.DivisionId,
            DivisionName = command.DivisionName,
            DistrictId = command.DistrictId,
            DistrictName = command.DistrictName,
            UpazilaId = command.UpazilaId,
            UpazilaName = command.UpazilaName,
            UnionId = command.UnionId,
            UnionName = command.UnionName,
            PostOfficeId = command.PostOfficeId,
            PostCode = command.PostOfficeCode,
            PostOfficeName = command.PostOfficeName,
            WardId = command.WardId,
            WardName = command.WardName,
            GPSLocation = CreateLocationCoordinates(command.Latitude, command.Longitude),
            Address = command.Address,
            CustomProperties = CustomPropertyHelperService.MapToEntityCustomProperties(command.CustomProperties),
            IdsAllowedToRead = new[] { userId },
            IdsAllowedToUpdate = new[] { userId },
            IdsAllowedToDelete = new[] { userId },
            IdsAllowedToWrite = new[] { userId }
        };

        officeEntity.AddEntityBasicInfo();
        return officeEntity;
    }



    private static BuroOfficeLocationHistory PrepareOfficeLocationHistory(CreateOfficeCommand command, SecurityContext securityContext, string officeId, BuroOffice parentOffice)
    {
        var officeLocation = new BuroOfficeLocationHistory
        {
            Tags = new[] { Tags.IsAOffice },
            CreatedBy = securityContext.UserId,
            LastUpdatedBy = securityContext.UserId,
            OfficeName = command.OfficeName,
            OfficeItemId = command.ItemId,
            OfficeCode = $"OC{officeId}",
            OpeningDate = command.OpeningDate,
            GPSLocation = CreateLocationCoordinates(command.Latitude, command.Longitude),
            Address = command.Address,
            ParentOfficeItemId = parentOffice.ItemId,
            ParentOfficeName = parentOffice.OfficeName,
            ParentOfficeCode = parentOffice.OfficeCode,
            DistrictName = command.DistrictName,
            UpazilaName = command.UpazilaName,
            UnionName = command.UnionName,
            PostOfficeName = command.PostOfficeName,
            WardName = command.WardName,
            LocationType = command.LocationType,
            ClosingDate = null
        };
        officeLocation.AddEntityBasicInfo();
        return officeLocation;
    }

    private static string GenerateOfficeNameLangKey(string? officeName)
        => officeName?.ToUpper().Replace(" ", "_") ?? string.Empty;

    private static LocationCoordinates CreateLocationCoordinates(double latitude, double longitude)
        => new() { Latitude = latitude, Longitude = longitude };

    private Task SendToQueue(string officeItemId)
    {
        var syncNumberOfOfficeEvent = new SyncNumberOfOfficeEvent
        {
            OfficeId = officeItemId
        };
        _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, syncNumberOfOfficeEvent);

        return Task.CompletedTask;
    }
}
