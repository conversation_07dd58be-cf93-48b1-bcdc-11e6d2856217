using Domain.Commands.Member;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Member;

public class DeleteMembershipInventoryItemService : IDeleteMembershipInventoryItemService
{
    private readonly ILogger<DeleteMembershipInventoryItemService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IFinMongoDbRepository _repository;

    public DeleteMembershipInventoryItemService(
        ILogger<DeleteMembershipInventoryItemService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> DeleteMembershipInventoryItemAsync(DeleteMembershipInventoryItemCommand command)
    {
        _logger.LogInformation("Deleting membership Inventory Item {InventoryItemId}", command.InventoryItemId);

        var commandResponse = new CommandResponse();

        try
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var isExistingSetup = await _repository.ExistsAsync<BuroMemberOnboardingFeeConfiguration>(x => x.ParticularItemId == command.InventoryItemId);
            if (!isExistingSetup)
            {
                _logger.LogError("Membership Inventory item cost setup does not exist for membership id {MembershipId}", command.InventoryItemId);
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Membership Inventory item cost setup does not exist");
                return commandResponse;
            }

            var properties = PrepareMembershipInventoryItemSetupProperties(command, securityContext.UserId);
            await _repository.UpdateOneAsync<BuroMemberOnboardingFeeConfiguration>(x => x.ParticularItemId == command.InventoryItemId, properties);
            _logger.LogInformation("Membership Inventory Item {InventoryItemId} deleted successfully", command.InventoryItemId);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error deleting membership Inventory Item {InventoryItemId}", command.InventoryItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error deleting membership Inventory Item");
        }

        return commandResponse;
    }

    private Dictionary<string, object> PrepareMembershipInventoryItemSetupProperties(DeleteMembershipInventoryItemCommand command, string userId)
    {
        _logger.LogInformation("Preparing membership Inventory Item {InventoryItemId} properties", command.InventoryItemId);

        var properties = new Dictionary<string, object>
        {
            {nameof(BuroMemberOnboardingFeeConfiguration.LastUpdateDate), DateTime.UtcNow},
            {nameof(BuroMemberOnboardingFeeConfiguration.LastUpdatedBy), userId},
            {nameof(BuroMemberOnboardingFeeConfiguration.IsMarkedToDelete), true}
        };

        return properties;
    }



}