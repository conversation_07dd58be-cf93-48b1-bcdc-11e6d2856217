using Domain.Commands.Member;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Member;

public class UpdateMembershipServiceCostService : IUpdateMembershipServiceCostService
{
    private readonly ILogger<UpdateMembershipServiceCostService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IFinMongoDbRepository _repository;

    public UpdateMembershipServiceCostService(
        ILogger<UpdateMembershipServiceCostService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> UpdateMembershipServiceCostAsync(UpdateMembershipServiceCostCommand command)
    {
        _logger.LogInformation("Updating membership service cost for membership id {MembershipId}", command.ServiceItemId);

        var commandResponse = new CommandResponse();

        try
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var isExistingSetup = await _repository.ExistsAsync<BuroMemberOnboardingFeeConfiguration>(x => x.ParticularItemId == command.ServiceItemId);
            if (!isExistingSetup)
            {
                _logger.LogError("Membership service cost setup does not exist for membership id {MembershipId}", command.ServiceItemId);
                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "Membership service cost setup does not exist");
                return commandResponse;
            }

            var properties = PrepareMembershipServiceSetupProperties(command, securityContext.UserId);
            await _repository.UpdateOneAsync<BuroMemberOnboardingFeeConfiguration>(x => x.ParticularItemId == command.ServiceItemId, properties);
            _logger.LogInformation("Updated membership service cost for membership id {MembershipId}", command.ServiceItemId);

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error updating membership service cost for membership id {MembershipId}", command.ServiceItemId);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating membership service cost");
        }

        return commandResponse;
    }


    private Dictionary<string, object> PrepareMembershipServiceSetupProperties(UpdateMembershipServiceCostCommand command, string userId)
    {
        _logger.LogInformation("Preparing membership service setup properties for membership id {MembershipId}", command.ServiceItemId);

        var properties = new Dictionary<string, object>
        {
            {nameof(BuroMemberOnboardingFeeConfiguration.LastUpdateDate), DateTime.UtcNow},
            {nameof(BuroMemberOnboardingFeeConfiguration.LastUpdatedBy), userId},
            {nameof(BuroMemberOnboardingFeeConfiguration.Amount), command.Cost}
        };

        return properties;
    }

}