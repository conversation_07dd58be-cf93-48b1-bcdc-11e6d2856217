using Domain.Commands.Member;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Member
{
    public class OtpVarificationForPhoneNumberChangeService : IOtpVarificationForPhoneNumberChangeService
    {
        private readonly ILogger<OtpVarificationForPhoneNumberChangeService> _logger;
        private readonly IOtpService _otpService;

        public OtpVarificationForPhoneNumberChangeService(
            ILogger<OtpVarificationForPhoneNumberChangeService> logger,
            IOtpService otpService)
        {
            _logger = logger;
            _otpService = otpService;
        }

        public async Task<CommandResponse> SendOtpForPhoneNumberChangeAsync(SendOtpForPhoneNumberChangeCommand command)
        {
            _logger.LogInformation("Inside SendOtpForPhoneNumberChangeAsync");

            var sendOtpCommand = new SendOtpCommand
            {
                Channel = OtpChannel.Phone,
                Recipient = command.Recipitent,
                UniqueKey = BuroOtpConstants.PhoneNumberChange,
                Purpose = EmailPurposeConstants.OtpForForgetPassword
            };

            var response = await _otpService.SendOtpAsync(sendOtpCommand);

            _logger.LogInformation("Finished SendOtpForPhoneNumberChangeAsync");

            return response;
        }

        public async Task<CommandResponse> MatchOtpForPhoneNumberChangeAsync(MatchOtpForPhoneNumberChangeCommand command)
        {
            _logger.LogInformation("Inside MatchOtpForPhoneNumberChangeAsync");

            var matchOtpCommand = new MatchOtpCommand
            {
                OtpToMatch = command.OtpToMatch,
                UniqueKey = BuroOtpConstants.PhoneNumberChange
            };

            var response = await _otpService.MatchOtpAsync(matchOtpCommand);

            _logger.LogInformation("Finished MatchOtpForPhoneNumberChangeAsync");

            return response;
        }
    }
}
