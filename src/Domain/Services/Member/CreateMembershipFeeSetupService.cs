using Domain.Commands.Member;
using Domain.Contracts.Member;
using FinMongoDbRepositories;
using Infrastructure.Constants;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Member;

public class CreateMembershipFeeSetupService : ICreateMembershipFeeSetupService
{
    private readonly ILogger<CreateMembershipFeeSetupService> _logger;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IFinMongoDbRepository _repository;

    public CreateMembershipFeeSetupService(
        ILogger<CreateMembershipFeeSetupService> logger,
        ISecurityContextProvider securityContextProvider,
        IFinMongoDbRepository repository)
    {
        _logger = logger;
        _securityContextProvider = securityContextProvider;
        _repository = repository;
    }

    public async Task<CommandResponse> MembershipFeeSetupAsync(MembershipFeeSetupCommand command)
    {
        _logger.LogInformation("Handling MembershipFeeSetupAsync: {Command}", command);
        var commandResponse = new CommandResponse();
        var securityContext = _securityContextProvider.GetSecurityContext();

        try
        {
            await (command.CostingType switch
            {
                EnumMembershipEnrollmentCostingType.Inventory => CreateInventoryMembershipFeeSetupAsync(command, securityContext),
                EnumMembershipEnrollmentCostingType.Service => CreateServiceMembershipFeeSetupAsync(command, securityContext),
                _ => throw new ArgumentException("Invalid costing type.")
            });

        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in MembershipFeeSetupAsync\n{StackTrace}", e.StackTrace);
            commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, e.Message);
        }

        return commandResponse;
    }

    private async Task CreateInventoryMembershipFeeSetupAsync(MembershipFeeSetupCommand command, SecurityContext securityContext)
    {
        _logger.LogInformation("Starting CreateInventoryMembershipFeeSetupAsync");
        var inventoryItem = await _repository.FindWithProjectionAsync<BuroInventoryItem, string>(
            i => i.ItemId == command.ParticularItemId,
            i => i.InventoryItemName);
        if (inventoryItem == null || !inventoryItem.Any())
            throw new ArgumentException("Inventory Item not found");


        var membershipFeeSetup = new BuroMemberOnboardingFeeConfiguration()
        {
            ItemId = Guid.NewGuid().ToString(),
            ParticularItemId = command.ParticularItemId,
            ItemName = inventoryItem.FirstOrDefault(),
            Amount = command.Amount ?? 0,
            CostingType = command.CostingType,
            CreateDate = DateTime.UtcNow,
            LastUpdatedBy = securityContext.UserId,
            LastUpdateDate = DateTime.UtcNow,
            Tags = new[] { Tags.IsAMembershipFeeSetup }
        };

        await _repository.InsertOneAsync(membershipFeeSetup);
        _logger.LogInformation("Ending CreateInventoryMembershipFeeSetupAsync");
    }

    private async Task CreateServiceMembershipFeeSetupAsync(MembershipFeeSetupCommand command, SecurityContext securityContext)
    {
        _logger.LogInformation("Starting CreateServiceMembershipFeeSetupAsync");
        var membershipFeeSetup = new BuroMemberOnboardingFeeConfiguration
        {
            ItemId = Guid.NewGuid().ToString(),
            ParticularItemId = Guid.NewGuid().ToString(),
            ItemName = command.ItemName,
            Amount = command.Amount,
            CostingType = command.CostingType,
            CreatedBy = securityContext.UserId,
            CreateDate = DateTime.UtcNow,
            LastUpdatedBy = securityContext.UserId,
            LastUpdateDate = DateTime.UtcNow,
            Tags = new[] { Tags.IsAMembershipFeeSetup }
        };

        await _repository.InsertOneAsync(membershipFeeSetup);
        _logger.LogInformation("Ending CreateServiceMembershipFeeSetupAsync");
    }

}