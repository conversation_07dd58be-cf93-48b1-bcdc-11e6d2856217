using Domain.Commands.Member;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Infrastructure.Events.MemberSurvey;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Member
{
    public class PhoneNumberChangeRequestCommandService : IPhoneNumberChangeRequestCommandService
    {
        private readonly ILogger<PhoneNumberChangeRequestCommandService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ISharedService _sharedService;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;

        public PhoneNumberChangeRequestCommandService(
            ILogger<PhoneNumberChangeRequestCommandService> logger,
            ISecurityContextProvider securityContextProvider,
            ISharedService sharedService,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> SendPhoneNumberChangeRequestAsync(PhoneNumberChangeRequestCommand command)
        {
            _logger.LogInformation("Starting Sending phone number change request for member Id: {MemberItemId}", command.MemberItemId);

            var commandResponse = new CommandResponse();

            try
            {
                await SubmitPhoneNumberChangeRequestAsync(command);

                _logger.LogInformation("Phone number change request successful for member Id: {MemberItemId}", command.MemberItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during phone number change request submission for member Id: {MemberItemId}", command.MemberItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating phone number change request.");
            }

            return commandResponse;
        }

        private async Task SubmitPhoneNumberChangeRequestAsync(PhoneNumberChangeRequestCommand command)
        {
            var currentEmployee = await GetLoggedInEmployeeDataAsync();
            var approverEmployee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(
                currentEmployee.CurrentOfficeItemId,
                BuroDesignationConstant.BranchManagerDesignationCode);

            var isSamePerson = currentEmployee.ItemId == approverEmployee.ItemId;
            var approvalItemId = isSamePerson ? string.Empty : await SubmitPhoneNumberChangeApprovalAsync(command, currentEmployee, approverEmployee);

            await CreateBuroMemberSurveyChangeRequestAsync(
                approvalItemId,
                command,
                currentEmployee,
                approverEmployee);

            if (isSamePerson)
            {
                SendToApprovePhoneNumberChangeRequest(command);
            }
        }

        private void SendToApprovePhoneNumberChangeRequest(PhoneNumberChangeRequestCommand command)
        {
            var payload = new MemberPhoneNumberChangeApprovalEvent
            {
                ChangeRequestItemId = command.ChangeRequestItemId,
                IsAccepted = true
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("Initiated auto approval for Phone number change request ID: {ChangeRequestItemId}", command.ChangeRequestItemId);
        }

        private async Task CreateBuroMemberSurveyChangeRequestAsync(
            string approvalItemId,
            PhoneNumberChangeRequestCommand command,
            BuroEmployee currentEmployee,
            BuroEmployee approverEmployee)
        {
            var member = await GetMemberByItemIdAsync(command.MemberItemId);
            var newListOfContactNumbers = MakeNewListOfContacts(member.ContactNumbers, command);

            var memberSurveyChangeRequest = new BuroMemberSurveyChangeRequest
            {
                ItemId = command.ChangeRequestItemId,
                MemberItemId = member.ItemId,
                SurveyItemId = member.SurveyItemId,
                MemberName = member.MemberName,
                CenterName = member.CenterName,
                BranchName = member.BranchOfficeName,
                Status = EnumChangeRequestStatus.Pending,
                RequestDate = DateTime.UtcNow,
                RequestedByEmployeeItemId = currentEmployee.ItemId,
                RequestedByEmployeeName = currentEmployee.EmployeeName,
                RequestedToEmployeeItemId = approverEmployee.ItemId,
                RequestedToEmployeeName = approverEmployee.EmployeeName,
                ApprovalItemId = approvalItemId,
                PropertyChanges = new List<PropertyChange>
                {
                    new()
                    {
                        PropertyName = nameof(BuroMember.ContactNumbers),
                        PropertyType = EnumDataType.OBJECTLIST,
                        OldValue = JsonConvert.SerializeObject(member.ContactNumbers),
                        NewValue = JsonConvert.SerializeObject(newListOfContactNumbers)
                    }
                }
            };

            memberSurveyChangeRequest.AddEntityBasicInfo();

            await _repository.SaveAsync(memberSurveyChangeRequest);
        }

        private async Task<string> SubmitPhoneNumberChangeApprovalAsync(
            PhoneNumberChangeRequestCommand command,
            BuroEmployee reviewer,
            BuroEmployee manager)
        {
            _logger.LogInformation("Starting SendPhoneNumberChangeApprovalAsync");

            var securityContext = _securityContextProvider.GetSecurityContext();
            var approvalEntityItemId = await CreatePhoneNumberChangeApprovalAsync(command, command.ChangeRequestItemId, reviewer, manager);
            var notificationPayload = MakeNotificationPayloadForBranchManager(command, command.ChangeRequestItemId, securityContext.DisplayName, approvalEntityItemId, manager);

            await _sharedService.NotifyUserAsync(
                BuroNotificationKeys.MemberPhoneNumberChangeApprovalCreated,
                notificationPayload,
                command.ChangeRequestItemId,
                manager.UserItemId);

            return approvalEntityItemId;
        }

        private static object MakeNotificationPayloadForBranchManager(
            PhoneNumberChangeRequestCommand command,
            string entityItemId,
            string addedBy,
            string approvalItemId,
            BuroEmployee manager)
        {
            return new
            {
                command.MemberItemId,
                RequestItemId = entityItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = addedBy,
                ApprovalItemId = approvalItemId,
                ApproverName = manager.EmployeeName,
                ApproverPin = manager.EmployeePin,
                ApproverDesignation = manager.DesignationTitle,
                CenterName = manager.CurrentCenterTitle
            };
        }

        private async Task<string> CreatePhoneNumberChangeApprovalAsync(
            PhoneNumberChangeRequestCommand command,
            string entityItemId,
            BuroEmployee approvalInitiator,
            BuroEmployee approver)
        {
            _logger.LogInformation("Starting CreatePhoneNumberChangeApprovalAsync");

            var currentUserId = approvalInitiator.UserItemId;

            var approval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsAPhoneNumberChangeApproval },
                CreatedBy = currentUserId,
                LastUpdatedBy = currentUserId,
                RelatedEntityItemId = entityItemId,
                RelatedEntityName = nameof(BuroMemberSurveyChangeRequest),
                RequestedFromPersonItemId = approvalInitiator.PersonItemId,
                RequestedFromEmployeeItemId = approvalInitiator.ItemId,
                RequestedFromEmployeePIN = approvalInitiator.EmployeePin,
                RequestedFromEmployeeName = approvalInitiator.EmployeeName,
                ActionedByPersonItemId = approver.PersonItemId,
                ActionedByEmployeeItemId = approver.ItemId,
                ActionedByEmployeePIN = approver.EmployeePin,
                ActionedByEmployeeName = approver.EmployeeName,
                ActionedByEmployeeDesignation = approver.DesignationTitle,
                Status = EnumApprovalStatusType.Pending,
                Category = EnumApprovalCategoryType.MemberPhoneNumberChangeApproval,
                MetaData = new List<MetaData>
                {
                    new() { Key = nameof(command.MemberItemId), Value = command.MemberItemId },
                    new() { Key = nameof(command.OldPhoneNumber), Value = command.OldPhoneNumber },
                    new() { Key = nameof(command.NewPhoneNumber), Value = command.NewPhoneNumber }
                },
                IdsAllowedToRead = new[] { currentUserId },
                IdsAllowedToUpdate = new[] { currentUserId },
                IdsAllowedToDelete = new[] { currentUserId },
                IdsAllowedToWrite = new[] { currentUserId }
            };

            approval.AddEntityBasicInfo();

            await _repository.SaveAsync(approval);

            _logger.LogInformation("Phone number change approval data added with approval Id: {ItemId}", approval.ItemId);

            return approval.ItemId;
        }

        public static List<string> MakeNewListOfContacts(List<string> contactNumbers, PhoneNumberChangeRequestCommand command)
        {
            var newListOfContactNumbers = contactNumbers.Where(c => c != command.OldPhoneNumber).ToList();

            newListOfContactNumbers.Add(command.NewPhoneNumber);

            return newListOfContactNumbers;
        }

        private async Task<BuroEmployee> GetLoggedInEmployeeDataAsync()
        {
            var context = _securityContextProvider.GetSecurityContext();
            var employee = await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == context.UserId);

            return employee;
        }

        private async Task<BuroMember> GetMemberByItemIdAsync(string memberItemId)
        {
            return await _repository.GetItemAsync<BuroMember>(m => m.ItemId == memberItemId);
        }
    }
}
