using Domain.Commands.Member;
using Domain.Contracts.Member;
using Infrastructure.Constants;
using Infrastructure.Events.MemberSurvey;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Member
{
    public class AddressChangeRequestCommandService : IAddressChangeRequestCommandService
    {
        private readonly ILogger<AddressChangeRequestCommandService> _logger;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ISharedService _sharedService;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;

        public AddressChangeRequestCommandService(
            ILogger<AddressChangeRequestCommandService> logger,
            ISecurityContextProvider securityContextProvider,
            ISharedService sharedService,
            IRepository repository,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
            _repository = repository;
            _serviceClient = serviceClient;
        }

        public async Task<CommandResponse> SendAddressChangeRequestAsync(AddressChangeRequestCommand command)
        {
            _logger.LogInformation("Starting Sending address change request for member Id: {MemberItemId}", command.MemberItemId);

            var commandResponse = new CommandResponse();

            try
            {
                await SubmitAddressChangeRequestAsync(command);

                _logger.LogInformation("Address change request successful for member Id: {MemberItemId}", command.MemberItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during address change request submission for member Id: {MemberItemId}", command.MemberItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while creating address change request.");
            }

            return commandResponse;
        }

        private async Task SubmitAddressChangeRequestAsync(AddressChangeRequestCommand command)
        {
            var currentEmployee = await GetLoggedInEmployeeDataAsync();
            var approverEmployee = await _sharedService.GetEmployeeByDesignationForAnOfficeAsync(
                currentEmployee.CurrentOfficeItemId,
                BuroDesignationConstant.BranchManagerDesignationCode);

            var isSamePerson = currentEmployee.ItemId == approverEmployee.ItemId;
            var approvalItemId = isSamePerson ? string.Empty : await SubmitAddressChangeApprovalAsync(command, currentEmployee, approverEmployee);

            await CreateBuroMemberSurveyChangeRequestAsync(
                approvalItemId,
                command,
                currentEmployee,
                approverEmployee);

            if (isSamePerson)
            {
                SendToApproveAddressChangeRequest(command);
            }
        }

        private void SendToApproveAddressChangeRequest(AddressChangeRequestCommand command)
        {
            var payload = new MemberAddressChangeApprovalEvent
            {
                ChangeRequestItemId = command.ChangeRequestItemId,
                IsAccepted = true
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, payload);

            _logger.LogInformation("Initiated auto approval for Address change request ID: {ChangeRequestItemId}", command.ChangeRequestItemId);
        }

        private async Task CreateBuroMemberSurveyChangeRequestAsync(
            string approvalItemId,
            AddressChangeRequestCommand command,
            BuroEmployee currentEmployee,
            BuroEmployee approverEmployee)
        {
            var member = await GetMemberByItemIdAsync(command.MemberItemId);
            var propertyChanges = GetPropertyChanges(command, member.SurveyItemId);

            var memberSurveyChangeRequest = new BuroMemberSurveyChangeRequest
            {
                ItemId = command.ChangeRequestItemId,
                MemberItemId = member.ItemId,
                SurveyItemId = member.SurveyItemId,
                MemberName = member.MemberName,
                CenterName = member.CenterName,
                BranchName = member.BranchOfficeName,
                Status = EnumChangeRequestStatus.Pending,
                RequestDate = DateTime.UtcNow,
                RequestedByEmployeeItemId = currentEmployee.ItemId,
                RequestedByEmployeeName = currentEmployee.EmployeeName,
                RequestedToEmployeeItemId = approverEmployee.ItemId,
                RequestedToEmployeeName = approverEmployee.EmployeeName,
                ApprovalItemId = approvalItemId,
                PropertyChanges = propertyChanges,
                AdditionalDocumentIds = command.AdditionalDocumentIds
            };

            memberSurveyChangeRequest.AddEntityBasicInfo();

            await _repository.SaveAsync(memberSurveyChangeRequest);
        }

        private List<PropertyChange> GetPropertyChanges(
            AddressChangeRequestCommand command,
            string surveyItemId)
        {
            var currentAddress = GetCurrentAddress(surveyItemId, command.IsPresentAddress);

            var propertyName = command.IsPresentAddress
                ? nameof(BuroMemberSurvey.PresentAddress)
                : nameof(BuroMemberSurvey.PermanentAddress);

            var propertyChanges = new List<PropertyChange>
            {
                new ()
                {
                    PropertyName = propertyName,
                    PropertyType = EnumDataType.OBJECT,
                    OldValue = JsonConvert.SerializeObject(currentAddress),
                    NewValue = JsonConvert.SerializeObject(command.Address)
                }
            };

            return propertyChanges;
        }

        private BuroMemberAddress? GetCurrentAddress(string surveyItemId, bool isPresentAddress)
        {
            Func<BuroMemberSurvey, BuroMemberAddress> addressSelector = isPresentAddress
                ? s => s.PresentAddress
                : s => s.PermanentAddress;

            var currentAddress = _repository
                .GetItems<BuroMemberSurvey>(s => s.ItemId == surveyItemId)
                .Select(addressSelector)
                .FirstOrDefault();

            return currentAddress;
        }

        private async Task<string> SubmitAddressChangeApprovalAsync(
            AddressChangeRequestCommand command,
            BuroEmployee reviewer,
            BuroEmployee manager)
        {
            _logger.LogInformation("Starting SendPhoneNumberChangeApprovalAsync");

            var securityContext = _securityContextProvider.GetSecurityContext();
            var approvalEntityItemId = await CreateAddressChangeApprovalAsync(command, command.ChangeRequestItemId, reviewer, manager);
            var notificationPayload = MakeNotificationPayloadForBranchManager(command, command.ChangeRequestItemId, securityContext.DisplayName, approvalEntityItemId, manager);

            await _sharedService.NotifyUserAsync(
                BuroNotificationKeys.MemberAddressChangeApprovalCreated,
                notificationPayload,
                command.ChangeRequestItemId,
                manager.UserItemId);

            return approvalEntityItemId;
        }

        private static object MakeNotificationPayloadForBranchManager(
            AddressChangeRequestCommand command,
            string entityItemId,
            string addedBy,
            string approvalItemId,
            BuroEmployee manager)
        {
            return new
            {
                command.MemberItemId,
                RequestItemId = entityItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = addedBy,
                ApprovalItemId = approvalItemId,
                ApproverName = manager.EmployeeName,
                ApproverPin = manager.EmployeePin,
                ApproverDesignation = manager.DesignationTitle,
                CenterName = manager.CurrentCenterTitle
            };
        }

        private async Task<string> CreateAddressChangeApprovalAsync(
            AddressChangeRequestCommand command,
            string entityItemId,
            BuroEmployee approvalInitiator,
            BuroEmployee approver)
        {
            _logger.LogInformation("Starting CreateAddressChangeApprovalAsync");

            var currentUserId = approvalInitiator.UserItemId;

            var approval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsAAddressChangeApproval },
                CreatedBy = currentUserId,
                LastUpdatedBy = currentUserId,
                RelatedEntityItemId = entityItemId,
                RelatedEntityName = nameof(BuroMemberSurveyChangeRequest),
                RequestedFromPersonItemId = approvalInitiator.PersonItemId,
                RequestedFromEmployeeItemId = approvalInitiator.ItemId,
                RequestedFromEmployeePIN = approvalInitiator.EmployeePin,
                RequestedFromEmployeeName = approvalInitiator.EmployeeName,
                ActionedByPersonItemId = approver.PersonItemId,
                ActionedByEmployeeItemId = approver.ItemId,
                ActionedByEmployeePIN = approver.EmployeePin,
                ActionedByEmployeeName = approver.EmployeeName,
                ActionedByEmployeeDesignation = approver.DesignationTitle,
                Status = EnumApprovalStatusType.Pending,
                Category = EnumApprovalCategoryType.MemberAddressChangeApproval,
                MetaData = new List<MetaData>
                {
                    new() { Key = nameof(command.MemberItemId), Value = command.MemberItemId },
                    new() { Key = nameof(command.Address), Value = JsonConvert.SerializeObject(command.Address) }
                },
                IdsAllowedToRead = new[] { currentUserId },
                IdsAllowedToUpdate = new[] { currentUserId },
                IdsAllowedToDelete = new[] { currentUserId },
                IdsAllowedToWrite = new[] { currentUserId }
            };

            approval.AddEntityBasicInfo();

            await _repository.SaveAsync(approval);

            _logger.LogInformation("Address change approval data added with approval Id: {ItemId}", approval.ItemId);

            return approval.ItemId;
        }

        private async Task<BuroEmployee> GetLoggedInEmployeeDataAsync()
        {
            var context = _securityContextProvider.GetSecurityContext();
            var employee = await _repository.GetItemAsync<BuroEmployee>(e => e.UserItemId == context.UserId);

            return employee;
        }

        private async Task<BuroMember> GetMemberByItemIdAsync(string memberItemId)
        {
            return await _repository.GetItemAsync<BuroMember>(m => m.ItemId == memberItemId);
        }
    }
}
