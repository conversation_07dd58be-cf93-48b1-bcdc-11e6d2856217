using Domain.Commands.Member;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Domain.Services.Member
{
    public class UpdateMemberConfigurationService
    {
        private readonly ILogger<UpdateMemberConfigurationService> _logger;
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IChangeLogRepository _businessRepository;

        public UpdateMemberConfigurationService(
            ILogger<UpdateMemberConfigurationService> logger,
            ISecurityContextProvider securityContextProvider,
            IRepository repository,
            IChangeLogRepository businessRepository)
        {
            _logger = logger;
            _securityContextProvider = securityContextProvider;
            _repository = repository;
            _businessRepository = businessRepository;
        }

        public async Task<CommandResponse> UpdateMemberConfigurationAsync(UpdateMemberConfigurationCommand command)
        {
            _logger.LogInformation("Starting Update Member Configuration for Configuration ID: {ItemId}", command.ItemId);

            var commandResponse = new CommandResponse();

            try
            {
                var securityContext = _securityContextProvider.GetSecurityContext();

                var existingConfiguration = await GetMemberConfigurationAsync(command.ItemId);

                var properties = PrepareMemberConfigurationProperties(command, existingConfiguration, securityContext.UserId);

                await _businessRepository.UpdateWithChangeLogAsync<BuroMemberConfiguration>(c => c.ItemId == command.ItemId, properties);

                _logger.LogInformation("Successfully Updated Member Configuration with Configuration ID: {ItemId}", command.ItemId);
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred during office update for Configuration ID: {ItemId}", command.ItemId);

                commandResponse.SetError(BuroErrorMessageKeys.ErrorOccurred, "An error occurred while updating the office.");
            }

            return commandResponse;
        }

        private async Task<BuroMemberConfiguration> GetMemberConfigurationAsync(string itemId)
        {
            return await _repository.GetItemAsync<BuroMemberConfiguration>(c => c.ItemId == itemId)
                    ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, "MEMBERCONFIGURATION"));
        }

        private Dictionary<string, object> PrepareMemberConfigurationProperties(
            UpdateMemberConfigurationCommand command,
            BuroMemberConfiguration existingConfiguration,
            string userId)
        {
            _logger.LogInformation("Preparing update properties for Member Configuration ID: {ItemId}", command.ItemId);

            var memberTypes = GetMemberTypeBsonData(command.MemberTypes);

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMemberConfiguration.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroMemberConfiguration.LastUpdatedBy), userId},
                {nameof(BuroMemberConfiguration.MemberTypes), memberTypes}
            };

            AddIfChanged(properties, nameof(command.ShouldHaveFeeForAccountClosing), command.ShouldHaveFeeForAccountClosing, existingConfiguration.ShouldHaveFeeForAccountClosing);
            AddIfChanged(properties, nameof(command.FeeForAccountClosing), command.FeeForAccountClosing, existingConfiguration.FeeForAccountClosing);
            AddIfChanged(properties, nameof(command.ShouldHaveDormantAccountConfiguration), command.ShouldHaveDormantAccountConfiguration, existingConfiguration.ShouldHaveDormantAccountConfiguration);
            AddIfChanged(properties, nameof(command.DormantAccountTimeLineLimit), command.DormantAccountTimeLineLimit, existingConfiguration.DormantAccountTimeLineLimit);
            AddIfChanged(properties, nameof(command.DormantAccountTimeLineLimitUnit), command.DormantAccountTimeLineLimitUnit, existingConfiguration.DormantAccountTimeLineLimitUnit);
            AddIfChanged(properties, nameof(command.ShouldGetFuneralContribution), command.ShouldGetFuneralContribution, existingConfiguration.ShouldGetFuneralContribution);
            AddIfChanged(properties, nameof(command.FuneralContributionAmount), command.FuneralContributionAmount, existingConfiguration.FuneralContributionAmount);
            AddIfChanged(properties, nameof(command.ShouldImposeForcedPasswordChange), command.ShouldImposeForcedPasswordChange, existingConfiguration.ShouldImposeForcedPasswordChange);
            AddIfChanged(properties, nameof(command.PasswordExpirationLimit), command.PasswordExpirationLimit, existingConfiguration.PasswordExpirationLimit);
            AddIfChanged(properties, nameof(command.PasswordExpirationLimitUnit), command.PasswordExpirationLimitUnit, existingConfiguration.PasswordExpirationLimitUnit);
            AddIfChanged(properties, nameof(command.SkipPasswordExpirationLimit), command.SkipPasswordExpirationLimit, existingConfiguration.SkipPasswordExpirationLimit);
            AddIfChanged(properties, nameof(command.SkipPasswordExpirationLimitUnit), command.SkipPasswordExpirationLimitUnit, existingConfiguration.SkipPasswordExpirationLimitUnit);
            AddIfChanged(properties, nameof(command.MemberIdStartingNumber), command.MemberIdStartingNumber, existingConfiguration.MemberIdStartingNumber);
            AddIfChanged(properties, nameof(command.MinimumIncomeForSamityCriteria), command.MinimumIncomeForSamityCriteria, existingConfiguration.MinimumIncomeForSamityCriteria);
            AddIfChanged(properties, nameof(command.MinimumIncomeForSamityCriteria), command.MinimumIncomeForSamityCriteria, existingConfiguration.MinimumIncomeForSamityCriteria);
            AddIfChanged(properties, nameof(command.MaximumIncomeForSamityCriteria), command.MaximumIncomeForSamityCriteria, existingConfiguration.MaximumIncomeForSamityCriteria);
            AddIfChanged(properties, nameof(command.ShouldTransferSMSCost), command.ShouldTransferSMSCost, existingConfiguration.ShouldTransferSMSCost);
            AddIfChanged(properties, nameof(command.UnitCostOfSMS), command.UnitCostOfSMS, existingConfiguration.UnitCostOfSMS);

            return properties;
        }

        private static BsonArray GetMemberTypeBsonData(List<MemberType> memberTypes)
        {
            memberTypes.ForEach(m =>
            {
                m.ItemId ??= Guid.NewGuid().ToString();
            });

            return new BsonArray(memberTypes.Select(m => m.ToBsonDocument()));
        }

        private static void AddIfChanged<T>(
            Dictionary<string, object> properties,
            string propertyName,
            T newValue,
            T oldValue) where T : IComparable
        {
            if (typeof(T) == typeof(double))
            {
                if (newValue.CompareTo(oldValue) != 0)
                {
                    properties[propertyName] = newValue;
                }
            }
            else
            {
                if (!Equals(newValue, oldValue))
                {
                    properties[propertyName] = newValue;
                }
            }
        }
    }
}
