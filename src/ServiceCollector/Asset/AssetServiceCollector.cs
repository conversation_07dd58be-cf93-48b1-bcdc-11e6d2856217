using Domain.Services.Asset;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Asset;

public static class AssetServiceCollector
{
    public static void AddAssetServices(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<CreateAssetGroupService>();
        serviceCollection.AddSingleton<CreateAssetSubGroupService>();
        serviceCollection.AddSingleton<UpdateAssetSubGroupService>();
        serviceCollection.AddSingleton<CreateAssetItemService>();
        serviceCollection.AddSingleton<DeleteAssetGroupService>();
        serviceCollection.AddSingleton<UpdateAssetGroupService>();
    }
}