using Domain.Contracts.DayEnd;
using Domain.Services.DayEnd;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.DayEnd;

public static class DayEndProcessServiceCollector
{
    public static void AddDayEndService(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<DayEndProcessService>();

        serviceCollection.AddSingleton<IDayEndProcessFactoryService, DayEndProcessFactoryService>();

        serviceCollection.AddSingleton<IDayEndExecuteService, CheckNewCsAccountWithoutDepositsService>();
        serviceCollection.AddSingleton<CheckNewCsAccountWithoutDepositsService>();

    }

}
