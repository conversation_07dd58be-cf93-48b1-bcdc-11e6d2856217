using Domain.Services;
using Domain.Services.Office;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Office;

public static class OfficeServiceCollector
{
    public static void AddOfficeService(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<CreateOfficeService>();
        serviceCollection.AddSingleton<UpdateOfficeService>();
    }
}