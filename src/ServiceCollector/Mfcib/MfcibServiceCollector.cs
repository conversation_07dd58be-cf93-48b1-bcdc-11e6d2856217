using Domain.Contracts.Mfcib;
using Domain.Services.Mfcib;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Mfcib
{
    public static class MfcibServiceCollector
    {
        public static void AddMfcibServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<IExportRequestCommandService, ExportRequestCommandService>();
        }
    }
}
