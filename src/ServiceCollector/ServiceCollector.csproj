<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
		<PackageReference Include="FinBlocks.LogProvider" Version="6.0.0.6" />
		<PackageReference Include="Microsoft.Extensions.Logging.Log4Net.AspNetCore" Version="8.0.0" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Domain\Domain.csproj" />
        <ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
        <ProjectReference Include="..\Validators\Validators.csproj" />
    </ItemGroup>
    <ItemGroup>
      <Folder Include="Asset\" />
    </ItemGroup>
</Project>
