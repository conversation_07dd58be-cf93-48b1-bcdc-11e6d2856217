using Domain.Services.Helper;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Helper
{
    public static class HelperServiceCollector
    {
        public static void AddHelperService(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<CommonHelperService>();
        }
    }
}
