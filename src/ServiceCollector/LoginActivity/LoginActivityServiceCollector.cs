using Domain.Services.LoginActivity;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.LoginActivity;

public static class LoginActivityServiceCollector
{
    public static void AddLoginActivityServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<SaveLoginActivityService>();
    }
}