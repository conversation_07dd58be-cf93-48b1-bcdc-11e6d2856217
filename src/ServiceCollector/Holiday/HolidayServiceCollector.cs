using Domain.Services.Holiday;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Holiday;

public static class HolidayServiceCollector
{
    public static void AddHolidayService(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<CreateHolidayService>();
        serviceCollection.AddSingleton<CreateWeekendService>();
        serviceCollection.AddSingleton<DeleteHolidayService>();
        serviceCollection.AddSingleton<EarlyEndHolidayService>();
    }
}