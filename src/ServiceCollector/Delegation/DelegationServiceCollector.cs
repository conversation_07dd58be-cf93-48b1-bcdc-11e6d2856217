using Domain.Services.Delegation;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Delegation
{
    public static class DelegationServiceCollector
    {
        public static void AddDelegationServices(this IServiceCollection serviceCollection,
            IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<DelegationCreateService>();
            serviceCollection.AddSingleton<DelegationUpdateService>();
        }
    }
}
