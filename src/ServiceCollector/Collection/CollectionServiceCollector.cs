using Domain.Contracts.Collection;
using Domain.Services.Collection;
using Microsoft.Extensions.DependencyInjection;

namespace ServiceCollector.Collection
{
    public static class CollectionServiceCollector
    {
        public static void AddCollectionServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IOneShotPaymentService, OneShotPaymentService>();
            serviceCollection.AddSingleton<IAccountOneShotPaymentHandler, ContractualSavingAccountOneShotPaymentHanlder>();
            serviceCollection.AddSingleton<ITransferCollectedfundsCommandService, TransferCollectedfundsCommandService>();
            serviceCollection.AddSingleton<IAccountOneShotPaymentHandler, GeneralSavingAccountOneShotPaymentHandler>();
            serviceCollection.AddSingleton<IAccountOneShotPaymentHandler, LoanAccountOneShotPaymentHandler>();
        }
    }
}
