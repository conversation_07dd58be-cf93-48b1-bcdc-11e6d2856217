using Domain.Contracts.VoucherTransaction;
using Domain.Services.VoucherTransaction;
using Microsoft.Extensions.DependencyInjection;

namespace ServiceCollector.VoucherTransaction;

public static class VoucherTransactionServiceCollector
{
    public static void AddVoucherTransactionService(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSingleton<IVoucherTransactionSetupCommandService, VoucherTransactionSetupCommandService>();

    }
}