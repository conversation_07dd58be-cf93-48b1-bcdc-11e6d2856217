using Domain.Contracts.Voucher.Configuration;
using Domain.Services.Voucher.Configuration;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Voucher;

public static class VoucherConfigurationServiceCollector
{
    public static void AddVoucherConfigurationService(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        //Create
        serviceCollection.AddSingleton<CreateStandardVoucherConfigurationService>();
        serviceCollection.AddSingleton<CreateProductLineVoucherConfigurationService>();
        serviceCollection.AddSingleton<ICreateVoucherConfigurationStrategyFactory, CreateStandardVoucherConfigurationStrategyFactory>();
        serviceCollection.AddSingleton<ICreateVoucherConfigurationStrategy, CreateStandardVoucherConfigurationStrategy>();
        serviceCollection.AddSingleton<ICreateVoucherConfigurationStrategy, CreateProductLineVoucherConfigurationStrategy>();

        //Update
        serviceCollection.AddSingleton<UpdateStandardVoucherConfigurationService>();
        serviceCollection.AddSingleton<UpdateProductLineVoucherConfigurationService>();
        serviceCollection.AddSingleton<IUpdateVoucherConfigurationStrategyFactory, UpdateStandardVoucherConfigurationStrategyFactory>();
        serviceCollection.AddSingleton<IUpdateVoucherConfigurationStrategy, UpdateStandardVoucherConfigurationStrategy>();
        serviceCollection.AddSingleton<IUpdateVoucherConfigurationStrategy, UpdateAutoVoucherConfigurationStrategy>();

        //Delete
        serviceCollection.AddSingleton<DeleteStandardVoucherConfigurationService>();
        serviceCollection.AddSingleton<DeleteProductLineVoucherConfigurationService>();
        serviceCollection.AddSingleton<IDeleteVoucherConfigurationStrategyFactory, DeleteStandardVoucherConfigurationStrategyFactory>();
        serviceCollection.AddSingleton<IDeleteVoucherConfigurationStrategy, DeleteStandardVoucherConfigurationStrategy>();
        serviceCollection.AddSingleton<IDeleteVoucherConfigurationStrategy, DeleteProductLineVoucherConfigurationStrategy>();

    }

}