using Domain.Contracts.Voucher;
using Domain.Services.Voucher;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Voucher;

public static class ManualVoucherServiceCollector
{
    public static void AddManualVoucherService(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<ICreateManualVoucherCommandService, CreateManualVoucherService>();
        serviceCollection.AddSingleton<IUpdateManualVoucherCommandService, UpdateManualVoucherService>();
        serviceCollection.AddSingleton<IDeleteManualVoucherCommandService, DeleteManualVoucherService>();
    }
}