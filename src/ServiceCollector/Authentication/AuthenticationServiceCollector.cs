using Domain.Contracts.Authentication;
using Domain.Services.Authentication;
using Infrastructure.Contracts;
using Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Authentication;

public static class AuthenticationServiceCollector
{
    public static void AddAuthenticationService(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<IAuthenticationService, AuthenticationService>();
        serviceCollection.AddSingleton<IAuthenticationEmailService, AuthenticationEmailService>();
        serviceCollection.AddSingleton<IAuthenticationSmsService, AuthenticationSmsService>();
        serviceCollection.AddSingleton<IOtpService, OtpService>();
    }
}