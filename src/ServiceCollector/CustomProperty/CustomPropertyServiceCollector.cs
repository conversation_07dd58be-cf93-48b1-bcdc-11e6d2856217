using Domain.Services.CustomProperty;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.CustomProperty
{
    public static class CustomPropertyServiceCollector
    {
        public static void AddCustomPropertyServices(this IServiceCollection serviceCollection,
            IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<AddCustomPropertyService>();
            serviceCollection.AddSingleton<EditCustomPropertyService>();
            serviceCollection.AddSingleton<RemoveCustomPropertyService>();
        }
    }
}
