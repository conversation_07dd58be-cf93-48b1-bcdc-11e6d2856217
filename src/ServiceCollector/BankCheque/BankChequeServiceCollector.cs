using Domain.Contracts.BankCheque;
using Domain.Services.BankCheque;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.BankCheque;

public static class BankChequeServiceCollector
{
    public static void AddBankChequeServices(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<ICreateChequeBookCommandService, CreateChequeBookCommandService>();

    }

}