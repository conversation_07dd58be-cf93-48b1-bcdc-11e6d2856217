using Domain.Services.Notice;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Notice
{
    public static class NoticeServiceCollector
    {
        public static void AddNoticeService(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<NoticeService>();
        }
    }
}
