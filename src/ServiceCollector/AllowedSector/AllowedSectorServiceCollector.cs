using Domain.Services.AllowedSector;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.AllowedSector;

public static class AllowedSectorServiceCollector
{
    public static void AddAllowedSectorServices(this IServiceCollection serviceCollection,
       IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<CreateAllowedSectorService>();
        serviceCollection.AddSingleton<UpdateAllowedSectorService>();
        serviceCollection.AddSingleton<UpdateAllowedSectorStatusService>();
    }
}