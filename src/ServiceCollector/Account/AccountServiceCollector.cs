using Domain.Contracts.Account;
using Domain.Services.Account;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Account
{
    public static class AccountServiceCollector
    {
        public static void AddAccountServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<ICreateCSAccountCommandService, CreateCSAccountCommandService>();
            serviceCollection.AddSingleton<IApplyLoanCommandService, ApplyLoanCommandService>();
            serviceCollection.AddSingleton<ILoanCommonService, LoanCommonService>();
            serviceCollection.AddSingleton<ISubmitLoanCommandService, SubmitLoanCommandService>();
            serviceCollection.AddSingleton<ISubmitDisbursementCommandService, SubmitDisbursementCommandService>();
            serviceCollection.AddSingleton<IChangeGSAccountCommandService, ChangeGSAccountCommandService>();
            serviceCollection.AddSingleton<IGenerateLoanScheduleCommandService, GenerateLoanScheduleCommandService>();
        }
    }
}
