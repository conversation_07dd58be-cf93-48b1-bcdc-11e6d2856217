using Domain.Contracts.Member;
using Domain.Services.Member;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Member
{
    public static class MemberServiceCollector
    {
        public static void AddMemberServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<UpdateMemberConfigurationService>();
            serviceCollection.AddSingleton<IOtpVarificationForPhoneNumberChangeService, OtpVarificationForPhoneNumberChangeService>();
            serviceCollection.AddSingleton<IPhoneNumberChangeRequestCommandService, PhoneNumberChangeRequestCommandService>();
            serviceCollection.AddSingleton<IAddressChangeRequestCommandService, AddressChangeRequestCommandService>();
            serviceCollection.AddSingleton<ICreateMembershipFeeSetupService, CreateMembershipFeeSetupService>();
            serviceCollection.AddSingleton<IUpdateMembershipServiceCostService, UpdateMembershipServiceCostService>();
            serviceCollection.AddSingleton<IDeleteMembershipInventoryItemService, DeleteMembershipInventoryItemService>();
        }
    }
}
