using Domain.Contracts.Employee;
using Domain.Services.Employee;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Employee
{
    public static class EmployeeServiceCollector
    {
        public static void AddEmployeeServices(this IServiceCollection serviceCollection,
            IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<AddEmployeeDesignationService>();
            serviceCollection.AddSingleton<EmployeeStaffingRequestService>();
            serviceCollection.AddSingleton<IAddEmployeeService, AddEmployeeService>();
            serviceCollection.AddSingleton<UpdateEmployeeService>();
        }
    }
}
