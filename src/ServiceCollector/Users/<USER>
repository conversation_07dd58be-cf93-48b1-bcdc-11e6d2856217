using Domain.Contracts.User;
using Domain.Services.Users;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Users;

public static class UserServiceCollector
{
    public static void AddUserServices(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<IUserService, UserService>();
        serviceCollection.AddSingleton<CreateUserPermissionService>();
        serviceCollection.AddSingleton<UpdateUserPermissionService>();
    }
}