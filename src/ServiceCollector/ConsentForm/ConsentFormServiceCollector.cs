using Domain.Contracts.ConsentForm;
using Domain.Services.ConsentForm;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.ConsentForm;

public static class ConsentFormServiceCollector
{
    public static void AddConsentFormServices(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<ICreateConsentFormCommandService, CreateConsentFormService>();
        serviceCollection.AddSingleton<IRevokeConsentFormCommandService, RevokeConsentFormService>();

    }
}