using ErrorTracker.ErrorBus;
using FinMongoDbDataContext;
using FinMongoDbRepositories;
using FinRepositories;
using HierarchyManager;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Logger
{
    public static class LoggerServiceCollector
    {
        public static void AddLoggerServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            var loggerServiceDescriptor = serviceCollection.FirstOrDefault(descriptor => descriptor.ServiceType == typeof(ILoggerProvider) && descriptor.ImplementationType.Name == nameof(Log4NetProvider));
            serviceCollection.Remove(loggerServiceDescriptor);
            serviceCollection.AddSingleton<ILoggerProvider, LogProvider.LogProvider>();
            serviceCollection.AddErrorBus();

            var repoServiceDescriptor = serviceCollection.FirstOrDefault(descriptor => descriptor.ServiceType == typeof(IRepository) && descriptor.ImplementationType.Name == nameof(MongoDbRepository));
            serviceCollection.Remove(repoServiceDescriptor);
            serviceCollection.AddFinMongodbDataContextServices();
            serviceCollection.AddFinRepositoryServices();
            serviceCollection.AddFinMongoDbRepositoryServices();
            serviceCollection.AddHierarchyManagerServices();
        }
    }
}
