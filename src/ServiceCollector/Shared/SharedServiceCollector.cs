using Domain.Contracts.Member;
using Domain.Services;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Shared
{
    public static class SharedServiceCollector
    {
        public static void AddSharedServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<ISharedService, SharedService>();
        }
    }
}
