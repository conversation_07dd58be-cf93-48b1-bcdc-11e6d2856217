using Domain.Services.Tasks;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Tasks
{
    public static class TasksServiceCollector
    {
        public static void AddTasksServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<TasksCommandService>();
        }
    }
}
