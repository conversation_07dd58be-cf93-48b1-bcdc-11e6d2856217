using Domain.Services.Center;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Center
{
    public static class CenterServiceCollector
    {
        public static void AddCenterServices(this IServiceCollection serviceCollection,
            IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<CenterCommandService>();
            serviceCollection.AddSingleton<UpdateCenterService>();
        }
    }
}
