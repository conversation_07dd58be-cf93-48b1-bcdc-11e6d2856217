using Domain.Services.Vendor;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Vendor;

public static class VendorServiceCollector
{
    public static void AddVendorServices(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<CreateVendorService>();
        serviceCollection.AddSingleton<UpdateVendorService>();
        serviceCollection.AddSingleton<UpdateVendorStatusService>();
    }
}