using Domain.Contracts.Role;
using Domain.Services.Role;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Role
{
    public static class RoleServiceCollector
    {
        public static void AddRoleServices(this IServiceCollection serviceCollection,
            IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<UpdateRolesService>();
            serviceCollection.AddSingleton<CreateRoleService>();
            serviceCollection.AddSingleton<ICreateFeatureRoleMapService, CreateFeatureRoleMapService>();
            serviceCollection.AddSingleton<IUpdateFeatureRoleMapService, UpdateFeatureRoleMapService>();
            serviceCollection.AddSingleton<UpdateDesignationsInRoleService>();
            serviceCollection.AddSingleton<RoleDeleteService>();
        }
    }
}
