using Domain.Contracts.Transaction;
using Domain.Services.Transaction;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Transaction
{
    public static class TransactionServiceCollector
    {
        public static void AddTransactionServices(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<ISubmitTransactionRequestCommandService, SubmitTransactionRequestCommandService>();
        }
    }
}
