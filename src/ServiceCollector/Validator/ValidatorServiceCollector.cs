using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Validator;

public static class ValidatorServiceCollector
{
    public static void RegisterValidators(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        //serviceCollection.RegisterCollections(typeof(IValidator<>), new[]
        //{
        //    typeof(VerifyEmployeePinForForgetPasswordCommandValidator).Assembly
        //});
    }
}