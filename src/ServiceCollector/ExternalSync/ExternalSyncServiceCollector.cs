using Domain.Services.ExternalSync;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.ExternalSync
{
    public static class ExternalSyncServiceCollector
    {
        public static void AddExternalSyncService(this IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<ExternalSyncService>();
        }
    }
}
