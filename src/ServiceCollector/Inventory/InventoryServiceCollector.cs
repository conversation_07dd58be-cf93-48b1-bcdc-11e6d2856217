using Domain.Contracts.Inventory;
using Domain.Services.Inventory;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Inventory
{
    public static class InventoryServiceCollector
    {
        public static void AddInventoryServices(this IServiceCollection serviceCollection,
            IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<CreateInventoryItemService>();
            serviceCollection.AddSingleton<UpdateInventoryItemService>();
            serviceCollection.AddSingleton<ArchiveInventoryItemService>();
            serviceCollection.AddSingleton<AddInventoryMovementService>();
            serviceCollection.AddSingleton<OutInventoryItemService>();
            serviceCollection.AddSingleton<TransferInventoryItemService>();
            serviceCollection.AddSingleton<DisposeInventoryItemService>();
            serviceCollection.AddSingleton<CreateInventoryRequestService>();
            serviceCollection.AddSingleton<RejectInventoryRequestService>();
            serviceCollection.AddSingleton<IInventoryStockManagementService, InventoryStockManagementService>();
        }
    }
}
