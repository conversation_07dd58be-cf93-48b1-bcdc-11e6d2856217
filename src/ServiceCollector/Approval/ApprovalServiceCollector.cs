using Domain.Services.Approval;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Approval
{
    public static class ApprovalServiceCollector
    {
        public static void AddApprovalServices(this IServiceCollection serviceCollection,
            IAppSettings appSettings)
        {
            serviceCollection.AddSingleton<ApprovalService>();
        }
    }
}
