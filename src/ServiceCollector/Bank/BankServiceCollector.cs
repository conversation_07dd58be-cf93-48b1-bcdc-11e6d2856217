using Domain.Contracts.Bank;
using Domain.Services.Bank;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace ServiceCollector.Bank;

public static class BankServiceCollector
{
    public static void AddBankServices(this IServiceCollection serviceCollection,
        IAppSettings appSettings)
    {
        serviceCollection.AddSingleton<ICreateBankBranchCommandService, CreateBankBranchCommandService>();

    }

}