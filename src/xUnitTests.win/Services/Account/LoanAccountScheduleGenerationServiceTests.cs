using EventHandlers.Contracts;
using EventHandlers.Services.Account;
using Infrastructure.Events.Account;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using xUnitTests.win.Helpers;

namespace xUnitTests.win.Services.Account
{
    public class LoanAccountScheduleGenerationServiceTests : MockServiceProviderBase<LoanAccountScheduleGenerationService>
    {
        private readonly LoanAccountScheduleGenerationService _service;
        private readonly Mock<IWorkingDayService> _helperServiceMock;
        public LoanAccountScheduleGenerationServiceTests()
        {
            _helperServiceMock = new Mock<IWorkingDayService>();
            _service = new LoanAccountScheduleGenerationService(
                MockLogger.Object,
                MockRepository.Object,
                MockFinMongoDbRepository.Object,
                _helperServiceMock.Object,
                MockSecurityContextProvider.Object,
                MockNotificationServiceClient.Object);
        }

        [Fact]
        public async Task GenerateLoanAccountSchedule_WhenSchedulesGenerated_ShouldSaveSchedules()
        {
            // Arrange
            var command = new LoanAccountScheduleGenerationEvent
            {
                LoanApplicationItemId = "application-01",
            };

            var application = new BuroLoanApplication
            {
                ItemId = "application-01",
                ProposedLoanAccountItemId = "account-1",
                FinalLoanAmount = 9200,
                PaymentInterval = EnumPaymentInterval.Monthly,
                ProductLineItemId = "Productline-01",
                MemberItemId = "member-1",
                TenureDetails = new LoanLineTenure
                {
                    Duration = new Period { Value = 1, Unit = EnumTenureUnit.Year }
                }
            };

            var disbursement = new BuroLoanApplicationDisbursement
            {
                LoanApplicationItemId = application.ItemId,
                NegotiationInformation = new LoanDisbursementNegotiationInformation
                {
                    FirstInstallmentDate = DateTime.UtcNow
                }
            };

            var account = new BuroLoanAccount
            {
                ItemId = "account-1",
                LoanApplicationItemId = "application-01",
                MemberItemId = "member-1",
                PaymentInterval = EnumPaymentInterval.Monthly,
                ProductLineItemId = "Productline-01",
                FirstInstallmentDate = DateTime.Now,
                LoanAmount = 9200,
                InterestRate = 5,
                TenureDetails = new LoanLineTenure
                {
                    Duration = new Period { Value = 1, Unit = EnumTenureUnit.Year }
                }
            };

            var productLineSchedule = new LoanProductLineSchedule
            {
                LoanProductLineItemId = "Productline-01",
                NumberOfInstallments = 10,
                ExamplePrincipal = 1000,
                Tenure = new ScheduleTunure { Value = 1, Unit = EnumTenureUnit.Year },
                ExcessAmount = 0.5,
                InstallmentBreakdown = new List<InstallmentBreakdown>
                {
                    new()
                    {
                        StartInstallment = 1,
                        EndInstallment = 8,
                        PrincipalAmount = 50,
                        ServiceCharge = 3
                    },
                    new()
                    {
                        StartInstallment = 9,
                        EndInstallment = 10,
                        PrincipalAmount = 300,
                        ServiceCharge = 10
                    }
                }
            };

            var member = new BuroMember
            {
                ItemId = "member-1",
                CenterItemId = "center-1"
            };

            var center = new BuroCenter
            {
                ItemId = "center-1",
                CenterDay = DayOfWeek.Monday
            };

            var holiday = new BuroHoliday
            {
                ItemId = "holiday-1",
                HolidayType = EnumHolidayType.CenterSpecific,
                CenterItemId = "center-1",
                HolidayStartDate = DateTime.UtcNow,
                HolidayEndDate = DateTime.UtcNow,
                IsActive = true
            };

            var weekend = new BuroWeekend
            {
                ItemId = "weekend",
                DaysOfWeek = new List<DayOfWeek> { DayOfWeek.Friday, DayOfWeek.Saturday }
            };

            SetupRepositoryGetItemAsync(new List<BuroLoanAccount> { account });
            SetupRepositoryGetItemAsync(new List<BuroLoanApplication> { application });
            SetupRepositoryGetItemAsync(new List<BuroLoanApplicationDisbursement> { disbursement });
            SetupRepositoryGetItemAsync(new List<BuroMember> { member });
            SetupRepositoryGetItemAsync(new List<BuroCenter> { center });
            SetupRepositoryGetItemAsync(new List<LoanProductLineSchedule> { productLineSchedule });
            SetupFinMongoDbRepositoryFindWithOptionAsync(new List<BuroHoliday> { holiday });
            SetupFinMongoDbRepositoryFindAsync(new List<BuroWeekend> { weekend });
            SetupRepositorySaveAsync();

            _helperServiceMock.Setup(x => x.GetHolidaySetAsync(It.IsAny<DateTime>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                              .ReturnsAsync(new HashSet<DateTime>());

            // Act
            await _service.GenerateLoanAccountSchedule(command);

            // Assert
            _helperServiceMock.Verify(helper => helper.GetHolidaySetAsync(It.IsAny<DateTime>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()), Times.Once());

            MockRepository.Verify(
                repo => repo.SaveAsync(It.IsAny<List<BuroMemberSchedule>>()),
                Times.Once
            );
        }
    }
}
