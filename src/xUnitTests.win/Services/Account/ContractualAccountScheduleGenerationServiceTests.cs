using EventHandlers.Contracts;
using EventHandlers.Services.Account;
using Infrastructure.Contracts;
using Infrastructure.Events.Account;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using xUnitTests.win.Helpers;

namespace xUnitTests.win.Services.Account
{
    public class ContractualAccountScheduleGenerationServiceTests : MockServiceProviderBase<ContractualAccountScheduleGenerationService>
    {
        private readonly ContractualAccountScheduleGenerationService _service;
        private readonly Mock<IWorkingDayService> MockWorkingService;
        protected Mock<IInfrastructureSharedService> MockInfrastructureSharedService;

        public ContractualAccountScheduleGenerationServiceTests()
        {
            MockWorkingService = new Mock<IWorkingDayService>();
            MockInfrastructureSharedService = new Mock<IInfrastructureSharedService>();

            _service = new ContractualAccountScheduleGenerationService(
                MockLogger.Object,
                MockFinMongoDbRepository.Object,
                MockWorkingService.Object,
                MockSecurityContextProvider.Object,
                MockInfrastructureSharedService.Object,
                MocServiceClient.Object);
        }

        [Fact]
        public async Task GenerateContractualAccountSchedule_WhenSchedulesGenerated_ShouldSaveSchedules()
        {
            // Arrange
            var command = new ContractualAccountScheduleGenerationEvent
            {
                ContractualAccountItemId = "account-1"
            };

            var account = new BuroContractualSavingsAccount
            {
                ItemId = "account-1",
                MemberItemId = "member-1",
                PaymentInterval = EnumPaymentInterval.Monthly,
                InstalmentAmount = 100,
                OriginalTenureDetails = new Tenure { NumberOfInstallment = 3 }
            };

            var member = new BuroMember
            {
                ItemId = "member-1",
                CenterItemId = "center-1"
            };

            var center = new BuroCenter
            {
                ItemId = "center-1",
                CenterDay = DayOfWeek.Monday
            };

            var holiday = new BuroHoliday
            {
                ItemId = "holiday-1",
                HolidayType = EnumHolidayType.CenterSpecific,
                CenterItemId = "center-1",
                HolidayStartDate = DateTime.UtcNow,
                HolidayEndDate = DateTime.UtcNow,
                IsActive = true
            };

            var weekend = new BuroWeekend
            {
                ItemId = "weekend",
                DaysOfWeek = new List<DayOfWeek> { DayOfWeek.Friday, DayOfWeek.Saturday }
            };

            SetupFindOneAsync(new List<BuroContractualSavingsAccount> { account });
            SetupFindOneAsync(new List<BuroMember> { member });
            SetupFindOneAsync(new List<BuroCenter> { center });
            SetupFinMongoDbRepositoryFindWithOptionAsync(new List<BuroHoliday> { holiday });
            SetupFinMongoDbRepositoryFindAsync(new List<BuroWeekend> { weekend });
            SetupRepositorySaveAsync();
            SetupRepositoryUpdateAsync<BuroContractualSavingsAccount>();

            MockWorkingService.Setup(x => x.GetHolidaySetAsync(It.IsAny<DateTime>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                              .ReturnsAsync(new HashSet<DateTime>());

            MockWorkingService.Setup(x => x.IsHolidayAsync(It.IsAny<DateTime>(), It.IsAny<HashSet<DateTime>>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
                  .ReturnsAsync((DateTime date, HashSet<DateTime> set, string a, string b, string c) => set.Contains(date.Date));

            MockWorkingService.Setup(x => x.IsWeekendAsync(It.IsAny<DateTime>(), It.IsAny<BuroWeekend>()))
                              .ReturnsAsync(false); // assuming no weekend for simplicity


            // Act
            await _service.GenerateContractualAccountSchedule(command);

            // Assert

            MockWorkingService.Verify(
                helper => helper.GetHolidaySetAsync(It.IsAny<DateTime>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()),
                Times.Once()
            );

            MockRepository.Verify(
                repo => repo.SaveAsync(It.IsAny<List<BuroMemberSchedule>>()),
                Times.Once
            );

            MockRepository.Verify(repo => repo.UpdateAsync(
                It.IsAny<System.Linq.Expressions.Expression<Func<BuroContractualSavingsAccount, bool>>>(),
                It.IsAny<IDictionary<string, object>>()), Times.Once);

        }
    }
}
