using EventHandlers.Services.EmployeeHistory;
using Infrastructure.Events.EmployeeHistory;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.win.Helpers;

namespace xUnitTests.win.Services.EmployeeHistory;

public class CreateEmployeeAssignmentHistoryServiceTests : BaseTest
{
    private readonly Mock<ISecurityContextProvider> _securityContextProvider;
    private readonly CreateEmployeeAssignmentHistoryService _service;

    public CreateEmployeeAssignmentHistoryServiceTests()
    {
        _securityContextProvider = new Mock<ISecurityContextProvider>();
        _service = new CreateEmployeeAssignmentHistoryService(_mockRepository.Object, _securityContextProvider.Object);
        SeedMockData();
    }

    [Fact]
    public async Task CreateEmployeeTransitionHistory_WhenSuccessful_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = PopulateCreateEmployeeAssignmentHistoryEvent();
        // Act
        var result = await _service.CreateEmployeeAssignmentHistory(command);

        // Assert
        Assert.True(result.Errors.IsValid);
    }

    private CreateEmployeeAssignmentHistoryEvent PopulateCreateEmployeeAssignmentHistoryEvent()
    {
        return new CreateEmployeeAssignmentHistoryEvent
        {
            EmployeeItemId = "EmployeeItemId",
            UserItemId = "UserItemId",
            CenterTitle = "CenterTitle",
            CenterItemId = "CenterItemId",
            OfficeItemId = "OfficeItemId",
            OfficeTitle = "OfficeTitle",
            OfficeCode = "OfficeCode",
            MemberCount = 2,
            StartDate = DateTime.UtcNow.AddYears(-1),
            EndDate = DateTime.UtcNow
        };
    }

    private void SeedMockData()
    {
        var list = new List<BuroCenterAssignmentHistory>()
        {
            new BuroCenterAssignmentHistory()
            {
                ItemId = Guid.NewGuid().ToString(),
                EmployeeItemId = "EmployeeItemId",
                CenterTitle = "CenterTitle",
                CenterItemId = "CenterItemId",
                OfficeItemId = "OfficeItemId",
                OfficeTitle = "OfficeTitle",
                OfficeCode = "OfficeCode",
                MemberCount = 2,
                StartDate = DateTime.UtcNow.AddYears(-1),
                EndDate = DateTime.UtcNow
            }
        };
        SetupRepositoryMock(list);
    }
}