using EventHandlers.Services.EmployeeHistory;
using Infrastructure.Events.EmployeeHistory;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.win.Helpers;

namespace xUnitTests.win.Services.EmployeeHistory;

public class CreateEmployeeTransferHistoryServiceTests : BaseTest
{
    private readonly Mock<ISecurityContextProvider> _securityContextProvider;
    private readonly CreateEmployeeTransferHistoryService _service;

    public CreateEmployeeTransferHistoryServiceTests()
    {
        _securityContextProvider = new Mock<ISecurityContextProvider>();
        _service = new CreateEmployeeTransferHistoryService(_mockRepository.Object, _securityContextProvider.Object);
        SeedMockData();
    }

    [Fact]
    public async Task CreateEmployeeTransitionHistory_WhenSuccessful_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = PopulateCreateEmployeeTransitionCommand();
        // Act
        var result = await _service.CreateEmployeeTransferHistory(command);

        // Assert
        Assert.True(result.Errors.IsValid);
    }

    private CreateEmployeeTransferHistoryEvent PopulateCreateEmployeeTransitionCommand()
    {
        return new CreateEmployeeTransferHistoryEvent
        {
            EmployeeItemId = "EmployeeItemId",
            DesignationItemId = "DesignationItemId",
            DesignationTitle = "DesignationTitle",
            DesignationCode = "DesignationCode",
            OfficeItemId = "OfficeItemId",
            OfficeTitle = "OfficeTitle",
            OfficeCode = "OfficeCode",
            MemberCount = 2,
            StartDate = DateTime.UtcNow.AddYears(-1),
            EndDate = DateTime.UtcNow,
            TransferHistoryType = EnumEmployeeTransferHistoryType.DesignationChange
        };
    }

    private void SeedMockData()
    {
        var list = new List<BuroEmployeeTransferHistory>()
        {
            new BuroEmployeeTransferHistory()
            {
                ItemId = Guid.NewGuid().ToString(),
                EmployeeItemId = "EmployeeItemId",
                DesignationItemId = "DesignationItemId",
                DesignationTitle = "DesignationTitle",
                DesignationCode = "DesignationCode",
                OfficeItemId = "OfficeItemId",
                OfficeTitle = "OfficeTitle",
                OfficeCode = "OfficeCode",
                MemberCount = 2,
                StartDate = DateTime.UtcNow.AddYears(-1),
                EndDate = DateTime.UtcNow,
                TransferHistoryType = EnumEmployeeTransferHistoryType.DesignationChange

            }
        };
        SetupRepositoryMock(list);
    }
}