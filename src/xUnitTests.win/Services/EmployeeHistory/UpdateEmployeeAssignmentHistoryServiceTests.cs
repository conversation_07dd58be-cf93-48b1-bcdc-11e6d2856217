using EventHandlers.Services.EmployeeHistory;
using Infrastructure.Events.EmployeeHistory;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using xUnitTests.win.Helpers;

namespace xUnitTests.win.Services.EmployeeHistory;

public class UpdateEmployeeAssignmentHistoryServiceTests : BaseTest
{
    private readonly UpdateEmployeeAssignmentHistoryService _service;

    public UpdateEmployeeAssignmentHistoryServiceTests()
    {
        _service = new UpdateEmployeeAssignmentHistoryService(_mockRepository.Object);
        SeedMockData();
    }

    [Fact]
    public async Task UpdateEmployeeCenterHistory_SuccessfulUpdate_ReturnsExpectedResult()
    {
        // Arrange
        var command = PopulateUpdateEmployeeAssignmentHistoryEvent();
        // Act
        await _service.UpdateEmployeeAssignmentHistory(command);

        // Assert
        var updatedItem = _mockRepository.Object.GetItem<BuroCenterAssignmentHistory>(item =>
            item.EmployeeItemId == command.EmployeeItemId && item.CenterItemId == command.CenterItemId);
        Assert.Equal(command.EndDate, updatedItem.EndDate);
    }

    private UpdateEmployeeAssignmentHistoryEvent PopulateUpdateEmployeeAssignmentHistoryEvent()
    {
        return new UpdateEmployeeAssignmentHistoryEvent
        {
            EmployeeItemId = "EmployeeItemId",
            CenterItemId = "CenterItemId",
            EndDate = DateTime.UtcNow
        };
    }

    private void SeedMockData()
    {
        var list = new List<BuroCenterAssignmentHistory>
        {
            new()
            {
                ItemId = Guid.NewGuid().ToString(),
                EmployeeItemId = "EmployeeItemId",
                CenterTitle = "CenterTitle",
                CenterItemId = "CenterItemId",
                MemberCount = 2,
                StartDate = DateTime.UtcNow.AddYears(-1)
            },
            new()
            {
                ItemId = Guid.NewGuid().ToString(),
                EmployeeItemId = "EmployeeItemId2",
                CenterTitle = "CenterTitle2",
                CenterItemId = "CenterItemId2",
                MemberCount = 2,
                StartDate = DateTime.UtcNow.AddYears(-1)
            }
        };
        SetupRepositoryMock(list);
    }
}