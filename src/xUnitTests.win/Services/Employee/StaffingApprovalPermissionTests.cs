using EventHandlers.Employee.StaffingApprovalPermission;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.win.Helpers;

namespace xUnitTests.win.Services.Employee
{
    public class StaffingApprovalPermissionTests : BaseTest
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly INotificationServiceClient _notificationServiceClient;
        private readonly StaffingApprovalPermissionService _staffingApprovalPermissionService;
        private readonly ILogger<StaffingApprovalPermissionService> _logger;

        public StaffingApprovalPermissionTests()
        {
            _logger = Mock.Of<ILogger<StaffingApprovalPermissionService>>();
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();
            _notificationServiceClient = Mock.Of<INotificationServiceClient>();
            _staffingApprovalPermissionService = new StaffingApprovalPermissionService(_mockRepository.Object, _notificationServiceClient, _logger);
        }

        private void SeedOfficeMockData()
        {
            var mockOffices = new List<BuroOffice>
            {
                new()
                {
                    ItemId = "B1",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z1",
                    AreaOfficeItemId = "A1",
                    BranchOfficeItemId = "B1"
                },
                new()
                {
                    ItemId = "B2",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z1",
                    AreaOfficeItemId = "A1",
                    BranchOfficeItemId = "B2"
                },
                new()
                {
                    ItemId = "B3",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z1",
                    AreaOfficeItemId = "A2",
                    BranchOfficeItemId = "B3"
                },
                new()
                {
                    ItemId = "B4",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z1",
                    AreaOfficeItemId = "A2",
                    BranchOfficeItemId = "B4"
                },
                new()
                {
                    ItemId = "B5",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z2",
                    AreaOfficeItemId = "A3",
                    BranchOfficeItemId = "B5"
                },
                new()
                {
                    ItemId = "B6",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z2",
                    AreaOfficeItemId = "A3",
                    BranchOfficeItemId = "B6"
                },
                new()
                {
                    ItemId = "B7",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z2",
                    AreaOfficeItemId = "A4",
                    BranchOfficeItemId = "B7"
                },
                new()
                {
                    ItemId = "B8",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z2",
                    AreaOfficeItemId = "A4",
                    BranchOfficeItemId = "B8"
                },
                new()
                {
                    ItemId = "B10",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z3",
                    AreaOfficeItemId = "A5",
                    BranchOfficeItemId = "B11"
                },
                new()
                {
                    ItemId = "B11",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z3",
                    AreaOfficeItemId = "A5",
                    BranchOfficeItemId = "B11"
                },
                new()
                {
                    ItemId = "B12",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z3",
                    AreaOfficeItemId = "A6",
                    BranchOfficeItemId = "B12"
                },
                new()
                {
                    ItemId = "B13",
                    OfficeType = EnumOfficeType.BranchOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z3",
                    AreaOfficeItemId = "A6",
                    BranchOfficeItemId = "B13"
                },
                new()
                {
                    ItemId = "A1",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z1",
                    AreaOfficeItemId = "A1",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "A2",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z1",
                    AreaOfficeItemId = "A2",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "A3",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z2",
                    AreaOfficeItemId = "A3",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "A4",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z2",
                    AreaOfficeItemId = "A4",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "A5",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z3",
                    AreaOfficeItemId = "A5",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "A6",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z3",
                    AreaOfficeItemId = "A6",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "A7",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z4",
                    AreaOfficeItemId = "A7",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "A8",
                    OfficeType = EnumOfficeType.AreaOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z4",
                    AreaOfficeItemId = "A8",
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "Z1",
                    OfficeType = EnumOfficeType.ZoneOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z1",
                    AreaOfficeItemId = null,
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "Z2",
                    OfficeType = EnumOfficeType.ZoneOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = "Z2",
                    AreaOfficeItemId = null,
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "Z3",
                    OfficeType = EnumOfficeType.ZoneOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z3",
                    AreaOfficeItemId = null,
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "Z4",
                    OfficeType = EnumOfficeType.ZoneOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = "Z4",
                    AreaOfficeItemId = null,
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "D1",
                    OfficeType = EnumOfficeType.DivisionOffice,
                    DivisionOfficeItemId = "D1",
                    ZoneOfficeItemId = null,
                    AreaOfficeItemId = null,
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "D2",
                    OfficeType = EnumOfficeType.DivisionOffice,
                    DivisionOfficeItemId = "D2",
                    ZoneOfficeItemId = null,
                    AreaOfficeItemId = null,
                    BranchOfficeItemId = null
                },
                new()
                {
                    ItemId = "HQ",
                    OfficeType = EnumOfficeType.HeadOffice,
                    DivisionOfficeItemId = null,
                    ZoneOfficeItemId = null,
                    AreaOfficeItemId = null,
                    BranchOfficeItemId = null
                }
            };
            SetupRepositoryMock(mockOffices);
        }

        private void SeedEmployeeMockData()
        {
            var employees = new List<BuroEmployee>
            {

            };
        }

        [Fact]
        public async Task StaffingApprovalPermission_ValidateApprovalFromOffices_BranchToBranchValidatedSuccessfully()
        {
            //arrange
            var expectedItemIds = new List<string>
            {
                "B1","B5","A1","A3","D1","Z1","Z2",
            };

            var sourceOffice = new BuroOffice
            {
                ItemId = "B1",
                OfficeType = EnumOfficeType.BranchOffice,
                DivisionOfficeItemId = "D1",
                ZoneOfficeItemId = "Z1",
                AreaOfficeItemId = "A1",
                BranchOfficeItemId = "B1"
            };


            var destinationOffice = new BuroOffice
            {
                ItemId = "B5",
                OfficeType = EnumOfficeType.BranchOffice,
                DivisionOfficeItemId = "D1",
                ZoneOfficeItemId = "Z2",
                AreaOfficeItemId = "A3",
                BranchOfficeItemId = "B5"
            };

            //act
            var outputData = await StaffingApprovalPermissionService.GetAllOfficesNeededForApproval(sourceOffice, destinationOffice);
            //assert
            //Assert.Equal(expectedItemIds, outputData);
            Assert.Equal(
                expectedItemIds.OrderBy(x => x).ToList(),
                outputData.OrderBy(x => x).ToList()
            );
        }
    }
}
