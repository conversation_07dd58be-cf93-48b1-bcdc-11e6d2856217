using Moq;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Linq.Expressions;

namespace xUnitTests.win.Helpers
{
    public class BaseTest
    {
        protected readonly Mock<IRepository> _mockRepository;

        public BaseTest()
        {
            _mockRepository = new Mock<IRepository>();
        }

        public void SetupRepositoryMock<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.GetItems(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> filter) => mockData.AsQueryable().Where(filter));

            _mockRepository.Setup(repo => repo.GetItems<T>())
                .Returns(mockData.AsQueryable());

            _mockRepository.Setup(repo => repo.GetItem(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> filter) => mockData.AsQueryable().FirstOrDefault(filter) ?? default!);

            _mockRepository.Setup(repo => repo.Save(It.IsAny<T>(), It.IsAny<string>()))
                .Callback((T item, string collection) => mockData.Add(item));

            _mockRepository.Setup(repo => repo.Save(It.IsAny<List<T>>()))
                .Callback((List<T> items) => mockData.AddRange(items));

            _mockRepository.Setup(repo => repo.Update(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<T>()))
                .Callback((Expression<Func<T, bool>> filter, T updatedItem) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null)
                    {
                        var index = mockData.IndexOf(item);
                        mockData[index] = updatedItem;
                    }
                });

            _mockRepository.Setup(repo => repo.Delete(It.IsAny<Expression<Func<T, bool>>>()))
                .Callback((Expression<Func<T, bool>> filter) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null) mockData.Remove(item);
                });

            _mockRepository.Setup(repo => repo.SaveAsync(It.IsAny<T>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask)
                .Callback((T item, string collection) => mockData.Add(item));

            _mockRepository.Setup(repo => repo.GetItemAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((Expression<Func<T, bool>> filter) => mockData.AsQueryable().FirstOrDefault(filter) ?? default!);

            _mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<T>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter, T updatedItem) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null)
                    {
                        var index = mockData.IndexOf(item);
                        mockData[index] = updatedItem;
                    }
                });

            _mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null) mockData.Remove(item);
                });

            _mockRepository.Setup(repo => repo.GetItemAsync<T>(It.IsAny<string>(), It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((string tenantId, Expression<Func<T, bool>> filter) =>
                    mockData.AsQueryable().FirstOrDefault(filter) ?? default!);

            _mockRepository.Setup(repo => repo.SaveAsync<T>(It.IsAny<string>(), It.IsAny<T>()))
                .Returns(Task.CompletedTask)
                .Callback((string tenantId, T item) => mockData.Add(item));

            _mockRepository.Setup(repo => repo.SaveAsync(It.IsAny<List<T>>()))
                .Returns(Task.CompletedTask)
                .Callback((List<T> items) => mockData.AddRange(items));

            _mockRepository.Setup(repo => repo.SaveExpectingFailuresAsync(It.IsAny<List<T>>()))
                .ReturnsAsync(Array.Empty<BulkWriteError>());

            _mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<IDictionary<string, object>>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter, IDictionary<string, object> updates) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null)
                    {
                        foreach (var update in updates)
                        {
                            var propertyInfo = item.GetType().GetProperty(update.Key);
                            if (propertyInfo != null && propertyInfo.CanWrite)
                            {
                                propertyInfo.SetValue(item, update.Value);
                            }
                        }
                    }
                });

            _mockRepository.Setup(repo => repo.UpdateManyAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<IDictionary<string, object>>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter, IDictionary<string, object> updates) =>
                {
                    var items = mockData.AsQueryable().Where(filter).ToList();
                    foreach (var item in items)
                    {
                        foreach (var update in updates)
                        {
                            var propertyInfo = item.GetType().GetProperty(update.Key);
                            if (propertyInfo != null && propertyInfo.CanWrite)
                            {
                                propertyInfo.SetValue(item, update.Value);
                            }
                        }
                    }
                });

            _mockRepository.Setup(repo => repo.ExistsAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((Expression<Func<T, bool>> filter) =>
                    mockData.AsQueryable().Any(filter));
        }
    }
}
