using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Linq.Expressions;

namespace xUnitTests.Helpers
{
    public abstract class MockServiceProviderBase<TService>
    {
        protected Mock<IRepository> _mockRepository { get; }
        protected Mock<ISecurityContextProvider> _mockSecurityContextProvider { get; }
        protected Mock<ILogger<TService>> _mockLogger { get; }
        protected Mock<ISequenceNumberClient> _mockSequenceNumberClient { get; }

        protected MockServiceProviderBase()
        {
            _mockRepository = new Mock<IRepository>();
            _mockSecurityContextProvider = new Mock<ISecurityContextProvider>();
            _mockLogger = new Mock<ILogger<TService>>();
            _mockSequenceNumberClient = new Mock<ISequenceNumberClient>();
        }

        protected void SetupRepositoryGetItemAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.GetItemAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> filter) => Task.FromResult(mockData.AsQueryable().FirstOrDefault(filter) ?? default!));
        }

        protected void SetupRepositoryExistsAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.ExistsAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((Expression<Func<T, bool>> filter) => mockData.AsQueryable().Any(filter));
        }


        protected void SetupRepositorySaveAsync()
        {
            _mockRepository.Setup(repo => repo.SaveAsync(It.IsAny<object>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask);
        }

        protected void SetupRepositoryUpdateAsync<T>() where T : class
        {
            _mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<IDictionary<string, object>>()))
                .Returns(Task.CompletedTask);
        }

        protected void SetupMockSequenceNumberClient()
        {
            _mockSequenceNumberClient.Setup(client => client.GetSequenceNumber(It.IsAny<SequenceNumberQuery>()))
                .ReturnsAsync(new SequenceNumberQueryResponse { CurrentNumber = 1 });
        }

        protected void SetupMockSecurityContextProvider(string userId = "user_id_01", IEnumerable<string>? roles = null)
        {
            var securityContext = CreateMockSecurityContext(userId, roles ?? new List<string> { "Role1", "Role2" });

            _mockSecurityContextProvider
                .Setup(provider => provider.GetSecurityContext())
                .Returns(securityContext);
        }

        private static SecurityContext CreateMockSecurityContext(string userId, IEnumerable<string> roles)
        {
            return new SecurityContext(
                userName: "testUser",
                roles: roles.ToArray(),
                tenantId: "tenant-123",
                oauthBearerToken: "sample_oauth_bearer_token",
                isAuthenticated: true,
                isUserAuthenticated: true,
                displayName: "Delower Hosen",
                userId: userId,
                requestOrigin: "burobd.seliselocal.com",
                siteName: "burobd.seliselocal.com",
                siteId: "site-123",
                email: "<EMAIL>",
                language: "en",
                phoneNumber: "123456789",
                sessionId: "session-123",
                requestUri: new Uri("https://test.com"),
                serviceVersion: "v1",
                hasDynamicRoles: false,
                tokenHijackingProtectionHash: string.Empty,
                userAutoExpire: false,
                userExpireOn: DateTime.UtcNow.AddHours(1),
                userPrefferedLanguage: "en",
                postLogOutHandlerDataKey: string.Empty,
                organizationId: "org-123"
            );
        }
    }
}
