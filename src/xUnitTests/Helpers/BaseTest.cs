using FinMongoDbRepositories;
using Infrastructure.Contracts;
using Infrastructure.Models;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Driver;
using Moq;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Linq.Expressions;
using System.Reflection;

namespace xUnitTests.Helpers
{
    public class BaseTest
    {
        protected readonly Mock<IRepository> _mockRepository;
        protected readonly Mock<ISecurityContextProvider> _mockSecurityContextProvider;
        protected readonly Mock<IServiceClient> _mockServiceClient;
        protected readonly Mock<IFinMongoDbRepository> _mockFinMongodbRepository;
        protected readonly Mock<ISequenceNumberClient> _mockSequenceNumberClient;
        protected readonly Mock<INotificationServiceClient> _mocksNotificationServiceClient;

        public BaseTest()
        {
            _mockRepository = new Mock<IRepository>();
            _mockSecurityContextProvider = new Mock<ISecurityContextProvider>();
            _mockServiceClient = new Mock<IServiceClient>();
            _mockFinMongodbRepository = new Mock<IFinMongoDbRepository>();
            _mockSequenceNumberClient = new Mock<ISequenceNumberClient>();
            _mocksNotificationServiceClient = new Mock<INotificationServiceClient>();
        }

        #region Setup

        public void SetupFindAsync<T>(List<T> mockData)
        {
            _mockFinMongodbRepository
                .Setup(repo => repo.FindAsync(It.IsAny<FilterDefinition<T>>(), It.IsAny<FindOptions>(), true))
                .ReturnsAsync(mockData);
        }
        
        public void SetupFindOneAsync<T>(List<T> mockData)
        {
            _mockFinMongodbRepository
                .Setup(repo => repo.FindOneAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<FindOptions>(), true))
                .ReturnsAsync((Expression<Func<T, bool>> expression, FindOptions options, bool applyHierarchy) =>
                {
                    return mockData.AsQueryable().FirstOrDefault(expression) ?? default!;
                });
        }

        public void SetupFindAsyncWithOption<T>(List<T> mockData)
        {
            _mockFinMongodbRepository
                .Setup(repo => repo.FindWithOptionAsync(It.IsAny<FilterDefinition<T>>(), It.IsAny<FindOptions<T, T>>(), true))
                .ReturnsAsync(mockData);
        }

        public void SetupFindWithProjectionAsync<T, TResult>(List<T> mockData, List<TResult>? mockResultData)
        {
            _mockFinMongodbRepository.Setup(repo => repo.FindWithProjectionAsync(
                    It.IsAny<Expression<Func<T, bool>>>(),
                    It.IsAny<Expression<Func<T, TResult>>>(),
                    It.IsAny<FindOptions>(),
                    It.IsAny<bool>()))
                .ReturnsAsync((
                    Expression<Func<T, bool>>? expression,
                    Expression<Func<T, TResult>>? projections,
                    FindOptions findOPtion,
                    bool applyHierarchy) =>
                {
                    if (mockData != null)
                    {
                        return mockResultData?.AsQueryable().ToList() ?? default!;
                    }

                    return new List<TResult>();
                });
        }


        public void SetupRepositoryMock<T>(List<T> mockData) where T : class
        {
            SetupGetItems(mockData);
            SetupAllGetItems(mockData);
            SetupGetItem(mockData);
            SetupSave(mockData);
            SetupSetup(mockData);
            SetupUpdate(mockData);
            SetupDelete(mockData);
            SetupDeleteAsync(mockData);
            SetupGetItemAsyncWithTenant(mockData);
            SetupGetItemAsync(mockData);
            SetupSaveAsync(mockData);
            SetupSaveAsyncWithTenant(mockData);
            SetupUpdateAsyncWithProperties(mockData);
            SetupUpdateAsync(mockData);
            SetupUpdateManyAsync(mockData);
            SetupExistsAsync(mockData);
            SetupFindAsync(mockData);
            SetupFindAsyncWithOption(mockData);
        }
        #endregion

        #region Public Setup Methods
        public void SetupAdditionalMockServices()
        {
            SetupMockServiceClient();
            SetupSequenceNumberClient();
            SetupNotificationServiceClient();
        }

        public void SetupGetItems<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.GetItems(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> filter) => mockData.AsQueryable().Where(filter));
        }

        public void SetupAllGetItems<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.GetItems<T>())
                .Returns(mockData.AsQueryable());
        }

        public void SetupGetItem<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.GetItem(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> filter) => mockData.AsQueryable().FirstOrDefault(filter) ?? default!);
        }

        public void SetupSave<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.Save(It.IsAny<T>(), It.IsAny<string>()))
                .Callback((T item, string collection) => mockData.Add(item));
        }

        public void SetupSetup<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.Save(It.IsAny<List<T>>()))
                .Callback((List<T> items) => mockData.AddRange(items));
        }

        public void SetupUpdate<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.Update(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<T>()))
                .Callback((Expression<Func<T, bool>> filter, T updatedItem) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null)
                    {
                        var index = mockData.IndexOf(item);
                        mockData[index] = updatedItem;
                    }
                });
        }

        public void SetupDelete<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.Delete(It.IsAny<Expression<Func<T, bool>>>()))
                .Callback((Expression<Func<T, bool>> filter) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null) mockData.Remove(item);
                });
        }

        public void SetupGetItemAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.GetItemAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((Expression<Func<T, bool>> filter) => mockData.AsQueryable().FirstOrDefault(filter) ?? default!);
        }

        public void SetupDeleteAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.DeleteAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null) mockData.Remove(item);
                });
        }

        public void SetupGetItemAsyncWithTenant<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.GetItemAsync<T>(It.IsAny<string>(), It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((string tenantId, Expression<Func<T, bool>> filter) =>
                mockData.AsQueryable().FirstOrDefault(filter) ?? default!);
        }

        public void SetupSaveAsyncWithTenant<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.SaveAsync<T>(It.IsAny<string>(), It.IsAny<T>()))
                .Returns(Task.CompletedTask)
                .Callback((string tenantId, T item) => mockData.Add(item));
        }

        public void SetupSaveAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.SaveAsync(It.IsAny<T>(), It.IsAny<string>()))
                .Returns(Task.CompletedTask)
                .Callback((T item, string collection) => mockData.Add(item));

            _mockRepository.Setup(repo => repo.SaveAsync(It.IsAny<List<T>>()))
                .Returns(Task.CompletedTask)
                .Callback((List<T> items) => mockData.AddRange(items));
        }

        public void SetupUpdateAsyncWithProperties<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<IDictionary<string, object>>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter, IDictionary<string, object> updates) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null)
                    {
                        SetUpdateProperties(item, updates);
                    }
                });
        }

        public void SetupUpdateAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.UpdateAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<T>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter, T updatedItem) =>
                {
                    var item = mockData.AsQueryable().FirstOrDefault(filter);
                    if (item != null)
                    {
                        var index = mockData.IndexOf(item);
                        mockData[index] = updatedItem;
                    }
                });
        }

        public void SetupUpdateManyAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.UpdateManyAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<IDictionary<string, object>>()))
                .Returns(Task.CompletedTask)
                .Callback((Expression<Func<T, bool>> filter, IDictionary<string, object> updates) =>
                {
                    var items = mockData.AsQueryable().Where(filter).ToList();
                    foreach (var item in items)
                    {
                        SetUpdateProperties(item, updates);
                    }
                });
        }

        public void SetupExistsAsync<T>(List<T> mockData) where T : class
        {
            _mockRepository.Setup(repo => repo.ExistsAsync(It.IsAny<Expression<Func<T, bool>>>()))
                .ReturnsAsync((Expression<Func<T, bool>> filter) =>
                    mockData.AsQueryable().Any(filter));
        }

        public void SetupMockSecurityContext(string userId)
        {
            var securityContext = CreateMockSecurityContext(userId);

            _mockSecurityContextProvider.Setup(x => x.GetSecurityContext())
                .Returns(securityContext);
        }

        #endregion

        #region Private Methods
        private void SetupMockServiceClient()
        {
            _mockServiceClient.Setup(s => s.SendToQueue<bool>(It.IsAny<string>(), It.IsAny<object>()))
                .Returns(true);
        }

        private void SetupSequenceNumberClient()
        {
            _mockSequenceNumberClient.Setup(client => client.GetSequenceNumber(It.IsAny<SequenceNumberQuery>()))
                .ReturnsAsync(new SequenceNumberQueryResponse { CurrentNumber = 1 });
        }

        private void SetupNotificationServiceClient()
        {
            _mocksNotificationServiceClient.Setup(client => client.NotifyAsync(It.IsAny<NotifierPayloadWithResponse>(), It.IsAny<bool>()))
                .ReturnsAsync(new HttpResponseMessage { StatusCode = System.Net.HttpStatusCode.OK });
        }

        private static SecurityContext CreateMockSecurityContext(string userId)
        {
            return new(
                userName: string.Empty,
                roles: Enumerable.Empty<string>(),
                tenantId: string.Empty,
                oauthBearerToken: "sample_oauth_bearer_token",
                isAuthenticated: false,
                isUserAuthenticated: false,
                displayName: "display_name",
                userId: userId,
                requestOrigin: string.Empty,
                siteName: string.Empty,
                siteId: string.Empty,
                email: string.Empty,
                language: string.Empty,
                phoneNumber: string.Empty,
                sessionId: string.Empty,
                requestUri: new Uri("about:blank"),
                serviceVersion: string.Empty,
                hasDynamicRoles: false,
                tokenHijackingProtectionHash: string.Empty,
                userAutoExpire: false,
                userExpireOn: DateTime.MinValue,
                userPrefferedLanguage: string.Empty,
                postLogOutHandlerDataKey: string.Empty,
                organizationId: string.Empty
            );
        }

        private static void SetUpdateProperties<T>(T item, IDictionary<string, object> updates) where T : class
        {
            foreach (var update in updates)
            {
                var propertyInfo = item.GetType().GetProperty(update.Key);

                if (propertyInfo != null && propertyInfo.CanWrite)
                {
                    if (update.Value is BsonArray bsonArrayData)
                    {
                        propertyInfo.SetValue(item, GetGenericListOfDataFromBsonArray(propertyInfo, bsonArrayData));
                    }
                    else if (update.Value is BsonDocument bsonDocumentData)
                    {
                        propertyInfo.SetValue(item, GetOriginalDataFromBsonDocument(propertyInfo, bsonDocumentData));
                    }
                    else
                    {
                        propertyInfo.SetValue(item, update.Value);
                    }
                }
            }
        }

        private static object? GetOriginalDataFromBsonDocument(
            PropertyInfo propertyInfo,
            BsonDocument bsonDocumentData)
        {
            return BsonSerializer.Deserialize(bsonDocumentData, propertyInfo.PropertyType);
        }

        private static object? GetGenericListOfDataFromBsonArray(
            PropertyInfo propertyInfo,
            BsonArray bsonArrayData)
        {
            var basePropertyType = propertyInfo.PropertyType.GetGenericArguments()[0];

            var deserializedData = bsonArrayData
                .Select(b => BsonSerializer.Deserialize(b.ToBsonDocument(), basePropertyType))
                .ToList();

            var requiredListOfData = MakeListFromBsonArray(basePropertyType, deserializedData);

            return requiredListOfData;
        }

        private static object? MakeListFromBsonArray(
            Type basePropertyType,
            List<object> deserializedData)
        {
            var listType = typeof(List<>).MakeGenericType(basePropertyType);
            var addMethodInfo = listType.GetMethod("Add");
            var requiredListOfData = Activator.CreateInstance(listType);

            foreach (var item in deserializedData)
            {
                addMethodInfo?.Invoke(requiredListOfData, new[] { item });
            }

            return requiredListOfData;
        }
        #endregion
    }
}
