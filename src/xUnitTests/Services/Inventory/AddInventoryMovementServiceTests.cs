using Domain.Commands.Inventory;
using Domain.Contracts.Inventory;
using Domain.Services.Inventory;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Inventory;

public class AddInventoryMovementServiceTests : BaseTest
{
    private readonly AddInventoryMovementService _service;
    private readonly Mock<IInventoryStockManagementService> _inventoryStockManagementService;
    public AddInventoryMovementServiceTests()
    {
        _inventoryStockManagementService = new Mock<IInventoryStockManagementService>();
        _service = new AddInventoryMovementService(_mockRepository.Object, _mockSecurityContextProvider.Object, _inventoryStockManagementService.Object);
        SeedMockData();
        SetupMockSecurityContext("001");
    }

    private void SeedMockData()
    {
        var inventoryMovements = new List<BuroInventoryMovement>();
        var employees = new List<BuroEmployee>()
        {
            new()
            {
                ItemId = "001",
                EmployeeName = "Name001",
                UserItemId = "001",
                CurrentOfficeItemId = "001",
                CurrentOfficeTitle = "Office001",
                CurrentCenterItemId = "001",
                CurrentCenterTitle = "Center001"
            }
        };
        var inventoryItems = new List<BuroInventoryItem>
        {
            new()
            {
                ItemId = "Inv456",
                ItemDefinition = EnumInventoryItemDefinition.Buy,
                InventoryItemName = "Item1",
                CategoryItemId = "Cat01",
                CategoryName = "Category1",
                SubCategoryItemId = "SubCat01",
                SubCategoryName = "SubCategory1",
                ChartOfAccountItemId = "ChartOfAccount01",
                ChartOfAccount = "ChartOfAccount1",
            }
        };
        SetupRepositoryMock(inventoryMovements);
        SetupRepositoryMock(employees);
        SetupRepositoryMock(inventoryItems);
    }

    [Fact]
    public async Task AddInventoryTransaction_ShouldAddItemToRepository_WhenCommandIsValid()
    {
        // Arrange
        var command = PrepareCommand();

        // Act
        await _service.AddInventoryManagement(command);
        var inventoryMovements = _mockRepository.Object.GetItems<BuroInventoryMovement>().ToList();

        // Assert
        Assert.Single(inventoryMovements);
        var inventoryMovement = inventoryMovements.First();
        Assert.Equal(command.InventoryItemId, inventoryMovement.InventoryItemId);
        Assert.Equal(command.Quantity, inventoryMovement.Quantity);
        Assert.Equal(command.Price, inventoryMovement.UnitPrice);
    }

    private AddInventoryMovementCommand PrepareCommand()
    {
        return new AddInventoryMovementCommand
        {
            InventoryItemId = "Inv456",
            Quantity = 10,
            SerialNumber = "SN001-12345",
            ExpiryDate = null,
            Price = 150.75
        };
    }
}