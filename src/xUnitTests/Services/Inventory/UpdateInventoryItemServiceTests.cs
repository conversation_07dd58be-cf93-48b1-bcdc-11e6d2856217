using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Inventory;

public class UpdateInventoryItemServiceTests : BaseTest
{
    private readonly UpdateInventoryItemService _service;
    public UpdateInventoryItemServiceTests()
    {
        _service = new UpdateInventoryItemService(_mockRepository.Object);
        SeedMockData();
    }

    [Fact]
    public async Task UpdateInventoryItem_WithValidCommand_ShouldPersistInventoryItemAndReturnExpectedAttributes()
    {
        // Arrange
        var command = PrepareCommand();

        // Act
        await _service.UpdateInventoryItem(command);
        var inventoryItem = _mockRepository.Object.GetItem<BuroInventoryItem>(x => x.ItemId == command.InventoryItemItemId);

        // Assert
        Assert.NotNull(inventoryItem);
        Assert.Equal(command.InventoryItemItemId, inventoryItem.ItemId);
        Assert.Equal(command.ItemDefinition, inventoryItem.ItemDefinition);
        Assert.Equal(command.InventoryItemName, inventoryItem.InventoryItemName);
        Assert.Equal(command.CategoryItemId, inventoryItem.CategoryItemId);
        Assert.Equal(command.CategoryName, inventoryItem.CategoryName);
        Assert.Equal(command.SubCategoryItemId, inventoryItem.SubCategoryItemId);
        Assert.Equal(command.SubCategoryName, inventoryItem.SubCategoryName);
        Assert.Equal(command.ChartOfAccountItemId, inventoryItem.ChartOfAccountItemId);
        Assert.Equal(command.ChartOfAccount, inventoryItem.ChartOfAccount);
        Assert.Equal(command.PriceType, inventoryItem.PriceType);
    }

    private UpdateInventoryItemCommand PrepareCommand()
    {
        return new UpdateInventoryItemCommand
        {
            InventoryItemItemId = "Item1",
            ItemDefinition = EnumInventoryItemDefinition.Buy,
            InventoryItemName = "ItemUpdated1",
            CategoryItemId = "CatUpdated01",
            CategoryName = "CategoryUpdated1",
            SubCategoryItemId = "SubCatUpdated01",
            SubCategoryName = "SubCategoryUpdated1",
            ChartOfAccountItemId = "AccUpdated01",
            ChartOfAccount = "AccountUpdated1",
            PriceType = EnumInventoryPriceType.Fixed,
            MinPrice = 100.0,
            MaxPrice = 200.0,
            FixedPrice = 150.0,
            HasSerialNumber = true,
            RequiresExpiryDate = false
        };
    }

    private void SeedMockData()
    {
        var inventoryItems = new List<BuroInventoryItem>
        {
            new BuroInventoryItem
            {
                ItemId = "Item1",
                ItemDefinition = EnumInventoryItemDefinition.Buy,
                InventoryItemName = "Item1",
                CategoryItemId = "Cat01",
                CategoryName = "Category1",
                SubCategoryItemId = "SubCat01",
                SubCategoryName = "SubCategory1",
                ChartOfAccountItemId = "Acc01",
                ChartOfAccount = "Account1",
                PriceType = EnumInventoryPriceType.Fixed,
                MinPrice = 100.0,
                MaxPrice = 200.0,
                FixedPrice = 150.0,
                HasSerialNumber = true,
                RequiresExpiryDate = false,
                CreatedByEmployeeName = "Employee One",
                CreatedByEmployeePin = "1234",
                CreatedByEmployeeDesignation = "Manager",
                IsArchived = false
            },
            new BuroInventoryItem
            {
                ItemId = "Item2",
                ItemDefinition = EnumInventoryItemDefinition.Request,
                InventoryItemName = "Item2",
                CategoryItemId = "Cat02",
                CategoryName = "Category2",
                SubCategoryItemId = "SubCat02",
                SubCategoryName = "SubCategory2",
                ChartOfAccountItemId = "Acc02",
                ChartOfAccount = "Account2",
                PriceType = EnumInventoryPriceType.Range,
                MinPrice = 50.0,
                MaxPrice = 100.0,
                FixedPrice = null,
                HasSerialNumber = false,
                RequiresExpiryDate = true,
                CreatedByEmployeeName = "Employee Two",
                CreatedByEmployeePin = "5678",
                CreatedByEmployeeDesignation = "Supervisor",
                IsArchived = true
            }
        };
        SetupRepositoryMock(inventoryItems);
    }
}