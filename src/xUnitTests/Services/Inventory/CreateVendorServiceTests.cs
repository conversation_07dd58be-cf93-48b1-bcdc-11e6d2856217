using Domain.Commands.Vendor;
using Domain.Services.Vendor;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Inventory;

public class CreateVendorServiceTests : BaseTest
{
    private readonly CreateVendorService _service;

    public CreateVendorServiceTests()
    {
        _service = new CreateVendorService(_mockRepository.Object, _mockSecurityContextProvider.Object,
            _mockSequenceNumberClient.Object);
        SeedMockData();
        SetupAdditionalMockServices();
    }

    private CreateVendorCommand PrepareCommand()
    {
        return new CreateVendorCommand
        {
            VendorType = EnumBuroVendorType.GeneralSupplier,
            CategoryItemId = "Category1",
            CategoryName = "Electronics",
            SubCategoryItemId = "SubCategory1",
            SubCategoryName = "Mobile Phones",
            VendorLegalName = "Vendor Legal Name 1",
            Remarks = "Remarks for Vendor 1",
            BusinessStartDate = new DateTime(2020, 1, 1),
            VendorContactPersons = new List<VendorContactPerson>
            {
                new()
                {
                    Name = "Contact Person 1",
                    PhoneNo = "**********",
                    Email = "<EMAIL>"
                }
            },
            CorporateStatus = EnumBuroCorporateStatus.CityLevel,
            VendorStatus = EnumBuroVendorStatus.Disabled,
            TransactionMediums = new List<EnumTransactionMedium>
            {
                EnumTransactionMedium.BankDraft,
                EnumTransactionMedium.MFS
            }
        };
    }

    private void SeedMockData()
    {
        var buroVendors = new List<BuroVendor>
        {
            /*new()
            {
                ItemId = "1",
                VendorType = EnumBuroVendorType.GeneralSupplier,
                CategoryItemId = "Category1",
                CategoryName = "Electronics",
                SubCategoryItemId = "SubCategory1",
                SubCategoryName = "Mobile Phones",
                VendorLegalName = "Vendor Legal Name 1",
                Remarks = "Remarks for Vendor 1",
                BusinessStartDate = new DateTime(2020, 1, 1),
                VendorContactPersons = new List<BuroVendorContactPerson>
                {
                    new()
                    {
                        Name = "Contact Person 1",
                        PhoneNo = "**********",
                        Email = "<EMAIL>",
                        EmergencyContact = "**********"
                    }
                },
                CorporateStatus = EnumBuroCorporateStatus.CityLevel,
                VendorStatus = EnumBuroVendorStatus.Enabled,
                TransactionMediums = new List<EnumTransactionMedium>()
                {
                    EnumTransactionMedium.MFS,
                    EnumTransactionMedium.BankDraft,
                }
            },
            new()
            {
                ItemId = "2",
                VendorType = EnumBuroVendorType.Retailer,
                CategoryItemId = "Category2",
                CategoryName = "Furniture",
                SubCategoryItemId = "SubCategory2",
                SubCategoryName = "Office Chairs",
                VendorLegalName = "Vendor Legal Name 2",
                Remarks = "Remarks for Vendor 2",
                TradeLicense = "License2",
                BusinessStartDate = new DateTime(2018, 5, 15),
                VendorContactPersons = new List<BuroVendorContactPerson>
                {
                    new()
                    {
                        Name = "Contact Person 2",
                        PhoneNo = "**********",
                        Email = "<EMAIL>",
                        BusinessAddress = "456 Avenue, City",
                        EmergencyContact = "**********"
                    }
                },
                CorporateStatus = EnumBuroCorporateStatus.CityLevel,
                VendorStatus = EnumBuroVendorStatus.Active,
                TransactionType = EnumBuroTransactionType.CashTransaction
            },
            new()
            {
                ItemId = "3",
                VendorType = EnumBuroVendorType.CorporateServices,
                CategoryItemId = "Category3",
                CategoryName = "Books",
                SubCategoryItemId = "SubCategory3",
                SubCategoryName = "Fiction",
                VendorLegalName = "Vendor Legal Name 3",
                Remarks = "Remarks for Vendor 3",
                TradeLicense = "License3",
                BusinessStartDate = new DateTime(2021, 10, 30),
                VendorContactPersons = new List<BuroVendorContactPerson>
                {
                    new()
                    {
                        Name = "Contact Person 3",
                        PhoneNo = "**********",
                        Email = "<EMAIL>",
                        BusinessAddress = "789 Road, City",
                        EmergencyContact = "**********"
                    }
                },
                CorporateStatus = EnumBuroCorporateStatus.CityLevel,
                VendorStatus = EnumBuroVendorStatus.Active,
                TransactionType = EnumBuroTransactionType.BankTransaction
            }*/
        };

        SetupRepositoryMock(buroVendors);
    }

    /*[Fact]
    public async Task CreateVendor_ShouldAddVendorToRepository_WhenCommandIsValid()
    {
        // Arrange
        var command = PrepareCommand();

        // Act
        await _service.CreateVendor(command);
        var vendors = _mockRepository.Object.GetItems<BuroVendor>().ToList();

        // Assert
        Assert.Single(vendors);
        var createdVendor = vendors.First();
        Assert.Equal(command.VendorLegalName, createdVendor.VendorLegalName);
        Assert.Equal(command.CategoryName, createdVendor.CategoryName);
        Assert.Equal(command.SubCategoryName, createdVendor.SubCategoryName);
        Assert.Equal(command.BusinessStartDate, createdVendor.BusinessStartDate);
        Assert.Equal(command.TransactionMediums, createdVendor.TransactionMediums);
    }*/
}