using Domain.Commands.Vendor;
using Domain.Services.Vendor;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Inventory;

public class UpdateVendorStatusServiceTests : BaseTest
{
    private readonly UpdateVendorStatusService _service;
    public UpdateVendorStatusServiceTests()
    {
        _service = new UpdateVendorStatusService(_mockRepository.Object);
        SeedMockData();
    }

    [Fact]
    public async Task UpdateVendorStatus_WithValidCommand_ShouldPersistVendorAndReturnExpectedAttributes()
    {
        // Arrange
        var command = PrepareCommand();

        // Act
        await _service.UpdateVendorStatus(command);
        var vendor = _mockRepository.Object.GetItem<BuroVendor>(x => x.ItemId == command.VendorItemId);

        // Assert
        Assert.NotNull(vendor);
        Assert.Equal(command.VendorStatus, vendor.VendorStatus);
    }

    private UpdateVendorStatusCommand PrepareCommand()
    {
        return new UpdateVendorStatusCommand
        {
            VendorItemId = "1",
            VendorStatus = EnumBuroVendorStatus.Archived
        };
    }

    private void SeedMockData()
    {
        var buroVendors = new List<BuroVendor>
        {
            new()
            {
                ItemId = "1",
                VendorType = EnumBuroVendorType.GeneralSupplier,
                CategoryItemId = "Category1",
                CategoryName = "Electronics",
                SubCategoryItemId = "SubCategory1",
                SubCategoryName = "Mobile Phones",
                VendorLegalName = "Vendor Legal Name 1",
                Remarks = "Remarks for Vendor 1",
                BusinessStartDate = new DateTime(2020, 1, 1),
                VendorContactPersons = new List<BuroVendorContactPerson>
                {
                    new()
                    {
                        Name = "Contact Person 1",
                        PhoneNo = "1234567890",
                        Email = "<EMAIL>",
                        EmergencyContact = "9876543210"
                    }
                },
                CorporateStatus = EnumBuroCorporateStatus.CityLevel,
                VendorStatus = EnumBuroVendorStatus.Disabled
            },
            new()
            {
                ItemId = "2",
                VendorType = EnumBuroVendorType.Retailer,
                CategoryItemId = "Category2",
                CategoryName = "Furniture",
                SubCategoryItemId = "SubCategory2",
                SubCategoryName = "Office Chairs",
                VendorLegalName = "Vendor Legal Name 2",
                Remarks = "Remarks for Vendor 2",
                BusinessStartDate = new DateTime(2018, 5, 15),
                VendorContactPersons = new List<BuroVendorContactPerson>
                {
                    new()
                    {
                        Name = "Contact Person 2",
                        PhoneNo = "0987654321",
                        Email = "<EMAIL>",
                        EmergencyContact = "0123456789"
                    }
                },
                CorporateStatus = EnumBuroCorporateStatus.CityLevel,
                VendorStatus = EnumBuroVendorStatus.Enabled,
                TransactionMediums = new List<EnumTransactionMedium>
                {
                    EnumTransactionMedium.Cash
                }
            },
            new()
            {
                ItemId = "3",
                VendorType = EnumBuroVendorType.CorporateServices,
                CategoryItemId = "Category3",
                CategoryName = "Books",
                SubCategoryItemId = "SubCategory3",
                SubCategoryName = "Fiction",
                VendorLegalName = "Vendor Legal Name 3",
                Remarks = "Remarks for Vendor 3",
                BusinessStartDate = new DateTime(2021, 10, 30),
                VendorContactPersons = new List<BuroVendorContactPerson>
                {
                    new()
                    {
                        Name = "Contact Person 3",
                        PhoneNo = "1231231234",
                        Email = "<EMAIL>",
                        EmergencyContact = "4324324321"
                    }
                },
                CorporateStatus = EnumBuroCorporateStatus.CityLevel,
                VendorStatus = EnumBuroVendorStatus.Disabled,
                TransactionMediums = new List<EnumTransactionMedium>
                {
                    EnumTransactionMedium.Cash
                }
            }
        };

        SetupRepositoryMock(buroVendors);
    }
}