using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Inventory;

public class ArchiveInventoryItemServiceTests : BaseTest
{
    private readonly ArchiveInventoryItemService _service;
    private readonly Mock<ISecurityContextProvider> _mockSecurityContextProvider;
    public ArchiveInventoryItemServiceTests()
    {
        _mockSecurityContextProvider = new Mock<ISecurityContextProvider>();
        _service = new ArchiveInventoryItemService(_mockRepository.Object, _mockSecurityContextProvider.Object);
        SeedMockData();
    }

    [Fact]
    public async Task ArchiveInventory_WithValidCommand_ShouldPersistVendorAndReturnExpectedAttributes()
    {
        // Arrange
        var command = PrepareCommand();

        // Act
        await _service.ArchiveInventoryItem(command);
        var inventoryItem = _mockRepository.Object.GetItem<BuroInventoryItem>(x => x.ItemId == command.InventoryItemItemId);

        // Assert
        Assert.NotNull(inventoryItem);
        Assert.Equal(command.InventoryItemItemId, inventoryItem.ItemId);
        Assert.Equal(command.IsArchived, inventoryItem.IsArchived);
    }

    private ArchiveInventoryItemCommand PrepareCommand()
    {
        return new ArchiveInventoryItemCommand
        {
            InventoryItemItemId = "Item1",
            IsArchived = true
        };
    }

    private void SeedMockData()
    {
        var inventoryItems = new List<BuroInventoryItem>
        {
            new BuroInventoryItem
            {
                ItemId = "Item1",
                ItemDefinition = EnumInventoryItemDefinition.Buy,
                InventoryItemName = "Item1",
                CategoryItemId = "Cat01",
                CategoryName = "Category1",
                SubCategoryItemId = "SubCat01",
                SubCategoryName = "SubCategory1",
                ChartOfAccountItemId = "Acc01",
                ChartOfAccount = "Account1",
                PriceType = EnumInventoryPriceType.Fixed,
                MinPrice = 100.0,
                MaxPrice = 200.0,
                FixedPrice = 150.0,
                HasSerialNumber = true,
                RequiresExpiryDate = false,
                CreatedByEmployeeName = "Employee One",
                CreatedByEmployeePin = "1234",
                CreatedByEmployeeDesignation = "Manager",
                IsArchived = false
            },
            new BuroInventoryItem
            {
                ItemId = "Item2",
                ItemDefinition = EnumInventoryItemDefinition.Request,
                InventoryItemName = "Item2",
                CategoryItemId = "Cat02",
                CategoryName = "Category2",
                SubCategoryItemId = "SubCat02",
                SubCategoryName = "SubCategory2",
                ChartOfAccountItemId = "Acc02",
                ChartOfAccount = "Account2",
                PriceType = EnumInventoryPriceType.Range,
                MinPrice = 50.0,
                MaxPrice = 100.0,
                FixedPrice = null,
                HasSerialNumber = false,
                RequiresExpiryDate = true,
                CreatedByEmployeeName = "Employee Two",
                CreatedByEmployeePin = "5678",
                CreatedByEmployeeDesignation = "Supervisor",
                IsArchived = true
            }
        };
        SetupRepositoryMock(inventoryItems);
    }
}