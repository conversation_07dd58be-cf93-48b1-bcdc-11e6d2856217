using Domain.Commands.Inventory;
using Domain.Services.Inventory;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Inventory;

public class CreateInventoryItemServiceTests : BaseTest
{
    private readonly CreateInventoryItemService _service;
    private readonly Mock<ISecurityContextProvider> _mockSecurityContextProvider;

    public CreateInventoryItemServiceTests()
    {
        _mockSecurityContextProvider = new Mock<ISecurityContextProvider>();
        _service = new CreateInventoryItemService(_mockRepository.Object, _mockSecurityContextProvider.Object);
        SeedMockData();
    }

    [Fact]
    public async Task CreateInventoryItem_ShouldAddInventoryItemToRepository_WhenCommandIsValid()
    {
        // Arrange
        var command = CreateCommand();

        // Act
        await _service.CreateInventoryItem(command);
        var inventoryItems = _mockRepository.Object.GetItems<BuroInventoryItem>().ToList();

        // Assert
        Assert.Single(inventoryItems);
        var createdVendor = inventoryItems.First();
        Assert.Equal(command.InventoryItemName, createdVendor.InventoryItemName);
    }

    private void SeedMockData()
    {
        var inventoryItems = new List<BuroInventoryItem>();
        SetupRepositoryMock(inventoryItems);
    }

    private CreateInventoryItemCommand CreateCommand()
    {
        return new CreateInventoryItemCommand
        {
            ItemDefinition = EnumInventoryItemDefinition.Buy,
            InventoryItemName = "SampleItemName",
            CategoryItemId = "SampleCategoryId",
            CategoryName = "SampleCategoryName",
            SubCategoryItemId = "SampleSubCategoryId",
            SubCategoryName = "SampleSubCategoryName",
            ChartOfAccountItemId = "SampleChartOfAccountId",
            ChartOfAccount = "SampleChartOfAccount",
            PriceType = EnumInventoryPriceType.Fixed,
            MinPrice = null,
            MaxPrice = null,
            FixedPrice = 300.00,
            HasSerialNumber = true,
            RequiresExpiryDate = false
        };
    }
}