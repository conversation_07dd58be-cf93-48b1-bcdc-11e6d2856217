using Domain.Commands.Role;
using Domain.Contracts.Role;
using Domain.Services.Role;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Role
{
    public class CreateFeatureRoleMapServiceTests : BaseTest
    {
        private readonly ILogger<CreateFeatureRoleMapService> _logger;
        private readonly ICreateFeatureRoleMapService _service;

        public CreateFeatureRoleMapServiceTests()
        {
            _logger = Mock.Of<ILogger<CreateFeatureRoleMapService>>();

            _service = new CreateFeatureRoleMapService(
                _logger,
                _mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            SetupRepositoryMock(new List<BuroFeatureRoleMap>());
        }

        private static CreateFeatureRoleMapCommand CreateCreateFeatureRoleMapCommand()
        {
            var featureRoleMap = new CreateFeatureRoleMapCommand
            {
                RoleItemId = nameof(BuroFeatureRoleMap.RoleItemId) + "1",
                RoleName = nameof(BuroFeatureRoleMap.RoleName) + "1",
                RoleKey = nameof(BuroFeatureRoleMap.RoleKey) + "1",
                FeatureRequests = new List<FeatureRequestWithStatus>
                {
                    new()
                    {
                        FeatureItemId = nameof(FeatureRequestWithStatus.FeatureItemId) + "1",
                        FeatureName = nameof(FeatureRequestWithStatus.FeatureName) + "1",
                        FeaturePath = nameof(FeatureRequestWithStatus.FeaturePath) + "1",
                        ApiPath = nameof(FeatureRequestWithStatus.ApiPath) + "1",
                        FeatureStatus = EnumFeatureStatus.None
                    },
                    new()
                    {
                        FeatureItemId = nameof(FeatureRequestWithStatus.FeatureItemId) + "2",
                        FeatureName = nameof(FeatureRequestWithStatus.FeatureName) + "2",
                        FeaturePath = nameof(FeatureRequestWithStatus.FeaturePath) + "2",
                        ApiPath = nameof(FeatureRequestWithStatus.ApiPath) + "2",
                        FeatureStatus = EnumFeatureStatus.None
                    }
                }
            };

            return featureRoleMap;
        }

        [Fact]
        public async Task CreateFeatureRoleMapAsync_ProvidedValidData_SuccessfullyCreatedRecords()
        {
            // Arrange
            var command = CreateCreateFeatureRoleMapCommand();

            // Act
            var response = await _service.CreateFeatureRoleMapAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
        }
    }
}
