using Domain.Commands.Role;
using Domain.Services.Role;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Role
{
    public class UpdateRolesServiceTests : BaseTest
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<UpdateRolesService> _logger;
        private readonly UpdateRolesService _updateRolesService;

        public UpdateRolesServiceTests()
        {
            _logger = Mock.Of<ILogger<UpdateRolesService>>();
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();

            _updateRolesService = new UpdateRolesService(
                _logger,
                _securityContextProvider,
                _mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            var roles = new List<BuroRole>
            {
                new()
                {
                    ItemId = nameof(BuroRole.ItemId) + "1",
                    RoleName = nameof(BuroRole.RoleName) + "1",
                    RoleKey = nameof(BuroRole.RoleKey) + "1"
                }
            };

            SetupRepositoryMock(roles);
        }

        private static UpdateRolesCommand GetEditCustomPropertyCommand(
            string itemId,
            string roleName)
        {
            var newUpdateRolesCommand = new UpdateRolesCommand
            {
                RoleItemId = itemId,
                RoleName = roleName
            };

            return newUpdateRolesCommand;
        }

        [Fact]
        public async Task UpdateRolesAsync_ValidRole_SuccessfullyUpdated()
        {
            // Arange
            var command = GetEditCustomPropertyCommand(nameof(BuroRole.ItemId) + "1", nameof(BuroRole.RoleName) + "1");

            // Act
            var response = await _updateRolesService.UpdateRolesAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.Empty(response.ErrorMessages);
        }
    }
}
