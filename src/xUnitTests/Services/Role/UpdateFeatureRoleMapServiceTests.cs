using Domain.Commands.Role;
using Domain.Contracts.Role;
using Domain.Services.Role;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Role
{
    public class UpdateFeatureRoleMapServiceTests : BaseTest
    {
        private readonly string _modifiedBy;
        private readonly ILogger<UpdateFeatureRoleMapService> _logger;
        private readonly IUpdateFeatureRoleMapService _service;

        public UpdateFeatureRoleMapServiceTests()
        {
            _modifiedBy = Guid.NewGuid().ToString();
            _logger = Mock.Of<ILogger<UpdateFeatureRoleMapService>>();

            SetupAdditionalMockServices();
            SetupMockSecurityContext(_modifiedBy);

            _service = new UpdateFeatureRoleMapService(
                _logger,
                _mockSecurityContextProvider.Object,
                _mockServiceClient.Object,
                _mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            var mappings = new List<BuroFeatureRoleMap>
            {
                new ()
                {
                    ItemId = nameof(BuroFeatureRoleMap.ItemId) + "1",
                    RoleItemId = nameof(BuroFeatureRoleMap.RoleItemId) + "1",
                    RoleName = nameof(BuroFeatureRoleMap.RoleName) + "1",
                    RoleKey = nameof(BuroFeatureRoleMap.RoleKey) + "1",
                    Features = new List<FeatureForRoleMap>
                    {
                        new()
                        {
                            ItemId = nameof(FeatureForRoleMap.ItemId) + "1",
                            FeatureName = nameof(FeatureForRoleMap.FeatureName) + "1",
                            FeaturePath = nameof(FeatureForRoleMap.FeaturePath) + "1",
                            ApiPath = nameof(FeatureForRoleMap.ApiPath) + "1"
                        },
                        new()
                        {
                            ItemId = nameof(FeatureForRoleMap.ItemId) + "2",
                            FeatureName = nameof(FeatureForRoleMap.FeatureName) + "2",
                            FeaturePath = nameof(FeatureForRoleMap.FeaturePath) + "2",
                            ApiPath = nameof(FeatureForRoleMap.ApiPath) + "2"
                        }
                    }
                },
                new ()
                {
                    ItemId = nameof(BuroFeatureRoleMap.ItemId) + "1",
                    RoleItemId = nameof(BuroFeatureRoleMap.RoleItemId) + "1",
                    RoleName = nameof(BuroFeatureRoleMap.RoleName) + "1",
                    RoleKey = nameof(BuroFeatureRoleMap.RoleKey) + "1",
                    Features = new List<FeatureForRoleMap>
                    {
                        new()
                        {
                            ItemId = nameof(FeatureForRoleMap.ItemId) + "1",
                            FeatureName = nameof(FeatureForRoleMap.FeatureName) + "1",
                            FeaturePath = nameof(FeatureForRoleMap.FeaturePath) + "1",
                            ApiPath = nameof(FeatureForRoleMap.ApiPath) + "1",
                            FeatureStatus = EnumFeatureStatus.None
                        },
                        new()
                        {
                            ItemId = nameof(FeatureForRoleMap.ItemId) + "2",
                            FeatureName = nameof(FeatureForRoleMap.FeatureName) + "2",
                            FeaturePath = nameof(FeatureForRoleMap.FeaturePath) + "2",
                            ApiPath = nameof(FeatureForRoleMap.ApiPath) + "2",
                            FeatureStatus = EnumFeatureStatus.None
                        }
                    }
                }
            };

            SetupRepositoryMock(mappings);
        }

        private static UpdateFeatureRoleMapCommand CreateUpdateFeatureRoleMapCommand()
        {
            var featureRoleMap = new UpdateFeatureRoleMapCommand
            {
                FeatureRoleMapItemId = nameof(BuroFeatureRoleMap.ItemId) + "1",
                RoleItemId = nameof(BuroFeatureRoleMap.RoleItemId) + "1",
                RoleName = nameof(BuroFeatureRoleMap.RoleName) + "1",
                RoleKey = nameof(BuroFeatureRoleMap.RoleKey) + "1",
                FeatureRequests = new List<FeatureRequest>
                {
                    new()
                    {
                        FeatureItemId = nameof(FeatureRequest.FeatureItemId) + "1",
                        FeatureName = nameof(FeatureRequest.FeatureName) + "1",
                        FeaturePath = nameof(FeatureRequest.FeaturePath) + "1",
                        ApiPath = nameof(FeatureRequest.ApiPath) + "1",
                    },
                    new()
                    {
                        FeatureItemId = nameof(FeatureRequest.FeatureItemId) + "2",
                        FeatureName = nameof(FeatureRequest.FeatureName) + "2",
                        FeaturePath = nameof(FeatureRequest.FeaturePath) + "2",
                        ApiPath = nameof(FeatureRequest.ApiPath) + "2",
                    }
                }
            };

            return featureRoleMap;
        }

        private static UpdateFeatureRoleMapWithStatusCommand CreateUpdateFeatureRoleMapWithStatusCommand()
        {
            var featureRoleMap = new UpdateFeatureRoleMapWithStatusCommand
            {
                FeatureRoleMapItemId = nameof(BuroFeatureRoleMap.ItemId) + "1",
                RoleItemId = nameof(BuroFeatureRoleMap.RoleItemId) + "1",
                RoleName = nameof(BuroFeatureRoleMap.RoleName) + "1",
                RoleKey = nameof(BuroFeatureRoleMap.RoleKey) + "1",
                FeatureRequests = new List<FeatureRequestWithStatus>
                {
                    new()
                    {
                        FeatureItemId = nameof(FeatureRequest.FeatureItemId) + "1",
                        FeatureName = nameof(FeatureRequest.FeatureName) + "1",
                        FeaturePath = nameof(FeatureRequest.FeaturePath) + "1",
                        ApiPath = nameof(FeatureRequest.ApiPath) + "1",
                        FeatureStatus = EnumFeatureStatus.None
                    },
                    new()
                    {
                        FeatureItemId = nameof(FeatureRequest.FeatureItemId) + "2",
                        FeatureName = nameof(FeatureRequest.FeatureName) + "2",
                        FeaturePath = nameof(FeatureRequest.FeaturePath) + "2",
                        ApiPath = nameof(FeatureRequest.ApiPath) + "2",
                        FeatureStatus = EnumFeatureStatus.Required
                    }
                }
            };

            return featureRoleMap;
        }

        [Fact]
        public async Task UpdateFeatureRoleMapAsync_ProvidedValidDataForAdmin_SuccessfullyUpdatedRecords()
        {
            // Arrange
            var command = CreateUpdateFeatureRoleMapCommand();

            // Act
            var response = await _service.UpdateFeatureRoleMapAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
        }

        [Fact]
        public async Task UpdateFeatureRoleMapAsync_ProvidedValidDataForSuperUserWithStatus_SuccessfullyUpdatedRecords()
        {
            // Arrange
            var command = CreateUpdateFeatureRoleMapWithStatusCommand();

            // Act
            var response = await _service.UpdateFeatureRoleMapAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
        }
    }
}
