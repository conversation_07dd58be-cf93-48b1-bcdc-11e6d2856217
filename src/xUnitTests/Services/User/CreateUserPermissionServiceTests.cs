using Domain.Commands.User;
using Domain.Services.Users;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.User;

public class CreateUserPermissionServiceTests : BaseTest
{
    private readonly Mock<IRepository> _repositoryMock;
    private readonly Mock<ISecurityContextProvider> _securityContextProvider;
    private readonly Mock<ILogger<CreateUserPermissionService>> _loggerMock;
    private CreateUserPermissionService _service;

    public CreateUserPermissionServiceTests()
    {
        _repositoryMock = new Mock<IRepository>();
        _securityContextProvider = new Mock<ISecurityContextProvider>();
        _loggerMock = new Mock<ILogger<CreateUserPermissionService>>();
        _service = new CreateUserPermissionService(_repositoryMock.Object, _securityContextProvider.Object, _loggerMock.Object);
        SeedMockUserPermissions();
    }

    [Fact]
    public async Task CreatesUserPermission_WhenSuccessful_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = PopulateCreateUserPermissionCommand();
        // Act
        var result = await _service.CreateUserPermissionAsync(command);

        // Assert
        Assert.True(result.Errors.IsValid);
    }

    private CreateUserPermissionCommand PopulateCreateUserPermissionCommand()
    {
        return new CreateUserPermissionCommand
        {
            UserItemId = Guid.NewGuid().ToString(),
            FeatureRoleItemId = Guid.NewGuid().ToString(),
            Features = new List<FeatureCommand>
            {
                new FeatureCommand
                {
                    FeatureItemId = Guid.NewGuid().ToString(),
                    FeatureName = "Feature1",
                    FeaturePath = "/feature1",
                    ApiPath = "/api/feature1"
                }
            }
        };
    }

    private void SeedMockUserPermissions()
    {
        var list = new List<BuroUserPermission>
        {
            new()
            {
                ItemId = Guid.NewGuid().ToString(),
                FeatureRoleItemId = Guid.NewGuid().ToString(),
                Features = new List<FeatureForPermission>
                {
                    new FeatureForPermission()
                    {
                        ItemId = Guid.NewGuid().ToString(),
                        FeatureName = "Feature1",
                        FeaturePath = "/feature1",
                        ApiPath = "/api/feature1"
                    }
                }
            }
        };

        SetupRepositoryMock(list);
    }
}