using Domain.Commands.Voucher.Configuration;
using Domain.Services.Voucher.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace xUnitTests.Services.Voucher;

public class CreateStandardVoucherConfigurationServiceTests : BaseTest
{
    private readonly Mock<ISecurityContextProvider> _securityContextProvider;
    private readonly CreateStandardVoucherConfigurationService _service;

    public CreateStandardVoucherConfigurationServiceTests()
    {
        ILogger<CreateStandardVoucherConfigurationService> logger = Mock.Of<ILogger<CreateStandardVoucherConfigurationService>>();
        _securityContextProvider = new Mock<ISecurityContextProvider>();

        SetupMockForSecurityContextProvider();
        SeedMockData();

        _service = new CreateStandardVoucherConfigurationService(
            logger,
            _securityContextProvider.Object,
            _mockFinMongodbRepository.Object);
    }

    private void SeedMockData()
    {
        SeedMockChartOfAccountData();
        SeedMockVoucherConfigurationData();
    }

    private void SeedMockChartOfAccountData()
    {
        var charOfAccounts = new List<BuroChartOfAccount>
        {
            new()
            {
                ItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                Code = "200",
                Name = "Cash at Bank"
            },
            new()
            {
                ItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                Code = "9",
                Name = "Income"
            }
        };

        SetupRepositoryMock(charOfAccounts);
    }

    private void SeedMockVoucherConfigurationData()
    {
        var voucherConfigs = new List<BuroStandardVoucherConfiguration>
        {
            new()
            {
                ItemId = "efce486d-7818-3434-9adc-345455",
                VoucherCreationType = 0,
                LedgerEntries = new List<LedgerEntry>()
                {
                    new LedgerEntry
                    {
                        ItemId = "082bcf74-5afe-48b8-bae8-243ec08765cd",
                        ChartOfAccountItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                        ChartOfAccountCode = "200",
                        ChartOfAccountName = "Cash at Bank",
                        VoucherType = EnumBuroVoucherType.Debit
                    },
                    new LedgerEntry
                    {
                        ItemId = "3421fab8-2a33-4dab-9633-a13549ff3301",
                        ChartOfAccountItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                        ChartOfAccountCode = "9",
                        ChartOfAccountName = "Income",
                        VoucherType = EnumBuroVoucherType.Credit
                    }
                }

            }
        };
        SetupRepositoryMock(voucherConfigs);
    }


    private void SetupMockForSecurityContextProvider()
    {
        var securityContext = CreateMockSecurityContext("e2e03735-058a-4b8c-bfe3-ea09246215b0");

        _securityContextProvider.Setup(x => x.GetSecurityContext())
            .Returns(securityContext);
    }

    private static SecurityContext CreateMockSecurityContext(string userItemId)
    {
        return new(
            userName: string.Empty,
            roles: Enumerable.Empty<string>(),
            tenantId: string.Empty,
            oauthBearerToken: "sample_oauth_bearer_token",
            isAuthenticated: false,
            isUserAuthenticated: false,
            displayName: "test admin",
            userId: userItemId,
            requestOrigin: string.Empty,
            siteName: string.Empty,
            siteId: string.Empty,
            email: string.Empty,
            language: string.Empty,
            phoneNumber: string.Empty,
            sessionId: string.Empty,
            requestUri: new Uri("about:blank"),
            serviceVersion: string.Empty,
            hasDynamicRoles: false,
            tokenHijackingProtectionHash: string.Empty,
            userAutoExpire: false,
            userExpireOn: DateTime.MinValue,
            userPrefferedLanguage: string.Empty,
            postLogOutHandlerDataKey: string.Empty,
            organizationId: string.Empty
        );
    }

    private static CreateVoucherConfigCommand GetCreateVoucherConfigCommand()
    {
        var command = new CreateVoucherConfigCommand
        {
            MessageCorrelationId = "721b7f5e-4d38-4634-9f46-9ce42c098e0e",
            ItemId = "f85b2713-ccc0-4aa2-9a01-eecca6d8db28",

            LedgerEntries = new List<Domain.Commands.Voucher.Configuration.LedgerEntry>
            {
                new Domain.Commands.Voucher.Configuration.LedgerEntry
                {
                    ChartOfAccountItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                    VoucherType = EnumBuroVoucherType.Debit
                },
                new Domain.Commands.Voucher.Configuration.LedgerEntry
                {
                    ChartOfAccountItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                    VoucherType = EnumBuroVoucherType.Credit
                }
            }
        };

        return command;
    }

    //[Fact]
    //public async Task CreateVoucherConfigurationAsync_ValidCommand_VoucherConfigurationCreationSuccessful()
    //{
    //    // Arrange
    //    var command = GetCreateVoucherConfigCommand();

    //    // Act
    //    var response = await _service.CreateStandardVoucherConfiguration(command);

    //    // Assert
    //    Assert.NotNull(response);
    //    Assert.True(response.Errors.IsValid);
    //    Assert.False(response.ErrorMessages.Any());
    //}

}

