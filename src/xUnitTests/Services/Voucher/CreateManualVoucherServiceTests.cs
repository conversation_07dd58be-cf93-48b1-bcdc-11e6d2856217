using Domain.Commands.Voucher;
using Domain.Services;
using Domain.Services.Voucher;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using xUnitTests.Helpers;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace xUnitTests.Services.Voucher;

public class CreateManualVoucherServiceTests : BaseTest
{
    private readonly CreateManualVoucherService _service;

    public CreateManualVoucherServiceTests()
    {
        const string userItemId = "001";
        ILogger<CreateManualVoucherService> logger = Mock.Of<ILogger<CreateManualVoucherService>>();
        ILogger<SharedService> sharedServiceLogger = Mock.Of<ILogger<SharedService>>();

        var sharedService = new SharedService(
            _mockFinMongodbRepository.Object,
            sharedServiceLogger,
            _mocksNotificationServiceClient.Object,
            _mockSequenceNumberClient.Object);

        SetupAdditionalMockServices();
        SeedMockData();
        SetupMockSecurityContext(userItemId);

        _service = new CreateManualVoucherService(
            logger,
            _mockSecurityContextProvider.Object,
            _mockFinMongodbRepository.Object,
            sharedService);

    }

    private void SeedMockData()
    {
        SeedMockVoucherConfigurationData();
        SeedMockEmployeeData();

    }

    private void SeedMockVoucherConfigurationData()
    {
        var voucherConfigs = new List<BuroStandardVoucherConfiguration>
        {
            new()
            {
                ItemId = "efce486d-7818-3434-9adc-dc41c71f0416",
                VoucherCreationType = 0,
                LedgerEntries = new List<LedgerEntry>()
                {
                    new LedgerEntry
                    {
                        ItemId = "082bcf74-5afe-48b8-bae8-243ec08765cd",
                        ChartOfAccountItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                        ChartOfAccountCode = "200",
                        ChartOfAccountName = "Cash at Bank",
                        VoucherType = EnumBuroVoucherType.Debit
                    },
                    new LedgerEntry
                    {
                        ItemId = "3421fab8-2a33-4dab-9633-a13549ff3301",
                        ChartOfAccountItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                        ChartOfAccountCode = "9",
                        ChartOfAccountName = "Income",
                        VoucherType = EnumBuroVoucherType.Credit
                    }
                }

            }
        };
        SetupRepositoryMock(voucherConfigs);
    }

    private void SeedMockEmployeeData()
    {
        var employees = new List<BuroEmployee>
        {
            new()
            {
                ItemId = "001",
                EmployeeName = "Name001",
                UserItemId = "001"
            }
        };
        SetupRepositoryMock(employees);
    }


    private static CreateManualVoucherCommand GetCreateManualVoucherCommand()
    {

        var command = new CreateManualVoucherCommand
        {
            VoucherConfigItemId = "efce486d-7818-3434-9adc-dc41c71f0416",
            VoucherGroupItemId = "90fae3fd-c82e-4c75-9d9e-9959c6063cfa",
            Amount = 500,
            Narration = "Member registers and pays a form fee",
        };

        return command;
    }

    [Fact]
    public async Task CreateManualVoucherAsync_ValidCommand_ManualVoucherCreationSuccessful()
    {
        // Arrange
        var command = GetCreateManualVoucherCommand();

        // Act
        var response = await _service.CreateManualVoucherAsync(command);

        // Assert
        // Assert.NotNull(response);
        // Assert.True(response.Errors.IsValid);
        // Assert.False(response.ErrorMessages.Any());
    }





}