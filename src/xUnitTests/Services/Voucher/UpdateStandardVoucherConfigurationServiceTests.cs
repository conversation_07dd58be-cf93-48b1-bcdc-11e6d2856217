using Domain.Commands.Voucher.Configuration;
using Domain.Services.Voucher.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using xUnitTests.Helpers;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace xUnitTests.Services.Voucher;

public class UpdateStandardVoucherConfigurationServiceTests : BaseTest
{
    private readonly UpdateStandardVoucherConfigurationService _service;

    public UpdateStandardVoucherConfigurationServiceTests()
    {
        const string userItemId = "001";
        ILogger<UpdateStandardVoucherConfigurationService> logger = Mock.Of<ILogger<UpdateStandardVoucherConfigurationService>>();

        _service = new UpdateStandardVoucherConfigurationService(
            logger,
            _mockSecurityContextProvider.Object,
            _mockRepository.Object);

        SetupMockSecurityContext(userItemId);
        SeedMockData();

    }

    private void SeedMockData()
    {
        SeedMockVoucherConfigData();
        SeedMockChartOfAccountData();
    }

    private void SeedMockVoucherConfigData()
    {
        var voucherConfigs = new List<BuroStandardVoucherConfiguration>
        {
            new()
            {
                ItemId = "001",
                VoucherCreationType = 0,
                LedgerEntries = new List<LedgerEntry>()
                {
                    new LedgerEntry
                    {
                        ItemId = "0001",
                        ChartOfAccountItemId = "Chart-1",
                        ChartOfAccountCode = "200",
                        ChartOfAccountName = "Cash at Bank",
                        VoucherType = EnumBuroVoucherType.Debit
                    },
                    new LedgerEntry
                    {
                        ItemId = "0002",
                        ChartOfAccountItemId = "Chart-2",
                        ChartOfAccountCode = "9",
                        ChartOfAccountName = "Income",
                        VoucherType = EnumBuroVoucherType.Credit
                    }
                }

            },
            new()
            {
                ItemId = "002",
                VoucherCreationType = 0,
                LedgerEntries = new List<LedgerEntry>()
                {
                    new LedgerEntry
                    {
                        ItemId = "0001",
                        ChartOfAccountItemId = "Chart-3",
                        ChartOfAccountCode = "100",
                        ChartOfAccountName = "Cash at Hand",
                        VoucherType = EnumBuroVoucherType.Debit
                    },
                    new LedgerEntry
                    {
                        ItemId = "0002",
                        ChartOfAccountItemId = "Chart-4",
                        ChartOfAccountCode = "10",
                        ChartOfAccountName = "Deposit",
                        VoucherType = EnumBuroVoucherType.Credit
                    }
                }

            }
        };
        SetupRepositoryMock(voucherConfigs);
    }

    private void SeedMockChartOfAccountData()
    {
        var charOfAccounts = new List<BuroChartOfAccount>
        {
            new()
            {
                ItemId = "Chart-1",
                Code = "200",
                Name = "Cash at Bank"
            },
            new()
            {
                ItemId = "Chart-2",
                Code = "9",
                Name = "Income"
            },
            new()
            {
            ItemId = "Chart-3",
            Code = "100",
            Name = "Cash at Hand"
            },
            new()
            {
                ItemId = "Chart-4",
                Code = "10",
                Name = "Deposit"
            }
        };

        SetupRepositoryMock(charOfAccounts);
    }

    private static UpdateVoucherConfigCommand GetUpdateVoucherConfigCommand()
    {
        var command = new UpdateVoucherConfigCommand
        {
            MessageCorrelationId = "721b7f5e-4d38-4634-9f46-9ce42c098e0e",
            ItemId = "001",
            LedgerEntries = new List<Domain.Commands.Voucher.Configuration.LedgerEntry>
            {
                new Domain.Commands.Voucher.Configuration.LedgerEntry
                {
                    ChartOfAccountItemId = "Chart-3",
                    VoucherType = EnumBuroVoucherType.Debit,
                },
                new Domain.Commands.Voucher.Configuration.LedgerEntry
                {
                    ChartOfAccountItemId = "Chart-4",
                    VoucherType = EnumBuroVoucherType.Credit,
                }
            }
        };

        return command;
    }

    [Fact]
    public async Task CreateVoucherConfigurationAsync_ValidCommand_VoucherConfigurationUpdateSuccessful()
    {
        // Arrange
        var command = GetUpdateVoucherConfigCommand();

        // Act
        var response = await _service.UpdateManualVoucherConfiguration(command);

        // Assert
        Assert.NotNull(response);
        Assert.True(response.Errors.IsValid);
        Assert.False(response.ErrorMessages.Any());
    }







}