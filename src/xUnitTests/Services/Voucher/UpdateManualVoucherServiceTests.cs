using Domain.Commands.Voucher;
using Domain.Services.Voucher;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using xUnitTests.Helpers;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace xUnitTests.Services.Voucher;

public class UpdateManualVoucherServiceTests : BaseTest
{
    private readonly UpdateManualVoucherService _service;

    public UpdateManualVoucherServiceTests()
    {
        const string userItemId = "001";
        ILogger<UpdateManualVoucherService> logger = Mock.Of<ILogger<UpdateManualVoucherService>>();

        _service = new UpdateManualVoucherService(
            logger,
            _mockSecurityContextProvider.Object,
            _mockFinMongodbRepository.Object);

        SetupMockSecurityContext(userItemId);
        SeedMockData();

    }

    private void SeedMockData()
    {
        SeedMockVoucherConfigurationData();
        SeedMockVoucherData();

    }

    private void SeedMockVoucherData()
    {
        var voucher = new List<BuroVoucher>
        {
            new()
            {
                ItemId = "0f702bad-2665-4601-8ea7-d644c749bfa7",
                IsMarkedToDelete = false,
                VoucherCode = "WM000051",
                VoucherGroupItemId = "cc0e6769-07ec-4922-9ac7-f9c9175d6faf",
                OfficeItemId = "Branch001",
                TransactionReferenceId = null,
                ChartOfAccountItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                ChartOfAccountCode = "200",
                ChartOfAccountName = "Cash at Bank",
                RelatedEntityName = null,
                RelatedEntityItemId = null,
                VoucherCreationType = EnumVoucherCreationType.Manual,
                VoucherType = EnumBuroVoucherType.Debit,
                Status = EnumVoucherStatus.Approved,
                DebitAmount = 1500.0,
                CreditAmount = 0.0,
                Narration = "only form fee mehrab",
                VoucherCreatedEmployeeName =  "Shanto Rahman",
                VoucherCreatedEmployeePin = "00214"

            },

            new()
            {
                ItemId = "bc08e3f9-5ea3-493a-9ced-6bf1f484053c",
                IsMarkedToDelete = false,
                VoucherCode = "WM000051",
                VoucherGroupItemId = "cc0e6769-07ec-4922-9ac7-f9c9175d6faf",
                OfficeItemId = "Branch001",
                TransactionReferenceId = null,
                ChartOfAccountItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                ChartOfAccountCode = "9",
                ChartOfAccountName =  "Income",
                RelatedEntityName = null,
                RelatedEntityItemId = null,
                VoucherCreationType = EnumVoucherCreationType.Manual,
                VoucherType = EnumBuroVoucherType.Credit,
                Status = EnumVoucherStatus.Approved,
                DebitAmount = 0.0,
                CreditAmount = 1500.0,
                Narration = "only form fee mehrab",
                VoucherCreatedEmployeeName =  "Shanto Rahman",
                VoucherCreatedEmployeePin = "00214"

            }

        };

        SetupRepositoryMock(voucher);
    }
    private void SeedMockVoucherConfigurationData()
    {
        var voucherConfigs = new List<BuroStandardVoucherConfiguration>
        {
            new()
            {
                ItemId = "f85b2713-ccc0-4aa2-9a01-eecca6d8db28",
                VoucherCreationType = 0,
                LedgerEntries = new List<LedgerEntry>()
                {
                    new LedgerEntry
                    {
                        ItemId = "082bcf74-5afe-48b8-bae8-243ec08765cd",
                        ChartOfAccountItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                        ChartOfAccountCode = "200",
                        ChartOfAccountName = "Cash at Bank",
                        VoucherType = EnumBuroVoucherType.Debit
                    },
                    new LedgerEntry
                    {
                        ItemId = "3421fab8-2a33-4dab-9633-a13549ff3301",
                        ChartOfAccountItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                        ChartOfAccountCode = "9",
                        ChartOfAccountName = "Income",
                        VoucherType = EnumBuroVoucherType.Credit
                    }
                }

            }
        };
        SetupRepositoryMock(voucherConfigs);
    }

    private static UpdateManualVoucherCommand GetUpdateManualVoucherCommand()
    {

        var command = new UpdateManualVoucherCommand
        {
            VoucherGroupItemId = "cc0e6769-07ec-4922-9ac7-f9c9175d6faf",
            VoucherConfigItemId = "f85b2713-ccc0-4aa2-9a01-eecca6d8db28",
            Amount = 500,
            Narration = "Member registers and pays a form fee updated"
        };

        return command;
    }
    

}