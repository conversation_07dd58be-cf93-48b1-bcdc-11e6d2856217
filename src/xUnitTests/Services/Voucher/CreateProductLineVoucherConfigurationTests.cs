using Domain.Commands.Voucher.Configuration;
using Domain.Services.Voucher.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;
using LedgerEntry = SeliseBlocks.Entities.PrimaryEntities.BURO.LedgerEntry;

namespace xUnitTests.Services.Voucher;

public class CreateProductLineVoucherConfigurationTests : BaseTest
{
    private readonly CreateProductLineVoucherConfigurationService _service;

    public CreateProductLineVoucherConfigurationTests()
    {
        const string userItemId = "001";
        var logger = Mock.Of<ILogger<CreateProductLineVoucherConfigurationService>>();
        Mock<ISecurityContextProvider> securityContextProvider = new();

        SetupMockSecurityContext(userItemId);
        SeedMockVoucherConfigurationData();

        _service = new CreateProductLineVoucherConfigurationService(
            logger,
            securityContextProvider.Object,
            _mockFinMongodbRepository.Object);
    }

    private void SeedMockVoucherConfigurationData()
    {
        var voucherConfigs = new List<BuroProductLineVoucherConfiguration>
        {
            new()
            {
                ItemId = "efce486d-7818-3434-9adc-345455",
                ProductLineItemId = "001",
                VoucherCreationType = 0,
                LedgerEntries = new List<LedgerEntry>()
                {
                    new LedgerEntry
                    {
                        ItemId = "082bcf74-5afe-48b8-bae8-243ec08765cd",
                        ChartOfAccountItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                        ChartOfAccountCode = "200",
                        ChartOfAccountName = "Cash at Bank",
                        VoucherType = EnumBuroVoucherType.Debit
                    },
                    new LedgerEntry
                    {
                        ItemId = "3421fab8-2a33-4dab-9633-a13549ff3301",
                        ChartOfAccountItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                        ChartOfAccountCode = "9",
                        ChartOfAccountName = "Income",
                        VoucherType = EnumBuroVoucherType.Credit
                    }
                }

            }
        };
        SetupRepositoryMock(voucherConfigs);
    }

    private static CreateVoucherConfigCommand GetCreateAutoVoucherConfigCommand()
    {
        var command = new CreateVoucherConfigCommand
        {
            MessageCorrelationId = "721b7f5e-4d38-4634-9f46-9ce42c098e0e",
            ProductLineItemId = "001",
            LedgerEntries = new List<Domain.Commands.Voucher.Configuration.LedgerEntry>
            {
                new Domain.Commands.Voucher.Configuration.LedgerEntry
                {
                    ChartOfAccountItemId = "f113b1d1-fd5e-4d72-b009-1edc0fba09f2",
                    VoucherType = EnumBuroVoucherType.Debit
                },
                new Domain.Commands.Voucher.Configuration.LedgerEntry
                {
                    ChartOfAccountItemId = "efce486d-7818-4162-9adc-dc41c71f0416",
                    VoucherType = EnumBuroVoucherType.Credit
                }
            }
        };

        return command;
    }


    [Fact]
    public async Task CreateAutoVoucherConfigurationAsync_ValidCommand_AutoVoucherConfigurationCreationSuccessful()
    {
        // Arrange
        var command = GetCreateAutoVoucherConfigCommand();

        // Act
        var response = await _service.CreateProductLineVoucherConfiguration(command);

        // Assert
        // Assert.NotNull(response);
        // Assert.True(response.Errors.IsValid);
        // Assert.False(response.ErrorMessages.Any());
    }


}