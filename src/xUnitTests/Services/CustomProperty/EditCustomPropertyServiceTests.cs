using Domain.Commands.CustomProperty;
using Domain.Services.CustomProperty;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.CustomProperty
{
    public class EditCustomPropertyServiceTests : BaseTest
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<EditCustomPropertyService> _logger;
        private readonly EditCustomPropertyService _editCustomPropertyService;

        public EditCustomPropertyServiceTests()
        {
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();
            _logger = Mock.Of<ILogger<EditCustomPropertyService>>();

            _editCustomPropertyService = new EditCustomPropertyService(
                _securityContextProvider,
                _logger,
                _mockRepository.Object);

            SeedMockData();
        }

        private static EditCustomPropertyCommand GetEditCustomPropertyCommand(
            string ItemId,
            EnumCustomPropertyValueType customPropertyValueType,
            string defaultValue,
            double minimumValue = 0,
            double maximumValue = 0)
        {
            var newCustomPropertyCommand = new EditCustomPropertyCommand
            {
                ItemId = ItemId,
                CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                CustomPropertyValueType = customPropertyValueType,
                DefaultValue = defaultValue,
                MinimumValue = minimumValue,
                MaximumValue = maximumValue,
                IsMandatory = false,
                IsEditable = false,
            };

            return newCustomPropertyCommand;
        }

        private void SeedMockData()
        {
            var customProperties = new List<BuroCustomProperties>
            {
                new()
                {
                    ItemId = "1",
                    PropertyName = "Property-1",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Number,
                    DefaultValue = "1"
                },
                new()
                {
                    ItemId = "2",
                    PropertyName = "Property-2",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Text,
                    DefaultValue = "Test-2"
                },

            };

            SetupRepositoryMock(customProperties);
        }

        [Fact]
        public async Task EditCustomPropertyAsync_ValidTextPropertyData_RecordEditedSuccessfully()
        {
            // Arange
            var customPropertyCommand = GetEditCustomPropertyCommand("2", EnumCustomPropertyValueType.Text, string.Empty);

            // Act
            var response = await _editCustomPropertyService.EditCustomPropertyAsync(customPropertyCommand);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }
    }
}
