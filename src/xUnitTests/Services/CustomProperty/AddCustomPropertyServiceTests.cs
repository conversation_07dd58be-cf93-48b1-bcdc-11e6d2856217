using Domain.Commands.CustomProperty;
using Domain.Services.CustomProperty;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.CustomProperty
{
    public class AddCustomPropertyServiceTests : BaseTest
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<AddCustomPropertyService> _logger;
        private readonly AddCustomPropertyService _addCustomPropertyService;
        public AddCustomPropertyServiceTests()
        {
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();
            _logger = Mock.Of<ILogger<AddCustomPropertyService>>();

            _addCustomPropertyService = new AddCustomPropertyService(
                _securityContextProvider,
                _logger,
                _mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            var customProperties = new List<BuroCustomProperties>
            {
                new()
                {
                    ItemId = "1",
                    PropertyName = "Property-1",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Number,
                    DefaultValue = "1"
                },
                new()
                {
                    ItemId = "2",
                    PropertyName = "Property-2",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Employee,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Text,
                    DefaultValue = "Test-2"
                },

            };

            SetupRepositoryMock(customProperties);
        }

        private static AddCustomPropertyCommand GetAddCustomPropertyCommand(
            string propertyName,
            EnumCustomPropertyValueType customPropertyValueType,
            string defaultValue,
            double minimumValue = 0,
            double maximumValue = 0)
        {
            var newCustomPropertyCommand = new AddCustomPropertyCommand
            {
                PropertyName = propertyName,
                CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                CustomPropertyValueType = customPropertyValueType,
                DefaultValue = defaultValue,
                MinimumValue = minimumValue,
                MaximumValue = maximumValue,
                IsMandatory = false,
                IsEditable = false,
            };

            return newCustomPropertyCommand;
        }

        [Fact]
        public async Task AddCustomPropertyAsync_ValidTextPropertyData_RecordAddedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "TextPropertyName",
                EnumCustomPropertyValueType.Text,
                string.Empty);

            // Act
            var response = await _addCustomPropertyService.AddCustomPropertyAsync(newCustomPropertyCommand);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }

        [Fact]
        public async Task AddCustomPropertyAsync_ValidNumberPropertyData_RecordAddedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "NumberPropertyName",
                EnumCustomPropertyValueType.Number,
                "0");

            // Act
            var response = await _addCustomPropertyService.AddCustomPropertyAsync(newCustomPropertyCommand);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }

        [Fact]
        public async Task AddCustomPropertyAsync_ValidRangePropertyData_RecordAddedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "RangePropertyName",
                EnumCustomPropertyValueType.Range,
                string.Empty,
                minimumValue: 0,
                maximumValue: 10);

            // Act
            var response = await _addCustomPropertyService.AddCustomPropertyAsync(newCustomPropertyCommand);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }
    }
}
