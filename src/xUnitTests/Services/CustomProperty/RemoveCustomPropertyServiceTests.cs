using Domain.Commands.CustomProperty;
using Domain.Services.CustomProperty;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.CustomProperty
{
    public class RemoveCustomPropertyServiceTests : BaseTest
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<RemoveCustomPropertyService> _logger;
        private readonly RemoveCustomPropertyService _removeCustomPropertyService;

        public RemoveCustomPropertyServiceTests()
        {
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();
            _logger = Mock.Of<ILogger<RemoveCustomPropertyService>>();

            _removeCustomPropertyService = new RemoveCustomPropertyService(
                _securityContextProvider,
                _logger,
                _mockRepository.Object);

            SeedMockData();
        }

        private static RemoveCustomPropertyCommand GetRemoveCustomPropertyCommand(
            string ItemId)
        {
            var newCustomPropertyCommand = new RemoveCustomPropertyCommand
            {
                ItemId = ItemId
            };

            return newCustomPropertyCommand;
        }

        private void SeedMockData()
        {
            var customProperties = new List<BuroCustomProperties>
            {
                new()
                {
                    ItemId = "1",
                    PropertyName = "Property-1",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Number,
                    DefaultValue = "1"
                },
                new()
                {
                    ItemId = "2",
                    PropertyName = "Property-2",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Text,
                    DefaultValue = "Test-2"
                },

            };

            SetupRepositoryMock(customProperties);
        }

        [Fact]
        public async Task RemoveCustomPropertyAsync_ExistingCustomProperty_RecordRemovedSuccessfully()
        {
            // Arange
            var customPropertyCommand = GetRemoveCustomPropertyCommand("2");

            // Act
            var response = await _removeCustomPropertyService.RemoveCustomPropertyAsync(customPropertyCommand);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }
    }
}
