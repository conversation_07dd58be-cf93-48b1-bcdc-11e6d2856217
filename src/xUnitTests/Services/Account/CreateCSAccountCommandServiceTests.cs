using Domain.Commands.Account;
using Domain.Services;
using Domain.Services.Account;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Account
{
    public class CreateCSAccountCommandServiceTests : BaseTest
    {
        private readonly string _userItemId;
        private readonly CreateCSAccountCommandService _service;
        private readonly SharedService _sharedService;
        private readonly ILogger<CreateCSAccountCommandService> _logger;
        private readonly ILogger<SharedService> _sharedServiceLogger;

        public CreateCSAccountCommandServiceTests()
        {
            _userItemId = "001";
            _logger = Mock.Of<ILogger<CreateCSAccountCommandService>>();
            _sharedServiceLogger = Mock.Of<ILogger<SharedService>>();

            _sharedService = new SharedService(
                _mockFinMongodbRepository.Object,
                _sharedServiceLogger,
                _mocksNotificationServiceClient.Object,
                _mockSequenceNumberClient.Object);

            SetupAdditionalMockServices();
            SetupMockSecurityContext(_userItemId);
            SeedMockData();

            _service = new CreateCSAccountCommandService(
                _logger,
                _mockRepository.Object,
                _mockSecurityContextProvider.Object,
                _sharedService,
                _mockServiceClient.Object);
        }

        private void SeedMockData()
        {
            SeedMockAccountData();
            SeedMockMemberData();
            SeedMockEmployeeData();
            SeedMockCSProductLineData();
            SeedMockCenterData();
        }

        private void SeedMockCenterData()
        {
            var centers = new List<BuroCenter>
            {
                new()
                {
                    ItemId = "001",
                    Name = "Center001",
                },
                new()
                {
                    ItemId = "002",
                    Name = "Center002",
                }
            };

            SetupRepositoryMock(centers);
        }

        private void SeedMockCSProductLineData()
        {
            var csProductLine = new List<ContractualSavingProductLine>
            {
                new()
                {
                    ItemId = "001"
                }
            };
            SetupRepositoryMock(csProductLine);
        }

        private void SeedMockEmployeeData()
        {
            var employees = new List<BuroEmployee>
            {
                new()
                {
                    ItemId = "001",
                    EmployeeName = "Name001",
                    UserItemId = "001"
                }
            };
            SetupRepositoryMock(employees);
        }

        private void SeedMockMemberData()
        {
            var members = new List<BuroMember>
            {
                new()
                {
                    ItemId = "001",
                    MemberName = "Name001",
                    CenterItemId = "001"
                },
                new()
                {
                    ItemId = "002",
                    MemberName = "Name002",
                    CenterItemId = "002"
                }
            };
            SetupRepositoryMock(members);
        }

        private void SeedMockAccountData()
        {
            var accounts = new List<BuroContractualSavingsAccount>
            {
                new()
                {
                    ItemId = "0001",
                    AccountCode = "GS",
                    MemberItemId = "001"
                }
            };
            SetupRepositoryMock(accounts);
        }

        private static CreateCSAccountCommand GetCreateCSAccountCommand()
        {
            var command = new CreateCSAccountCommand
            {
                MemberItemId = "001",
                InstalmentAmount = 500,
                MemberSignatureId = string.Empty,
                Nominees = new List<Nominee>()
            };

            return command;
        }

        [Fact]
        public async Task CreateCSAccountAsync_ValidCommand_CSAccountCreationSuccessfull()
        {
            // Arrange
            var command = GetCreateCSAccountCommand();

            // Act
            var response = await _service.ApplyForCSAccountAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }
    }
}
