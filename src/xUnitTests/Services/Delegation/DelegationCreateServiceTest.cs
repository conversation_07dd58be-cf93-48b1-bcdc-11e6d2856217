using Domain.Commands.Delegation;
using Domain.Commands.User;
using Domain.Services.Delegation;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Delegation;

public class DelegationCreateServiceTest : BaseTest
{
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly DelegationCreateService _service;

    public DelegationCreateServiceTest()
    {
        _securityContextProvider = Mock.Of<ISecurityContextProvider>();
        _service = new DelegationCreateService(_mockRepository.Object, _securityContextProvider);

        SeedMockData();
    }

    public DelegationCreateCommand CreateCommand()
    {
        return new DelegationCreateCommand
        {
            SourceEmployeeItemId = "Employee1",
            DelegateeEmployeeItemId = "Employee2",
            StartDate = DateTime.Parse("2023-11-01"),
            EndDate = DateTime.Parse("2023-12-01"),
            IsLifeTimeDelegation = false,
            FeatureList = new List<FeatureCommand>
            {
                new()
                {
                    FeatureItemId = "Feature1",
                    FeatureName = "Feature A",
                    FeaturePath = "/feature-a",
                    ApiPath = "/api/feature-a",
                    IconName = "icon-a"
                },
                new()
                {
                    FeatureItemId = "Feature2",
                    FeatureName = "Feature B",
                    FeaturePath = "/feature-b",
                    ApiPath = "/api/feature-b",
                    IconName = "icon-b"
                }
            }
        };
    }

    public void SeedMockData()
    {
        var employees = new List<BuroEmployee>
        {
            new()
            {
                ItemId = "Employee1",
                UserItemId = "User1",
                PersonItemId = "Person1",
                EmployeeName = "John Doe",
                EmployeePin = "12345",
                EmployeeId = "EMP001",
                DesignationItemId = "Desg1",
                DesignationCode = "D001",
                DesignationTitle = "Manager",
                CurrentDesignationEffectiveDate = DateTime.Parse("2023-01-01"),
                JoiningDate = DateTime.Parse("2020-01-01"),
                TerminationDate = null,
                CurrentCenterTitle = "Head Office",
                CurrentOfficeItemId = "Office1",
                CurrentOfficeTitle = "Main Branch"
            },
            new()
            {
                ItemId = "Employee2",
                UserItemId = "User2",
                PersonItemId = "Person2",
                EmployeeName = "Jane Smith",
                EmployeePin = "67890",
                EmployeeId = "EMP002",
                DesignationItemId = "Desg2",
                DesignationCode = "D002",
                DesignationTitle = "Assistant Manager",
                CurrentDesignationEffectiveDate = DateTime.Parse("2022-06-15"),
                JoiningDate = DateTime.Parse("2019-07-10"),
                TerminationDate = null,
                CurrentCenterTitle = "Regional Office",
                CurrentOfficeItemId = "Office2",
                CurrentOfficeTitle = "Branch 2"
            }
        };

        SetupRepositoryMock(employees);
    }

    [Fact]
    public async Task CreateAsync_ValidCommand_DelegationCreatedSuccessfully()
    {
        // Arrange
        var command = CreateCommand();

        // Act
        var response = await _service.CreateAsync(command);

        // Assert
        Assert.NotNull(response);
        Assert.True(response.Errors.IsValid);
        Assert.False(response.ErrorMessages.Any());
    }
}