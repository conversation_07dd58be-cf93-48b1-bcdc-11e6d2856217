using Domain.Commands.Delegation;
using Domain.Services.Delegation;
using Infrastructure.Contracts;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Delegation;

public class DelegationUpdateServiceTest : BaseTest
{
    private readonly Mock<IBuroKeyStore> _buroKeyStore;
    private readonly DelegationUpdateService _service;

    public DelegationUpdateServiceTest()
    {
        _buroKeyStore = new Mock<IBuroKeyStore>();
        _service = new DelegationUpdateService(_mockRepository.Object, _buroKeyStore.Object);

        SeedMockData();
    }

    public DelegationEndDateUpdateCommand CreateUpdateCommand(bool endEarly, string delegationItemId, DateTime? endDate = null)
    {
        return new DelegationEndDateUpdateCommand
        {
            DelegationItemId = delegationItemId,
            EndEarly = endEarly,
            EndDate = endDate
        };
    }

    public void SeedMockData()
    {
        var delegationRequests = new List<BuroDelegationRequest>
        {
            new()
            {
                ItemId = "Delegation1",
                DelegationStatus = EnumDelegationStatus.Delegated,
                EndDate = DateTime.Parse("2023-12-01")
            },
            new()
            {
                ItemId = "Delegation2",
                DelegationStatus = EnumDelegationStatus.Pending,
                EndDate = DateTime.Parse("2024-12-01")
            }
        };

        SetupRepositoryMock(delegationRequests);

    }

    [Fact]
    public async Task UpdateDelegationEndDate_WithValidEndDate_UpdatesSuccessfully()
    {
        // Arrange
        var newEndDate = DateTime.Parse("2024-01-01");
        var command = CreateUpdateCommand(endEarly: false, delegationItemId: "Delegation2", endDate: newEndDate);

        // Act
        var response = await _service.UpdateDelegationEndDate(command);

        // Assert
        Assert.NotNull(response);
        Assert.True(response.Errors.IsValid);
        Assert.False(response.ErrorMessages.Any());
    }

    [Fact]
    public async Task UpdateDelegationEndDate_WhenEndingEarly_UpdatesStatusToTerminated()
    {
        // Arrange
        var command = CreateUpdateCommand(endEarly: true, delegationItemId: "Delegation1");

        // Act
        var response = await _service.UpdateDelegationEndDate(command);

        // Assert
        Assert.NotNull(response);
        Assert.True(response.Errors.IsValid);
        Assert.False(response.ErrorMessages.Any());
    }
}