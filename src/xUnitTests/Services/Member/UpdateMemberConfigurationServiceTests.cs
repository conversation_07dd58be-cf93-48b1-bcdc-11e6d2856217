using Domain.Commands.Member;
using Domain.Services.Member;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Member
{
    public class UpdateMemberConfigurationServiceTests : BaseTest
    {
        private readonly Mock<IChangeLogRepository> _businessRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<UpdateMemberConfigurationService> _logger;
        private readonly UpdateMemberConfigurationService _updateMemberConfigurationService;

        public UpdateMemberConfigurationServiceTests()
        {
            _logger = Mock.Of<ILogger<UpdateMemberConfigurationService>>();
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();
            _businessRepository = new Mock<IChangeLogRepository>();

            _updateMemberConfigurationService = new UpdateMemberConfigurationService(
                _logger,
                _securityContextProvider,
                _mockRepository.Object,
                _businessRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            var configurations = new List<BuroMemberConfiguration>()
            {
                new()
                {
                    ItemId = "Configuration-1",
                    ShouldHaveFeeForAccountClosing = true,
                    FeeForAccountClosing = 100,
                    ShouldHaveDormantAccountConfiguration = true,
                    DormantAccountTimeLineLimit = 6,
                    DormantAccountTimeLineLimitUnit = EnumTimeLineUnit.Month,
                    ShouldGetFuneralContribution = true,
                    FuneralContributionAmount = 1000,
                    ShouldImposeForcedPasswordChange = true,
                    PasswordExpirationLimit = 90,
                    PasswordExpirationLimitUnit = EnumTimeLineUnit.Day,
                    SkipPasswordExpirationLimit = 3,
                    SkipPasswordExpirationLimitUnit = EnumTimeLineUnit.Day,
                    MemberIdStartingNumber = 1500,
                    MinimumIncomeForSamityCriteria = 5000,
                    MaximumIncomeForSamityCriteria = 50000,
                    ShouldTransferSMSCost = false,
                    UnitCostOfSMS = 1,
                    MemberTypes = new List<MemberType>()
                    {
                        new()
                        {
                            ItemId = "MemberType-1",
                            Name = "Ankur",
                            MinimumMonthlyIncome = 1000,
                            MaximumMonthlyIncome = 10000
                        },
                        new()
                        {
                            ItemId = "MemberType-2",
                            Name = "Agradut",
                            MinimumMonthlyIncome = 10001,
                            MaximumMonthlyIncome = 20000
                        }
                    }
                }
            };

            SetupRepositoryMock(configurations);
        }

        private static UpdateMemberConfigurationCommand GetMockUpdateMemberConfigurationCommand(string itemId)
        {
            return new UpdateMemberConfigurationCommand()
            {
                ItemId = itemId,
                ShouldHaveFeeForAccountClosing = true,
                FeeForAccountClosing = 100,
                ShouldHaveDormantAccountConfiguration = false,
                DormantAccountTimeLineLimit = 6,
                DormantAccountTimeLineLimitUnit = EnumTimeLineUnit.Month,
                ShouldGetFuneralContribution = true,
                FuneralContributionAmount = 1000,
                ShouldImposeForcedPasswordChange = false,
                PasswordExpirationLimit = 90,
                PasswordExpirationLimitUnit = EnumTimeLineUnit.Day,
                SkipPasswordExpirationLimit = 3,
                SkipPasswordExpirationLimitUnit = EnumTimeLineUnit.Day,
                MemberIdStartingNumber = 1500,
                MinimumIncomeForSamityCriteria = 1000,
                MaximumIncomeForSamityCriteria = 10000,
                ShouldTransferSMSCost = false,
                UnitCostOfSMS = 1,
                MemberTypes = new List<MemberType>()
                {
                    new()
                    {
                        ItemId = "MemberType-1",
                        Name = "Ankur",
                        MinimumMonthlyIncome = 1000,
                        MaximumMonthlyIncome = 10000
                    }
                }
            };
        }

        [Fact]
        public async Task UpdateMemberConfigurationAsync_ValidData_UpdatedSuccessfully()
        {
            // Arange
            var command = GetMockUpdateMemberConfigurationCommand("Configuration-1");

            // Act
            var response = await _updateMemberConfigurationService.UpdateMemberConfigurationAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }

        [Fact]
        public async Task UpdateMemberConfigurationAsync_InValidItemId_UpdateFailed()
        {
            // Arange
            var command = GetMockUpdateMemberConfigurationCommand("config-1");

            // Act
            var response = await _updateMemberConfigurationService.UpdateMemberConfigurationAsync(command);

            // Assert
            Assert.NotNull(response);
            Assert.False(response.Errors.IsValid);
            Assert.True(response.ErrorMessages.Any());
        }
    }
}
