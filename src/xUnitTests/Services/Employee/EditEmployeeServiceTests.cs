using Domain.Commands.Employee;
using Domain.Services.Employee;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Employee
{
    public class EditEmployeeServiceTests : BaseTest
    {
        private readonly ILogger<UpdateEmployeeService> _logger;
        private readonly UpdateEmployeeService _updateEmployeeService;
        private readonly ISecurityContextProvider _securityContextProvider;

        public EditEmployeeServiceTests()
        {
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();
            _logger = Mock.Of<ILogger<UpdateEmployeeService>>();
            _updateEmployeeService = new UpdateEmployeeService(
                _mockRepository.Object,
                _logger,
                _securityContextProvider);


            SeedMockBuroEmployee();
        }

        private static EditEmployeeCommand GetEditEmployeeCommand(
            string itemId,
            string nidNo,
            string employeeName,
            string fatherName,
            string motherName,
            string workPhone,
            string personalPhone,
            string countryId,
            string countryName,
            string countryLangKey,
            string districtId,
            string districtName,
            string districtLangKey,
            string address,
            string postCode,
            string postOfficeId,
            string postOfficeName,
            string postOfficeLangKey,
            string unionId,
            string unionName,
            string unionLangKey,
            string upazilaId,
            string upazilaName,
            string upazilaLangKey,
            string wardId,
            string wardName,
            string wardLangKey)
        {
            var editEmployeeCommand = new EditEmployeeCommand
            {
                ItemId = itemId,
                NidNo = nidNo,
                EmployeeName = employeeName,
                FatherName = fatherName,
                MotherName = motherName,
                WorkPhone = workPhone,
                PersonalPhone = personalPhone,
                CountryId = countryId,
                CountryName = countryName,
                CountryLangKey = countryLangKey,
                DistrictId = districtId,
                DistrictName = districtName,
                DistrictLangKey = districtLangKey,
                Address = address,
                PostCode = postCode,
                PostOfficeId = postOfficeId,
                PostOfficeName = postOfficeName,
                PostOfficeLangKey = postOfficeLangKey,
                UnionId = unionId,
                UnionName = unionName,
                UnionLangKey = unionLangKey,
                UpazilaId = upazilaId,
                UpazilaName = upazilaName,
                UpazilaLangKey = upazilaLangKey,
                WardId = wardId,
                WardName = wardName,
                WardLangKey = wardLangKey
            };

            return editEmployeeCommand;
        }

        private void SeedMockBuroEmployee()
        {
            var existingEmployee = new List<BuroEmployee>
            {
                new()
                {
                    ItemId = "1",
                    EmployeeNid = "123456789",
                    EmployeeName = "John Doe",
                    FatherName = "Father Doe",
                    MotherName = "Mother Doe",
                    WorkPhone = "0123456789",
                    PersonalPhone = "9876543210",
                    CountryId = "Country-001",
                    CountryName = "Sample Country",
                    CountryLangKey = "EN",
                    DistrictId = "District-001",
                    DistrictName = "Sample District",
                    DistrictLangKey = "EN",
                    Address = "123 Sample Street",
                    PostCode = "12345",
                    PostOfficeId = "PostOffice-001",
                    PostOfficeName = "Sample Post Office",
                    PostOfficeLangKey = "EN",
                    UnionId = "Union-001",
                    UnionName = "Sample Union",
                    UnionLangKey = "EN",
                    UpazilaId = "Upazila-001",
                    UpazilaName = "Sample Upazila",
                    UpazilaLangKey = "EN",
                    WardId = "Ward-001",
                    WardName = "Sample Ward",
                    WardLangKey = "EN"
                },
                new()
                {
                    ItemId = "2",
                    EmployeeNid = "123456789",
                    EmployeeName = "John Doe",
                    FatherName = "Father Doe",
                    MotherName = "Mother Doe",
                    WorkPhone = "0123456789",
                    PersonalPhone = "9876543210",
                    CountryId = "Country-001",
                    CountryName = "Sample Country",
                    CountryLangKey = "EN",
                    DistrictId = "District-001",
                    DistrictName = "Sample District",
                    DistrictLangKey = "EN",
                    Address = "123 Sample Street",
                    PostCode = "12345",
                    PostOfficeId = "PostOffice-001",
                    PostOfficeName = "Sample Post Office",
                    PostOfficeLangKey = "EN",
                    UnionId = "Union-001",
                    UnionName = "Sample Union",
                    UnionLangKey = "EN",
                    UpazilaId = "Upazila-001",
                    UpazilaName = "Sample Upazila",
                    UpazilaLangKey = "EN",
                    WardId = "Ward-001",
                    WardName = "Sample Ward",
                    WardLangKey = "EN"
                }
            };

            SetupRepositoryMock(existingEmployee);
        }

        [Fact]
        public async Task EditEmployeeAsync_ValidTextPropertyData_RecordEditedSuccessfully()
        {
            // Arange
            var updateEmployeeCommand = GetEditEmployeeCommand(
                "1",                  // itemId
                "123456789",          // nidNo
                "John Doe",           // employeeName
                "Father Doe",         // fatherName
                "Mother Doe",         // motherName
                "0123456789",         // workPhone
                "9876543210",         // personalPhone
                "Country-001",        // countryId
                "Sample Country",     // countryName
                "EN",                 // countryLangKey
                "District-001",       // districtId
                "Sample District",    // districtName
                "EN",                 // districtLangKey
                "123 Sample Street",  // address
                "12345",              // postCode
                "PostOffice-001",     // postOfficeId
                "Sample Post Office", // postOfficeName
                "EN",                 // postOfficeLangKey
                "Union-001",          // unionId
                "Sample Union",       // unionName
                "EN",                 // unionLangKey
                "Upazila-001",        // upazilaId
                "Sample Upazila",     // upazilaName
                "EN",                 // upazilaLangKey
                "Ward-001",           // wardId
                "Sample Ward",        // wardName
                "EN"                  // wardLangKey
            );

            // Act
            var response = await _updateEmployeeService.EditEmployeeAsync(updateEmployeeCommand);

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }
    }
}
