using Domain.Commands.Employee;
using Domain.Contracts.User;
using Domain.Services.Employee;
using FluentValidation.Results;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Employee;

public class AddEmployeeServiceTests : BaseTest
{
    private readonly ILogger<AddEmployeeService> _logger;
    private readonly AddEmployeeService _service;
    private readonly Mock<IUserService> _userServiceMock;

    public AddEmployeeServiceTests()
    {
        _logger = Mock.Of<ILogger<AddEmployeeService>>();
        _userServiceMock = new Mock<IUserService>();

        SetupAdditionalMockServices();
        SetupMockUserService();

        _service = new AddEmployeeService(
            _mockRepository.Object,
            _userServiceMock.Object,
            _logger,
            _mockSequenceNumberClient.Object,
            _mockServiceClient.Object);

        SeedMockBuroEmployee();
    }

    private void SetupMockUserService()
    {
        _userServiceMock.Setup(x => x.CreateUserFromEmployee(It.IsAny<BuroEmployee>(), It.IsAny<string>()))
            .ReturnsAsync((BuroEmployee employee, string userItemId) =>
            {
                return new CommandResponse
                {
                    Errors = new ValidationResult()
                };
            });
    }

    private void SeedMockBuroEmployee()
    {
        var list = new List<BuroEmployee>
        {
            new()
            {
                ItemId = Guid.NewGuid().ToString(),
                EmployeeName = "John Doe"
            }
        };

        SetupRepositoryMock(list);
    }

    private static AddEmployeeCommand CreateAddEmployeeCommand()
    {
        return new AddEmployeeCommand { EmployeeName = "John Doe" }; // populate it
    }

    [Fact]
    public async Task SaveEmployee_WhenSaveIsSuccessful_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = CreateAddEmployeeCommand();

        // Act
        var result = await _service.SaveEmployee(command);

        // Assert
        Assert.NotNull(result);
    }

    [Fact]
    public async Task SaveEmployee_WhenSaveIsFailed_ShouldReturnSuccessResponse()
    {
        // Arrange
        var command = CreateAddEmployeeCommand();

        // Act
        var result = await _service.SaveEmployee(command);

        // Assert
        Assert.False(result.IsSuccess());
    }
}