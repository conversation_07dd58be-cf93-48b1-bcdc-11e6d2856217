using Domain.Commands.Register;
using Domain.Services;
using Domain.Services.Register;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Register;

public class ReturnChequeServiceTests : BaseTest
{
    private readonly ReturnChequeService _service;
    private readonly ILogger<SharedService> _sharedServiceLogger;
    private readonly SharedService _sharedService;

    public ReturnChequeServiceTests()
    {
        const string userItemId = "001";
        var logger = Mock.Of<ILogger<ReturnChequeService>>();
        _sharedServiceLogger = Mock.Of<ILogger<SharedService>>();
        Mock<ISecurityContextProvider> securityContextProvider = new();

        _sharedService = new SharedService(
            _mockFinMongodbRepository.Object,
            _sharedServiceLogger,
            _mocksNotificationServiceClient.Object,
            _mockSequenceNumberClient.Object);

        SetupMockSecurityContext(userItemId);
        SetupMockBuroChequeRegisterData();
        SetupMockBuroChequeEmployeeData();

        _service = new ReturnChequeService(
            logger,
            securityContextProvider.Object,
            _mockFinMongodbRepository.Object,
            _sharedService
            );
    }

    private void SetupMockBuroChequeRegisterData()
    {
        var buroChequeRegister = new List<BuroChequeRegister>
        {
            new()
            {
                ItemId = "001",
                ChequeNumber = "CHQ123456",
                BranchItemId = "branch001",
                MemberItemId = "member001",
                MemberName = "John Doe",
                MemberId = "MEM001",
                BankName = "Test Bank",
                RoutingNumber = "*********",
                Amount = 5000.00,
                DepositDate = DateTime.UtcNow.AddDays(-5),
                Status = EnumChequeRegisterStatus.Deposit,
                IsEligibleReturn = true,
                ChequeDepositedByEmployeeName = "Jane Smith",
                ChequeDepositedByEmployeePin = "EMP123",
                ChequeDepositedByEmployeeDesignation = "Cashier"
            },
            new()
            {
                ItemId = "002",
                ChequeNumber = "CHQ789012",
                BranchItemId = "branch001",
                MemberItemId = "member002",
                MemberName = "Jane Doe",
                MemberId = "MEM002",
                BankName = "Test Bank",
                RoutingNumber = "*********",
                Amount = 10000.00,
                DepositDate = DateTime.UtcNow.AddDays(-10),
                Status = EnumChequeRegisterStatus.Deposit,
                IsEligibleReturn = true,
                ChequeDepositedByEmployeeName = "John Smith",
                ChequeDepositedByEmployeePin = "EMP456",
                ChequeDepositedByEmployeeDesignation = "Cashier"
            }

        };

        var resultBuroChequeRegister = new List<BuroChequeRegister>
        {
            new()
            {
                ItemId = "001",
                ChequeNumber = "CHQ123456",
                BranchItemId = "branch001",
                MemberItemId = "member001",
                MemberName = "John Doe",
                MemberId = "MEM001",
                BankName = "Test Bank",
                RoutingNumber = "*********",
                Amount = 5000.00,
                DepositDate = DateTime.UtcNow.AddDays(-5),
                Status = EnumChequeRegisterStatus.Deposit,
                IsEligibleReturn = true,
                ChequeDepositedByEmployeeName = "Jane Smith",
                ChequeDepositedByEmployeePin = "EMP123",
                ChequeDepositedByEmployeeDesignation = "Cashier"
            },
        };

        SetupFindWithProjectionAsync(buroChequeRegister, resultBuroChequeRegister);
    }

    private void SetupMockBuroChequeEmployeeData()
    {
        var buroChequeEmployee = new List<BuroEmployee>
        {
            new()
            {
                ItemId = "001",
                EmployeeName = "Jane Smith",
                EmployeePin = "EMP123",
                DesignationTitle = "Manager"
            },
            new()
            {
                ItemId = "002",
                EmployeeName = "Mehrab Shahriar",
                EmployeePin = "EMP456",
                DesignationTitle = "Manager"
            }
        };

        SetupRepositoryMock(buroChequeEmployee);
    }


    private static ReturnChequeCommand GetReturnChequeCommand()
    {
        var command = new ReturnChequeCommand()
        {
            ChequeRegisterItemId = "001",
            ReturnAckStorageItemId = "we23rt34gmrf45t",
            MessageCorrelationId = "721b7f5e-4d38-4634-9f46-9ce42c098e0e",
        };
        return command;
    }

    [Fact]
    public async Task ChequeReturnAsync_ValidCommand_ChequeReturnAsyncSuccessful()
    {
        // Arrange
        var command = GetReturnChequeCommand();

        // Act
        var response = await _service.ChequeReturnAsync(command);

        // Assert
        // Assert.NotNull(response);
        // Assert.True(response.Errors.IsValid);
        // Assert.False(response.ErrorMessages.Any());
    }


}
