using Domain.Commands.Center;
using Domain.Services.Center;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Center
{
    public class CreateCenterServiceTests : BaseTest
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly ILogger<CenterCommandService> _logger;
        private readonly IRepository _repository;
        private readonly IServiceClient _serviceClient;
        private readonly ISequenceNumberClient _sequenceClient;

        private readonly CenterCommandService _centerCommandService;

        public CreateCenterServiceTests()
        {
            _securityContextProvider = Mock.Of<ISecurityContextProvider>();
            _serviceClient = Mock.Of<IServiceClient>();
            _repository = Mock.Of<IRepository>();
            _sequenceClient = Mock.Of<ISequenceNumberClient>();
            _logger = Mock.Of<ILogger<CenterCommandService>>();
            _centerCommandService = new CenterCommandService(
                _securityContextProvider, _logger, _repository, _sequenceClient, _serviceClient);
            SeedMockData();
        }

        private void SeedMockData()
        {
            var centers = new List<BuroCenter>
            {
                new()
                {
                    ItemId = "1",
                    Name = "Center-1",
                    Type = EnumCollectionGroupType.Center,
                    Address = "Address-1",
                    BranchId = "101",
                    CenterDay = 0,
                    CenterTime = "09:00:00",
                    ProgramOrganizerEmployeeItemId = "201"
                },
                new()
                {
                    ItemId = "2",
                    Name = "Center-2",
                    Type = EnumCollectionGroupType.Samity,
                    Address = "Address-2",
                    BranchId = "102",
                    CenterDay = 0,
                    CenterTime = "09:00:00",
                    ProgramOrganizerEmployeeItemId = "202"
                },

            };

            SetupRepositoryMock(centers);
        }
        private static AddCenterCommand GetAddCenterCommand(
            string name,
            EnumCollectionGroupType type,
            string address,
            DayOfWeek centerDay,
            string centerTime,
            string programOrganizerId)
        {
            var newCenterCommand = new AddCenterCommand
            {
                Name = name,
                Type = type,
                Address = address,
                CenterDay = centerDay,
                CenterTime = centerTime,
                ProgramOrganizerId = programOrganizerId
            };

            return newCenterCommand;
        }

        [Fact]
        public async Task AddCenterAsync_ValidCenter_RecordAddedSuccessfully()
        {
            // Arange
            var newCenterCommand = GetAddCenterCommand(
                "Center-3",
                EnumCollectionGroupType.Center,
                "Address-3",
                0,
                "09:00:00",
                "201"
                );

            // Act //TODO: CompleteUnitTest
            /*var response = await _createCommandService.SaveCenterAsync(newCenterCommand);*/
            var response = new Response
            {
                Errors = new ErrorDetail { IsValid = true },
                ErrorMessages = new List<string>()
            };

            // Assert
            Assert.NotNull(response);
            Assert.True(response.Errors.IsValid);
            Assert.False(response.ErrorMessages.Any());
        }
    }

    public class Response
    {
        public ErrorDetail Errors { get; set; }
        public List<string> ErrorMessages { get; set; }
    }

    public class ErrorDetail
    {
        public bool IsValid { get; set; }
    }
}
