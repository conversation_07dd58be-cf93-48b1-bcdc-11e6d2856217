using Domain.Commands.Holiday;
using Domain.Services.Holiday;
using Microsoft.Extensions.Logging;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using xUnitTests.Helpers;

namespace xUnitTests.Services.Holiday;

public class CreateHolidayServiceTests : BaseTest
{

    private readonly CreateHolidayService _service;
    private readonly ILogger<CreateHolidayService> _logger;
    private readonly Mock<ISecurityContextProvider> _securityContextProvider;

    public CreateHolidayServiceTests()
    {
        _logger = Mock.Of<ILogger<CreateHolidayService>>();
        _securityContextProvider = new Mock<ISecurityContextProvider>();

        SetupMockForSecurityContextProvider();
        SeedMockData();

        _service = new CreateHolidayService(
            _logger,
            _securityContextProvider.Object,
            _mockRepository.Object,
            _mockServiceClient.Object);
    }

    private void SetupMockForSecurityContextProvider()
    {
        var securityContext = CreateMockSecurityContext("e2e03735-058a-4b8c-bfe3-ea09246215b0");

        _securityContextProvider.Setup(x => x.GetSecurityContext())
            .Returns(securityContext);
    }

    private static SecurityContext CreateMockSecurityContext(string userItemId)
    {
        return new(
            userName: string.Empty,
            roles: Enumerable.Empty<string>(),
            tenantId: string.Empty,
            oauthBearerToken: "sample_oauth_bearer_token",
            isAuthenticated: false,
            isUserAuthenticated: false,
            displayName: "test admin",
            userId: userItemId,
            requestOrigin: string.Empty,
            siteName: string.Empty,
            siteId: string.Empty,
            email: string.Empty,
            language: string.Empty,
            phoneNumber: string.Empty,
            sessionId: string.Empty,
            requestUri: new Uri("about:blank"),
            serviceVersion: string.Empty,
            hasDynamicRoles: false,
            tokenHijackingProtectionHash: string.Empty,
            userAutoExpire: false,
            userExpireOn: DateTime.MinValue,
            userPrefferedLanguage: string.Empty,
            postLogOutHandlerDataKey: string.Empty,
            organizationId: string.Empty
        );
    }

    private void SeedMockData()
    {
        SeedMockMemberData();
        SeedMockEmployeeData();
        SeedMockCenterData();
        SeedMockHolidayData();
    }

    private void SeedMockEmployeeData()
    {
        var employees = new List<BuroEmployee>
        {
            new()
            {
                ItemId = "001",
                EmployeeName = "test admin",
                UserItemId = "e2e03735-058a-4b8c-bfe3-ea09246215b0",
                EmployeePin = "2004"
            }
        };

        SetupRepositoryMock(employees);

    }

    private void SeedMockMemberData()
    {
        var members = new List<BuroMember>
        {
            new()
            {
                ItemId = "001",
                MemberName = "Name001",
                CenterItemId = "001",
                CenterName = "Center-1",
            },
            new()
            {
                ItemId = "002",
                MemberName = "Name002",
                CenterItemId = "002",
                CenterName = "Center-2",
            }
        };
        SetupRepositoryMock(members);
    }

    private void SeedMockCenterData()
    {
        var centers = new List<BuroCenter>
        {
            new()
            {
                ItemId = "001",
                Name = "Center-1",

            },
            new()
            {
                ItemId = "002",
                Name = "Center-1",
            }
        };
        SetupRepositoryMock(centers);
    }

    private void SeedMockHolidayData()
    {
        var holidays = new List<BuroHoliday>
        {
            new()
            {
                ItemId = "091a5b56-6f16-4749-b811-3d27642ec4f3",
                CreatedBy = "e2e03735-058a-4b8c-bfe3-ea09246215b0",
                CreateDate = DateTime.Parse("2024-12-23T06:23:56.545+0000"),
                Language = "en-US",
                LastUpdateDate = DateTime.Parse("2024-12-23T06:23:56.545+0000"),
                LastUpdatedBy = "e2e03735-058a-4b8c-bfe3-ea09246215b0",
                Tags = new [] { "Is-A-Holiday" },
                TenantId = "B14ED887-CBE7-4B8D-AA66-4FA0FAD9E48F",
                IsMarkedToDelete = false,
                RolesAllowedToRead = Array.Empty<string>(),
                IdsAllowedToRead = new [] { "e2e03735-058a-4b8c-bfe3-ea09246215b0" },
                RolesAllowedToWrite = Array.Empty<string>(),
                IdsAllowedToWrite = new [] { "e2e03735-058a-4b8c-bfe3-ea09246215b0" },
                RolesAllowedToUpdate = Array.Empty<string>(),
                IdsAllowedToUpdate = new [] { "e2e03735-058a-4b8c-bfe3-ea09246215b0" },
                RolesAllowedToDelete = Array.Empty<string>(),
                IdsAllowedToDelete = new [] { "e2e03735-058a-4b8c-bfe3-ea09246215b0" },
                GlobalHolidayName = "New Year's Eve",
                HolidayStartDate = DateTime.Parse("2024-12-31T00:00:00.000+0000"),
                HolidayEndDate = DateTime.Parse("2024-12-31T23:59:59.999+0000"),
                PreviousHolidayEndDate = null,
                HolidayType = 0,
                OfficeItemId = null,
                OfficeName = null,
                OfficeCode = null,
                OfficeType = 0,
                MemberItemId = null,
                MemberName = null,
                MemberId = null,
                CenterItemId = null,
                CenterName = null,
                CenterCode = null,
                CenterType = 0,
                PoName = null,
                Reason = null,
                CreatedByEmployeeName = "test admin",
                CreatedByEmployeePin = "2048",
                DeletedByEmployeeName = null,
                DeletedByEmployeePin = null,
                EarlyEndedByEmployeeName = null,
                EarlyEndedByEmployeePin = null,
                IsEarlyEnd = false,
                IsActive = true
            }

        };
        SetupRepositoryMock(holidays);
    }

    private static CreateHolidayCommand CreateHolidayCommand()
    {
        var command = new CreateHolidayCommand
        {
            GlobalHolidayName = "Buro Day",
            HolidayStartDate = DateTime.Parse("2025-01-01T00:00:00.000Z"),
            HolidayEndDate = DateTime.Parse("2025-01-01T23:59:59.999Z"),
            HolidayType = EnumHolidayType.Global
        };

        return command;
    }


    [Fact]
    public async Task CreateHolidayAsync_ValidCommand_HolidayCreationSuccessfull()
    {
        // Arrange
        var command = CreateHolidayCommand();

        // Act
        var response = await _service.CreateHoliday(command);

        // Assert
        Assert.NotNull(response);
    }





}