using Domain.Commands.Member;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using Validators.Member;
using xUnitTests.Helpers;

namespace xUnitTests.Validators.Member
{
    public class UpdateMemberConfigurationCommandValidatorTests : BaseTest
    {
        private readonly UpdateMemberConfigurationCommandValidator _validator;

        public UpdateMemberConfigurationCommandValidatorTests()
        {
            _validator = new UpdateMemberConfigurationCommandValidator();
        }

        private static UpdateMemberConfigurationCommand GetMockUpdateMemberConfigurationCommand()
        {
            return new UpdateMemberConfigurationCommand()
            {
                ItemId = "Configuration-1",
                ShouldHaveFeeForAccountClosing = true,
                FeeForAccountClosing = 100,
                ShouldHaveDormantAccountConfiguration = true,
                DormantAccountTimeLineLimit = 6,
                DormantAccountTimeLineLimitUnit = EnumTimeLineUnit.Month,
                ShouldGetFuneralContribution = true,
                FuneralContributionAmount = 1000,
                ShouldImposeForcedPasswordChange = true,
                PasswordExpirationLimit = 90,
                PasswordExpirationLimitUnit = EnumTimeLineUnit.Day,
                SkipPasswordExpirationLimit = 3,
                SkipPasswordExpirationLimitUnit = EnumTimeLineUnit.Day,
                MemberIdStartingNumber = 1500,
                MinimumIncomeForSamityCriteria = 5000,
                MaximumIncomeForSamityCriteria = 50000,
                ShouldTransferSMSCost = false,
                UnitCostOfSMS = 1,
                MemberTypes = new List<MemberType>()
                {
                    new()
                    {
                        ItemId = "MemberType-1",
                        Name = "Ankur",
                        MinimumMonthlyIncome = 1000,
                        MaximumMonthlyIncome = 10000
                    },
                    new()
                    {
                        ItemId = "MemberType-2",
                        Name = "Agradut",
                        MinimumMonthlyIncome = 10001,
                        MaximumMonthlyIncome = 20000
                    }
                }
            };
        }

        [Fact]
        public void UpdateMemberConfigurationAsync_ValidPayload_ValidatedSuccessfully()
        {
            // Arrange
            var command = GetMockUpdateMemberConfigurationCommand();

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void UpdateMemberConfigurationAsync_InvalidRangeInAMemberTypeData_ValidationFailed()
        {
            // Arrange
            var command = GetMockUpdateMemberConfigurationCommand();
            command.MemberTypes = new List<MemberType>()
            {
                new()
                {
                    ItemId = "Type-1",
                    Name = "Ankur",
                    MinimumMonthlyIncome= 1000,
                    MaximumMonthlyIncome= 500,
                }
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public void UpdateMemberConfigurationAsync_GapeInAMemberTypeRanges_ValidationFailed()
        {
            // Arrange
            var command = GetMockUpdateMemberConfigurationCommand();
            command.MemberTypes = new List<MemberType>()
            {
                new()
                {
                    ItemId = "Type-1",
                    Name = "Ankur",
                    MinimumMonthlyIncome= 1000,
                    MaximumMonthlyIncome= 5000,
                },
                new()
                {
                    ItemId = "Type-2",
                    Name = "Agradut",
                    MinimumMonthlyIncome= 6000,
                    MaximumMonthlyIncome= 10000,
                }
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public void UpdateMemberConfigurationAsync_OverlapInAMemberTypeRanges_ValidationFailed()
        {
            // Arrange
            var command = GetMockUpdateMemberConfigurationCommand();
            command.MemberTypes = new List<MemberType>()
            {
                new()
                {
                    ItemId = "Type-1",
                    Name = "Ankur",
                    MinimumMonthlyIncome= 1000,
                    MaximumMonthlyIncome= 5000,
                },
                new()
                {
                    ItemId = "Type-2",
                    Name = "Agradut",
                    MinimumMonthlyIncome= 4000,
                    MaximumMonthlyIncome= 10000,
                }
            };

            // Act
            var result = _validator.TestValidate(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
