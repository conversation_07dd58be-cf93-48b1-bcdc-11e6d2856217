using Domain.Commands.Member;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using Validators.Member;
using xUnitTests.Helpers;

namespace xUnitTests.Validators.Member
{
    public class PhoneNumberChangeRequestCommandValidatorTests : BaseTest
    {
        private readonly string _memberItemId;
        private readonly PhoneNumberChangeRequestCommandValidator _validator;

        public PhoneNumberChangeRequestCommandValidatorTests()
        {
            _memberItemId = Guid.NewGuid().ToString();
            _validator = new PhoneNumberChangeRequestCommandValidator(
                _mockRepository.Object);

            SeedMockData();
        }

        public void SeedMockData()
        {
            var members = new List<BuroMember>
            {
                new()
                {
                    ItemId = _memberItemId,
                    MemberName = "Member001",
                    ContactNumbers = new List<string>
                    {
                        "123456789",
                        "234567890"
                    }
                }
            };

            SetupRepositoryMock(members);
        }

        private PhoneNumberChangeRequestCommand MakePhoneNumberChangeRequestCommand(string oldNumber, string newNumber)
        {
            return new PhoneNumberChangeRequestCommand
            {
                MemberItemId = _memberItemId,
                OldPhoneNumber = oldNumber,
                NewPhoneNumber = newNumber
            };
        }

        public async Task ValidatePhoneNumberChangeRequestCommand_ValidRequest_ValidationSuccessful()
        {
            // Arrange
            var command = MakePhoneNumberChangeRequestCommand("123456789", "123444789");

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        public async Task ValidatePhoneNumberChangeRequestCommand_InValidOldPhoneNumber_ValidationFailed()
        {
            // Arrange
            var command = MakePhoneNumberChangeRequestCommand("1234", "123444789");

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        public async Task ValidatePhoneNumberChangeRequestCommand_InValidNewPhoneNumber_ValidationFailed()
        {
            // Arrange
            var command = MakePhoneNumberChangeRequestCommand("123456789", "234567890");

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
