using Domain.Commands.Role;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using Validators.Role;
using xUnitTests.Helpers;

namespace xUnitTests.Validators.Role
{
    public class UpdateFeatureRoleMapWithStatusCommandValidatorTests : BaseTest
    {
        private readonly int _numberOfFeatures;
        private readonly int _numberOfRoles;

        private readonly UpdateFeatureRoleMapWithStatusCommandValidator _validator;

        public UpdateFeatureRoleMapWithStatusCommandValidatorTests()
        {
            _numberOfFeatures = 3;
            _numberOfRoles = 3;

            _validator = new UpdateFeatureRoleMapWithStatusCommandValidator(_mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            SeedFeatureData();
            SeedRoleData();
            SeedFeatureRoleMapData();
        }

        private void SeedFeatureData()
        {
            var features = new List<BuroFeature>();
            for (int i = 0; i < _numberOfFeatures; i++)
            {
                var serial = i.ToString();

                features.Add(new BuroFeature
                {
                    ItemId = nameof(BuroFeature.ItemId) + serial,
                    FeatureName = nameof(BuroFeature.FeatureName) + serial,
                    FeaturePath = nameof(BuroFeature.FeaturePath) + serial,
                    ApiPath = nameof(BuroFeature.ApiPath) + serial,
                });
            }

            SetupRepositoryMock(features);
        }

        private void SeedRoleData()
        {
            var roles = new List<BuroRole>();
            for (int i = 0; i < _numberOfRoles; i++)
            {
                var serial = i.ToString();

                roles.Add(new BuroRole
                {
                    ItemId = nameof(BuroRole.ItemId) + serial,
                    RoleName = nameof(BuroRole.RoleName) + serial
                });
            }

            SetupRepositoryMock(roles);
        }

        private void SeedFeatureRoleMapData()
        {
            var featureRoleMaps = new List<BuroFeatureRoleMap>
            {
                new()
                {
                    ItemId = nameof(BuroFeatureRoleMap.ItemId) + "1",
                    RoleItemId = nameof(BuroRole.ItemId) + "1",
                    RoleName = nameof(BuroRole.RoleName) + "1",
                    Features = new List<FeatureForRoleMap>
                    {
                        new()
                        {
                            ItemId = nameof(BuroFeature.ItemId) + "1",
                            FeatureName = nameof(BuroFeature.FeatureName) + "1",
                            FeaturePath = nameof(BuroFeature.FeaturePath) + "1",
                            ApiPath = nameof(BuroFeature.ApiPath) + "1",
                            FeatureStatus = EnumFeatureStatus.None
                        },
                        new()
                        {
                            ItemId = nameof(BuroFeature.ItemId) + "2",
                            FeatureName = nameof(BuroFeature.FeatureName) + "2",
                            FeaturePath = nameof(BuroFeature.FeaturePath) + "2",
                            ApiPath = nameof(BuroFeature.ApiPath) + "2",
                            FeatureStatus = EnumFeatureStatus.Required
                        }
                    }
                }
            };

            SetupRepositoryMock(featureRoleMaps);
        }

        private static UpdateFeatureRoleMapWithStatusCommand PrepareCreateFeatureRoleMapCommand(
            string featureRoleMapId,
            int roleStartsFrom,
            int numberOfFeature)
        {
            var roleSerial = roleStartsFrom.ToString();

            var featureRoleMapCommand = new UpdateFeatureRoleMapWithStatusCommand
            {
                FeatureRoleMapItemId = nameof(BuroFeatureRoleMap.ItemId) + featureRoleMapId,
                RoleItemId = nameof(BuroRole.ItemId) + roleSerial,
                RoleName = nameof(BuroRole.RoleName) + roleSerial,
                RoleKey = nameof(BuroRole.RoleKey) + roleSerial,
                FeatureRequests = new List<FeatureRequestWithStatus>()
            };

            for (int i = 0; i < numberOfFeature; i++)
            {
                var serial = i.ToString();

                featureRoleMapCommand.FeatureRequests.Add(new FeatureRequestWithStatus
                {
                    FeatureItemId = nameof(BuroFeature.ItemId) + serial,
                    FeatureName = nameof(BuroFeature.FeatureName) + serial,
                    FeaturePath = nameof(BuroFeature.FeaturePath) + serial,
                    ApiPath = nameof(BuroFeature.ApiPath) + serial,
                });
            }

            return featureRoleMapCommand;
        }

        [Fact]
        public async Task ValidateUpdateFeatureRoleMapWithStatusCommand_AllValidData_ValidatedSuccessfully()
        {
            // Arange
            var command = PrepareCreateFeatureRoleMapCommand("1", 1, 2);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateUpdateFeatureRoleMapWithStatusCommand_InvalidRoleData_ValidationFailed()
        {
            // Arange
            var command = PrepareCreateFeatureRoleMapCommand("1", 4, 2);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public async Task ValidateUpdateFeatureRoleMapWithStatusCommand_InvalidFeatureData_ValidationFailed()
        {
            // Arange
            var command = PrepareCreateFeatureRoleMapCommand("1", 1, 5);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public async Task ValidateUpdateFeatureRoleMapWithStatusCommand_InvalidFeatureRoleMapId_ValidationFailed()
        {
            // Arange
            var command = PrepareCreateFeatureRoleMapCommand("2", 1, 5);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}