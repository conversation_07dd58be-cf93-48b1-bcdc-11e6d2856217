using Domain.Commands.Role;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using Validators.Role;
using xUnitTests.Helpers;

namespace xUnitTests.Validators.Role
{
    public class UpdateRolesCommandValidatorTests : BaseTest
    {
        private readonly string _itemId;
        private readonly UpdateRolesCommandValidator _validator;

        public UpdateRolesCommandValidatorTests()
        {
            _itemId = Guid.NewGuid().ToString();
            _validator = new UpdateRolesCommandValidator(_mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            var mockData = new List<BuroRole>
            {
                new ()
                {
                    ItemId = _itemId,
                    RoleName = nameof(BuroRole.RoleName)
                }
            };

            SetupRepositoryMock(mockData);
        }

        private static UpdateRolesCommand GetEditCustomPropertyCommand(
            string ItemId,
            string roleName)
        {
            var newUpdateRolesCommand = new UpdateRolesCommand
            {
                RoleItemId = ItemId,
                RoleName = roleName
            };

            return newUpdateRolesCommand;
        }

        [Fact]
        public async Task ValidateUpdateRolesCommand_ValidData_ValidatedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                _itemId,
                "NewRoleName");

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateUpdateRolesCommand_RoleDoesNotExists_ValidationFailed()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                _itemId + "1",
                "NewRoleName");

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public async Task ValidateUpdateRolesCommand_InvalidRoleName_ValidationFailed()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                _itemId + "1",
                string.Empty);

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
