using Domain.Commands.Role;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using Validators.Role;
using xUnitTests.Helpers;

namespace xUnitTests.Validators.Role
{
    public class CreateFeatureRoleMapCommandValidatorTests : BaseTest
    {
        private readonly int _numberOfFeatures;
        private readonly int _numberOfRoles;

        private readonly CreateFeatureRoleMapCommandValidator _validator;

        public CreateFeatureRoleMapCommandValidatorTests()
        {
            _numberOfFeatures = 3;
            _numberOfRoles = 3;

            _validator = new CreateFeatureRoleMapCommandValidator(_mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            SeedFeatureData();
            SeedRoleData();
            SeedFeatureRoleMapData();
        }

        private void SeedFeatureData()
        {
            var features = new List<BuroFeature>();
            for (int i = 0; i < _numberOfFeatures; i++)
            {
                var serial = i.ToString();

                features.Add(new BuroFeature
                {
                    ItemId = nameof(BuroFeature.ItemId) + serial,
                    FeatureName = nameof(BuroFeature.FeatureName) + serial,
                    FeaturePath = nameof(BuroFeature.FeaturePath) + serial,
                    ApiPath = nameof(BuroFeature.ApiPath) + serial,
                });
            }

            SetupRepositoryMock(features);
        }

        private void SeedRoleData()
        {
            var roles = new List<BuroRole>();
            for (int i = 0; i < _numberOfRoles; i++)
            {
                var serial = i.ToString();

                roles.Add(new BuroRole
                {
                    ItemId = nameof(BuroRole.ItemId) + serial,
                    RoleName = nameof(BuroRole.RoleName) + serial
                });
            }

            SetupRepositoryMock(roles);
        }

        private void SeedFeatureRoleMapData()
        {
            SetupRepositoryMock(new List<BuroFeatureRoleMap>());
        }

        private static CreateFeatureRoleMapCommand PrepareCreateFeatureRoleMapCommand(int roleStartsFrom, int numberOfFeature)
        {
            var roleSerial = roleStartsFrom.ToString();

            var featureRoleMapCommand = new CreateFeatureRoleMapCommand
            {
                RoleItemId = nameof(BuroRole.ItemId) + roleSerial,
                RoleName = nameof(BuroRole.RoleName) + roleSerial,
                RoleKey = nameof(BuroRole.RoleKey) + roleSerial,
                FeatureRequests = new List<FeatureRequestWithStatus>()
            };

            for (int i = 0; i < numberOfFeature; i++)
            {
                var serial = i.ToString();

                featureRoleMapCommand.FeatureRequests.Add(new FeatureRequestWithStatus
                {
                    FeatureItemId = nameof(BuroFeature.ItemId) + serial,
                    FeatureName = nameof(BuroFeature.FeatureName) + serial,
                    FeaturePath = nameof(BuroFeature.FeaturePath) + serial,
                    ApiPath = nameof(BuroFeature.ApiPath) + serial,
                    FeatureStatus = EnumFeatureStatus.None
                });
            }

            return featureRoleMapCommand;
        }

        [Fact]
        public async Task ValidateCreateFeatureRoleMapCommand_AllValidData_ValidatedSuccessfully()
        {
            // Arange
            var command = PrepareCreateFeatureRoleMapCommand(1, 2);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateCreateFeatureRoleMapCommand_InvalidRoleData_ValidationFailed()
        {
            // Arange
            var command = PrepareCreateFeatureRoleMapCommand(4, 2);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public async Task ValidateCreateFeatureRoleMapCommand_InvalidFeatureData_ValidationFailed()
        {
            // Arange
            var command = PrepareCreateFeatureRoleMapCommand(1, 5);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
