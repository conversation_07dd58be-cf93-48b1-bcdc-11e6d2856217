using Domain.Commands.Account;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using Validators.Account;
using xUnitTests.Helpers;

namespace xUnitTests.Validators.Account
{
    public class CreateCSAccountCommandValidatorTests : BaseTest
    {
        private readonly CreateCSAccountCommandValidator _validator;

        public CreateCSAccountCommandValidatorTests()
        {
            _validator = new CreateCSAccountCommandValidator(_mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            var csProductLines = new List<ContractualSavingProductLine>
            {
                new()
                {
                    ItemId = "001",
                    InstallmentMultiplier = 100
                }
            };

            SetupRepositoryMock(csProductLines);
        }

        private static CreateCSAccountCommand GetCreateCSAccountCommand(
            List<Nominee> nominees,
            EnumProductApplicationStatus status = EnumProductApplicationStatus.Draft,
            double instalmentAmount = 100)
        {
            var command = new CreateCSAccountCommand
            {
                MemberItemId = "001",
                ProductLineItemId = "001",
                InstalmentAmount = instalmentAmount,
                MemberSignatureId = string.Empty,
                Nominees = nominees,
                Status = status
            };

            return command;
        }

        private static List<Nominee> GetNominees(double firstNomineeShare, double secondNomineeShare)
        {
            var nominees = new List<Nominee>
            {
                new()
                {
                    ItemId = "001",
                    PhotoId = "001",
                    SignatureId = "001",
                    Name = "Name001",
                    SharedPercentage = firstNomineeShare,
                },
                new()
                {
                    ItemId = "001",
                    PhotoId = "001",
                    SignatureId = "001",
                    Name = "Name001",
                    SharedPercentage = secondNomineeShare,
                }
            };

            return nominees;
        }

        [Fact]
        public async Task ValidateCreateCSAccountCommand_ValidShareAmongNominees_ValidationPassed()
        {
            // Arrange
            var command = GetCreateCSAccountCommand(GetNominees(50, 50));

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateCreateCSAccountCommand_InconsistantShareAmongNominees_ValidationFailed()
        {
            // Arrange
            var command = GetCreateCSAccountCommand(GetNominees(50, 60), EnumProductApplicationStatus.Pending);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public async Task ValidateCreateCSAccountCommand_HavingZeroShareAmongNominees_ValidationFailed()
        {
            // Arrange
            var command = GetCreateCSAccountCommand(GetNominees(0, 60), EnumProductApplicationStatus.Pending);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public async Task ValidateCreateCSAccountCommand_HavingInvalidInstalmentamount_ValidationFailed()
        {
            // Arrange
            var command = GetCreateCSAccountCommand(GetNominees(0, 60), EnumProductApplicationStatus.Pending, 1100);

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
