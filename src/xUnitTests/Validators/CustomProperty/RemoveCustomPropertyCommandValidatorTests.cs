using Domain.Commands.CustomProperty;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using Validators.CustomProperty;
using xUnitTests.Helpers;

namespace xUnitTests.Validators.CustomProperty
{
    public class RemoveCustomPropertyCommandValidatorTests : BaseTest
    {
        private readonly RemoveCustomPropertyCommandValidator _validator;

        public RemoveCustomPropertyCommandValidatorTests()
        {
            _validator = new RemoveCustomPropertyCommandValidator(
                _mockRepository.Object);

            SeedMockData();
        }

        private void SeedMockData()
        {
            var customProperties = new List<BuroCustomProperties>
            {
                new()
                {
                    ItemId = "1",
                    PropertyName = "Property-1",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Number,
                    DefaultValue = "1"
                },
                new()
                {
                    ItemId = "2",
                    PropertyName = "Property-2",
                    CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                    CustomPropertyValueType = EnumCustomPropertyValueType.Text,
                    DefaultValue = "Test-2"
                },

            };

            SetupRepositoryMock(customProperties);
        }

        private static RemoveCustomPropertyCommand GetRemoveCustomPropertyCommand(
            string ItemId)
        {
            var newCustomPropertyCommand = new RemoveCustomPropertyCommand
            {
                ItemId = ItemId
            };

            return newCustomPropertyCommand;
        }

        [Fact]
        public async Task ValidateRemoveCustomPropertyCommand_ExistingCustomProperty_ValidatedSuccessfully()
        {
            // Arange
            var customPropertyCommand = GetRemoveCustomPropertyCommand("1");

            // Act
            var result = await _validator.TestValidateAsync(customPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateRemoveCustomPropertyCommand_NonExistingCustomProperty_ValidationFailed()
        {
            // Arange
            var customPropertyCommand = GetRemoveCustomPropertyCommand("11");

            // Act
            var result = await _validator.TestValidateAsync(customPropertyCommand);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
