using Domain.Commands.CustomProperty;
using FluentValidation.TestHelper;
using Moq;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using Validators.CustomProperty;

namespace xUnitTests.Validators.CustomProperty
{
    public class AddCustomPropertyCommandValidatorTests
    {
        private readonly IRepository _repository;
        private readonly AddCustomPropertyCommandValidator _validator;

        public AddCustomPropertyCommandValidatorTests()
        {
            _repository = Mock.Of<IRepository>();
            _validator = new AddCustomPropertyCommandValidator(_repository);
        }

        private static AddCustomPropertyCommand GetAddCustomPropertyCommand(
            string propertyName,
            EnumCustomPropertyValueType customPropertyValueType,
            string defaultValue,
            double minimumValue = 0,
            double maximumValue = 0)
        {
            var newCustomPropertyCommand = new AddCustomPropertyCommand
            {
                PropertyName = propertyName,
                CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                CustomPropertyValueType = customPropertyValueType,
                DefaultValue = defaultValue,
                MinimumValue = minimumValue,
                MaximumValue = maximumValue,
                IsMandatory = false,
                IsEditable = false,
            };

            return newCustomPropertyCommand;
        }

        [Fact]
        public async Task ValidateAddCustomPropertyCommand_ValidTextPropertyData_ValidatedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "TextPropertyName",
                EnumCustomPropertyValueType.Text,
                string.Empty);

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateAddCustomPropertyCommand_ValidNumberPropertyData_ValidatedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "NumberPropertyName",
                EnumCustomPropertyValueType.Number,
                "0");

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateAddCustomPropertyCommand_NonNumberAsDefaultValueForNumberType_ValidationFailedForNewProperty()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "NumberPropertyName",
                EnumCustomPropertyValueType.Number,
                "Text");

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public async Task ValidateAddCustomPropertyCommand_ValidRangePropertyData_ValidatedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "RangePropertyName",
                EnumCustomPropertyValueType.Range,
                string.Empty,
                minimumValue: 0,
                maximumValue: 10);

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public async Task ValidateAddCustomPropertyCommand_InvalidRangeForRangeTypeData_ValidationFailedforNewProperty()
        {
            // Arange
            var newCustomPropertyCommand = GetAddCustomPropertyCommand(
                "RangePropertyName",
                EnumCustomPropertyValueType.Range,
                "5",
                minimumValue: 10,
                maximumValue: 0);

            // Act
            var result = await _validator.TestValidateAsync(newCustomPropertyCommand);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
