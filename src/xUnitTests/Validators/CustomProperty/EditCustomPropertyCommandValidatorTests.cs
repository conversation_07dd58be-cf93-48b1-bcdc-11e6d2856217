using Domain.Commands.CustomProperty;
using FluentValidation.TestHelper;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using Validators.CustomProperty;

namespace xUnitTests.Validators.CustomProperty
{
    public class EditCustomPropertyCommandValidatorTests
    {
        private readonly EditCustomPropertyCommandValidator _validator;

        public EditCustomPropertyCommandValidatorTests()
        {
            _validator = new EditCustomPropertyCommandValidator();
        }

        private static EditCustomPropertyCommand GetEditCustomPropertyCommand(
            string ItemId,
            EnumCustomPropertyValueType customPropertyValueType,
            string defaultValue,
            double minimumValue = 0,
            double maximumValue = 0)
        {
            var newCustomPropertyCommand = new EditCustomPropertyCommand
            {
                ItemId = ItemId,
                CustomPropertyCategory = EnumCustomPropertyCategory.Office,
                CustomPropertyValueType = customPropertyValueType,
                DefaultValue = defaultValue,
                MinimumValue = minimumValue,
                MaximumValue = maximumValue,
                IsMandatory = false,
                IsEditable = false,
            };

            return newCustomPropertyCommand;
        }

        [Fact]
        public void ValidateEditCustomPropertyCommand_ValidTextPropertyData_ValidatedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                "100",
                EnumCustomPropertyValueType.Text,
                string.Empty);

            // Act
            var result = _validator.TestValidate(newCustomPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void ValidateEditCustomPropertyCommand_ValidNumberPropertyData_ValidatedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                "101",
                EnumCustomPropertyValueType.Number,
                "0");

            // Act
            var result = _validator.TestValidate(newCustomPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void ValidateEditCustomPropertyCommand_NonNumberAsDefaultValueForNumberType_ValidationFailedForNewProperty()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                "102",
                EnumCustomPropertyValueType.Number,
                "Text");

            // Act
            var result = _validator.TestValidate(newCustomPropertyCommand);

            // Assert
            result.ShouldHaveAnyValidationError();
        }

        [Fact]
        public void ValidateEditCustomPropertyCommand_ValidRangePropertyData_ValidatedSuccessfully()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                "103",
                EnumCustomPropertyValueType.Range,
                string.Empty,
                minimumValue: 0,
                maximumValue: 10);

            // Act
            var result = _validator.TestValidate(newCustomPropertyCommand);

            // Assert
            result.ShouldNotHaveAnyValidationErrors();
        }

        [Fact]
        public void ValidateEditCustomPropertyCommand_InvalidRangeForRangeTypeData_ValidationFailedForNewProperty()
        {
            // Arange
            var newCustomPropertyCommand = GetEditCustomPropertyCommand(
                "104",
                EnumCustomPropertyValueType.Range,
                "5",
                minimumValue: 10,
                maximumValue: 0);

            // Act
            var result = _validator.TestValidate(newCustomPropertyCommand);

            // Assert
            result.ShouldHaveAnyValidationError();
        }
    }
}
