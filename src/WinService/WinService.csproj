<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
	<OutputType>Exe</OutputType>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FinBlocks.LogProvider" Version="6.0.0.1" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.4" />
		<PackageReference Include="Selise.AppSuite.DataDefination.Core" Version="1.1.19203.1" />
	    <PackageReference Include="SeliseBlocks.Genesis.Framework.Worker" Version="6.0.1.2" />
		
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\EventHandlers\EventHandlers.csproj" />
		<ProjectReference Include="..\Infrastructure\Infrastructure.csproj" />
	</ItemGroup>
	<ItemGroup>
		<None Update="appsettings.Development.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.dev-az.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.stg-az.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.prod-az.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.uat-az.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Aspose.Words.lic">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="itext-license.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="Properties\launchSettings.json">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>

</Project>
