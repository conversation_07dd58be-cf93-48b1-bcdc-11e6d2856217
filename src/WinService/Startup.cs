using SeliseBlocks.Genesis.Framework.Worker.Application;
using SeliseBlocks.Genesis.Framework.Worker.Infrastructure;
using SeliseBlocks.Genesis.Framework.Worker.Middlewares;

namespace WinService
{
    public class Startup : IStartup
    {
        public void Configure(IApplicationBuilder applicationBuilder)
        {
            applicationBuilder.UseMiddleware<AuthorizationMiddleware>();
            applicationBuilder.UseMiddleware<RoutingMiddleware>();
            applicationBuilder.UseMiddleware<CommandHandlerMiddleware>();
            applicationBuilder.UseMiddleware<ExceptionHandlerMiddleware>();
        }
    }
}
