{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:17999", "sslPort": 44301}}, "profiles": {"WinService": {"commandName": "Project", "dotnetRunMessages": true, "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "https://localhost:7018;http://localhost:5161", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}