using EventHandlers.Contracts;
using EventHandlers.Services.Role;
using EventHandlers.Services.UserPermission;
using Infrastructure.Contracts;
using Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class UserServiceCollector
    {
        public static void AddUserServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IBuroKeyStore, BuroKeyStore>();
            serviceCollection.AddSingleton<IAssignUserPermissionService, AssignUserPermissionService>();
            serviceCollection.AddSingleton<UserRoleUpdaterFromDesignationService>();
            serviceCollection.AddSingleton<RoleAndRelatedDataDeleteService>();
        }
    }
}
