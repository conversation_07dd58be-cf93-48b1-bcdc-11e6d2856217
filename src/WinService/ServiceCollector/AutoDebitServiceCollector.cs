using EventHandlers.Contracts;
using EventHandlers.Services.AutoDebit;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector;

public static class AutoDebitServiceCollector
{
    public static void AddAutoDebitServices(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSingleton<IExecuteAutoDebitEventService, ExecuteAutoDebitEventService>();
    }
}