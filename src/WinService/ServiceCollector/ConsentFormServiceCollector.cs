using EventHandlers.Contracts;
using EventHandlers.Services.ConsentForm;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector;

public static class ConsentFormServiceCollector
{
    public static void AddConsentFormServices(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSingleton<IConsentFormApprovalEventService, ConsentFormApprovalEventService>();
    }

}