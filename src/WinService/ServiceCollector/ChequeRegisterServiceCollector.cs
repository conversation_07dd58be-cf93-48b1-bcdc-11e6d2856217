using EventHandlers.Contracts;
using EventHandlers.Services.Register;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector;

public static class ChequeRegisterServiceCollector
{
    public static void AddChequeRegisterServices(this IServiceCollection serviceCollection)
    {
        serviceCollection.AddSingleton<IChequeRegisterEventService, ChequeRegisterEventService>();
    }
}