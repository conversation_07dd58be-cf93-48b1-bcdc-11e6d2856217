using EventHandlers.Contracts;
using EventHandlers.Services.Center;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class CenterServiceCollector
    {
        public static void AddCenterServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IChangePOrForMembersService, ChangePOrForMembersService>();
        }
    }
}
