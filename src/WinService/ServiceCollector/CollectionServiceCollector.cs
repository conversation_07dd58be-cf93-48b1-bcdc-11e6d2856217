using EventHandlers.Contracts;
using EventHandlers.Services.Collection;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class CollectionServiceCollector
    {
        public static void AddCollectionServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<ICollectionFundTransferPostProcessingEventService, CollectionFundTransferPostProcessingEventService>();
            serviceCollection.AddSingleton<ICollectionFundTransferRevertApprovalEventService, CollectionFundTransferRevertApprovalEventService>();
            serviceCollection.AddSingleton<IAdjustCashInHandBalanceEventService, AdjustCashInHandBalanceEventService>();
        }
    }
}
