using EventHandlers.Contracts;
using EventHandlers.Services.Account;
using Infrastructure.Contracts;
using Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class SharedServiceCollector
    {
        public static void AddSharedServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IInfrastructureSharedService, InfrastructureSharedService>();
            serviceCollection.AddSingleton<ILoanApplicationSharedService, LoanApplicationSharedService>();
        }
    }
}
