using EventHandlers.Contracts.inventory;
using EventHandlers.Services.Inventory;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class InventoryServiceCollector
    {
        public static void AddInventoryServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<InventoryOutItemApprovalService>();
            serviceCollection.AddSingleton<IInventoryStockManagementService, InventoryStockManagementService>();
            serviceCollection.AddSingleton<InventoryTransferSourceApprovalService>();
            serviceCollection.AddSingleton<InventoryTransferRecipientApprovalService>();
            serviceCollection.AddSingleton<InventoryDisposeApprovalService>();
        }
    }
}
