using EventHandlers.Contracts;
using EventHandlers.Employee.UpdateEmployeeNumberUnderOffice;
using EventHandlers.Services.ExternalSync;
using EventHandlers.Services.Office;
using EventHandlers.Services.UserPermission;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class SyncServiceCollector
    {
        public static void AddSyncServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<ISyncNumberOfOfficeService, SyncNumberOfOfficeService>();
            serviceCollection.AddSingleton<ISyncSylviaDataService, SyncSylviaDataService>();
            serviceCollection.AddSingleton<ISyncUserPermissionByRolesService, SyncUserPermissionByRolesService>();
            serviceCollection.AddSingleton<SyncNumberOfEmployeesOfOfficeService>();
        }
    }
}
