using EventHandlers.Contracts;
using EventHandlers.Services.Holiday;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class HolidayServiceCollector
    {
        public static void AddHolidayServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IWorkingDayService, WorkingDayService>();
            serviceCollection.AddSingleton<IHolidayCreationEventService, HolidayCreationEventService>();
            serviceCollection.AddSingleton<ICSMemberScheduleHolidayAdjustmentService, CSMemberScheduleHolidayAdjustmentService>();
            serviceCollection.AddSingleton<ILoanMemberScheduleHolidayAdjustmentService, LoanMemberScheduleHolidayAdjustmentService>();
        }
    }
}
