using EventHandlers.Contracts;
using EventHandlers.Services.Transaction;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Services.Transactions;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class TransactionServiceCollector
    {
        public static void AddTransactionServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IProductAccountTransactionRequestPostProcessingEventService, ProductAccountTransactionRequestPostProcessingEventService>();
            serviceCollection.AddSingleton<ITrackAccountPaymentEventService, TrackLoanPaymentEventService>();

            serviceCollection.AddSingleton<ITransactionContextFactoryResolverService, TransactionContextFactoryResolverService>();
            serviceCollection.AddTransient<ITransactionContextFactory, GeneralSavingsTransactionFactory>();
            serviceCollection.AddTransient<ITransactionContextFactory, ContractualSavingsTransactionFactory>();
            serviceCollection.AddTransient<ITransactionContextFactory, LoanTransactionFactory>();

            serviceCollection.AddTransient<IAccountTransactionFactoryResolverService, AccountTransactionFactoryResolverService>();
            serviceCollection.AddTransient<IAccountTransactionFactory, GSDepositTransactionService>();
            serviceCollection.AddTransient<IAccountTransactionFactory, GSWithdrawTransactionService>();
            serviceCollection.AddTransient<IAccountTransactionFactory, CSWithdrawTransactionService>();
            serviceCollection.AddTransient<IAccountTransactionFactory, CSInstallmentTransactionService>();
            serviceCollection.AddTransient<IAccountTransactionFactory, LoanInstallmentRepaymentService>();

            serviceCollection.AddTransient<IProductAccountFactoryResolverService, ProductAccountFactoryResolverService>();
            serviceCollection.AddTransient<IProductAccountFactory, GeneralSavingsAccountService>();
            serviceCollection.AddTransient<IProductAccountFactory, ContractualSavingsAccountService>();
            serviceCollection.AddTransient<IProductAccountFactory, LoanAccountService>();

            serviceCollection.AddTransient<IPaymentProcessorFactoryResolverService, PaymentProcessorFactoryResolverService>();
            serviceCollection.AddTransient<IPaymentProcessorFactory, BankTransferProcessorService>();
            serviceCollection.AddTransient<IPaymentProcessorFactory, CashPaymentProcessorService>();
            serviceCollection.AddTransient<IPaymentProcessorFactory, MfsPaymentProcessorService>();

            serviceCollection.AddTransient<IProductAccountTransactionService, ProductAccountTransactionService>();
            serviceCollection.AddTransient<IGSWithdrawalApprovalEventService, GSWithdrawalApprovalEventService>();
            serviceCollection.AddTransient<ICSWithdrawalApprovalEventService, CSWithdrawalApprovalEventService>();
        }
    }
}
