using EventHandlers.Contracts;
using EventHandlers.Services.Tasks;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class TaskServiceCollector
    {
        public static void AddTaskServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<ICreateTaskService, CreateTaskService>();
        }
    }
}
