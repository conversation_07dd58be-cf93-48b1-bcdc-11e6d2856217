using EventHandlers.Contracts;
using EventHandlers.Services.Account;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class AccountServiceCollector
    {
        public static void AddAccountServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IContractualAccountApprovalEventService, ContractualAccountApprovalEventService>();
            serviceCollection.AddSingleton<ICreateCSApplicationVoucherEventService, CreateCSApplicationVoucherEventService>();
            serviceCollection.AddSingleton<ILoanApplicationPostProcessingEventService, LoanApplicationPostProcessingEventService>();
            serviceCollection.AddSingleton<IContractualAccountScheduleGenerationService, ContractualAccountScheduleGenerationService>();
            serviceCollection.AddSingleton<ILoanAccountScheduleGenerationService, LoanAccountScheduleGenerationService>();
            serviceCollection.AddSingleton<ILoanDisbursementPostProcessingEventService, LoanDisbursementPostProcessingEventService>();
            serviceCollection.AddSingleton<IGSChangeApprovalEventService, GSChangeApprovalEventService>();
        }
    }
}
