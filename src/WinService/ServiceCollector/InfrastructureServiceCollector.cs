using Infrastructure.Contracts;
using Infrastructure.Contracts.Retry;
using Infrastructure.Services;
using Infrastructure.Services.RetryServices;
using Infrastructure.Services.Transactions;
using Microsoft.Extensions.DependencyInjection;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WinService.ServiceCollector
{
    public static class InfrastructureServiceCollector
    {
        public static void AddInfrastructureServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<INotificationServiceClient, NotificationServiceClient>();
            serviceCollection.AddSingleton<IServiceClient, ServiceClient>();
            serviceCollection.AddSingleton<IBusinessConfig, BusinessConfig>();
            serviceCollection.AddSingleton<ISequenceNumberClient, SequenceNumberClientService>();
            serviceCollection.AddSingleton<IBusinessRepository, BusinessRepository>();
            serviceCollection.AddSingleton<IExcelGenerationService, ExcelGenerationService>();
            serviceCollection.AddSingleton<ITransactionClientService, TransactionClientService>();
            serviceCollection.AddSingleton<IRetryExecutorService, RetryExecutorService>();
            serviceCollection.AddSingleton<IRetryStrategy, RetryStrategy>();
        }
    }
}
