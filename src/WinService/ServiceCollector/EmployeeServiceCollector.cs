using EventHandlers.Employee.StaffingApprovalPermission;
using EventHandlers.Employee.StaffingRequestApprovalStatusUpdate;
using EventHandlers.Services.EmployeeHistory;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class EmployeeServiceCollector
    {
        public static void AddEmployeeServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<StaffingApprovalPermissionService>();
            serviceCollection.AddSingleton<StaffingRequestApprovalStatusUpdateService>();
            serviceCollection.AddSingleton<CreateEmployeeTransferHistoryService>();
            serviceCollection.AddSingleton<CreateEmployeeAssignmentHistoryService>();
            serviceCollection.AddSingleton<UpdateEmployeeAssignmentHistoryService>();
        }
    }
}
