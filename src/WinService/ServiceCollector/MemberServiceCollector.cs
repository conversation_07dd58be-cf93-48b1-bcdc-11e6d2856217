using EventHandlers.Contracts;
using EventHandlers.Services.Center;
using EventHandlers.Services.Member;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class MemberServiceCollector
    {
        public static void AddMemberServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IMemberSurveyPostProcessingEventService, MemberSurveyPostProcessingEventService>();
            serviceCollection.AddSingleton<IMemberPhoneNumberChangeApprovalEventService, MemberPhoneNumberChangeApprovalEventService>();
            serviceCollection.AddSingleton<IMemberAddressChangeApprovalEventService, MemberAddressChangeApprovalEventService>();
            serviceCollection.AddSingleton<ChangePOrForMembersService>();
        }
    }
}
