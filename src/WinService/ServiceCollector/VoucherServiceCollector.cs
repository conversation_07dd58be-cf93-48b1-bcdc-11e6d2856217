using EventHandlers.Contracts;
using EventHandlers.Services.Voucher;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector;

public static class VoucherServiceCollector
{
    public static void AddVoucherServices(this IServiceCollection serviceCollection)
    {
        // serviceCollection.AddSingleton<IUpdateAutoVoucherEventService, UpdateAutoVoucherEventService>();

        serviceCollection.AddSingleton<IVoucherCreationService, VoucherCreationService>();
        serviceCollection.AddSingleton<IVoucherCreationStrategy, UnifiedVoucherCreationStrategy>();
        serviceCollection.AddSingleton<IStandardVoucherConfigurationProvider, StandardVoucherConfigurationProvider>();
        serviceCollection.AddSingleton<IProductLineVoucherConfigurationProvider, ProductLineVoucherConfigurationProvider>();
        serviceCollection.AddSingleton<VoucherConfigurationProviderFactory>();
    }

}