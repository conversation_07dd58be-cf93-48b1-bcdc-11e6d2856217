using EventHandlers.Contracts;
using EventHandlers.Services.Mfcib;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class MfcibServiceCollector
    {
        public static void AddMfcibServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<IMfcibRequestPostProcessingEventService, MfcibRequestPostProcessingEventService>();
            serviceCollection.AddSingleton<IMfcibDataSubmissionPostProcessingEventService, MfcibDataSubmissionPostProcessingEventService>();
        }
    }
}
