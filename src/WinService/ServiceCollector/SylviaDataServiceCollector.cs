using EventHandlers.Contracts;
using EventHandlers.Services.ExternalSync;
using Infrastructure.Contracts;
using Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection;

namespace WinService.ServiceCollector
{
    public static class SylviaDataServiceCollector
    {
        public static void AddSylviaDataServices(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddSingleton<ISyncSylviaDataService, SyncSylviaDataService>();
            serviceCollection.AddSingleton<ISylviaClient, SylviaClient>();
        }
    }
}
