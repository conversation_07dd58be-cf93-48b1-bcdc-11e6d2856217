using EventHandlers.Employee.StaffingApprovalPermission;
using EventHandlers.Employee.StaffingRequestApprovalStatusUpdate;
using FinMongoDbDataContext;
using FinMongoDbRepositories;
using FinRepositories;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.Genesis.Framework.Worker.Infrastructure;
using SeliseBlocks.Genesis.Framework.Worker.RabbitMq;
using WinService.ServiceCollector;

namespace WinService
{
    public static class Program
    {
        public static async Task Main(string[] args)
        {
            var rabbitMqHostBuilderOptions = new RabbitMqHostBuilderOptions
            {
                UseFileLogging = true,
                UseConsoleLogging = true,
                CommandLineArguments = args,
                AddApplicationServices = AddApplicationServices,
                AddRequiredQueues = AddRequiredQueues,
                AddAmpqMessageConsumerOptions = (appSettings) =>
                {
                    var ampqMessageConsumerOptions = AmpqMessageConsumerOptionsProvider.GetRequiredQueues();

                    return ampqMessageConsumerOptions;
                }
            };

            var hostBuilder = await RabbitMqHostBuilder.BuildRabbitMqHost(rabbitMqHostBuilderOptions);
            hostBuilder.UserStartupClass<Startup>();
            await hostBuilder.Build().RunAsync();
        }

        private static IEnumerable<string> AddRequiredQueues(IAppSettings appSettings)
        {
            return new[] { appSettings.BlocksAuditLogQueueName };
        }

        private static void AddApplicationServices(IServiceCollection serviceCollection, IAppSettings appSettings)
        {
            serviceCollection.RegisterCollection(typeof(ICommandHandler<,>), new[] {
                typeof(StaffingRequestApprovalStatusUpdateService).Assembly,
                typeof(StaffingApprovalPermissionService).Assembly
            });

            var repoServiceDescriptor = serviceCollection.FirstOrDefault(
                descriptor => descriptor.ServiceType == typeof(IRepository)
                          && descriptor.ImplementationType.Name == nameof(MongoDbRepository));

            serviceCollection.Remove(repoServiceDescriptor);

            serviceCollection.AddFinMongodbDataContextServices();
            serviceCollection.AddFinRepositoryServices();
            serviceCollection.AddFinMongoDbRepositoryServices();
            serviceCollection.AddInfrastructureServices();
            serviceCollection.AddAccountServices();
            serviceCollection.AddMemberServices();
            serviceCollection.AddSharedServices();
            serviceCollection.AddMfcibServices();
            serviceCollection.AddSylviaDataServices();
            serviceCollection.AddSyncServices();
            serviceCollection.AddUserServices();
            serviceCollection.AddTaskServices();
            serviceCollection.AddTransactionServices();
            serviceCollection.AddVoucherServices();
            serviceCollection.AddCollectionServices();
            serviceCollection.AddConsentFormServices();
            serviceCollection.AddChequeRegisterServices();
            serviceCollection.AddHolidayServices();
            serviceCollection.AddCenterServices();
            serviceCollection.AddEmployeeServices();
            serviceCollection.AddInventoryServices();
            serviceCollection.AddAutoDebitServices();
        }
    }
}
