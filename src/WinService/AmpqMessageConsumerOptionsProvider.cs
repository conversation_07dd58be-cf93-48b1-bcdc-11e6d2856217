using Infrastructure.Constants;
using SeliseBlocks.Genesis.Framework.Worker.RabbitMq;

namespace WinService
{
    public static class AmpqMessageConsumerOptionsProvider
    {
        public static AmpqMessageConsumerOptions GetRequiredQueues()
        {
            var ampqMessageConsumerOptions = new AmpqMessageConsumerOptions();

            ampqMessageConsumerOptions.ConsumerSubscriptions.ListenOn(QueueNames.BuroCommandQueue, 1);
            ampqMessageConsumerOptions.ConsumerSubscriptions.ListenOn(QueueNames.BuroTransactionQueue, 1);
            ampqMessageConsumerOptions.ConsumerSubscriptions.ListenOn(QueueNames.BuroProductAccountQueue, 1);

            return ampqMessageConsumerOptions;
        }
    }
}
