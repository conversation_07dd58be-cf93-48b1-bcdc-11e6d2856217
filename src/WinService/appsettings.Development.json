{"ServiceName": "BuroBusinessWinService", "GlobalConfigJsonPath": "C:\\ECAP_CONFIGS\\GlobalConfig-Buro.json", "NotifierServerBaseUrl": "http://msblocks.seliselocal.com/api/notification", "NotifierServerVersion": "v3", "SequenceNumberServiceBaseUrl": "http://msblocks.seliselocal.com/api/SequenceNumber/", "SequenceNumberServiceVersion": "v2", "Sts2EndPointUri": "http://msblocks.seliselocal.com/api/sts2/", "SylviaBaseUrl": "http://27.147.137.34/CF_TEST", "RequestedMfcibFilePath": "C:\\RequestedMfcibFiles\\CibFiles", "MfcibExcelTemplatePath": "C:\\RequestedMfcibFiles\\ExcelTemplates", "NumberOfRetries": 3, "DelaySecondsBetweenRetries": 1, "MaxDelaySecondsBetweenRetries": 5, "TransactionServiceBaseUrl": "https://fin-transaction.inb.seliselocal.com"}