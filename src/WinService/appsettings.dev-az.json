{"ServiceName": "BuroBusinessWinService", "GlobalConfigJsonPath": "/app/config/GlobalConfig.json", "NotifierServerBaseUrl": "http://msblocks.seliselocal.com/api/notification", "NotifierServerVersion": "v3", "SequenceNumberServiceBaseUrl": "http://msblocks.seliselocal.com/api/SequenceNumber/", "SequenceNumberServiceVersion": "v2", "Sts2EndPointUri": "http://msblocks.seliselocal.com/api/sts2/", "SylviaBaseUrl": "http://27.147.137.34/CF_TEST", "RequestedMfcibFilePath": "/RequestedMfcibFiles", "MfcibExcelTemplatePath": "/RequestedMfcibFiles/Template", "NumberOfRetries": 3, "DelaySecondsBetweenRetries": 10, "MaxDelaySecondsBetweenRetries": 60, "TransactionServiceBaseUrl": "https://fin-transaction.inb.seliselocal.com"}