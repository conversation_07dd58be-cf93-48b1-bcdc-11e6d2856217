<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="BuroUserPermission" Version="1.0.0.5" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.4" />
        <PackageReference Include="Selise.AppSuite.DataDefination.Core" Version="1.1.19203.1" />
		<PackageReference Include="SeliseBlocks.Genesis.Framework" Version="6.0.1.4" />
		<PackageReference Include="SeliseBlocks.Genesis.Framework.Worker" Version="6.0.1.2" />
		<PackageReference Include="SeliseBlocks.Genesis.Framework.Repositories" Version="6.0.1.2" />
		<PackageReference Include="SeliseBlocks.Genesis.Framework.Api" Version="6.0.0.15" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Domain\Domain.csproj" />
        <ProjectReference Include="..\ServiceCollector\ServiceCollector.csproj" />
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.Development.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.dev-az.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.stg-az.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.prod-az.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="appsettings.uat-az.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Properties\launchSettings.json">
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>Never</CopyToPublishDirectory>
        </None>
    </ItemGroup>
	<PropertyGroup>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<NoWarn>$(NoWarn);1591</NoWarn>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<DocumentationFile>WebService.xml</DocumentationFile>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DocumentationFile>WebService.xml</DocumentationFile>
	</PropertyGroup>
</Project>
