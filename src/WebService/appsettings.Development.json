{"ServiceName": "BuroBusinessWebService", "GlobalConfigJsonPath": "/Users/<USER>/Projects/Backend-file/GlobalConfig-Buro.json", "UamBaseUrl": "http://msblocks.seliselocal.com/api/uam", "UamVersion": "v23", "Origin": "burobd.seliselocal.com", "StsBaseUrl": "http://msblocks.seliselocal.com/api/sts2/", "MailServiceBaseUrl_Version": "api/mailservice/v12", "SmsServerUrl": "http://msblocks.seliselocal.com/api/shortmessage", "SmsServerVersion": "v3", "Captcha": {"ClientKey": "6LcGGUAqAAAAACqMKcmy3sRbuBUWMFM998b9epIf", "ServerKey": "6LcGGUAqAAAAAJTDf_Hnn3lVwy-BRUnT6DSs5q48"}, "GoogleCaptchaVerificationUrl": "https://www.google.com/recaptcha/api/siteverify", "SequenceNumberServiceBaseUrl": "http://msblocks.seliselocal.com/api/SequenceNumber/", "SequenceNumberServiceVersion": "v2", "IsEnableDataChangeTrace": false, "NotifierServerBaseUrl": "http://msblocks.seliselocal.com/api/notification", "NotifierServerVersion": "v3", "NumberOfRetries": 3, "DelaySecondsBetweenRetries": 1, "MaxDelaySecondsBetweenRetries": 5, "TransactionServiceBaseUrl": "http://fin-transaction.inb.seliselocal.com"}