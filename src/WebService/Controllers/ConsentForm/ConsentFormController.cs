using BuroUserPermission.CustomAttribute;
using Domain.Commands.ConsentForm;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.ConsentForm;

public class ConsentFormController : ControllerBase
{

    private readonly ICommandSender _commandSender;
    private readonly ILogger<ConsentFormController> _logger;

    public ConsentFormController(ILogger<ConsentFormController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateConsentForm([FromBody] CreateConsentFormCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, consent form  create command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> RevokeConsentForm([FromB<PERSON>] RevokeConsentFormCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, consent form  revoke command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }



}