using BuroUserPermission.CustomAttribute;
using Domain.Commands.Holiday;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Holiday;

public class HolidayController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<HolidayController> _logger;

    public HolidayController(ILogger<HolidayController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateHoliday([FromBody] CreateHolidayCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, Create Holiday command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateWeekend([FromBody] CreateWeekendCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, Create Weekend command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> DeleteHoliday([FromBody] DeleteHolidayCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, Delete Holiday command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> EarlyEndHoliday([FromBody] EarlyEndHolidayCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, EarlyEnd Holiday command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }
}