using BuroUserPermission.CustomAttribute;
using Domain.Commands.Delegation;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Delegation;

public class DelegationController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<DelegationController> _logger;

    public DelegationController(ILogger<DelegationController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateDelegation([FromBody] DelegationCreateCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, DelegationCreateCommand received", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateDelegationEndDate([FromBody] DelegationEndDateUpdateCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, DelegationEndDateUpdateCommand received", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }
}