using Domain.Commands.LoginActivity;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.LoginActivity;

public class LoginActivityController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<LoginActivityController> _logger;

    public LoginActivityController(ILogger<LoginActivityController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;
    }

    [HttpPost]
    [ProtectedEndPoint]
    public async Task<CommandResponse> SaveLoginActivity([FromBody] SaveLoginActivityCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, SaveLoginActivityCommand received",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }
}