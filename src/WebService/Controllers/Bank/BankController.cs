using BuroUserPermission.CustomAttribute;
using Domain.Commands.Bank;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Bank;

public class BankController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<BankController> _logger;

    public BankController(ILogger<BankController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateBankBranch([FromBody] CreateBankBranchCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, bank branch create command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }


}