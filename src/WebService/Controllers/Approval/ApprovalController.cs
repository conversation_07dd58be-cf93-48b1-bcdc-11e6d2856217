using BuroUserPermission.CustomAttribute;
using Domain.Commands.Approval;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Approval
{
    public class ApprovalController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<ApprovalController> _logger;

        public ApprovalController(
            ILogger<ApprovalController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> UpdateApprovalRequestStatus([FromBody] ApprovalRequestStatusUpdateCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, ApprovalRequestStatusUpdateCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
