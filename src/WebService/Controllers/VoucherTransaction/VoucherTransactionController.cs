using BuroUserPermission.CustomAttribute;
using Domain.Commands.VoucherTransaction;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.VoucherTransaction;

public class VoucherTransactionController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<VoucherTransactionController> _logger;

    public VoucherTransactionController(ILogger<VoucherTransactionController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }
    
    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateVoucherTransaction([FromBody] VoucherTransactionSetupCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, voucher transaction create command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }
    
}