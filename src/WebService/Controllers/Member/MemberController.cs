using BuroUserPermission.CustomAttribute;
using Domain.Commands.Member;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Member
{
    public class MemberController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<MemberController> _logger;

        public MemberController(
            ILogger<MemberController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> UpdateConfiguration([FromBody] UpdateMemberConfigurationCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateMemberConfigurationCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> SendOTPForPhoneNumberChange([FromBody] SendOtpForPhoneNumberChangeCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, SendOTPForPhoneNumberChangeCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> MatchOTPForPhoneNumberChange([FromBody] MatchOtpForPhoneNumberChangeCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, MatchOTPForPhoneNumberChange command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> RequestPhoneNumberChange([FromBody] PhoneNumberChangeRequestCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, RequestPhoneNumberChange command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> RequestAddressChange([FromBody] AddressChangeRequestCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, RequestAddressChange command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> CreateMembershipFeeSetup([FromBody] MembershipFeeSetupCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateMembershipFeeSetup command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }


        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> UpdateMembershipServiceCost([FromBody] UpdateMembershipServiceCostCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateMembershipServiceCost command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> DeleteMembershipInventoryItem([FromBody] DeleteMembershipInventoryItemCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, DeleteMembershipServiceCost command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }


    }
}
