using BuroUserPermission.CustomAttribute;
using Domain.Commands.Office;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers
{
    public class OfficeController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<OfficeController> _logger;

        public OfficeController(
            ILogger<OfficeController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        /// <summary>
        /// Creates a new office with the specified details.
        /// If the office type is 'Branch', a valid survey must be completed before creation.
        /// This endpoint is intended for administrative use.
        /// </summary>
        /// <param name="command">The command containing all necessary office properties, including location and type.</param>
        /// <returns>A CommandResponse indicating whether the office was successfully created, or if there were validation errors.</returns>
        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> Create([FromBody] CreateOfficeCommand command)
        {
            _logger.LogInformation("MessageCorrelationId:{MessageCorrelationId}, new CreateOfficeCommand command.",
                command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> Update([FromBody] UpdateOfficeCommand command)
        {
            _logger.LogInformation("MessageCorrelationId:{MessageCorrelationId}, new UpdateOffice command.",
                command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}