using BuroUserPermission.CustomAttribute;
using Domain.Commands.Transaction;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Transaction
{
    public class TransactionController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<TransactionController> _logger;

        public TransactionController(
            ILogger<TransactionController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> SubmitTransactionRequest([FromBody] SubmitTransactionRequestCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, SubmitTransactionRequestCommand received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
