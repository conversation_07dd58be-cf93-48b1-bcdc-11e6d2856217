using Domain.Commands.ExternalSync;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.ExternalSync
{
    public class ExternalSyncController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<ExternalSyncController> _logger;

        public ExternalSyncController(
            ILogger<ExternalSyncController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        public async Task<CommandResponse> SyncSylviaData([FromBody] SyncSylviaDataCommand command)
        {
            _logger.LogInformation($"ExternalSyncController->SyncSylviaData->START MessageCorrelationId: {command.MessageCorrelationId}");
            return await _commandSender.SendAsync(command);
        }


    }
}
