using BuroUserPermission.CustomAttribute;
using Domain.Commands.BankCheque;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.BankCheque;

public class BankChequeController : ControllerBase
{

    private readonly ICommandSender _commandSender;
    private readonly ILogger<BankChequeController> _logger;

    public BankChequeController(ILogger<BankChequeController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateChequeBook([FromBody] CreateChequeBookCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, CreateChequeBookCommand command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

}