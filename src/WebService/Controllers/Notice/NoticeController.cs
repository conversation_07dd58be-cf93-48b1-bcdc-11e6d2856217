using BuroUserPermission.CustomAttribute;
using Domain.Commands.Notice;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Notice
{
    public class NoticeController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<NoticeController> _logger;

        public NoticeController(ICommandSender commandSender, ILogger<NoticeController> logger)
        {
            _commandSender = commandSender;
            _logger = logger;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> Create([FromBody] CreateNoticeCommand command)
        {
            _logger.LogInformation("MessageCorrelationId:{MessageCorrelationId}, new CreateNoticeCommand command.", command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        public async Task<CommandResponse> CreateNoticeRecipientAction([FromBody] CreateNoticeRecipientActionCommand command)
        {
            _logger.LogInformation("MessageCorrelationId:{MessageCorrelationId}, new CreateNoticeRecipientActionCommand command.", command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

    }
}
