using BuroUserPermission.CustomAttribute;
using Domain.Commands.Inventory;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Inventory;

public class InventoryController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<InventoryController> _logger;

    public InventoryController(ICommandSender commandSender, ILogger<InventoryController> logger)
    {
        _commandSender = commandSender;
        _logger = logger;
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateInventoryItem([FromBody] CreateInventoryItemCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateInventoryItemCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateInventoryItem([FromBody] UpdateInventoryItemCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateInventoryItemCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> ArchiveInventoryItem([FromBody] ArchiveInventoryItemCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, ArchiveInventoryItemCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> AddInventoryMovement([FromBody] AddInventoryMovementCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, AddInventoryMovementCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> OutInventoryItem([FromBody] OutInventoryItemCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, OutInventoryItemCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> TransferInventoryItem([FromBody] TransferInventoryItemCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, TransferInventoryItemCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> DisposeInventoryItem([FromBody] DisposeInventoryItemCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, DisposeInventoryItemCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateInventoryRequest([FromBody] CreateInventoryRequestCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateInventoryRequestCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> TransferRequestedInventoryItems([FromBody] TransferRequestedInventoryListCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, TransferRequestedInventoryListCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> RejectInventoryRequest([FromBody] RejectInventoryRequestCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, RejectInventoryRequestCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

}