using BuroUserPermission.CustomAttribute;
using Domain.Commands.Center;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers
{
    public class CenterController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<CenterController> _logger;

        public CenterController(
            ILogger<CenterController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> AddCenter([FromBody] AddCenterCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, AddCenterCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> EditCenter([FromBody] EditCenterCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, EditCenterCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> DeleteCenter([FromBody] DeleteCenterCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, DeleteCenterCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

    }
}
