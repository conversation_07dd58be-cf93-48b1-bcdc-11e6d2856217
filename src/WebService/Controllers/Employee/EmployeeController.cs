using BuroUserPermission.CustomAttribute;
using Domain.Commands.Employee;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers
{
    public class EmployeeController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<EmployeeController> _logger;

        public EmployeeController(
            ILogger<EmployeeController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> AddEmployeeDesignation([FromBody] AddEmployeeDesignationCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, AddEmployeeDesignationCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> AddStaffingRequest([FromBody] AddStaffingRequestCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, AddStaffingRequest command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> AddEmployee([FromBody] AddEmployeeCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, AddEmployeeCommand command", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> EditEmployee([FromBody] EditEmployeeCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, Edit Employee command", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
