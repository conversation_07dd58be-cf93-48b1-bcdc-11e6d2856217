using Domain.Commands;
using Domain.Commands.Role;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Role
{
    public class RoleController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<RoleController> _logger;

        public RoleController(
            ICommandSender commandSender,
            ILogger<RoleController> logger)
        {
            _commandSender = commandSender;
            _logger = logger;
        }

        [HttpPost]
        [ProtectedEndPoint]
        public async Task<CommandResponse> CreateRole([FromBody] CreateRoleCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateRoleCommand command received", command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [ProtectedEndPoint]
        public async Task<CommandResponse> UpdateRole([FromBody] UpdateRolesCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateRolesCommand received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [ProtectedEndPoint]
        public async Task<CommandResponse> CreateFeatureRoleMap([FromBody] CreateFeatureRoleMapCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateFeatureRoleMap command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [ProtectedEndPoint]
        public async Task<CommandResponse> UpdateFeatureRoleMap([FromBody] UpdateFeatureRoleMapCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateFeatureRoleMap command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [ProtectedEndPoint]
        public async Task<CommandResponse> UpdateFeatureRoleMapWithStatus([FromBody] UpdateFeatureRoleMapWithStatusCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateFeatureRoleMapWithStatus command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [ProtectedEndPoint]
        public async Task<CommandResponse> UpdateDesignationsInRole([FromBody] UpdateDesignationsInRoleCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateDesignationsInRole command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [ProtectedEndPoint]
        public async Task<CommandResponse> DeleteRole([FromBody] RoleDeleteCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, DeleteRole command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

    }
}
