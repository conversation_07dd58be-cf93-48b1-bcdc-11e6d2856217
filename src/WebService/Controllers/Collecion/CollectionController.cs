using Domain.Commands.Collection;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Collecion
{
    public class CollectionController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<CollectionController> _logger;

        public CollectionController(
            ILogger<CollectionController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [AnonymousEndPoint]
        public async Task<CommandResponse> OneShotPayment([FromBody] OneShotPaymentCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, OneShotPaymentCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [AnonymousEndPoint]
        public async Task<CommandResponse> TransferCollectedfunds([FromBody] TransferCollectedfundsCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, TransferCollectedfundsCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
