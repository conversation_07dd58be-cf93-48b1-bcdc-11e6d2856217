using BuroUserPermission.CustomAttribute;
using Domain.Commands.DayEnd;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.DayEnd;

public class DayEndProcessController : ControllerBase
{

    private readonly ICommandSender _commandSender;
    private readonly ILogger<DayEndProcessController> _logger;

    public DayEndProcessController(ILogger<DayEndProcessController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateDayEndProcess([FromBody] UpdateDayEndProcessCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, DayEnd Process update command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }



}