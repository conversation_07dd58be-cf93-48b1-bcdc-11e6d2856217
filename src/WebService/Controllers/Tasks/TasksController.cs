using BuroUserPermission.CustomAttribute;
using Domain.Commands.Tasks;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Tasks
{
    public class TasksController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<TasksController> _logger;

        public TasksController(
            ILogger<TasksController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> UpdateManualAssignment([FromBody] UpdateManualAssignmentCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateManualAssignmentCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
