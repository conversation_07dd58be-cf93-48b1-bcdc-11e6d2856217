using BuroUserPermission.CustomAttribute;
using Domain.Commands.Account;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Account
{
    public class AccountController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<AccountController> _logger;

        public AccountController(
            ILogger<AccountController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> CreateCSAccount([FromBody] CreateCSAccountCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateCSAccountCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> ApplyLoan([FromBody] ApplyLoanCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, Apply<PERSON>oan command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> CloneLoan([FromBody] CloneLoanCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CloneLoan command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> SubmitLoan([FromBody] SubmitLoanCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, SubmitLoan command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> SubmitDisbursement([FromBody] SubmitDisbursementCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, SubmitDisbursement command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> ChangeGSAccount([FromBody] ChangeGSAccountCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, ChangeGSAccountCommand received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> GenerateLoanSchedule([FromBody] GenerateLoanScheduleCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, GenerateLoanScheduleCommand received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
