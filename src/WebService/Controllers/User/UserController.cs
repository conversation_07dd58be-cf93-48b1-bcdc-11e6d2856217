using Domain.Commands.User;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.User;

public class UserController : ControllerBase
{
    private readonly ILogger<UserController> _logger;
    private readonly ICommandSender _commandSender;

    public UserController(ILogger<UserController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;
    }

    [HttpPost]
    [ProtectedEndPoint]
    public async Task<CommandResponse> UpdateUserPermission([FromBody] UpdateUserPermissionCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateUserPermissionCommand received", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }
}