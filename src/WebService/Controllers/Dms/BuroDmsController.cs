
using BuroUserPermission.CustomAttribute;
using Domain.Commands.Dms;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Dms;

public class BuroDmsController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<BuroDmsController> _logger;

    public BuroDmsController(ILogger<BuroDmsController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }


    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateDmsArtifactTag([FromBody] UpdateDmsArtifactTagCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, DMS Artifact Tag update command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }



}