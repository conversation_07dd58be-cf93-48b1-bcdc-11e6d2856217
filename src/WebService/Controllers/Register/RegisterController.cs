using BuroUserPermission.CustomAttribute;
using Domain.Commands.Register;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Register;

public class RegisterController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<RegisterController> _logger;

    public RegisterController(
        ILogger<RegisterController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> ReturnSecurityCheque([FromBody] ReturnChequeCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, ReturnChequeCommand command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateIssueChequeRegister([FromBody] CreateIssueChequeRegisterCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, CreateIssueChequeRegister command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }
    
    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreatePostRegister([FromBody] CreatePostRegisterCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, CreatePostRegister command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }
    
    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateCommentRegister([FromBody] CreateCommentRegisterCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, CreateCommentRegister command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }
    
    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdatePostRegister([FromBody] UpdatePostRegisterCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, UpdatePostRegister command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }
    
    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> DeletePostRegister([FromBody] DeletePostRegisterCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, DeletePostRegister command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

}