using BuroUserPermission.CustomAttribute;
using Domain.Commands.AllowedSector;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.AllowedSector
{
    public class AllowedSectorController : ControllerBase
    {
        private readonly ILogger<AllowedSectorController> _logger;
        private readonly ICommandSender _commandSender;

        public AllowedSectorController(ILogger<AllowedSectorController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> CreateAllowedSector([FromBody] CreateAllowedSectorCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateAllowedSectorCommand received.",
                command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> UpdateAllowedSector([FromBody] UpdateAllowedSectorCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateAllowedSectorCommand received.",
                command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> UpdateAllowedSectorStatus([FromBody] UpdateAllowedSectorStatusCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateAllowedSectorStatusCommand received.",
                command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }
    }
}