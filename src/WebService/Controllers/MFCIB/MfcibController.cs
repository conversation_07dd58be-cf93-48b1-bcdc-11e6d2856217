using BuroUserPermission.CustomAttribute;
using Domain.Commands.Mfcib;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.MFCIB
{
    public class MfcibController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<MfcibController> _logger;

        public MfcibController(
            ILogger<MfcibController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> Export([FromBody] ExportRequestCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateRequestCommand command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
