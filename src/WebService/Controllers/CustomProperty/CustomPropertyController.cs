using BuroUserPermission.CustomAttribute;
using Domain.Commands.CustomProperty;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.CustomProperty
{
    public class CustomPropertyController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<CustomPropertyController> _logger;

        public CustomPropertyController(
            ILogger<CustomPropertyController> logger,
            ICommandSender commandSender)
        {
            _logger = logger;
            _commandSender = commandSender;
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> AddCustomProperty([FromBody] AddCustomPropertyCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, AddCustomProperty command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> EditCustomProperty([FromBody] EditCustomPropertyCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, EditCustomProperty command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [BuroProtectedEndpoint]
        public async Task<CommandResponse> RemoveCustomProperty([FromBody] RemoveCustomPropertyCommand command)
        {
            _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, RemoveCustomProperty command received.", command.MessageCorrelationId);

            return await _commandSender.SendAsync(command);
        }
    }
}
