using BuroUserPermission.CustomAttribute;
using Domain.Commands.Asset;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Asset;

public class AssetController : ControllerBase
{
    private readonly ILogger<AssetController> _logger;
    private readonly ICommandSender _commandSender;

    public AssetController(ILogger<AssetController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateAssetGroup([FromBody] CreateAssetGroupCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateAssetGroupCommand command received.", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateAssetGroup([FromBody] UpdateAssetGroupCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateAssetGroupCommand command received.", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> DeleteAssetGroup([FromBody] DeleteAssetGroupCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, DeleteAssetGroupCommand command received.", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }


    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateAssetSubGroup([FromBody] CreateAssetSubGroupCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateAssetSubGroupCommand command received.", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateAssetSubGroup([FromBody] UpdateAssetSubGroupCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateAssetSubGroupCommand command received.", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateAssetItem([FromBody] CreateAssetItemCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateAssetItemCommand command received.", command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

}