using Domain.Commands.Authentication;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers
{
    public class AuthenticationController : ControllerBase
    {
        private readonly ICommandSender _commandSender;
        private readonly ILogger<AuthenticationController> _logger;

        public AuthenticationController(ICommandSender commandSender, ILogger<AuthenticationController> logger)
        {
            _commandSender = commandSender;
            _logger = logger;
        }

        [HttpPost]
        [AnonymousEndPoint]
        public async Task<CommandResponse> VerifyEmployeePinForForgetPassword(
            [FromBody] VerifyEmployeePinForForgetPasswordCommand command)
        {
            _logger.LogInformation(
                "MessageCorrelationId:{MessageCoRelationId}, new VerifyEmployeePinForForgetPasswordCommand command.",
                command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [AnonymousEndPoint]
        public async Task<CommandResponse> SendOtpForForgetPassword([FromBody] SendOtpForForgetPasswordCommand command)
        {
            _logger.LogInformation(
                "MessageCorrelationId:{MessageCoRelationId}, new SendOtpForForgetPasswordCommand command.",
                command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [AnonymousEndPoint]
        public async Task<CommandResponse> ResendOtpForForgetPassword([FromBody] ResendOtpForForgetPasswordCommand command)
        {
            _logger.LogInformation(
                "MessageCorrelationId:{MessageCoRelationId}, new ResendOtpForForgetPasswordCommand command.",
                command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [AnonymousEndPoint]
        public async Task<CommandResponse> MatchOtpForForgetPassword([FromBody] MatchOtpForForgetPasswordCommand command)
        {
            _logger.LogInformation(
                "MessageCorrelationId:{MessageCoRelationId}, new MatchOtpForForgetPasswordCommand command.",
                command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }

        [HttpPost]
        [AnonymousEndPoint]
        public async Task<CommandResponse> ResetPassword([FromBody] ResetPasswordCommand command)
        {
            _logger.LogInformation(
                "MessageCorrelationId:{MessageCoRelationId}, new ResetPasswordCommand command.",
                command.MessageCorrelationId);
            return await _commandSender.SendAsync(command);
        }
    }
}