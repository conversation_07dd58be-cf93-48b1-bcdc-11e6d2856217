using BuroUserPermission.CustomAttribute;
using Domain.Commands.Voucher;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Voucher;

public class VoucherController : ControllerBase
{

    private readonly ICommandSender _commandSender;
    private readonly ILogger<VoucherController> _logger;

    public VoucherController(ILogger<VoucherController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateManualVoucher([FromBody] CreateManualVoucherCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, manual voucher create command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateManualVoucher([FromBody] UpdateManualVoucherCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, manual voucher update command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> DeleteManualVoucher([FromBody] DeleteManualVoucherCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, manual voucher delete command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }
}