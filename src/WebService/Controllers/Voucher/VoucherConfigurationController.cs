using BuroUserPermission.CustomAttribute;
using Domain.Commands.Voucher.Configuration;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Voucher;

public class VoucherConfigurationController : ControllerBase
{
    private readonly ICommandSender _commandSender;
    private readonly ILogger<VoucherConfigurationController> _logger;

    public VoucherConfigurationController(ILogger<VoucherConfigurationController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;

    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateVoucherConfiguration([FromBody] CreateVoucherConfigCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, voucher configuration setup create command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateVoucherConfiguration([FromBody] UpdateVoucherConfigCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, voucher configuration setup update command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> DeleteVoucherConfiguration([FromBody] DeleteVoucherConfigCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {Command}, voucher configuration setup delete command received.", command.MessageCorrelationId);

        return await _commandSender.SendAsync(command);
    }


}