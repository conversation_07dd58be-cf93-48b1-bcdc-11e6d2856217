using BuroUserPermission.CustomAttribute;
using Domain.Commands.Vendor;
using Microsoft.AspNetCore.Mvc;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService.Controllers.Vendor;

public class VendorController : ControllerBase
{
    private readonly ILogger<VendorController> _logger;
    private readonly ICommandSender _commandSender;

    public VendorController(ILogger<VendorController> logger,
        ICommandSender commandSender)
    {
        _logger = logger;
        _commandSender = commandSender;
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> CreateVendor([FromBody] CreateVendorCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, CreateVendorCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateVendor([FromBody] UpdateVendorCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateVendorCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }

    [HttpPost]
    [BuroProtectedEndpoint]
    public async Task<CommandResponse> UpdateVendorStatus([FromBody] UpdateVendorStatusCommand command)
    {
        _logger.LogInformation("MessageCorrelationId: {MessageCorrelationId}, UpdateVendorStatusCommand received.",
            command.MessageCorrelationId);
        return await _commandSender.SendAsync(command);
    }
}