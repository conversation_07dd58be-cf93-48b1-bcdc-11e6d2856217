using FluentValidation;
using FluentValidation.Results;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace WebService;

public class CommandSender : ICommandSender
{
    private readonly CommandHandler _commandHandler;
    private readonly IServiceClient _serviceClient;
    private readonly IServiceProvider _services;

    public CommandSender(
        IServiceProvider services,
        CommandHandler commandHandler,
        IServiceClient serviceClient)
    {
        _services = services ?? throw new ArgumentNullException(nameof(services));
        _commandHandler = commandHandler ?? throw new ArgumentNullException(nameof(commandHandler));
        _serviceClient = serviceClient ?? throw new ArgumentNullException(nameof(serviceClient));
    }

    public async Task<CommandResponse> SendAsync<TCommand>(
        TCommand command)
    {
        var failures = await ValidateCommandAsync(command);

        return failures.Any()
            ? CreateFailedCommandResponse(failures)
            : await _commandHandler
                .SubmitAsync<TCommand, CommandResponse>(command);
    }

    public CommandResponse Send<TCommand>(TCommand command)
    {
        var failures = ValidateCommandAsync(command).GetAwaiter().GetResult();

        return failures.Any()
            ? CreateFailedCommandResponse(failures)
            : _commandHandler
                .Submit<TCommand, CommandResponse>(command);
    }

    public async Task<CommandResponse> SendToQueueAsync<TCommand>(
        string queueName, TCommand command)
    {
        var failures = await ValidateCommandAsync(command);

        return failures.Any()
            ? CreateFailedCommandResponse(failures)
            : await Task.Run(() =>
                _serviceClient
                    .SendToQueue<CommandResponse>(
                        queueName,
                        command));
    }

    public CommandResponse SendToQueue<TCommand>(
        string queueName, TCommand command)
    {
        var failures = ValidateCommandAsync(command).GetAwaiter().GetResult();

        return failures.Any()
            ? CreateFailedCommandResponse(failures)
            : _serviceClient
                .SendToQueue<CommandResponse>(
                    queueName,
                    command);
    }

    private static CommandResponse CreateFailedCommandResponse(
        IEnumerable<ValidationFailure> failures)
    {
        return new CommandResponse(new ValidationResult(failures));
    }

    private async Task<List<ValidationFailure>> ValidateCommandAsync<TCommand>(
        TCommand command)
    {
        var validators = _services
            .GetServices<IValidator<TCommand>>();

        var validationTasks = validators
            .Select(v => v.ValidateAsync(command));

        var validationResults = await Task.WhenAll(validationTasks);

        var failures = validationResults
            .SelectMany(result => result.Errors)
            .Where(error => error != null)
            .ToList();

        return failures;
    }
}