<?xml version="1.0"?>
<doc>
    <assembly>
        <name>WebService</name>
    </assembly>
    <members>
        <member name="M:WebService.Controllers.OfficeController.Create(Domain.Commands.Office.CreateOfficeCommand)">
            <summary>
            Creates a new office with the specified details.
            If the office type is 'Branch', a valid survey must be completed before creation.
            This endpoint is intended for administrative use.
            </summary>
            <param name="command">The command containing all necessary office properties, including location and type.</param>
            <returns>A CommandResponse indicating whether the office was successfully created, or if there were validation errors.</returns>
        </member>
    </members>
</doc>
