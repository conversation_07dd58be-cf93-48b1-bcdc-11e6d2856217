using Domain.Commands.Member;
using FluentValidation;
using Infrastructure.Constants;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Validators.Member
{
    public class UpdateMemberConfigurationCommandValidator : AbstractValidator<UpdateMemberConfigurationCommand>
    {
        public UpdateMemberConfigurationCommandValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .InvalidMessage();

            RuleFor(command => command.DormantAccountTimeLineLimit)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.DormantAccountTimeLineLimit));

            RuleFor(command => command.FuneralContributionAmount)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.FuneralContributionAmount));

            RuleFor(command => command.PasswordExpirationLimit)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.PasswordExpirationLimit));

            RuleFor(command => command.SkipPasswordExpirationLimit)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.SkipPasswordExpirationLimit));

            RuleFor(command => command.MemberIdStartingNumber)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.MemberIdStartingNumber));

            RuleFor(command => command.MinimumIncomeForSamityCriteria)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.MinimumIncomeForSamityCriteria));

            RuleFor(command => command.MaximumIncomeForSamityCriteria)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.MaximumIncomeForSamityCriteria));

            RuleFor(command => command)
                .NotNull()
                .Must(a => a.MinimumIncomeForSamityCriteria < a.MaximumIncomeForSamityCriteria)
                .InvalidMessage(BuroErrorMessageKeys.InvalidIncomeRangeForSamityCriteria);

            RuleFor(command => command.UnitCostOfSMS)
                .GreaterThanOrEqualTo(0)
                .InvalidMessage(nameof(UpdateMemberConfigurationCommand.UnitCostOfSMS));

            RuleFor(command => command.MemberTypes)
                .NotNull()
                .Must(BeValidMemberTypes)
                .WithMessage(BuroErrorMessageKeys.InvalidMemberType);
        }

        private static bool BeValidMemberTypes(List<MemberType> memberTypes)
        {
            memberTypes = memberTypes.OrderBy(t => t.MinimumMonthlyIncome).ToList();

            for (int i = 0; i < memberTypes.Count; i++)
            {
                var currentRange = memberTypes[i];

                if (string.IsNullOrEmpty(currentRange.Name.Trim()))
                {
                    return false;
                }

                if (currentRange.MinimumMonthlyIncome.CompareTo(currentRange.MaximumMonthlyIncome) >= 0)
                {
                    return false;
                }

                if (i < memberTypes.Count - 1)
                {
                    var nextRange = memberTypes[i + 1];

                    if (nextRange.MinimumMonthlyIncome.CompareTo(currentRange.MaximumMonthlyIncome + 1) != 0)
                    {
                        return false;
                    }
                }
            }

            return true;
        }
    }
}
