using Domain.Commands.Member;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Member;

public class MembershipFeeSetupValidator : AbstractValidator<MembershipFeeSetupCommand>
{
    public MembershipFeeSetupValidator()
    {
        RuleFor(x => x.ParticularItemId)
            .NotEmpty()
            .NotNull()
            .When(x => x.CostingType == EnumMembershipEnrollmentCostingType.Inventory)
            .WithMessage("Inventory Item Id is required when Costing Type is Inventory");

        RuleFor(x => x.CostingType)
            .NotNull()
            .NotEmpty()
            .IsInEnum()
            .WithMessage("Costing type must be a valid value from EnumMembershipEnrollmentCostingType.");


    }

}