using Domain.Commands.Member;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Member
{
    public class PhoneNumberChangeRequestCommandValidator : AbstractValidator<PhoneNumberChangeRequestCommand>
    {
        private readonly IRepository _repository;

        public PhoneNumberChangeRequestCommandValidator(IRepository repository)
        {
            _repository = repository;
            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .InvalidMessage();

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(PhoneNumberChangeRequestCommand.OldPhoneNumber))
                .MustAsync(BeAValidOldNumberAsync)
                .DoesNotExistMessage(nameof(PhoneNumberChangeRequestCommand.OldPhoneNumber));

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(PhoneNumberChangeRequestCommand.NewPhoneNumber))
                .MustAsync(BeAValidNewNumberAsync)
                .ExistMessage(nameof(PhoneNumberChangeRequestCommand.NewPhoneNumber));
        }

        private async Task<bool> BeAValidOldNumberAsync(PhoneNumberChangeRequestCommand command, CancellationToken cancellationToken)
        {
            return await _repository.ExistsAsync<BuroMember>(
                m => m.ItemId == command.MemberItemId
                  && m.ContactNumbers.Contains(command.OldPhoneNumber));
        }

        private async Task<bool> BeAValidNewNumberAsync(PhoneNumberChangeRequestCommand command, CancellationToken cancellationToken)
        {
            var isExists = await _repository.ExistsAsync<BuroMember>(
                m => m.ItemId == command.MemberItemId
                  && m.ContactNumbers.Contains(command.NewPhoneNumber));

            return !isExists;
        }
    }
}
