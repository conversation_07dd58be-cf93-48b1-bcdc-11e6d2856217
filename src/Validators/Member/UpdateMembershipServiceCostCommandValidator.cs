using Domain.Commands.Member;
using FluentValidation;

namespace Validators.Member;

public class UpdateMembershipServiceCostCommandValidator : AbstractValidator<UpdateMembershipServiceCostCommand>
{
    public UpdateMembershipServiceCostCommandValidator()
    {
        RuleFor(command => command)
            .NotNull()
            .NotEmpty()
            .InvalidMessage();

        RuleFor(x => x.ServiceItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Service item id is required");
        RuleFor(x => x.Cost).GreaterThanOrEqualTo(0).WithMessage("Cost must be greater than or equal to 0");
    }

}