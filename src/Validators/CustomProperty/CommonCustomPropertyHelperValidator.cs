using Domain.Commands;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.CustomProperty
{
    public static class CommonCustomPropertyHelperValidator
    {
        public static bool IsValidCustomProperty(CustomPropertiesCommand[] customPropertyCommandArray, List<BuroCustomProperties> customProperty)
        {
            foreach (var item in customPropertyCommandArray)
            {
                bool isValid = item.CustomPropertyValueType switch
                {
                    EnumCustomPropertyValueType.Text => true,
                    EnumCustomPropertyValueType.Range => ValidateRangePropertyForInsert(item, customProperty),
                    EnumCustomPropertyValueType.Number => ValidateNumberPropertyForInsert(item),
                    _ => false
                };

                if (!isValid)
                {
                    return false;
                }
            }
            return true;
        }

        private static bool ValidateRangePropertyForInsert(CustomPropertiesCommand item, List<BuroCustomProperties> customProperty)
        {
            var range = customProperty.Find(c => c.ItemId == item.ItemId);
            return double.TryParse(item.Value, out double rangedValue) &&
                   range != null &&
                   rangedValue >= range.MinimumValue && rangedValue <= range.MaximumValue;
        }

        private static bool ValidateNumberPropertyForInsert(CustomPropertiesCommand item)
        {
            return double.TryParse(item.Value, out _);
        }
    }
}
