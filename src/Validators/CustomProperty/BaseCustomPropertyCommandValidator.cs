using Domain.Commands.CustomProperty;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.CustomProperty
{
    public class BaseCustomPropertyCommandValidator<T> : AbstractValidator<T> where T : BaseCustomPropertyCommand
    {
        public BaseCustomPropertyCommandValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .Must(BeValidDefaultValue)
                .WithMessage(BuroErrorMessageKeys.DefaultValueInvalid);
        }

        public bool BeValidDefaultValue(T command)
        {
            if (string.IsNullOrEmpty(command.DefaultValue))
            {
                return true;
            }

            return command.CustomPropertyValueType switch
            {
                EnumCustomPropertyValueType.Number => IsValidNumber(command.DefaultValue, out _),
                EnumCustomPropertyValueType.Range => IsValidRange(command.MinimumValue, command.MaximumValue, command.DefaultValue),
                _ => true
            };
        }

        private static bool IsValidNumber(string defaultValue, out double parsedValue)
        {
            return double.TryParse(defaultValue, out parsedValue);
        }

        private static bool IsValidRange(
            double minimumValue,
            double maximumValue,
            string defaultValue)
        {
            if (!IsValidNumber(defaultValue, out double parsedValue))
            {
                return false;
            }

            return minimumValue.CompareTo(parsedValue) <= 0
                && maximumValue.CompareTo(parsedValue) >= 0
                && minimumValue < maximumValue;
        }
    }
}
