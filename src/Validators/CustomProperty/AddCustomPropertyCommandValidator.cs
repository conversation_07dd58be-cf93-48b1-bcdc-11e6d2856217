using Domain.Commands.CustomProperty;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.CustomProperty
{
    public class AddCustomPropertyCommandValidator : BaseCustomPropertyCommandValidator<AddCustomPropertyCommand>
    {
        private readonly IRepository _repository;

        public AddCustomPropertyCommandValidator(IRepository repository)
        {
            _repository = repository;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.RequestBodyInvalid);

            RuleFor(command => command.CustomPropertyCategory)
                .IsInEnum()
                .WithMessage(BuroErrorMessageKeys.CustomPropertyCategoryInvalid);

            RuleFor(command => command.CustomPropertyValueType)
                .IsInEnum()
                .WithMessage(BuroErrorMessageKeys.PropertyValueTypeInvalid);

            RuleFor(command => command.PropertyName)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.PropertyNameInvalid);

            RuleFor(command => command.PropertyName)
                .MustAsync(BeANewCustomPropertyAsync)
                .WithMessage(BuroErrorMessageKeys.PropertyNameExists);
        }

        private async Task<bool> BeANewCustomPropertyAsync(string propertyName, CancellationToken token)
        {
            var hasProperty = await _repository.ExistsAsync<BuroCustomProperties>(
                p => string.Equals(p.PropertyName, propertyName, StringComparison.OrdinalIgnoreCase));

            return !hasProperty;
        }
    }
}
