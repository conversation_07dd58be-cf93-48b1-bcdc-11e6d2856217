using Domain.Commands.CustomProperty;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.CustomProperty
{
    public class RemoveCustomPropertyCommandValidator : AbstractValidator<RemoveCustomPropertyCommand>
    {
        private readonly IRepository _repository;

        public RemoveCustomPropertyCommandValidator(IRepository repository)
        {
            _repository = repository;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.RequestBodyInvalid);

            RuleFor(command => command.ItemId)
                .MustAsync(BeAnExistingCustomPropertyAsync)
                .WithMessage(BuroErrorMessageKeys.PropertyNameExists);
        }

        private async Task<bool> BeAnExistingCustomPropertyAsync(string itemId, CancellationToken token)
        {
            var hasProperty = await _repository.ExistsAsync<BuroCustomProperties>(
                p => string.Equals(p.ItemId, itemId, StringComparison.OrdinalIgnoreCase));

            return hasProperty;
        }
    }
}
