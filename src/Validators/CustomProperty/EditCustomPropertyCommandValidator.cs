using Domain.Commands.CustomProperty;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.CustomProperty
{
    public class EditCustomPropertyCommandValidator : BaseCustomPropertyCommandValidator<EditCustomPropertyCommand>
    {
        public EditCustomPropertyCommandValidator()
        {
            RuleFor(command => command)
                .NotNull()
                .NotEmpty()

                .WithMessage(BuroErrorMessageKeys.RequestBodyInvalid);

            RuleFor(command => command.CustomPropertyCategory)
                .IsInEnum()
                .WithMessage(BuroErrorMessageKeys.CustomPropertyCategoryInvalid);

            RuleFor(command => command.CustomPropertyValueType)
                .IsInEnum()
                .WithMessage(BuroErrorMessageKeys.PropertyValueTypeInvalid);
        }
    }
}
