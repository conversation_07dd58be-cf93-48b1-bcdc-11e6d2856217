using Domain.Commands.Account;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Account
{
    public class ChangeGSAccountCommandValidator : AbstractValidator<ChangeGSAccountCommand>
    {
        private readonly IRepository _repository;

        public ChangeGSAccountCommandValidator(IRepository repository)
        {
            _repository = repository;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(x => x.MemberItemId)
                .NotEmpty().NotNull();

            RuleFor(x => x.ProductLineItemId)
                .NotEmpty().NotNull()
                .MustAsync(ProductLineMustBeDifferent);
        }

        private async Task<bool> ProductLineMustBeDifferent(ChangeGSAccountCommand command, string newProductLineItemId, CancellationToken cancellationToken)
        {
            var currentGSAccount = await _repository.GetItemAsync<BuroGeneralSavingsAccount>(
                x => x.MemberItemId == command.MemberItemId && x.AccountStatus == EnumProductAccountStatus.Active);

            if (currentGSAccount == null)
                return true;

            return currentGSAccount.ProductLineItemId != newProductLineItemId;
        }
    }
}
