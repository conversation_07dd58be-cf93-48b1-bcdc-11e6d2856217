using Domain.Commands.Account;
using EnumsNET;
using FinMongoDbRepositories;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Account
{
    public class SubmitDisbursementCommandValidator : AbstractValidator<SubmitDisbursementCommand>
    {
        private readonly IFinMongoDbRepository _repository;

        public SubmitDisbursementCommandValidator(IFinMongoDbRepository repository)
        {
            _repository = repository;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .InvalidMessage();

            RuleFor(command => command.LoanApplicationItemId)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(SubmitDisbursementCommand.LoanApplicationItemId))
                .MustAsync(BeAExistingLoanApplictionAsync)
                .InvalidMessage(nameof(SubmitDisbursementCommand.LoanApplicationItemId));

            RuleFor(command => command)
                .Must((command) => command.Status.IsDefined())
                .MustAsync(BeAValidStatusAsync)
                .InvalidMessage(nameof(SubmitDisbursementCommand.Status));

            When(command => command.Status == EnumLoanApplicationDisbursementStatus.NegotiationSubmited, () =>
            {
                RuleFor(command => command.FirstInstallmentDate)
                    .NotNull()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.FirstInstallmentDate))
                    .Must(BeAValidInstallmentDate)
                    .InvalidMessage(nameof(SubmitDisbursementCommand.FirstInstallmentDate));

                RuleFor(command => command.ChequeInformation)
                    .NotNull()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.ChequeInformation));

                RuleFor(command => command.ChequeInformation.BankName)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.ChequeInformation.BankName));

                RuleFor(command => command.ChequeInformation.BankAccountNumber)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.ChequeInformation.BankAccountNumber));

                RuleFor(command => command.ChequeInformation.Amount)
                    .GreaterThan(0)
                    .InvalidMessage(nameof(SubmitDisbursementCommand.ChequeInformation.Amount));

                RuleFor(command => command.ChequeInformation.InfavorOf)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.ChequeInformation.InfavorOf));

                RuleFor(command => command.ChequeInformation.RoutingNumber)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.ChequeInformation.RoutingNumber));

                RuleFor(command => command)
                    .NotNull()
                    .NotEmpty()
                    .MustAsync(BeAValidChequeAsync)
                    .RequiredMessage("Duplicate cheque found");

                RuleFor(command => command.LoanAgreementFormFileItemId)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.LoanAgreementFormFileItemId));

                RuleFor(command => command.SecurityChequeFileItemId)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.SecurityChequeFileItemId));
            });

            When(command => command.Status == EnumLoanApplicationDisbursementStatus.CustomerSecurityFundCollected, () =>
            {
                RuleFor(command => command.FinalMicroInsuranceFee)
                    .GreaterThan(0)
                    .InvalidMessage(nameof(SubmitDisbursementCommand.FinalMicroInsuranceFee));
            });

            When(command => command.Status == EnumLoanApplicationDisbursementStatus.DisbursementAcknowledged, () =>
            {
                RuleFor(command => command.DisbursementAcknowledgementDetails)
                    .NotNull()
                    .RequiredMessage(nameof(SubmitDisbursementCommand.DisbursementAcknowledgementDetails));

                RuleFor(command => command.DisbursementAcknowledgementDetails.DisbursementPaymentOption)
                    .IsInEnum()
                    .InvalidMessage(nameof(SubmitDisbursementCommand.DisbursementAcknowledgementDetails.DisbursementPaymentOption));

                RuleFor(command => command.DisbursementAcknowledgementDetails.Amount)
                    .NotNull()
                    .GreaterThan(0)
                    .InvalidMessage(nameof(SubmitDisbursementCommand.DisbursementAcknowledgementDetails.Amount));

                When(command => command.DisbursementAcknowledgementDetails.DisbursementPaymentOption != EnumTransactionMedium.Cash, () =>
                {
                    RuleFor(command => command.DisbursementAcknowledgementDetails.AccountNumber)
                        .NotNull()
                        .NotEmpty()
                        .InvalidMessage(nameof(SubmitDisbursementCommand.DisbursementAcknowledgementDetails.AccountNumber));
                });
            });
        }

        private async Task<bool> BeAValidChequeAsync(SubmitDisbursementCommand command, CancellationToken token)
        {
            if (command.ChequeInformation == null)
            {
                return false;
            }

            var isChequeExists = await _repository.ExistsAsync<BuroLoanApplicationDisbursement>(
                c => c.LoanApplicationItemId == command.LoanApplicationItemId
                && c.NegotiationInformation != null
                && c.NegotiationInformation.ChequeInformation != null
                && c.NegotiationInformation.ChequeInformation.BankName == command.ChequeInformation.BankName
                && c.NegotiationInformation.ChequeInformation.BankAccountNumber == command.ChequeInformation.BankAccountNumber
                && c.NegotiationInformation.ChequeInformation.Amount.CompareTo(command.ChequeInformation.Amount) == 0
                && c.NegotiationInformation.ChequeInformation.RoutingNumber == command.ChequeInformation.RoutingNumber);

            return !isChequeExists;
        }

        private async Task<bool> BeAValidStatusAsync(SubmitDisbursementCommand command, CancellationToken cancellationToken)
        {
            var disbursement = await _repository.FindOneAsync<BuroLoanApplicationDisbursement>(
                d => d.ItemId == command.LoanApplicationDisbursementItemId
                  || d.LoanApplicationItemId == command.LoanApplicationItemId);

            if (disbursement == null)
            {
                return true;
            }

            var statuses = (await _repository.FindAsync<BuroLoanApplicationDisbursementHistory>(
                h => h.LoanApplicationDisbursementItemId == disbursement.ItemId))
                .Select(h => h.Status)
                .ToList();

            if (!statuses.Any())
            {
                return true;
            }

            if (statuses.Exists(s => s == command.Status))
            {
                return false;
            }

            var enumValues = Enum.GetValues<EnumLoanApplicationDisbursementStatus>().ToList();
            var requestedStatusIndex = enumValues.IndexOf(command.Status);
            var priorStatuses = enumValues.Take(requestedStatusIndex);

            return statuses.TrueForAll(s => priorStatuses.Contains(s));
        }

        private async Task<bool> BeAExistingLoanApplictionAsync(string loanApplicationItemId, CancellationToken token)
        {
            var isExisting = await _repository.ExistsAsync<BuroLoanApplication>(a => a.ItemId == loanApplicationItemId);

            return isExisting;
        }

        private static bool BeAValidInstallmentDate(DateTime? installmentDate)
        {
            if (!installmentDate.HasValue)
            {
                return false;
            }

            return installmentDate.GetValueOrDefault().ToUniversalTime().Date >= DateTime.UtcNow.Date;
        }
    }
}
