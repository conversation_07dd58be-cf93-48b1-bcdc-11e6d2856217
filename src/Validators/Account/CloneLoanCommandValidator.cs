using Domain.Commands.Account;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Account
{
    public class CloneLoanCommandValidator : AbstractValidator<CloneLoanCommand>
    {
        private readonly IRepository _repository;

        public CloneLoanCommandValidator(IRepository repository)
        {
            _repository = repository;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .InvalidMessage();

            RuleFor(command => command.LoanApplicationItemId)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(CloneLoanCommand.LoanApplicationItemId))
                .MustAsync(BeAnExistingLoanAsync)
                .InvalidMessage(nameof(CloneLoanCommand.LoanApplicationItemId));
        }

        private async Task<bool> BeAnExistingLoanAsync(string loanApplicationItemId, CancellationToken token)
        {
            var isExistingLoan = await _repository.ExistsAsync<BuroLoanApplication>(a => a.ItemId == loanApplicationItemId);

            return isExistingLoan;
        }
    }
}
