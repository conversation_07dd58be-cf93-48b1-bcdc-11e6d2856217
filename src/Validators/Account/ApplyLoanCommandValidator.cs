using Domain.Commands.Account;
using FinMongoDbRepositories;
using FluentValidation;
using Infrastructure.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;

namespace Validators.Account
{
    public class ApplyLoanCommandValidator : AbstractValidator<ApplyLoanCommand>
    {
        private readonly IFinMongoDbRepository _repository;

        public ApplyLoanCommandValidator(IFinMongoDbRepository repository)
        {
            _repository = repository;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .InvalidMessage();

            RuleFor(command => command.MemberItemId)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(ApplyLoanCommand.MemberItemId));

            RuleFor(command => command.LoanAmount)
                .GreaterThan(0)
                .InvalidMessage(nameof(ApplyLoanCommand.LoanAmount));

            RuleFor(command => command.LoanRequiredByDate)
                .NotNull()
                .RequiredMessage(nameof(ApplyLoanCommand.LoanRequiredByDate))
                .Must(BeAvalidLoanRequiredDate)
                .InvalidMessage(nameof(ApplyLoanCommand.LoanRequiredByDate));

            RuleFor(command => command.Status)
                .IsInEnum()
                .Must(BeAValidStatus)
                .InvalidMessage(nameof(ApplyLoanCommand.Status));

            RuleFor(command => command.LoanSectorItemId)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(ApplyLoanCommand.LoanSectorItemId))
                .MustAsync(BeAnExistingSectorAsync)
                .InvalidMessage(nameof(ApplyLoanCommand.LoanSectorItemId));

            RuleFor(command => command.ProductLineItemId)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(ApplyLoanCommand.ProductLineItemId))
                .MustAsync(BeAnExistingLoanProductLineAsync)
                .InvalidMessage(nameof(ApplyLoanCommand.ProductLineItemId));

            RuleFor(command => command.TenureDetails)
                .NotNull()
                .InvalidMessage(nameof(ApplyLoanCommand.TenureDetails));

            When(command => command.Status == EnumProductApplicationStatus.Pending, () =>
            {
                RuleFor(command => command.FatherOrSpouseName)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(ApplyLoanCommand.FatherOrSpouseName));

                RuleFor(command => command.MotherName)
                    .NotNull()
                    .NotEmpty()
                    .RequiredMessage(nameof(ApplyLoanCommand.MotherName));

                RuleFor(command => command.Investment)
                    .Must(BeValidInvestment)
                    .InvalidMessage(nameof(ApplyLoanCommand.Investment));

                RuleFor(command => command.BusinessInformation)
                    .NotNull()
                    .RequiredMessage(nameof(ApplyLoanCommand.BusinessInformation))
                    .Must(BeAValidBusinessInformation)
                    .RequiredMessage(nameof(ApplyLoanCommand.BusinessInformation.ProductOrServiceName));

                RuleFor(command => command.SelfNetIncome)
                    .NotNull()
                    .RequiredMessage(nameof(ApplyLoanCommand.SelfNetIncome))
                    .Must(BeValidNetIncome)
                    .InvalidMessage(nameof(ApplyLoanCommand.SelfNetIncome));

                RuleFor(command => command.FamilyNetIncome)
                    .NotNull()
                    .RequiredMessage(nameof(ApplyLoanCommand.FamilyNetIncome))
                    .Must(BeValidNetIncome)
                    .InvalidMessage(nameof(ApplyLoanCommand.FamilyNetIncome));

                RuleFor(command => command)
                    .MustAsync(BeValidGuarantorInformationAsync)
                    .InvalidMessage(nameof(ApplyLoanCommand.Guarantors));

                RuleFor(command => command.PaymentInterval)
                    .IsInEnum()
                    .InvalidMessage(nameof(ApplyLoanCommand.PaymentInterval));

                RuleFor(command => command.InitialMicroInsuranceFee)
                    .GreaterThan(0)
                    .InvalidMessage(nameof(ApplyLoanCommand.InitialMicroInsuranceFee));
            });
        }

        private async Task<bool> BeAnExistingSectorAsync(string sectorItemId, CancellationToken token)
        {
            var isExistingSector = await _repository.ExistsAsync<BuroSector>(s => s.ItemId == sectorItemId && !s.IsMarkedToDelete);

            return isExistingSector;
        }

        private async Task<bool> BeAnExistingLoanProductLineAsync(string productLineItemId, CancellationToken token)
        {
            var isExistingProductLine = await _repository.ExistsAsync<LoanProductLine>(p => p.ItemId == productLineItemId && !p.IsMarkedToDelete);

            return isExistingProductLine;
        }

        private static bool BeAvalidLoanRequiredDate(DateTime? date)
        {
            if (!date.HasValue)
            {
                return false;
            }

            return date.Value.Date >= DateTime.Now.Date;
        }

        private async Task<bool> BeValidGuarantorInformationAsync(ApplyLoanCommand command, CancellationToken cancellationToken)
        {
            if (!command.Guarantors.Any())
            {
                return false;
            }

            var hasValidGurantorInformation = command.Guarantors.TrueForAll(
                g => g.IsGuarantorExistingBuroMember
                  ? !string.IsNullOrWhiteSpace(g.MemberItemId)
                  : (!string.IsNullOrWhiteSpace(g.Name)
                 && !string.IsNullOrWhiteSpace(g.Phone)
                 && !string.IsNullOrWhiteSpace(g.NID)
                 && !string.IsNullOrWhiteSpace(g.PhotoId)
                 && Enum.IsDefined(typeof(EnumRelationShip), g.Relationship)
                 && !string.IsNullOrWhiteSpace(g.Address)));

            var buroGuarantorItemIds = command.Guarantors
                .Where(g => g.IsGuarantorExistingBuroMember)
                .Select(g => g.MemberItemId)
                .ToList();

            var isAGurantor = await _repository.ExistsAsync<BuroMember>(
                m => buroGuarantorItemIds.Contains(m.ItemId)
                  && m.IsAGuarantor
                  && !m.IsMarkedToDelete);

            return hasValidGurantorInformation && !isAGurantor;
        }

        private static bool BeValidNetIncome(NetIncome income)
        {
            if (income == null)
            {
                return false;
            }

            var isValidTotalIncome = income.TotalIncome.CompareTo(income.Incomes.Sum(a => a.Amount)) == 0;
            var isValidTotalExpense = income.TotalExpense.CompareTo(income.Expenses.Sum(a => a.Amount)) == 0;

            return isValidTotalIncome && isValidTotalExpense;
        }

        private static bool BeAValidBusinessInformation(BusinessInformation information)
        {
            return information == null || !information.ProductOrServiceName.IsNullOrEmptyOrWhiteSpace();
        }

        private static bool BeValidInvestment(BuroInvestment investments)
        {
            if (investments == null)
            {
                return false;
            }

            return investments.TotalInvestedAmount.CompareTo(
                investments.OwnInvestedAmount
                + investments.BankLoans.Sum(x => x.Amount)
                + investments.NGOLoans.Sum(x => x.Amount)
                + investments.OtherLoans.Sum(x => x.Amount)) == 0;
        }

        private static bool BeAValidStatus(EnumProductApplicationStatus status)
        {
            return status == EnumProductApplicationStatus.Draft
                || status == EnumProductApplicationStatus.Pending;
        }
    }
}
