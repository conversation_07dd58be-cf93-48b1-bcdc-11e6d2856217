using Domain.Commands.Account;
using FinMongoDbRepositories;
using FluentValidation;
using MongoDB.Driver;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Account
{
    public class SubmitLoanCommandValidator : AbstractValidator<SubmitLoanCommand>
    {
        private readonly IFinMongoDbRepository _repository;

        public SubmitLoanCommandValidator(IFinMongoDbRepository repository)
        {
            _repository = repository;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .InvalidMessage();

            RuleFor(command => command.LoanApplicationItemId)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(SubmitLoanCommand.LoanApplicationItemId));

            RuleFor(command => command.Status)
                .IsInEnum()
                .Must(BeAValidStatus)
                .InvalidMessage(nameof(SubmitLoanCommand.Status));

            When(command => command.Status == EnumProductApplicationStatus.ApprovalOngoing, () =>
            {
                RuleFor(command => command.FinalLoanAmount)
                    .NotNull()
                    .GreaterThan(0)
                    .InvalidMessage(nameof(SubmitLoanCommand.FinalLoanAmount));

                RuleFor(command => command)
                    .MustAsync(IsReviewedLoanApplicationAsync)
                    .InvalidMessage(nameof(SubmitLoanCommand.Status));
            });
        }

        private async Task<bool> IsReviewedLoanApplicationAsync(SubmitLoanCommand command, CancellationToken cancellation)
        {
            var filterDefinition = Builders<BuroProductApplicationHistory>.Filter
                .Where(h => h.ApplicationItemId == command.LoanApplicationItemId
                         && h.ProductType == EnumProductType.Loan
                         && h.Status == EnumProductApplicationStatus.InReview);

            var isReviewed = (await _repository.GetCountByFilterDefinitionAsync(filterDefinition)) > 0;

            return isReviewed;
        }

        private static bool BeAValidStatus(EnumProductApplicationStatus status)
        {
            return status == EnumProductApplicationStatus.InReview
                || status == EnumProductApplicationStatus.Correction
                || status == EnumProductApplicationStatus.Rejected
                || status == EnumProductApplicationStatus.Discarded
                || status == EnumProductApplicationStatus.ApprovalOngoing;
        }
    }
}
