using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Validators.Account
{
    public class NomineeValidator : AbstractValidator<List<Nominee>>
    {
        public NomineeValidator()
        {
            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .InvalidMessage(nameof(Nominee));

            RuleFor(command => command)
                .Must(HasRequiredPhoto)
                .RequiredMessage(nameof(Nominee.PhotoId))
                .Must(HasRequiredSignature)
                .RequiredMessage(nameof(Nominee.SignatureId))
                .Must(BeNonZeroValidShareHolder)
                .WithMessage(BuroErrorMessageKeys.InconsistentShareHolder);
        }

        private static bool HasRequiredSignature(List<Nominee> nominees)
        {
            return !nominees.Exists(n => string.IsNullOrEmpty(n.SignatureId.Trim()));
        }

        private static bool HasRequiredPhoto(List<Nominee> nominees)
        {
            return !nominees.Exists(n => string.IsNullOrEmpty(n.PhotoId.Trim()));
        }

        private static bool BeNonZeroValidShareHolder(List<Nominee> nominees)
        {
            if (nominees == null || !nominees.Any())
            {
                return false;
            }

            var hasAllNonZeroSharePercentage = nominees.TrueForAll(n => n.SharedPercentage.CompareTo(0) == 1);
            var hundredPercentInTotal = hasAllNonZeroSharePercentage && (nominees.Sum(n => n.SharedPercentage).CompareTo(100) == 0);

            return hundredPercentInTotal;
        }
    }
}
