using Domain.Commands.Account;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Account
{
    public class CreateCSAccountCommandValidator : AbstractValidator<CreateCSAccountCommand>
    {
        private readonly IRepository _repository;

        public CreateCSAccountCommandValidator(IRepository repository)
        {
            _repository = repository;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .InvalidMessage();

            RuleFor(command => command.Status)
                .Must(BeAValidStatus)
                .InvalidMessage(nameof(CreateCSAccountCommand.Status));

            When(command => command.Status == EnumProductApplicationStatus.Pending, () =>
            {
                RuleFor(command => command)
                    .MustAsync(BeAValidInstalmentAmountAsync)
                    .InvalidMessage(nameof(CreateCSAccountCommand.InstalmentAmount));

                RuleFor(command => command.TenureDetails)
                    .NotNull()
                    .InvalidMessage(nameof(CreateCSAccountCommand.TenureDetails));

                RuleFor(command => command.PaymentInterval)
                    .IsInEnum()
                    .InvalidMessage(nameof(CreateCSAccountCommand.PaymentInterval));

                RuleFor(command => command.Nominees)
                    .SetValidator(new NomineeValidator());
            });
        }

        private async Task<bool> BeAValidInstalmentAmountAsync(CreateCSAccountCommand command, CancellationToken cancellationToken)
        {
            if (command.InstalmentAmount.CompareTo(0) == 0)
            {
                return false;
            }

            var csProductLine = await _repository.GetItemAsync<ContractualSavingProductLine>(pl => pl.ItemId == command.ProductLineItemId);
            var isValidInstalmentAmount = (command.InstalmentAmount % csProductLine.InstallmentMultiplier).CompareTo(0) == 0;

            return isValidInstalmentAmount;
        }

        private static bool BeAValidStatus(EnumProductApplicationStatus status)
        {
            return status == EnumProductApplicationStatus.Draft || status == EnumProductApplicationStatus.Pending;
        }
    }
}
