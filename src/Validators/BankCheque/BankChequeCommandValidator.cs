using Domain.Commands.BankCheque;
using FluentValidation;

namespace Validators.BankCheque;

public class BankChequeCommandValidator : AbstractValidator<CreateChequeBookCommand>
{
    public BankChequeCommandValidator()
    {
        RuleFor(command => command.OfficeItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Office item id is required.");

        RuleFor(command => command.BankBranchItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Bank Branch item id is required.");

        RuleFor(command => command.AccountNumber)
            .NotNull()
            .NotEmpty()
            .WithMessage("Account number is required.");

        RuleFor(command => command.AccountName)
            .NotNull()
            .NotEmpty()
            .WithMessage("Account name is required.");

        RuleFor(command => command.TotalLeaves)
            .GreaterThan(0)
            .WithMessage("Total leaves must be greater than 0.");

        RuleFor(command => command.LeavesThreshold)
            .GreaterThan(0)
            .WithMessage("Leaves threshold must be greater than 0.");

        RuleFor(command => command.StartingLeavesSerial)
            .NotNull()
            .NotEmpty()
            .WithMessage("StartingLeavesSerial is required.");

    }
}