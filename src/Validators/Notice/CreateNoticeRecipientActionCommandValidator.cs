using Domain.Commands.Notice;
using FluentValidation;

namespace Validators.Notice
{
    public class CreateNoticeRecipientActionCommandValidator : AbstractValidator<CreateNoticeRecipientActionCommand>
    {
        public CreateNoticeRecipientActionCommandValidator()
        {
            RuleFor(command => command)
              .NotNull()
              .NotEmpty()
              .InvalidMessage();

            RuleFor(command => command.NoticeItemId)
             .NotNull()
             .NotEmpty()
             .InvalidMessage();

            RuleFor(command => command.ClickedButtonType)
            .IsInEnum()
            .InvalidMessage();
        }
    }
}
