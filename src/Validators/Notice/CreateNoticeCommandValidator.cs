using Domain.Commands.Notice;
using FluentValidation;

namespace Validators.Notice
{
    public class CreateNoticeCommandValidator : AbstractValidator<CreateNoticeCommand>
    {
        public CreateNoticeCommandValidator()
        {
            RuleFor(command => command)
             .NotNull()
             .NotEmpty()
             .InvalidMessage();

            RuleFor(command => command.StartDate)
                .NotNull()
                .NotEmpty()
                .InvalidMessage();

            RuleFor(command => command.EndDate)
                .NotNull()
                .NotEmpty()
                .InvalidMessage();

            RuleFor(command => command.Title)
                .NotNull()
                .NotEmpty()
                .InvalidMessage();

            RuleFor(command => command.Description)
                .NotNull()
                .NotEmpty()
                .InvalidMessage();

            RuleFor(command => command.RecipientOfficeItemIds)
             .NotNull()
             .NotEmpty()
             .InvalidMessage();

            RuleFor(command => command.RecipientDesignationItemIds)
             .NotNull()
             .NotEmpty()
             .InvalidMessage();
        }
    }
}
