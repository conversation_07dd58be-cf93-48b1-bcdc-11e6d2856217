using Domain.Commands.Inventory;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Inventory;

public class CreateInventoryRequestCommandValidator : AbstractValidator<CreateInventoryRequestCommand>
{
    public CreateInventoryRequestCommandValidator()
    {
        RuleFor(x => x.InventoryItemId)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.InventoryItemIdRequired);

        RuleFor(x => x.InventoryName)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.InventoryNameRequired);

        RuleFor(x => x.RequestedQuantity)
            .GreaterThan(0).WithMessage(BuroErrorMessageKeys.QuantityRequired);
    }
}