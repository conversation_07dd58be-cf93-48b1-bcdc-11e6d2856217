using Domain.Commands.Inventory;
using FinMongoDbRepositories;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace Validators.Inventory;

public class OutInventoryItemValidator : AbstractValidator<OutInventoryItemCommand>
{
    private readonly IFinMongoDbRepository _repository;

    public OutInventoryItemValidator(IFinMongoDbRepository repository)
    {
        _repository = repository;
        RuleFor(x => x.InventoryItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.InventoryItemIdRequired);
        RuleFor(command => command)
            .MustAsync((cmd, token) => EntityExists(cmd.InventoryItemId))
            .WithMessage(BuroErrorMessageKeys.InventoryItemDoesntExists);
        RuleFor(x => x.VendorItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.VendorItemIdRequired);
        RuleFor(x => x.VendorName).NotEmpty().WithMessage(BuroErrorMessageKeys.VendorNameRequired);
        RuleFor(x => x.TransferEmployeeItemId)
            .NotEmpty()
            .When(x => x.OutDestination == EnumInventoryOutDestination.Employee)
            .WithMessage(BuroErrorMessageKeys.EmployeeItemIdRequired);

        RuleFor(x => x.TransferEmployeeName)
            .NotEmpty()
            .When(x => x.OutDestination == EnumInventoryOutDestination.Employee)
            .WithMessage(BuroErrorMessageKeys.EmployeeNameRequired);

        RuleFor(x => x.InventoryItemDetails)
            .NotEmpty()
            .Must(x => x.Count > 0)
            .WithMessage(BuroErrorMessageKeys.AtLeastOneItemRequired)
            .DependentRules(() =>
            {
                RuleForEach(x => x.InventoryItemDetails)
                    .SetValidator(new OutInventoryDetailValidator());
            });
    }

    private async Task<bool> EntityExists(string inventoryItemId)
    {
        return await _repository.ExistsAsync<BuroInventoryItem>(item => item.ItemId == inventoryItemId);
    }
}