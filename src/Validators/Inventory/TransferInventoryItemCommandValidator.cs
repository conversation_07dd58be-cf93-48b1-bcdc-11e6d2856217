using Domain.Commands.Inventory;
using FinMongoDbRepositories;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Inventory;

public class TransferInventoryItemCommandValidator : AbstractValidator<TransferInventoryItemCommand>
{
    private readonly IFinMongoDbRepository _repository;
    private readonly ISecurityContextProvider _securityContextProvider;

    public TransferInventoryItemCommandValidator(IFinMongoDbRepository repository,
        ISecurityContextProvider securityContextProvider)
    {
        _repository = repository;
        _securityContextProvider = securityContextProvider;
        RuleFor(x => x.InventoryItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.InventoryItemIdRequired);
        RuleFor(command => command)
            .MustAsync((cmd, token) => EntityExists(cmd.InventoryItemId))
            .WithMessage(BuroErrorMessageKeys.InventoryItemDoesntExists);

        RuleFor(command => command)
            .MustAsync((cmd, token) => IsDifferentOffice(cmd.TransferOfficeItemId))
            .WithMessage(BuroErrorMessageKeys.InvalidTransferOfficeId);

        RuleFor(x => x.VendorItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.VendorItemIdRequired);
        RuleFor(x => x.VendorName).NotEmpty().WithMessage(BuroErrorMessageKeys.VendorNameRequired);
        RuleFor(x => x.TransferOfficeItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.TransferOfficeItemIdRequired);

        RuleFor(x => x.TransferOfficeName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.TransferOfficeNameRequired);

        RuleFor(x => x.InventoryItemDetails)
            .NotEmpty()
            .Must(x => x.Count > 0)
            .WithMessage(BuroErrorMessageKeys.AtLeastOneItemRequired)
            .DependentRules(() =>
            {
                RuleForEach(x => x.InventoryItemDetails)
                    .SetValidator(new OutInventoryDetailValidator());
            });
    }

    private async Task<bool> EntityExists(string inventoryItemId)
    {
        return await _repository.ExistsAsync<BuroInventoryItem>(item => item.ItemId == inventoryItemId);
    }

    private async Task<bool> IsDifferentOffice(string transferOfficeItemId)
    {
        var loggedInEmployee = await _repository.FindOneAsync<BuroEmployee>(item => item.UserItemId == _securityContextProvider.GetSecurityContext().UserId);
        return loggedInEmployee.CurrentOfficeItemId != transferOfficeItemId;
    }
}