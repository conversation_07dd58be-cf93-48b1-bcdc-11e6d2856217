using Domain.Commands.Inventory;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Inventory;

public class CreateInventoryItemCommandValidator : AbstractValidator<CreateInventoryItemCommand>
{
    public CreateInventoryItemCommandValidator()
    {
        RuleFor(x => x.ItemDefinition)
            .NotNull().WithMessage(BuroErrorMessageKeys.ItemDefinitionRequired);

        RuleFor(x => x.InventoryItemName)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.InventoryItemNameRequired);

        RuleFor(x => x.CategoryItemId)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.CategoryItemIdRequired);
        RuleFor(x => x.CategoryName)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.CategoryNameRequired);

        RuleFor(x => x.SubCategoryItemId)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.SubCategoryItemIdRequired);

        RuleFor(x => x.SubCategoryName)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.SubCategoryNameRequired);

        RuleFor(x => x.PriceType)
            .NotNull().WithMessage(BuroErrorMessageKeys.PriceTypeRequired);

        When(x => x.PriceType == EnumInventoryPriceType.Range, () =>
        {
            RuleFor(x => x.MinPrice)
                .GreaterThanOrEqualTo(0).WithMessage(BuroErrorMessageKeys.MinPriceRequired);

            RuleFor(x => x.MaxPrice)
                .GreaterThanOrEqualTo(0).WithMessage(BuroErrorMessageKeys.MaxPriceRequired);
            RuleFor(x => x)
                .Must(x => !x.MinPrice.HasValue || !x.MaxPrice.HasValue || x.MinPrice <= x.MaxPrice)
                .WithMessage(BuroErrorMessageKeys.MinPriceMustBeLessThanMaxPrice);
        });

        When(x => x.PriceType == EnumInventoryPriceType.Fixed, () =>
            RuleFor(x => x.FixedPrice).GreaterThanOrEqualTo(0).WithMessage(BuroErrorMessageKeys.FixedPriceRequired));
    }
}