using Domain.Commands.Inventory;
using FinMongoDbRepositories;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Inventory;

public class TransferRequestedInventoryListCommandValidator : AbstractValidator<TransferRequestedInventoryListCommand>
{
    public TransferRequestedInventoryListCommandValidator(IFinMongoDbRepository repository, ISecurityContextProvider securityContextProvider)
    {
        RuleFor(x => x.TransferInventoryItems)
            .NotNull()
            .WithMessage(BuroErrorMessageKeys.AtLeastOneItemRequired)
            .Must(x => x.Count > 0)
            .WithMessage(BuroErrorMessageKeys.AtLeastOneItemRequired)
            .DependentRules(() =>
            {
                RuleForEach(x => x.TransferInventoryItems)
                    .SetValidator(new TransferInventoryItemCommandValidator(repository, securityContextProvider));
            });
    }

}