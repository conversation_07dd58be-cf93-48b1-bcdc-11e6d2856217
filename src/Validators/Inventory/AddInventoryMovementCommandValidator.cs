using Domain.Commands.Inventory;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Inventory;

public class AddInventoryMovementCommandValidator : AbstractValidator<AddInventoryMovementCommand>
{
    public AddInventoryMovementCommandValidator()
    {
        RuleFor(x => x.InventoryItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.InventoryItemIdRequired);
        RuleFor(x => x.VendorItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.VendorItemIdRequired);
        RuleFor(x => x.VendorName).NotEmpty().WithMessage(BuroErrorMessageKeys.VendorNameRequired);
        RuleFor(x => x.Quantity).GreaterThan(0).WithMessage(BuroErrorMessageKeys.QuantityRequired);
        RuleFor(x => x.Price).GreaterThanOrEqualTo(0).WithMessage(BuroErrorMessageKeys.PriceRequired);
    }
}