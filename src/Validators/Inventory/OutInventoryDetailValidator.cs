using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Inventory;

namespace Validators.Inventory;

public class OutInventoryDetailValidator : AbstractValidator<InventoryItemDetail>
{
    public OutInventoryDetailValidator()
    {
        RuleFor(x => x.InventoryStockBreakdownItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.InventoryStockBreakdownItemIdRequired);

        RuleFor(x => x.Quantity)
            .GreaterThan(0)
            .WithMessage(BuroErrorMessageKeys.QuantityRequired);

        RuleFor(x => x.Price)
            .GreaterThanOrEqualTo(0)
            .WithMessage(BuroErrorMessageKeys.PriceRequired);
    }
}