using Domain.Commands.Holiday;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Holiday;

public class CreateHolidayCommandValidator : AbstractValidator<CreateHolidayCommand>
{

    public CreateHolidayCommandValidator()
    {

        // Validate Holiday Start Date
        RuleFor(command => command.HolidayStartDate)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.InvalidDate)
            .Must(date => date.HasValue && date.Value != DateTime.MinValue)
            .WithMessage("Holiday start date cannot be empty or default")
            .Must(date => date.HasValue && date.Value.Kind == DateTimeKind.Utc)
            .WithMessage("Holiday start date must be in UTC");


        RuleFor(command => command.HolidayEndDate)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.InvalidDate)
            .Must(date => date.HasValue && date.Value != DateTime.MinValue)
            .WithMessage("Holiday end date cannot be empty or default")
            .Must(date => date.HasValue && date.Value.Kind == DateTimeKind.Utc)
            .WithMessage("Holiday end date must be in UTC");

        // Validate End Date is after Start Date
        RuleFor(command => command)
            .Must(command => !command.HolidayStartDate.HasValue || !command.HolidayEndDate.HasValue ||
                             command.HolidayStartDate.Value <= command.HolidayEndDate.Value)
            .WithMessage("Holiday start date cannot be later than the end date")
            .When(command => command.HolidayStartDate.HasValue && command.HolidayEndDate.HasValue);



        // Validate Holiday Type
        RuleFor(command => command.HolidayType)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.HolidayTypeRequired);

        // Office Specific Holiday Validation
        When(command => command.HolidayType == EnumHolidayType.OfficeSpecific, () =>
        {
            RuleFor(command => command.OfficeItemIds)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.OfficeListRequired);
        });


        // Center Specific Holiday Validation

        When(command => command.HolidayType == EnumHolidayType.CenterSpecific, () =>
        {
            RuleFor(command => command.CenterItemIds)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.CenterListRequired);
        });


        When(command => command.HolidayType == EnumHolidayType.Personal, () =>
        {
            RuleFor(command => command.MemberItemId)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.MemberItemIdRequired);
        });
    }

}
