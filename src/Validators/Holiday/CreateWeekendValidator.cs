using Domain.Commands.Holiday;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Holiday;

public class CreateWeekendValidator : AbstractValidator<CreateWeekendCommand>
{

    public CreateWeekendValidator()
    {

        RuleFor(command => command)
            .MustAsync(ValidateWeekend)
            .WithMessage(BuroErrorMessageKeys.WeekendEnumListInvalid);

    }

    private static Task<bool> ValidateWeekend(CreateWeekendCommand command, CancellationToken cancellationToken)
    {
        return Task.FromResult(command.DaysOfWeek.Distinct().Count() == command.DaysOfWeek.Count);
    }

}