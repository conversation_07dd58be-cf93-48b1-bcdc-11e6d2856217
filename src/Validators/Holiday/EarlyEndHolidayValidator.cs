using Domain.Commands.Holiday;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Holiday;

public class EarlyEndHolidayValidator : AbstractValidator<EarlyEndHolidayCommand>
{

    public EarlyEndHolidayValidator()
    {

        RuleFor(command => command.ItemId)
            .NotNull().NotEmpty()
            .WithMessage(BuroErrorMessageKeys.ItemIdRequired);
        RuleFor(command => command.HolidayEndDate)
            .NotNull().NotEmpty()
            .WithMessage(BuroErrorMessageKeys.HolidayEndDateRequired);


    }

}