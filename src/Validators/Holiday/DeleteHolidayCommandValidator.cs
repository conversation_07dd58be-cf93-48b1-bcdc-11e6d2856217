using Domain.Commands.Holiday;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Holiday;

public class DeleteHolidayCommandValidator : AbstractValidator<DeleteHolidayCommand>
{
    private readonly IRepository _repository;

    public DeleteHolidayCommandValidator(IRepository repository)
    {
        _repository = repository;

        RuleFor(command => command.ItemId)
            .NotNull().NotEmpty()
            .WithMessage(BuroErrorMessageKeys.ItemIdRequired);

        RuleFor(command => command)
            .MustAsync(ValidateDateRange)
            .WithMessage(BuroErrorMessageKeys.HolidayExpired);

    }

    private async Task<bool> ValidateDateRange(DeleteHolidayCommand command, CancellationToken cancellationToken)
    {
        var currentDate = DateTime.Now;
        var existingData =
            await _repository.GetItemAsync<BuroHoliday>(c => c.ItemId == command.ItemId);
        return IsWithinEndDate(currentDate, existingData);

    }
    private static bool IsWithinEndDate(DateTime currentDate, BuroHoliday holiday)
    {
        return currentDate <= holiday.HolidayEndDate;
    }
}