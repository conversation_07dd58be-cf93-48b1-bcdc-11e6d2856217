using Domain.Commands.Vendor;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Vendor;

public class VendorContactPersonValidator : AbstractValidator<VendorContactPerson>
{
    public VendorContactPersonValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorContactPersonNameRequired);

        RuleFor(x => x.PhoneNo)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorContactPersonPhoneRequired);
    }
}