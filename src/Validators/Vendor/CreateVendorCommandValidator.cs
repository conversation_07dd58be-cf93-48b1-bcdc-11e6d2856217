using Domain.Commands.Vendor;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Vendor;

public class CreateVendorCommandValidator : AbstractValidator<CreateVendorCommand>
{
    public CreateVendorCommandValidator()
    {
        RuleFor(x => x.VendorType)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VendorTypeRequired);
        RuleFor(x => x.VendorDomain)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VendorDomainRequired);

        RuleFor(x => x.CategoryItemId)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Inventory)
            .WithMessage(BuroErrorMessageKeys.VendorItemCategoryRequired);

        RuleFor(x => x.CategoryName)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Inventory)
            .WithMessage(BuroErrorMessageKeys.VendorItemCategoryRequired);

        RuleFor(x => x.SubCategoryItemId)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Inventory)
            .WithMessage(BuroErrorMessageKeys.VendorItemSubCategoryRequired);

        RuleFor(x => x.SubCategoryName)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Inventory)
            .WithMessage(BuroErrorMessageKeys.VendorItemSubCategoryRequired);

        RuleFor(x => x.GroupItemId)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Asset)
            .WithMessage(BuroErrorMessageKeys.VendorGroupRequired);

        RuleFor(x => x.GroupName)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Asset)
            .WithMessage(BuroErrorMessageKeys.VendorGroupRequired);

        RuleFor(x => x.SubGroupItemId)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Asset)
            .WithMessage(BuroErrorMessageKeys.VendorSubGroupRequired);

        RuleFor(x => x.SubGroupName)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Asset)
            .WithMessage(BuroErrorMessageKeys.VendorSubGroupRequired);

        RuleFor(x => x.SubCategoryItemId)
            .NotEmpty()
            .When(x => x.VendorDomain == EnumVendorDomain.Inventory)
            .WithMessage(BuroErrorMessageKeys.VendorItemSubCategoryRequired);

        RuleFor(x => x.BusinessStartDate)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.StartDateRequired);

        RuleFor(x => x.TradeLicenseNo)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorTradeLicenseRequired);

        RuleFor(x => x.VendorLegalName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorLegalNameRequired);

        RuleFor(x => x.VendorContactPersons)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorContactPersonRequired)
            .ForEach(contact => { contact.SetValidator(new VendorContactPersonValidator()); });

        RuleFor(x => x.Email).NotEmpty().WithMessage(BuroErrorMessageKeys.EmailRequired);
        RuleFor(x => x.OfficePhoneNo).NotEmpty().WithMessage(BuroErrorMessageKeys.PhoneNoRequired);

        RuleFor(x => x.DistrictItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictIdRequired);
        RuleFor(x => x.District).NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictNameRequired);

        RuleFor(x => x.UpazilaItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaIdRequired);
        RuleFor(x => x.Upazila).NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaNameRequired);

        RuleFor(x => x.PostOfficeItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeIdRequired);
        RuleFor(x => x.PostOffice).NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeNameRequired);

        RuleFor(x => x.UnionItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.UnionIdRequired);
        RuleFor(x => x.Union).NotEmpty().WithMessage(BuroErrorMessageKeys.UnionNameRequired);

        RuleFor(x => x.WardItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.WardIdRequired);
        RuleFor(x => x.Ward).NotEmpty().WithMessage(BuroErrorMessageKeys.WardNameRequired);

        RuleFor(x => x.VendorStatus)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VendorStatusRequired);
    }
}