using Domain.Commands.Vendor;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Vendor;

public class UpdateVendorCommandValidator : AbstractValidator<UpdateVendorCommand>
{
    public UpdateVendorCommandValidator()
    {
        RuleFor(x => x.ItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorIdRequired);
        RuleFor(x => x.VendorType)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VendorTypeRequired);

        RuleFor(x => x.CategoryItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorItemCategoryRequired);

        RuleFor(x => x.TradeLicenseNo)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorTradeLicenseRequired);

        RuleFor(x => x.SubCategoryItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorItemSubCategoryRequired);

        RuleFor(x => x.VendorLegalName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorLegalNameRequired);

        RuleFor(x => x.VendorContactPersons)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorContactPersonRequired)
            .ForEach(contact =>
            {
                contact.SetValidator(new VendorContactPersonValidator());
            });

        RuleFor(x => x.DistrictItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictIdRequired);
        RuleFor(x => x.District).NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictNameRequired);

        RuleFor(x => x.UpazilaItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaIdRequired);
        RuleFor(x => x.Upazila).NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaNameRequired);

        RuleFor(x => x.PostOfficeItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeIdRequired);
        RuleFor(x => x.PostOffice).NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeNameRequired);

        RuleFor(x => x.UnionItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.UnionIdRequired);
        RuleFor(x => x.Union).NotEmpty().WithMessage(BuroErrorMessageKeys.UnionNameRequired);

        RuleFor(x => x.WardItemId).NotEmpty().WithMessage(BuroErrorMessageKeys.WardIdRequired);
        RuleFor(x => x.Ward).NotEmpty().WithMessage(BuroErrorMessageKeys.WardNameRequired);

        RuleFor(x => x.VendorStatus)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VendorStatusRequired);

    }
}