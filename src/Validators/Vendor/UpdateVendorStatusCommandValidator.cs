using Domain.Commands.Vendor;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Vendor;

public class UpdateVendorStatusCommandValidator : AbstractValidator<UpdateVendorStatusCommand>
{
    public UpdateVendorStatusCommandValidator()
    {
        RuleFor(item => item.VendorItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VendorIdRequired);
        RuleFor(item => item.VendorStatus)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VendorStatusRequired);
    }
}