using Domain.Commands.Bank;
using FinMongoDbRepositories;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Validators.Bank;

public class CreateBankBranchCommandValidator : AbstractValidator<CreateBankBranchCommand>
{
    private readonly IFinMongoDbRepository _ifinMongoDbRepository;

    public CreateBankBranchCommandValidator(IFinMongoDbRepository ifinMongoDbRepository)
    {
        _ifinMongoDbRepository = ifinMongoDbRepository;

        RuleFor(command => command.BankItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Bank item id is required.");

        RuleFor(command => command.BranchName)
            .NotNull()
            .NotEmpty()
            .WithMessage("Branch name is required.");

        RuleFor(command => command.RoutingNumber)
            .NotNull()
            .NotEmpty()
            .WithMessage("Routing number is required.");

        RuleFor(command => command)
            .NotNull()
            .NotEmpty()
            .RequiredMessage(nameof(CreateBankBranchCommand.BranchName))
            .MustAsync(BeAValidNewBranchNameAsync)
            .ExistMessage(nameof(CreateBankBranchCommand.BranchName));

    }

    private async Task<bool> BeAValidNewBranchNameAsync(CreateBankBranchCommand command, CancellationToken cancellationToken)
    {
        bool exists = await _ifinMongoDbRepository.ExistsAsync<BuroBankBranch>(b => b.BankItemId == command.BankItemId
            && b.RoutingNumber == command.RoutingNumber
            && b.BranchName.Equals(command.BranchName, StringComparison.OrdinalIgnoreCase));
        return !exists;
    }
}