using Domain.Commands.Voucher;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Voucher;

public class CreateManualVoucherValidator : AbstractValidator<CreateManualVoucherCommand>
{
    public CreateManualVoucherValidator()
    {
        RuleFor(command => command.VoucherGroupItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VoucherGroupItemId);

        RuleFor(command => command.OfficeItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.OfficeItemIdRequired);

        RuleFor(command => command.Amount)
            .Must(ValidateVoucherAmount)
            .WithMessage("Voucher amount must be a positive value.");
    }

    private static bool ValidateVoucherAmount(double amount)
    {
        return amount > 0;
    }
}