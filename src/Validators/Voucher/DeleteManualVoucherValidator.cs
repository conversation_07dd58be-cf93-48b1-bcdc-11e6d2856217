using Domain.Commands.Voucher;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Voucher;

public class DeleteManualVoucherValidator : AbstractValidator<DeleteManualVoucherCommand>
{
    public DeleteManualVoucherValidator()
    {
        RuleFor(command => command.VoucherGroupItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VoucherGroupItemId);
    }
}