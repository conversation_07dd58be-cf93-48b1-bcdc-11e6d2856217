using Domain.Commands.Voucher.Configuration;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Voucher.Configuration;

public class UpdateVoucherConfigurationValidator : AbstractValidator<UpdateVoucherConfigCommand>
{

    public UpdateVoucherConfigurationValidator()
    {
        RuleFor(command => command.ItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.ItemIdRequired);

        RuleFor(command => command.VoucherCreationType)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VoucherCreationType);

        RuleFor(command => command.LedgerEntries)
            .Must(ValidateLedgerEntries)
            .WithMessage("LedgerEntries must contain at least two entries: one with VoucherType 'DEBIT' and another with VoucherType 'CREDIT'.");

    }

    private static bool ValidateLedgerEntries(List<LedgerEntry>? ledgerEntries)
    {
        if (ledgerEntries == null || ledgerEntries.Count < 2)
        {
            return false;
        }

        var hasVoucherType0 = ledgerEntries.Exists(entry => entry.VoucherType == EnumBuroVoucherType.Debit);
        var hasVoucherType1 = ledgerEntries.Exists(entry => entry.VoucherType == EnumBuroVoucherType.Credit);

        return hasVoucherType0 && hasVoucherType1;
    }

}