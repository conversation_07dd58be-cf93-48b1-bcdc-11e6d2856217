using Domain.Commands.Voucher.Configuration;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using LedgerEntry = Domain.Commands.Voucher.Configuration.LedgerEntry;

namespace Validators.Voucher.Configuration;

public class CreateVoucherConfigurationValidator : AbstractValidator<CreateVoucherConfigCommand>
{
    public CreateVoucherConfigurationValidator()
    {
        RuleFor(command => command.ItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.ItemIdRequired);

        RuleFor(command => command.TransactionTypeItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.TransactionCategoryItemId);

        RuleFor(command => command.VoucherScopeCategory)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VoucherScopeCategory);

        RuleFor(command => command.ProductLineItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.ProductLineItemIdRequired)
            .When(command => command.VoucherScopeCategory == EnumVoucherScopeCategory.Product);

        RuleFor(command => command.LedgerEntries)
            .Must(ValidateLedgerEntries)
            .WithMessage("LedgerEntries must contain at least two entries: one with VoucherType 'DEBIT' and another with VoucherType 'CREDIT'.");

    }

    private static bool ValidateLedgerEntries(List<LedgerEntry>? ledgerEntries)
    {
        if (ledgerEntries == null || ledgerEntries.Count < 2)
        {
            return false;
        }

        var hasVoucherType0 = ledgerEntries.Exists(entry => entry.VoucherType == EnumBuroVoucherType.Debit);
        var hasVoucherType1 = ledgerEntries.Exists(entry => entry.VoucherType == EnumBuroVoucherType.Credit);

        return hasVoucherType0 && hasVoucherType1;
    }

}