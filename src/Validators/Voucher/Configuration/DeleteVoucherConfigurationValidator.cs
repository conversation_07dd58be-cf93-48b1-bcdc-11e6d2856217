using Domain.Commands.Voucher.Configuration;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Voucher.Configuration;

public class DeleteVoucherConfigurationValidator : AbstractValidator<DeleteVoucherConfigCommand>
{
    public DeleteVoucherConfigurationValidator()
    {
        RuleFor(command => command.ItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.ItemIdRequired);

        RuleFor(command => command.VoucherCreationType)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.VoucherCreationType);
    }

}