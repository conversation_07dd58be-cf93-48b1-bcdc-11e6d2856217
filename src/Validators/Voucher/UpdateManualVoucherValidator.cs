using Domain.Commands.Voucher;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Voucher;

public class UpdateManualVoucherValidator : AbstractValidator<UpdateManualVoucherCommand>
{
    public UpdateManualVoucherValidator()
    {
        RuleFor(command => command.VoucherGroupItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.VoucherGroupItemId);

        RuleFor(command => command.Amount)
            .Must(ValidateVoucherAmount)
            .WithMessage("Voucher amount must be a positive value.");
    }

    private bool ValidateVoucherAmount(double amount)
    {
        return amount > 0;
    }
}