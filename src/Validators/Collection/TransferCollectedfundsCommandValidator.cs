using Domain.Commands.Collection;
using FinMongoDbRepositories;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Collection
{
    public class TransferCollectedfundsCommandValidator : AbstractValidator<TransferCollectedfundsCommand>
    {
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;

        public TransferCollectedfundsCommandValidator(
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider)
        {
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .MustAsync(BeAProgramOrganizerAsync)
                .InvalidMessage("INVALID_EMPLOYEE");

            RuleFor(command => command)
                .MustAsync(DayEndNotCompletedAsync)
                .InvalidMessage("DAYEND_COMPLETED");

            When(command => command.Status == EnumCollectionFundTransferStatus.Reverted, () =>
            {
                RuleFor(command => command.CollectionFundTransferItemId)
                    .MustAsync(BeAValidRevertFundTransferRequestAsync)
                    .InvalidMessage(nameof(TransferCollectedfundsCommand.CollectionFundTransferItemId));
            });
        }

        private async Task<bool> DayEndNotCompletedAsync(TransferCollectedfundsCommand command, CancellationToken token)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var officeItemId = (await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, string>(
                l => l.UserItemId == securityContext.UserId,
                l => l.CurrentOfficeItemId))
                .FirstOrDefault();

            var isDayEndCompleted = await _finMongoDbRepository.ExistsAsync<BuroDayEndActivityLog>(
                l => l.BranchItemId == officeItemId
                  && l.BranchClosingDate == DateTime.UtcNow.Date
                  && l.Status == EnumDayEndStatus.Completed);

            return !isDayEndCompleted;
        }

        private async Task<bool> BeAValidRevertFundTransferRequestAsync(string collectionFundTransferItemId, CancellationToken token)
        {
            var transfer = await _finMongoDbRepository.FindOneAsync<BuroCollectionFundTransfer>(t => t.ItemId == collectionFundTransferItemId);

            return transfer != null && transfer.Status == EnumCollectionFundTransferStatus.Submitted;
        }

        private async Task<bool> BeAProgramOrganizerAsync(TransferCollectedfundsCommand command, CancellationToken token)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();

            var isProgramOrganizer = await _finMongoDbRepository.ExistsAsync<BuroEmployee>(
                e => e.UserItemId == securityContext.UserId
                && e.DesignationCode == BuroDesignationConstant.ProgramOrganizerDesignationCode);

            return isProgramOrganizer;
        }
    }
}
