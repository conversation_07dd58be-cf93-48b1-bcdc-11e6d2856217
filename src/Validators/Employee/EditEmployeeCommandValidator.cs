using Domain.Commands.Employee;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using Validators.CustomProperty;

namespace Validators.Employee
{
    public class EditEmployeeCommandValidator : AbstractValidator<EditEmployeeCommand>
    {
        private readonly IRepository _repository;
        public EditEmployeeCommandValidator(IRepository repository)
        {
            _repository = repository;

            RuleFor(x => x.ItemId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.InvalidEmployeeId);


            RuleFor(command => command)
                .Must(BeAValidCustomProperty)
                .WithMessage(BuroErrorMessageKeys.InvalidCustomProperty);
        }

        private bool BeAValidCustomProperty(EditEmployeeCommand command)
        {
            if (!command.CustomProperties.Any())
            {
                return true;
            }

            var customPropertyIds = command.CustomProperties.Select(c => c.ItemId).ToList();

            var customProperty = _repository.GetItems<BuroCustomProperties>(x => customPropertyIds.Contains(x.ItemId)).ToList();

            return CommonCustomPropertyHelperValidator.IsValidCustomProperty(command.CustomProperties, customProperty);
        }
    }
}
