using Domain.Commands.Employee;
using FluentValidation;
using Infrastructure.Constants;
using Infrastructure.Extensions;

namespace Validators.Employee;

public class BaseEmployeeCommandValidator : AbstractValidator<BaseEmployeeCommand>
{

    public BaseEmployeeCommandValidator()
    {

        RuleFor(x => x.EmployeeName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.EmployeeNameRequired);

        RuleFor(x => x.FatherName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.FatherNameRequired);

        RuleFor(x => x.MotherName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.MotherNameRequired);

        RuleFor(x => x)
            .Must(x => !x.WorkPhone.IsNullOrEmptyOrWhiteSpace() || !x.PersonalPhone.IsNullOrEmptyOrWhiteSpace())
            .WithMessage(BuroErrorMessageKeys.OnePhoneRequired);

        RuleFor(x => x.DistrictId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictIdRequired);

        RuleFor(x => x.DistrictName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictNameRequired);

        RuleFor(x => x.DistrictLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictLangKeyRequired);

        RuleFor(x => x.Address)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.AddressRequired);

        RuleFor(x => x.PostCode)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostCodeRequired);

        RuleFor(x => x.PostOfficeId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeIdRequired);

        RuleFor(x => x.PostOfficeName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeNameRequired);

        RuleFor(x => x.PostOfficeLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeLangKeyRequired);

        RuleFor(x => x.UnionId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UnionIdRequired);

        RuleFor(x => x.UnionName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UnionNameRequired);

        RuleFor(x => x.UnionLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UnionLangKeyRequired);

        RuleFor(x => x.UpazilaId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaIdRequired);

        RuleFor(x => x.UpazilaName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaNameRequired);

        RuleFor(x => x.UpazilaLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaLangKeyRequired);

        RuleFor(x => x.WardId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.WardIdRequired);

        RuleFor(x => x.WardName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.WardNameRequired);

        RuleFor(x => x.WardLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.WardLangKeyRequired);

    }
}