using Domain.Commands.Employee;
using FluentValidation;
using Infrastructure.Constants;
using Infrastructure.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using Validators.CustomProperty;

namespace Validators.Employee;

public class AddEmployeeCommandValidator : AbstractValidator<AddEmployeeCommand>
{
    private readonly IRepository _repository;

    public AddEmployeeCommandValidator(IRepository repository)
    {
        _repository = repository;

        RuleFor(x => x.NidNo).MustAsync(BeANewEmployee).WithMessage(BuroErrorMessageKeys.EmployeeAlreadyExists);

        RuleFor(x => x.EmployeeName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.EmployeeNameRequired);

        RuleFor(x => x.FatherName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.FatherNameRequired);

        RuleFor(x => x.MotherName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.MotherNameRequired);

        RuleFor(x => x.NidNo)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.NidNoRequired);

        RuleFor(x => x)
            .Must(x => !x.WorkPhone.IsNullOrEmptyOrWhiteSpace() || !x.PersonalPhone.IsNullOrEmptyOrWhiteSpace())
            .WithMessage(BuroErrorMessageKeys.OnePhoneRequired);

        RuleFor(x => x.WorkPhone)
            .Matches(@"^\+").When(x => !x.WorkPhone.IsNullOrEmptyOrWhiteSpace())
            .WithMessage(BuroErrorMessageKeys.PhoneNumberWithCodeRequired);

        RuleFor(x => x.PersonalPhone)
            .Matches(@"^\+").When(x => !x.PersonalPhone.IsNullOrEmptyOrWhiteSpace())
            .WithMessage(BuroErrorMessageKeys.PhoneNumberWithCodeRequired);

        RuleFor(x => x.DesignationId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.DesignationIdRequired);

        RuleFor(x => x.DistrictId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictIdRequired);

        RuleFor(x => x.DistrictName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictNameRequired);

        RuleFor(x => x.DistrictLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.DistrictLangKeyRequired);

        RuleFor(x => x.Address)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.AddressRequired);

        RuleFor(x => x.PostCode)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostCodeRequired);

        RuleFor(x => x.PostOfficeId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeIdRequired);

        RuleFor(x => x.PostOfficeName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeNameRequired);

        RuleFor(x => x.PostOfficeLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.PostOfficeLangKeyRequired);

        RuleFor(x => x.UnionId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UnionIdRequired);

        RuleFor(x => x.UnionName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UnionNameRequired);

        RuleFor(x => x.UnionLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UnionLangKeyRequired);

        RuleFor(x => x.UpazilaId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaIdRequired);

        RuleFor(x => x.UpazilaName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaNameRequired);

        RuleFor(x => x.UpazilaLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.UpazilaLangKeyRequired);

        RuleFor(x => x.WardId)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.WardIdRequired);

        RuleFor(x => x.WardName)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.WardNameRequired);

        RuleFor(x => x.WardLangKey)
            .NotNull().NotEmpty().WithMessage(BuroErrorMessageKeys.WardLangKeyRequired);

        RuleFor(command => command)
           .Must(BeAValidCustomPropertyForInsert)
           .WithMessage(BuroErrorMessageKeys.InvalidCustomProperty);
    }

    private async Task<bool> BeANewEmployee(string employeeNid, CancellationToken token = default)
    {
        var isExistingEmployee = await _repository.ExistsAsync<BuroEmployee>(x => x.EmployeeNid == employeeNid);
        return !isExistingEmployee;
    }

    private bool BeAValidCustomPropertyForInsert(AddEmployeeCommand command)
    {
        if (!command.CustomProperties.Any())
        {
            return true;
        }

        var customPropertyIds = command.CustomProperties.Select(c => c.ItemId).ToList();

        // Retrieve existing custom properties by ItemId for validation
        var customProperty = _repository.GetItems<BuroCustomProperties>(x => customPropertyIds.Contains(x.ItemId)).ToList();

        return CommonCustomPropertyHelperValidator.IsValidCustomProperty(command.CustomProperties, customProperty);
    }
}