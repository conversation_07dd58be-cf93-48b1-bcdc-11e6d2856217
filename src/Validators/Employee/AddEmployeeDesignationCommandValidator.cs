using Domain.Commands.Employee;
using FluentValidation;

namespace Validators.Employee
{
    public class AddEmployeeDesignationCommandValidator : AbstractValidator<AddEmployeeDesignationCommand>
    {
        public AddEmployeeDesignationCommandValidator()
        {
            CascadeMode = CascadeMode.StopOnFirstFailure;

            RuleFor(item => item.EffectiveDate)
                .NotNull().NotEmpty();

            RuleFor(item => item.FileIds)
                .NotNull().NotEmpty();

            RuleFor(item => item.EmployeeItemId)
                .NotNull().NotEmpty();

            //RuleFor(item => item.PreviousDesignationItemId)
            //    .NotNull().NotEmpty();

            RuleFor(item => item.NewDesignationItemId)
                .NotNull().NotEmpty();
        }
    }
}
