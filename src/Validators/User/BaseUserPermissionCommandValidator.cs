using Domain.Commands.User;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.User;

public class BaseUserPermissionCommandValidator<T> : AbstractValidator<T> where T : BaseUserPermissionCommand
{
    private readonly IRepository _repository;

    protected BaseUserPermissionCommandValidator(IRepository repository)
    {
        _repository = repository;

        RuleFor(x => x.UserItemId)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.UserIdRequired);

        RuleFor(command => command)
            .MustAsync(BeAnExistingUser)
            .WithMessage(BuroErrorMessageKeys.UserDoesntExists);

        RuleFor(x => x.Features)
            .NotNull()
            .NotEmpty().WithMessage(BuroErrorMessageKeys.FeaturesCantBeEmpty)
            .ForEach(feature => feature.SetValidator(new FeatureValidator()));
    }

    private async Task<bool> BeAnExistingUser(T parameter, CancellationToken token)
    {
        var isExistingUser = await _repository.ExistsAsync<Selise.Ecap.Entities.PrimaryEntities.Security.User>
            (x => x.ItemId == parameter.UserItemId);
        return isExistingUser;
    }
}