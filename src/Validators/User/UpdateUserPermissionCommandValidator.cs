using Domain.Commands.User;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.User;

public class UpdateUserPermissionCommandValidator : BaseUserPermissionCommandValidator<UpdateUserPermissionCommand>
{
    private readonly IRepository _repository;

    public UpdateUserPermissionCommandValidator(IRepository repository) : base(repository)
    {
        _repository = repository;
        RuleFor(x => x.UserPermissionItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.UserPermissionIdRequired);

        RuleFor(x => x)
            .MustAsync(BeAnExistingUserPermission)
            .WithMessage(BuroErrorMessageKeys.UserPermissionDoesntExist);
    }

    private async Task<bool> BeAnExistingUserPermission(UpdateUserPermissionCommand command, CancellationToken token)
    {
        var isExistingUserPermission = await _repository.ExistsAsync<BuroUserPermission>
            (x => x.ItemId == command.UserPermissionItemId);
        return isExistingUserPermission;
    }
}