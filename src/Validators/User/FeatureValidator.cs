using Domain.Commands.User;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.User;

public class FeatureValidator : AbstractValidator<FeatureCommand>
{
    public FeatureValidator()
    {
        RuleFor(x => x.FeatureItemId)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.FeatureIdRequired);

        RuleFor(x => x.FeatureName)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.FeatureNameRequired);

        RuleFor(x => x.FeaturePath)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.FeaturePathRequired);

        RuleFor(x => x.ApiPath)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.ApiPathRequired);
    }
}