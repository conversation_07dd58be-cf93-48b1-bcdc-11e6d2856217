using Domain.Commands.AllowedSector;
using FluentValidation;
using Infrastructure.Constants;


namespace Validators.Allowedsector;

public class UpdateAllowedSectorStatusCommandValidator : AbstractValidator<UpdateAllowedSectorStatusCommand>
{
    public UpdateAllowedSectorStatusCommandValidator()
    {
        RuleFor(item => item.SectorItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.SectorIdRequired);

        RuleFor(x => x.IsActive)
            .NotNull()
            .WithMessage(BuroErrorMessageKeys.SectorStatusRequired);
    }

}