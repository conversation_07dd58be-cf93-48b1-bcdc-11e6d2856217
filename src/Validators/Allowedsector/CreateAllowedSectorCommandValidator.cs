using Domain.Commands.AllowedSector;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Allowedsector;

public class CreateAllowedSectorCommandValidator : AbstractValidator<CreateAllowedSectorCommand>
{
    public CreateAllowedSectorCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .NotNull()
            .WithMessage(BuroErrorMessageKeys.NameRequired);
        RuleFor(x => x.CategoryType)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.SectorTypeRequired);
        RuleFor(x => x.IsActive)
           .NotNull()
           .WithMessage(BuroErrorMessageKeys.SectorStatusRequired);

        When(command => command.CategoryType == AllowedSectorType.Sector, () =>
        {
            RuleFor(command => command.ParentItemId)
                .NotEmpty()
                .NotNull()
                .WithMessage(BuroErrorMessageKeys.ParentIdRequired);
            RuleFor(command => command.BuroSectorCode)
               .NotEmpty()
               .NotNull()
               .WithMessage(BuroErrorMessageKeys.SectorCodeRequired);
            RuleFor(command => command.MFCIBCode)
              .NotEmpty()
              .NotNull()
              .WithMessage(BuroErrorMessageKeys.MFCIBCodeCodeRequired);
        });

    }
}