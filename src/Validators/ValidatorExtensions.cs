using FluentValidation;
using Infrastructure.Constants;
using Infrastructure.Extensions;

namespace Validators
{
    public static class ValidatorExtensions
    {
        public static IRuleBuilderOptions<T, TProperty> InvalidMessage<T, TProperty>(
            this IRuleBuilderOptions<T, TProperty> rule,
            string propertyName = "")
        {
            return rule.WithMessage(GenerateMessage(propertyName, BuroErrorMessageKeys.InvalidProperty));
        }

        public static IRuleBuilderOptions<T, TProperty> RequiredMessage<T, TProperty>(
            this IRuleBuilderOptions<T, TProperty> rule,
            string propertyName = "")
        {
            return rule.WithMessage(GenerateMessage(propertyName, BuroErrorMessageKeys.RequiredProperty));
        }

        public static IRuleBuilderOptions<T, TProperty> ExistMessage<T, TProperty>(
            this IRuleBuilderOptions<T, TProperty> rule,
            string propertyName = "")
        {
            return rule.WithMessage(GenerateMessage(propertyName, BuroErrorMessageKeys.AlreadyExist));
        }

        public static IRuleBuilderOptions<T, TProperty> DoesNotExistMessage<T, TProperty>(
            this IRuleBuilderOptions<T, TProperty> rule,
            string propertyName = "")
        {
            return rule.WithMessage(GenerateMessage(propertyName, BuroErrorMessageKeys.DoesNotExist));
        }

        private static string GenerateMessage(string propertyName, string messageTemplate)
        {
            return propertyName.IsNullOrEmptyOrWhiteSpace()
                ? BuroErrorMessageKeys.RequestBodyInvalid
                : string.Format(messageTemplate, propertyName.ToUpper());
        }
    }
}
