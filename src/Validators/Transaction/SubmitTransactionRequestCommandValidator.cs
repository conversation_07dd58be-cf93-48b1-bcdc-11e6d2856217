using Domain.Commands.Transaction;
using FinMongoDbRepositories;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Transaction
{
    public class SubmitTransactionRequestCommandValidator : AbstractValidator<SubmitTransactionRequestCommand>
    {
        private readonly IFinMongoDbRepository _finMongoDbRepository;

        public SubmitTransactionRequestCommandValidator(
            IFinMongoDbRepository finMongoDbRepository)
        {
            _finMongoDbRepository = finMongoDbRepository;

            RuleLevelCascadeMode = CascadeMode.Stop;
            ClassLevelCascadeMode = CascadeMode.Stop;

            RuleFor(command => command)
                .NotNull()
                .InvalidMessage();

            RuleFor(command => command.MemberItemId)
                .NotNull()
                .NotEmpty()
                .RequiredMessage(nameof(SubmitTransactionRequestCommand.MemberItemId));

            RuleFor(command => command)
                .Must(command => command.Amount > 0)
                .InvalidMessage(nameof(SubmitTransactionRequestCommand.Amount));

            When(command => IsWithdrawalTransactionType(command.TransactionType), () =>
            {
                RuleFor(command => command)
                    .MustAsync(IsValidWithdrawalAmountAsync)
                    .WithMessage(BuroErrorMessageKeys.InsufficientBalance);
            });

            RuleFor(command => command.AccountItemId)
                .NotNull()
                .NotEmpty()
                .InvalidMessage(nameof(SubmitTransactionRequestCommand.AccountItemId));

            RuleFor(command => command.TransactionMedium)
                .IsInEnum()
                .InvalidMessage(nameof(SubmitTransactionRequestCommand.TransactionMedium));

            RuleFor(command => command.ProductType)
                .IsInEnum()
                .Must(BeAValidProductType)
                .InvalidMessage(nameof(SubmitTransactionRequestCommand.ProductType));
        }

        private static bool IsWithdrawalTransactionType(EnumBuroTransactionType type)
        {
            return type == EnumBuroTransactionType.GsWithdrawal ||
                   type == EnumBuroTransactionType.CsWithdrawalMaturity ||
                   type == EnumBuroTransactionType.CsEarlyWithdrawal;
        }

        private static bool BeAValidProductType(EnumProductType type)
        {
            return type == EnumProductType.GeneralSaving ||
                   type == EnumProductType.ContractualSaving;
        }

        private async Task<bool> IsValidWithdrawalAmountAsync(SubmitTransactionRequestCommand command, CancellationToken token)
        {
            var hasAvailableBalance = command.ProductType switch
            {
                EnumProductType.GeneralSaving => await HasSufficientBalanceAsync<BuroGeneralSavingsAccount>(command.AccountItemId, command.Amount),
                EnumProductType.ContractualSaving => await HasSufficientBalanceAsync<BuroContractualSavingsAccount>(command.AccountItemId, command.Amount),
                _ => false
            };

            return hasAvailableBalance;
        }

        private async Task<bool> HasSufficientBalanceAsync<T>(string accountItemId, double amount)
            where T : BuroProductAccountBase
        {
            return await _finMongoDbRepository.ExistsAsync<T>(a => a.ItemId == accountItemId && a.Balance >= amount);
        }
    }
}
