using Domain.Commands.Asset;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Asset;

public class CreateAssetItemCommandValidator : AbstractValidator<CreateAssetItemCommand>
{
    public CreateAssetItemCommandValidator()
    {
        RuleFor(item => item.GroupItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.GroupItemIdRequired);
        RuleFor(item => item.GroupName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.AssetGroupNameRequired);
        RuleFor(item => item.SubGroupItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.SubGroupItemIdRequired);
        RuleFor(item => item.SubGroupName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.SubGroupNameRequired); ;
        RuleFor(item => item.AssetName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.AssetNameRequired);
    }
}