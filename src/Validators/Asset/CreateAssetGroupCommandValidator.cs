using Domain.Commands.Asset;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Asset;

public class CreateAssetGroupCommandValidator : AbstractValidator<CreateAssetGroupCommand>
{
    public CreateAssetGroupCommandValidator()
    {
        RuleFor(item => item.GroupCode)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.AssetGroupCodeRequired);

        RuleFor(item => item.GroupName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.AssetGroupNameRequired);
    }
}