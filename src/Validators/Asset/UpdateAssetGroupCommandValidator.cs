using Domain.Commands.Asset;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Asset;

public class UpdateAssetGroupCommandValidator : AbstractValidator<UpdateAssetGroupCommand>
{
    public UpdateAssetGroupCommandValidator()
    {
        RuleFor(item => item.GroupItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.GroupItemIdRequired);

        RuleFor(item => item.GroupCode)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.AssetGroupCodeRequired);

        RuleFor(item => item.GroupName)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.AssetGroupNameRequired);
    }
}