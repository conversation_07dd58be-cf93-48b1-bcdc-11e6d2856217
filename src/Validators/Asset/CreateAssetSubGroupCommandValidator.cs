using Domain.Commands.Asset;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Asset;

public class CreateAssetSubGroupCommandValidator : AbstractValidator<CreateAssetSubGroupCommand>
{
    public CreateAssetSubGroupCommandValidator()
    {
        RuleFor(x => x.GroupItemId)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.GroupItemIdRequired);

        RuleFor(x => x.SubGroupName)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.SubGroupNameRequired);

        RuleFor(x => x.SubGroupCode)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.SubGroupCodeRequired);

        RuleFor(x => x.DepreciationRate)
            .GreaterThanOrEqualTo(0).WithMessage(BuroErrorMessageKeys.DepreciationRateNegative)
            .LessThanOrEqualTo(100).WithMessage(BuroErrorMessageKeys.DepreciationRateExceedsLimit);

        RuleFor(x => x.PartialSale)
            .IsInEnum()
            .WithMessage(BuroErrorMessageKeys.PartialSaleInvalidEnumValue);
    }
}