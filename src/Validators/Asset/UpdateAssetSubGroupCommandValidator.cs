using Domain.Commands.Asset;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Asset;

public class UpdateAssetSubGroupCommandValidator : AbstractValidator<UpdateAssetSubGroupCommand>
{
    public UpdateAssetSubGroupCommandValidator()
    {
        RuleFor(item => item.SubGroupItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.SubGroupItemIdRequired);

        Include(new CreateAssetSubGroupCommandValidator());
    }
}