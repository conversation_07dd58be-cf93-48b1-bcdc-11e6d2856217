using Domain.Commands.Authentication;
using FluentValidation;
using Infrastructure.Constants;
using Infrastructure.Contracts;

namespace Validators.Authentication;

public class
    VerifyEmployeePinForForgetPasswordCommandValidator : AbstractValidator<VerifyEmployeePinForForgetPasswordCommand>
{
    private readonly IGoogleCaptchaVerificationService _captchaVerificationService;

    public VerifyEmployeePinForForgetPasswordCommandValidator(
        IGoogleCaptchaVerificationService captchaVerificationService)
    {
        _captchaVerificationService = captchaVerificationService;

        RuleFor(c => c.EmployeePin)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.EmployeePinRequired);

        RuleFor(x => x)
            .MustAsync(ValidateCaptcha)
            .WithMessage(BuroErrorMessageKeys.CaptchaRequired);
    }

    private async Task<bool> ValidateCaptcha(VerifyEmployeePinForForgetPasswordCommand command, CancellationToken arg)
    {
        return await _captchaVerificationService.IsCaptchaValid(command.CaptchaToken);
    }
}