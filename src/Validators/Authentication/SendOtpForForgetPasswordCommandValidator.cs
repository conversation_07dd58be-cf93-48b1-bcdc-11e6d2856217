using Domain.Commands.Authentication;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.Authentication;

public class SendOtpForForgetPasswordCommandValidator : AbstractValidator<SendOtpForForgetPasswordCommand>
{
    public SendOtpForForgetPasswordCommandValidator()
    {
        RuleFor(c => c.OtpChannel)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.OtpChannelRequired);
    }
}