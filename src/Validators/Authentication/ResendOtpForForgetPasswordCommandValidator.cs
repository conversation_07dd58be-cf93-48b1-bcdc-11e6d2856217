using Domain.Commands.Authentication;
using FluentValidation;
using Infrastructure.Constants;
using Infrastructure.Contracts;

namespace Validators.Authentication;

public class ResendOtpForForgetPasswordCommandValidator : AbstractValidator<ResendOtpForForgetPasswordCommand>
{
    private readonly IGoogleCaptchaVerificationService _captchaVerificationService;

    public ResendOtpForForgetPasswordCommandValidator(IGoogleCaptchaVerificationService captchaVerificationService)
    {
        _captchaVerificationService = captchaVerificationService;
        RuleFor(c => c.OtpChannel)
            .NotNull()
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.OtpChannelRequired);
        RuleFor(x => x)
            .MustAsync(ValidateCaptcha)
            .WithMessage(BuroErrorMessageKeys.CaptchaRequired);
    }

    private async Task<bool> ValidateCaptcha(ResendOtpForForgetPasswordCommand command, CancellationToken arg)
    {
        return await _captchaVerificationService.IsCaptchaValid(command.CaptchaToken);
    }
}