using Domain.Commands.Authentication;
using FluentValidation;

namespace Validators.Authentication;

public class ResetPasswordCommandValidator : AbstractValidator<ResetPasswordCommand>
{
    public ResetPasswordCommandValidator()
    {
        RuleFor(x => x.NewPassword)
            .NotEmpty()
            .MinimumLength(8)
            .MaximumLength(14)
            .Matches("[A-Z]").WithMessage("Must contain at least 1 uppercase letter")
            .Matches("[a-z]").WithMessage("Must contain at least 1 lowercase letter")
            .Matches("[~!@#$%^&.]").WithMessage("Must contain at least 1 special character from ~!@#$%^&.");
    }
}