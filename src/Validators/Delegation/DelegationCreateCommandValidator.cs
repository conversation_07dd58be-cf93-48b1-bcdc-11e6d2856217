using Domain.Commands.Delegation;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using Validators.User;

namespace Validators.Delegation;

public class DelegationCreateCommandValidator : AbstractValidator<DelegationCreateCommand>
{
    private readonly IRepository _repository;

    public DelegationCreateCommandValidator(IRepository repository)
    {
        _repository = repository;

        RuleFor(command => command)
            .MustAsync((cmd, token) => EntityExists(cmd.SourceEmployeeItemId))
            .WithMessage(BuroErrorMessageKeys.SourceDoesntExists);

        RuleFor(command => command)
            .MustAsync((cmd, token) => EntityExists(cmd.DelegateeEmployeeItemId))
            .WithMessage(BuroErrorMessageKeys.DelegateeDoesntExists);

        RuleFor(command => command)
            .MustAsync(async (cmd, token) =>
                !await DelegationExistsWithOverlap(cmd.SourceEmployeeItemId, cmd.DelegateeEmployeeItemId, cmd.StartDate,
                    cmd.EndDate))
            .WithMessage(BuroErrorMessageKeys.OverlappingDelegationExists);

        RuleFor(command => command.StartDate)
            .NotEmpty().WithMessage(BuroErrorMessageKeys.StartDateRequired);

        When(item => !item.IsLifeTimeDelegation, () =>
        {
            RuleFor(command => command.EndDate)
                .NotEmpty().WithMessage(BuroErrorMessageKeys.EndDateRequired)
                .GreaterThanOrEqualTo(command => command.StartDate)
                .WithMessage(BuroErrorMessageKeys.InvalidDate);

        });

        RuleFor(x => x.FeatureList)
            .NotNull()
            .NotEmpty().WithMessage(BuroErrorMessageKeys.FeaturesCantBeEmpty)
            .ForEach(feature => feature.SetValidator(new FeatureValidator()));
    }

    private async Task<bool> EntityExists(string employeeItemId)
    {
        return await _repository.ExistsAsync<BuroEmployee>(employee => employee.ItemId == employeeItemId);
    }

    private async Task<bool> DelegationExistsWithOverlap(string sourceEmployeeItemId, string delegateeEmployeeItemId,
        DateTime startDate, DateTime endDate)
    {
        return await _repository.ExistsAsync<BuroDelegationRequest>(delegation =>
            delegation.SourceEmployeeItemId == sourceEmployeeItemId &&
            delegation.DelegateeEmployeeItemId == delegateeEmployeeItemId &&
            (delegation.DelegationStatus == EnumDelegationStatus.Pending ||
             delegation.DelegationStatus == EnumDelegationStatus.Delegated) &&
            (delegation.IsLifeTimeDelegation ||
             (delegation.StartDate <= endDate && delegation.EndDate >= startDate) ||
             (delegation.StartDate <= startDate && delegation.EndDate >= startDate) ||
             (delegation.StartDate <= endDate && delegation.EndDate >= endDate)));
    }
}