using Domain.Commands.Delegation;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Delegation;

public class DelegationEndDateUpdateCommandValidator : AbstractValidator<DelegationEndDateUpdateCommand>
{
    private readonly IRepository _repository;

    public DelegationEndDateUpdateCommandValidator(IRepository repository)
    {
        _repository = repository;
        RuleFor(item => item.DelegationItemId)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.DelegationIdRequired);

        RuleFor(command => command)
            .MustAsync((cmd, token) => EntityExists(cmd.DelegationItemId))
            .WithMessage(BuroErrorMessageKeys.InvalidDelegation);

        RuleFor(item => item)
            .Must(item => item.EndEarly || item.EndDate.HasValue)
            .WithMessage(BuroErrorMessageKeys.InvalidData);
    }

    private async Task<bool> EntityExists(string delegationId)
    {
        return await _repository.ExistsAsync<BuroDelegationRequest>(item => item.ItemId == delegationId
                                                                            && (item.DelegationStatus ==
                                                                                EnumDelegationStatus.Pending ||
                                                                                item.DelegationStatus ==
                                                                                EnumDelegationStatus.Delegated));
    }
}