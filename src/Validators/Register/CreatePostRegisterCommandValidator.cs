using Domain.Commands.Register;
using FluentValidation;

namespace Validators.Register;

public class CreatePostRegisterCommandValidator : AbstractValidator<CreatePostRegisterCommand>
{
    public CreatePostRegisterCommandValidator()
    {
        RuleFor(command => command.OfficeItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Office ItemId is required");

        RuleFor(command => command.PostText)
            .NotNull()
            .NotEmpty()
            .WithMessage("Post Text is required");
        
        RuleFor(command => command.OfficeType)
            .IsInEnum()
            .NotEmpty()
            .WithMessage("Office Type is required");
        
    }
}