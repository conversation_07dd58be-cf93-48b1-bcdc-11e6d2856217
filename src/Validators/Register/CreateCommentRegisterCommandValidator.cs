using Domain.Commands.Register;
using FluentValidation;

namespace Validators.Register;

public class CreateCommentRegisterCommandValidator : AbstractValidator<CreateCommentRegisterCommand>
{
    public CreateCommentRegisterCommandValidator()
    {
        RuleFor(command => command.PostItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Post ItemId is required");

        RuleFor(command => command.CommentText)
            .NotNull()
            .NotEmpty()
            .WithMessage("Comment Text is required");
    }
}