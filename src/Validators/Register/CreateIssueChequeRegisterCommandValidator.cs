using Domain.Commands.Register;
using FluentValidation;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Validators.Register;

public class CreateIssueChequeRegisterCommandValidator : AbstractValidator<CreateIssueChequeRegisterCommand>
{
    public CreateIssueChequeRegisterCommandValidator()
    {
        RuleFor(command => command.ItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("ItemId is required");

        RuleFor(command => command.ChequeBookItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Cheque Book ItemId is required");
        RuleFor(command => command.ChequeLeafItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Cheque Leaf ItemId is required");

        RuleFor(command => command.PayeeName)
            .NotNull()
            .NotEmpty()
            .WithMessage("Payee Name is required");

        RuleFor(command => command.Amount)
            .NotNull()
            .NotEmpty()
            .WithMessage("Amount is required");
        RuleFor(command => command.Amount)
            .GreaterThan(0)
            .WithMessage("Amount must be greater than 0");

        RuleFor(command => command.TransactionTypeItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Transaction Type ItemId is required");

        RuleFor(command => command.InstrumentType)
            .NotNull()
            .NotEmpty()
            .WithMessage("Instrument Type is required");
        RuleFor(command => command.InstrumentType)
            .IsInEnum()
            .WithMessage("Instrument Type is not valid");

        RuleFor(command => command.ChequeIssueType)
            .NotNull()
            .NotEmpty()
            .WithMessage("Cheque Issue Type is required");

        RuleFor(command => command.ChequeIssueType)
            .IsInEnum()
            .WithMessage("Cheque Issue Type is not valid");

        RuleFor(command => command.OfficeItemId)
            .NotNull()
            .NotEmpty()
            .When(command => command.ChequeIssueType == EnumBuroChequeIssueType.Office)
            .WithMessage("Office ItemId is required when Cheque Issue Type is Office");

    }
}