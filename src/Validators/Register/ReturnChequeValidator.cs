using Domain.Commands.Register;
using FluentValidation;

namespace Validators.Register;

public class ReturnChequeValidator : AbstractValidator<ReturnChequeCommand>
{
    public ReturnChequeValidator()
    {
        RuleFor(command => command.ChequeRegisterItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Cheque Register ItemId is required");
        RuleFor(command => command.ReturnAckStorageItemId)
            .NotNull()
            .NotEmpty()
            .WithMessage("Return Acknowledgement Storage ItemId is required");
    }

}