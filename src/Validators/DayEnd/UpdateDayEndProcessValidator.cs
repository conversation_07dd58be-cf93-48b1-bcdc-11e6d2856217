using Domain.Commands.DayEnd;
using FluentValidation;
using Infrastructure.Constants;

namespace Validators.DayEnd;

public class UpdateDayEndProcessValidator : AbstractValidator<UpdateDayEndProcessCommand>
{
    public UpdateDayEndProcessValidator()
    {
        RuleFor(command => command.BranchItemId).NotNull().NotEmpty()
            .WithMessage(BuroErrorMessageKeys.ItemIdRequired);

        RuleFor(command => command.BranchOpeningDate).NotNull().NotEmpty().Must(BeAValidDate)
            .WithMessage(BuroErrorMessageKeys.InvalidDate);
    }

    private static bool BeAValidDate(DateTime date)
    {
        return date > DateTime.MinValue;
    }

}