using Domain.Commands.Office;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using Validators.CustomProperty;
using Validators.Location;

namespace Validators.Office;

public class CreateOfficeCommandValidator : AbstractValidator<CreateOfficeCommand>
{
    private readonly IRepository _repository;

    public CreateOfficeCommandValidator(IRepository repository)
    {
        _repository = repository;

        RuleFor(command => command)
            .MustAsync(ValidateBranchSurvey)
            .WithMessage(BuroErrorMessageKeys.InvalidSurvey);

        RuleFor(command => command.OfficeName)
            .NotNull().NotEmpty();

        RuleFor(command => command.AuthorizationLetterFileIds)
            .NotNull().NotEmpty();

        RuleFor(command => command)
            .MustAsync(ValidateParentOffice)
            .WithMessage(BuroErrorMessageKeys.InvalidParentOffice);

        RuleFor(command => command)
            .MustAsync(BeAValidDistrict)
            .WithMessage(BuroErrorMessageKeys.InvalidDistrict);

        RuleFor(command => command)
            .MustAsync(BeAValidUpazilla)
            .WithMessage(BuroErrorMessageKeys.InvalidUpazila);

        RuleFor(command => command)
            .MustAsync(BeAValidUnion)
            .WithMessage(BuroErrorMessageKeys.InvalidUnion);

        RuleFor(command => command)
            .MustAsync(BeAValidPostOffice)
            .WithMessage(BuroErrorMessageKeys.InvalidPostOffice);

        RuleFor(command => command)
            .MustAsync(BeAValidWard)
            .WithMessage(BuroErrorMessageKeys.InvalidWard);

        RuleFor(command => command)
            .Must(BeAValidCustomPropertyForInsert)
            .WithMessage(BuroErrorMessageKeys.InvalidCustomProperty);

        RuleFor(command => new CoordinatesWrapper
        {
            Latitude = command.Latitude,
            Longitude = command.Longitude
        })
            .SetValidator(new CoordinatesValidator());
    }

    private async Task<bool> ValidateBranchSurvey(CreateOfficeCommand command, CancellationToken cancellationToken)
    {
        if (command.OfficeType != EnumOfficeType.BranchOffice)
        {
            return true;
        }

        if (string.IsNullOrWhiteSpace(command.SurveyItemId))
        {
            return false;
        }

        var survey = await _repository.GetItemAsync<BuroOfficeSurvey>(x => x.ItemId == command.SurveyItemId);

        return survey != null &&
               (survey.Status == EnumOfficeSurveyStatus.ManagementReview ||
                survey.Status == EnumOfficeSurveyStatus.Passed);
    }

    private async Task<bool> ValidateParentOffice(CreateOfficeCommand command, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(command.ParentOfficeItemId))
        {
            return false;
        }

        var parentOffice = await _repository.GetItemAsync<BuroOffice>(x => x.ItemId == command.ParentOfficeItemId);

        if (parentOffice == null)
        {
            return false;
        }

        return command.OfficeType switch
        {
            EnumOfficeType.BranchOffice => parentOffice.OfficeType == EnumOfficeType.AreaOffice,
            EnumOfficeType.AreaOffice => parentOffice.OfficeType == EnumOfficeType.ZoneOffice,
            EnumOfficeType.ZoneOffice => parentOffice.OfficeType == EnumOfficeType.DivisionOffice,
            EnumOfficeType.DivisionOffice => parentOffice.OfficeType == EnumOfficeType.HeadOffice,
            _ => false
        };
    }


    private async Task<bool> BeAValidDistrict(CreateOfficeCommand command, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(command.DistrictId)) return false;

        var district = await _repository.GetItemAsync<BuroDistrict>(
            x => x.DistrictId == command.DistrictId
                 && x.DistrictName == command.DistrictName
                 && x.DivisionId == command.DivisionId);

        return district != null;
    }

    private async Task<bool> BeAValidUpazilla(CreateOfficeCommand command, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(command.UpazilaId)) return false;

        var upazilla = await _repository.GetItemAsync<BuroUpazila>(
            x => x.UpazilaId == command.UpazilaId
                 && x.UpazilaName == command.UpazilaName
                 && x.DivisionId == command.DivisionId
                 && x.DistrictId == command.DistrictId);

        return upazilla != null;
    }

    private async Task<bool> BeAValidUnion(CreateOfficeCommand command, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(command.UnionId)) return false;

        var union = await _repository.GetItemAsync<BuroUnion>(
            x => x.UnionId == command.UnionId
                 && x.UnionName == command.UnionName
                 && x.DivisionId == command.DivisionId
                 && x.DistrictId == command.DistrictId
                 && x.UpazilaId == command.UpazilaId);

        return union != null;
    }

    private async Task<bool> BeAValidPostOffice(CreateOfficeCommand command, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(command.PostOfficeId)) return false;

        var postOffice = await _repository.GetItemAsync<BuroPostOffice>(
            x => x.PostOfficeId == command.PostOfficeId
                 && x.PostOfficeCode == command.PostOfficeCode
                 && x.PostOfficeName == command.PostOfficeName
                 && x.DivisionId == command.DivisionId
                 && x.DistrictId == command.DistrictId
                 && x.UpazilaId == command.UpazilaId);

        return postOffice != null;
    }

    private async Task<bool> BeAValidWard(CreateOfficeCommand command, CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(command.WardId)) return false;

        var ward = await _repository.GetItemAsync<BuroWard>(
            x => x.WardId == command.WardId
                 && x.WardName == command.WardName
                 && x.DivisionId == command.DivisionId
                 && x.DistrictId == command.DistrictId
                 && x.UpazilaId == command.UpazilaId
                 && x.UnionId == command.UnionId);

        return ward != null;
    }

    private bool BeAValidCustomPropertyForInsert(CreateOfficeCommand command)
    {
        if (!command.CustomProperties.Any())
        {
            return false;
        }

        var customPropertyIds = command.CustomProperties.Select(c => c.ItemId).ToList();

        // Retrieve existing custom properties by ItemId for validation
        var customProperty = _repository.GetItems<BuroCustomProperties>(x => customPropertyIds.Contains(x.ItemId)).ToList();

        return CommonCustomPropertyHelperValidator.IsValidCustomProperty(command.CustomProperties, customProperty);
    }



}