using Domain.Commands.Office;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using Validators.CustomProperty;

namespace Validators.Office
{
    public class UpdateOfficeCommandValidator : AbstractValidator<UpdateOfficeCommand>
    {
        private readonly IRepository _repository;

        public UpdateOfficeCommandValidator(IRepository repository)
        {
            _repository = repository;
            CascadeMode = CascadeMode.StopOnFirstFailure;

            RuleFor(command => command)
                .MustAsync(BeAValidDistrict)
                .WithMessage(BuroErrorMessageKeys.InvalidDistrict);

            RuleFor(command => command)
                .MustAsync(BeAValidUpazila)
                .WithMessage("Not a valid upazila");

            RuleFor(command => command)
                .MustAsync(BeAValidUnion)
                .WithMessage(BuroErrorMessageKeys.InvalidUnion);

            RuleFor(command => command)
                .MustAsync(BeAValidPostOffice)
                .WithMessage(BuroErrorMessageKeys.InvalidPostOffice);

            RuleFor(command => command)
                .MustAsync(BeAValidWard)
                .WithMessage(BuroErrorMessageKeys.InvalidWard);

            RuleFor(command => command)
                .Must(BeAValidCustomProperty)
                .WithMessage(BuroErrorMessageKeys.InvalidCustomProperty);
        }

        private bool BeAValidCustomProperty(UpdateOfficeCommand command)
        {
            if (!command.CustomProperties.Any())
            {
                return false;
            }

            var customPropertyIds = command.CustomProperties.Select(c => c.ItemId).ToList();

            var customProperty = _repository.GetItems<BuroCustomProperties>(
                x => customPropertyIds.Contains(x.ItemId)).ToList();

            return CommonCustomPropertyHelperValidator.IsValidCustomProperty(command.CustomProperties, customProperty);
        }

        private async Task<bool> BeAValidDistrict(UpdateOfficeCommand command, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(command.DistrictId)) return true;

            var district = await _repository.GetItemAsync<BuroDistrict>(
                x => x.DistrictId == command.DistrictId
                     && x.DistrictName == command.DistrictName);

            return district != null;
        }

        private async Task<bool> BeAValidUpazila(UpdateOfficeCommand command, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(command.UpazilaId)) return true;

            var upazilla = await _repository.GetItemAsync<BuroUpazila>(
                x => x.UpazilaId == command.UpazilaId
                     && x.UpazilaName == command.UpazilaName
                     && x.DistrictId == command.DistrictId);

            return upazilla != null;
        }

        private async Task<bool> BeAValidUnion(UpdateOfficeCommand command, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(command.UnionId)) return true;

            var union = await _repository.GetItemAsync<BuroUnion>(
                x => x.UnionId == command.UnionId
                     && x.UnionName == command.UnionName
                     && x.DistrictId == command.DistrictId
                     && x.UpazilaId == command.UpazilaId);

            return union != null;
        }

        private async Task<bool> BeAValidPostOffice(UpdateOfficeCommand command, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(command.PostOfficeId)) return true;

            var postOffice = await _repository.GetItemAsync<BuroPostOffice>(
                x => x.PostOfficeId == command.PostOfficeId
                     && x.PostOfficeName == command.PostOfficeName
                     && x.DistrictId == command.DistrictId
                     && x.UpazilaId == command.UpazilaId);

            return postOffice != null;
        }

        private async Task<bool> BeAValidWard(UpdateOfficeCommand command, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(command.WardId)) return true;

            var ward = await _repository.GetItemAsync<BuroWard>(
                x => x.WardId == command.WardId
                     && x.WardName == command.WardName
                     && x.DistrictId == command.DistrictId
                     && x.UpazilaId == command.UpazilaId
                     && x.UnionId == command.UnionId);

            return ward != null;
        }
    }
}
