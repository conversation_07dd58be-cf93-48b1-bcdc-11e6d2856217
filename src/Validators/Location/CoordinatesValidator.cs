using FluentValidation;

namespace Validators.Location;

public class CoordinatesValidator : AbstractValidator<CoordinatesWrapper>
{

    // Define the polygon coordinates for Bangladesh
    private readonly List<(double Latitude, double Longitude)> _bangladeshPolygon = new List<(double, double)>
    {
        (26.6319, 88.0693), // Northwest
        (26.6319, 92.6729),  // Northeast
        (20.6707, 92.6729),  // Southeast
        (20.6707, 88.0693), // Southwest
        (26.6319, 88.0693)  // Close the polygon
    };

    // Bounding box for Bangladesh (minLat, minLon, maxLat, maxLon)
    private readonly (double MinLat, double MinLon, double MaxLat, double MaxLon) _boundingBox =
        (20.6707, 88.0693, 26.6319, 92.6729);

    public CoordinatesValidator()
    {
        RuleFor(x => x.Latitude)
            .NotNull()
            .WithMessage("Latitude is required.")
            .InclusiveBetween(20.6707, 26.6319)
            .WithMessage("Latitude is outside the valid range for Bangladesh.");

        RuleFor(x => x.Longitude)
            .NotNull()
            .WithMessage("Longitude is required.")
            .InclusiveBetween(88.0693, 92.6729)
            .WithMessage("Longitude is outside the valid range for Bangladesh.");

        RuleFor(x => x)
            .Must(BeWithinBangladesh)
            .WithMessage("The provided coordinates do not fall within the boundaries of Bangladesh.");
    }


    private bool BeWithinBangladesh(CoordinatesWrapper coordinates)
    {
        double latitude = coordinates.Latitude;
        double longitude = coordinates.Longitude;

        if (latitude < _boundingBox.MinLat || latitude > _boundingBox.MaxLat ||
            longitude < _boundingBox.MinLon || longitude > _boundingBox.MaxLon)
        {
            return false;
        }

        return IsPointInPolygon(_bangladeshPolygon, latitude, longitude);
    }


    // Ray Casting Algorithm Implementation 
    private static bool IsPointInPolygon(List<(double Latitude, double Longitude)> polygon, double latitude, double longitude)
    {
        var inside = false;
        var count = polygon.Count;

        for (int i = 0, j = count - 1; i < count; j = i++)
        {
            double latI = polygon[i].Latitude;
            double lonI = polygon[i].Longitude;
            double latJ = polygon[j].Latitude;
            double lonJ = polygon[j].Longitude;

            // Check if the point is within the latitude range of the edge
            bool withinLatitudeRange = (latI > latitude) != (latJ > latitude);

            if (withinLatitudeRange)
            {
                // Calculate the intersection point
                double intersectionLon = (lonJ - lonI) * (latitude - latI) / (latJ - latI) + lonI;

                // Check if the point is to the left of the intersection
                if (longitude < intersectionLon)
                {
                    inside = !inside;
                }
            }
        }

        return inside;
    }
}

public class CoordinatesWrapper
{
    public double Latitude { get; set; }
    public double Longitude { get; set; }
}
