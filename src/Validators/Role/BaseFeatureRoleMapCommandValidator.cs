using Domain.Commands.Role;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Role
{
    public class BaseFeatureRoleMapCommandValidator<T> : AbstractValidator<T> where T : BaseFeatureRoleMapCommand
    {
        private readonly IRepository _repository;

        public BaseFeatureRoleMapCommandValidator(IRepository repository)
        {
            _repository = repository;

            RuleFor(command => command)
                .MustAsync(BeAnExistingRole)
                .WithMessage(BuroErrorMessageKeys.InvalidRole);

            RuleFor(command => command)
                .Must(BeValidFeature)
                .WithMessage(BuroErrorMessageKeys.InvalidFeature);
        }

        private async Task<bool> BeAnExistingRole(T command, CancellationToken cancellationToken)
        {
            var isExistingRole = await _repository.ExistsAsync<BuroRole>(r => r.ItemId == command.RoleItemId);

            return isExistingRole;
        }

        private bool BeValidFeature(T command)
        {
            if (!BeValidFeatureStatus(command)) return false;

            var givenFeatureIds = GetFeatureIds(command);

            var existingFeaturesCount = GetFeatureCountByItemId(givenFeatureIds);

            return existingFeaturesCount == givenFeatureIds.Count();
        }

        private static bool BeValidFeatureStatus(T command)
        {
            if (command is UpdateFeatureRoleMapWithStatusCommand actualCommand)
            {
                return actualCommand.FeatureRequests.TrueForAll(f => Enum.IsDefined(f.FeatureStatus));
            }

            return true;
        }

        private int GetFeatureCountByItemId(IEnumerable<string> featureIds)
        {
            return _repository.GetItems<BuroFeature>(f => featureIds.Contains(f.ItemId)).Count();
        }

        private static IEnumerable<string> GetFeatureIds(T command)
        {
            return command switch
            {
                UpdateFeatureRoleMapWithStatusCommand updateCommand => updateCommand.FeatureRequests.Select(f => f.FeatureItemId),
                CreateFeatureRoleMapCommand createCommand => createCommand.FeatureRequests.Select(f => f.FeatureItemId),
                _ => command.FeatureRequests.Select(f => f.FeatureItemId)
            };
        }
    }
}
