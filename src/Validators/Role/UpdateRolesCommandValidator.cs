using Domain.Commands.Role;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Role
{
    public class UpdateRolesCommandValidator : AbstractValidator<UpdateRolesCommand>
    {
        private readonly IRepository _repository;

        public UpdateRolesCommandValidator(
            IRepository repository)
        {
            _repository = repository;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.RequestBodyInvalid);

            RuleFor(command => command.RoleName)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.InvalidRoleName);

            RuleFor(command => command.RoleItemId)
                .MustAsync(BeAnExistingRoleAsync)
                .WithMessage(BuroErrorMessageKeys.RoleNotFound);
        }

        private async Task<bool> BeAnExistingRoleAsync(string roleItemId, CancellationToken token)
        {
            var hasRole = await _repository.ExistsAsync<BuroRole>(r => r.ItemId == roleItemId);

            return hasRole;
        }
    }
}
