using Domain.Commands.Role;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Role
{
    public class CreateFeatureRoleMapCommandValidator : BaseFeatureRoleMapCommandValidator<CreateFeatureRoleMapCommand>
    {
        public CreateFeatureRoleMapCommandValidator(IRepository repository) : base(repository)
        {
            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.RequestBodyInvalid);
        }
    }
}
