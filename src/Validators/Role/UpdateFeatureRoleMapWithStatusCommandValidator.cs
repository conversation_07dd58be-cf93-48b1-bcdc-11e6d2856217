using Domain.Commands.Role;
using FluentValidation;
using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Validators.Role
{
    public class UpdateFeatureRoleMapWithStatusCommandValidator : BaseFeatureRoleMapCommandValidator<UpdateFeatureRoleMapWithStatusCommand>
    {
        private readonly IRepository _repository;

        public UpdateFeatureRoleMapWithStatusCommandValidator(IRepository repository) : base(repository)
        {
            _repository = repository;

            RuleFor(command => command)
                .NotNull()
                .NotEmpty()
                .WithMessage(BuroErrorMessageKeys.RequestBodyInvalid);

            RuleFor(command => command.FeatureRoleMapItemId)
                .NotNull()
                .NotEmpty()
                .MustAsync(BeAnExistingFeatureRoleMap)
                .WithMessage(BuroErrorMessageKeys.InvalidFeatureRoleMap);
        }

        private async Task<bool> BeAnExistingFeatureRoleMap(string featureRoleMapItemId, CancellationToken cancellationToken)
        {
            var exists = await _repository.ExistsAsync<BuroFeatureRoleMap>(frm => frm.ItemId == featureRoleMapItemId);

            return exists;
        }
    }
}
