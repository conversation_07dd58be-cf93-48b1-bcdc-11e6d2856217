using Domain.Commands.Role;
using FluentValidation;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Validators.Role;

public class UpdateDesignationsInRoleCommandValidator : AbstractValidator<UpdateDesignationsInRoleCommand>
{
    private readonly IBusinessRepository _repository;

    public UpdateDesignationsInRoleCommandValidator(IBusinessRepository repository)
    {
        _repository = repository;
        RuleFor(command => command.RoleItemId)
            .MustAsync(RoleExists)
            .NotEmpty()
            .WithMessage(BuroErrorMessageKeys.RolesRequired);

        RuleFor(command => command)
            .MustAsync(BeValidDesignationAsync)
            .WithMessage(BuroErrorMessageKeys.DesignationInvalid);
    }

    private async Task<bool> RoleExists(string roleItemId, CancellationToken token)
    {
        var role = await _repository.GetItemAsync<BuroRole>(x => x.ItemId == roleItemId);
        return role != null;
    }
    private async Task<bool> BeValidDesignationAsync(UpdateDesignationsInRoleCommand command, CancellationToken cancellationToken)
    {
        var existingDesignationCount = await GetDesignationCountByItemIds(command.DesignationItemIds);

        return existingDesignationCount == command.DesignationItemIds.Count;
    }

    private async Task<int> GetDesignationCountByItemIds(List<string> itemIds)
    {
        var designations = await _repository.GetItemsAsync<BuroDesignation>(x => itemIds.Contains(x.ItemId));
        return designations.Count;
    }

}