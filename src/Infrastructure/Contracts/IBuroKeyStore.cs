namespace Infrastructure.Contracts;

public interface IBuroKeyStore
{
    Task<T?> GetValueAsync<T>(string key);
    Task<string> GetValueAsync(string key);

    Task<bool> AddKeyWithExpiryAsync(string key, object value, long keyLifeSpanInSecond);
    Task<bool> AddKeyWithExpiryAsync(string key, string value, long keyLifeSpanInSecond);

    Task<bool> AddKeyAsync(string key, string value);

    Task<bool> RemoveKeyAsync(string key);
}