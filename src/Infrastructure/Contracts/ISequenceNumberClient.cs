namespace Infrastructure.Contracts
{
    public interface ISequenceNumberClient
    {
        Task<SequenceNumberQueryResponse> GetSequenceNumber(SequenceNumberQuery sequenceNumberQuery);
    }

    public class SequenceNumberQuery
    {
        public string Context { get; set; }
    }

    public class SequenceNumberQueryResponse
    {
        public string Context { get; set; }
        public long CurrentNumber { get; set; }
    }
}
