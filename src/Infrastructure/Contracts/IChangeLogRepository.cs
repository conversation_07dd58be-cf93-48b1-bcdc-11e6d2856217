using System.Linq.Expressions;

namespace Infrastructure.Contracts
{
    public interface IChangeLogRepository
    {
        Task UpdateWithChangeLogAsync<T>(Expression<Func<T, bool>> dataFilters, T data);
        Task UpdateWithChangeLogAsync<T>(Expression<Func<T, bool>> dataFilters, IDictionary<string, object> updates);
        Task UpdateManyWithChangeLogAsync<T>(Expression<Func<T, bool>> dataFilters, IDictionary<string, object> updates);
    }
}
