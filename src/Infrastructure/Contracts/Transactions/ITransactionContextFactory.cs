using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Contracts.Transactions
{
    public interface ITransactionContextFactory
    {
        EnumProductType ProductType { get; }
        IAccountTransactionFactory GetAccountTransactionService(EnumBuroTransactionType transactionType);
        IProductAccountFactory GetProductAccountService();
        IPaymentProcessorFactory GetPaymentProcessorService(EnumTransactionMedium transactionMedium);
    }
}
