using Infrastructure.Models.Transaction;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Contracts.Transactions
{
    public interface IAccountPaymentPostProcessor
    {
        EnumProductType AccountType { get; }
        Task<CommandResponse> ProcessPaymentAsync(string memberId, PaymentDetail paymentDetail);
    }
}
