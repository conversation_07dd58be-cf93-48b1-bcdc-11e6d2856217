using Infrastructure.Models.ExternalSync.Responses;

namespace Infrastructure.Contracts
{
    public interface ISylviaClient
    {
        Task<SylviaGetOfficesResponse> GetOffices(int pageNo, int pageSize);
        Task<SylviaGetOfficeDetailsResponse> GetOfficeDetails(string officeCode);
        Task<SylviaGetDesignationResponse> GetDeisnations();
        Task<SylviaGetOfficeEmployeesResponse> GetOfficeEmployees(string officeCode);
        Task<SylviaGetEmployeeDetailResponse> GetEmployeeDetail(string employeePIN);
    }
}
