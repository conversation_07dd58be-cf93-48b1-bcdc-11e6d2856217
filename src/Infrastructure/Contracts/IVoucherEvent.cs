using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Contracts;

public interface IVoucherEvent
{
    string? VoucherCode { get; set; }
    string VoucherGroupItemId { get; set; }
    string OfficeItemId { get; set; }
    string RelatedEntityName { get; set; }
    string RelatedEntityItemId { get; set; }
    string? TransactionRefNo { get; set; }
    EnumVoucherScopeCategory VoucherScopeCategory { get; set; }
    double? Amount { get; set; }
    string Narration { get; set; }
    string TransactionTypeTag { get; set; }
    string? VoucherConfigItemId { get; set; } // optional 
    string? ProductLineItemId { get; set; }
}