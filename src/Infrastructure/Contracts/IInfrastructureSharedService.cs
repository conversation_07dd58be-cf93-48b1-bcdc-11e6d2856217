using Infrastructure.Models;
using SeliseBlocks.Entities.PrimaryEntities.BURO;

namespace Infrastructure.Contracts
{
    public interface IInfrastructureSharedService
    {
        Task<string> GetSequenceNumberAsync(string context, string prefix);
        Task NotifyUserAsync(string responseKey, object notificationPayload, string entityItemId, string userId, bool skipToSaveOfflineNotification = false);
        string GenerateAbbreviation(string camelCasedStr);
        Task<EmployeeIdentityDto> GetEmployeeInfoAsync(string userId);
        BuroProductAccountTransactionRequest CreateTransactionRequestAsync(CreateTransactionRequestDto command, BuroEmployee currentEmployee, BuroMember member, BuroProductAccountBase account);
        Task InitiateProductAccountTransactionAsync(bool IsIndependentTransaction = true, params BuroProductAccountTransactionRequest[] transactionRequests);
    }
}
