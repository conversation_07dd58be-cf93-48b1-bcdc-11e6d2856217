using System.Net.Http.Headers;

namespace Infrastructure.Contracts;

public interface IBusinessHttpClient
{
    Task<TResponse?> GetDataAsync<TResponse>(string url);
    Task<TResponse?> PostAsJsonAsync<TResponse>(string url, HttpContent httpContent);
    Task<TResponse?> PostAsJsonAsync<TResponse, TRequest>(string url, TRequest tRequest, string accessToken = "");

    Task<HttpResponseHeaders> GetHeaders(string url);
    public Task<HttpResponseMessage> SendToHttpAsync(HttpRequestMessage request);
}