using Infrastructure.Models;

namespace Infrastructure.Contracts;

public interface IBusinessConfig
{
    string TenantId { get; }
    string SiteId { get; }
    string SiteName { get; }
    string DefaultLanguage { get; }
    string DefaultUserId { get; }
    string DefaultAdminEmail { get; }
    string GetUamBaseUrl();
    string GetUamVersion();
    string GetResetPasswordUrl();
    string CreateUserUrl();
    string GetPortalOrigin();
    string GetStsTokenUrl();
    string GetSmsServerUrl();
    string GetSmsServerVersion();
    GoogleCaptchaSettings GetCaptchaSettings();
    string GetGoogleCaptchaVerificationUrl();
    string GetSequenceNumberServiceBaseUrl();
    string GetSequenceNumberServiceVersion();
    string GetSylviaBaseUrl();
    string GetRequestedMfcibFilePath();
    string GetMfcibExcelTemplatePath();
    string GetNumberOfRetries();
    string GetDelaySecondsBetweenRetries();
    string GetMaxDelaySecondsBetweenRetries();
    string GetTransactionServiceSettings();
}