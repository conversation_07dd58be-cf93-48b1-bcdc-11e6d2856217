using MongoDB.Bson;
using MongoDB.Driver;
using System.Linq.Expressions;

namespace Infrastructure.Contracts;

public interface IBusinessRepository
{
    public IFindFluent<T, T> Find<T>(
        Expression<Func<T, bool>> expression,
        FindOptions options = null,
        MongoCollectionSettings settings = null
    );
    IFindFluent<T, T> Find<T>(
        FilterDefinition<T> filter,
        FindOptions options = null,
        MongoCollectionSettings settings = null
    );
    Task<UpdateResult> UpdateOneAsync<T>(FilterDefinition<T> filter, UpdateDefinition<T> update, UpdateOptions options = null);

    Task<UpdateResult> UpdateOneAsync<T>(
        Expression<Func<T, bool>> dataFilters,
        UpdateDefinition<T> update,
        UpdateOptions options = null
    );

    int GetCountByFilterDefinition<T>(FilterDefinition<T> filterDefinition);
    Task<int> GetCountByFilterDefinitionAsync<T>(FilterDefinition<T> filterDefinition);

    IMongoCollection<T> GetCollection<T>(MongoCollectionSettings settings = null);
    IMongoCollection<T> GetCollectionByName<T>(string collectionName, MongoCollectionSettings settings = null);

    Task<T> GetItemAsync<T>(Expression<Func<T, bool>> dataFilters);
    Task<List<T>> GetItemsAsync<T>(FilterDefinition<T> filterDefinition, FindOptions<T, T> findOptions = null);
    Task<List<T>> GetItemsAsync<T>(Expression<Func<T, bool>> dataFilters);
    Task<long> CountDocumentsAsync<T>(FilterDefinition<T> filterDefinition);
    Task<long> CountDocumentsAsync<T>(Expression<Func<T, bool>> dataFilters);

    Task InsertOneAsync<T>(T data);
    Task InsertManyAsync<T>(List<T> dataList);
    Task UpdateManyAsync<T>(FilterDefinition<T> filterDefinition, UpdateDefinition<T> update);
    Task DeleteOneAsync<T>(Expression<Func<T, bool>> dataFilters);

    Task DeleteAsync<T>(Expression<Func<T, bool>> dataFilters);

    Task<List<BsonDocument>> GetDistinctCountByField<T>(FilterDefinition<T> filterDefinition, string fieldName, string? categoryName);
    Task<List<TResult>> FindWithProjectionAsync<T, TResult>(
        Expression<Func<T, bool>>? filterExpression = null,
        Expression<Func<T, TResult>>? projectionExpression = null);
}