namespace Infrastructure.Contracts.Retry
{
    /// <summary>
    /// Defines methods for executing operations with retry logic, supporting synchronous and asynchronous execution.
    /// </summary>
    public interface IRetryExecutorService
    {
        /// <summary>
        /// Executes the specified synchronous action with retry logic.
        /// </summary>
        /// <param name="action">The synchronous action to execute.</param>
        /// <exception cref="InvalidOperationException">
        /// Thrown if the action fails after the configured number of retry attempts.
        /// </exception>
        void Execute(Action action);

        /// <summary>
        /// Executes the specified synchronous function with retry logic and returns the result.
        /// </summary>
        /// <typeparam name="TResult">The return type of the function.</typeparam>
        /// <param name="func">The synchronous function to execute.</param>
        /// <returns>The result returned by the function.</returns>
        /// <exception cref="InvalidOperationException">
        /// Thrown if the function fails after the configured number of retry attempts.
        /// </exception>
        TResult? Execute<TResult>(Func<TResult> func);

        /// <summary>
        /// Executes the specified asynchronous action with retry logic.
        /// </summary>
        /// <param name="action">The asynchronous action to execute.</param>
        /// <param name="cancellationToken">A token to observe while waiting for the operation to complete.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        /// <exception cref="InvalidOperationException">
        /// Thrown if the action fails after the configured number of retry attempts.
        /// </exception>
        /// <exception cref="OperationCanceledException">
        /// Thrown if the operation is canceled via the cancellation token.
        /// </exception>
        Task ExecuteAsync(Func<Task> action, CancellationToken cancellationToken = default);

        /// <summary>
        /// Executes the specified asynchronous function with retry logic and returns the result.
        /// </summary>
        /// <typeparam name="TResult">The return type of the asynchronous function.</typeparam>
        /// <param name="func">The asynchronous function to execute.</param>
        /// <param name="cancellationToken">A token to observe while waiting for the operation to complete.</param>
        /// <returns>A task representing the asynchronous operation with the function's result.</returns>
        /// <exception cref="InvalidOperationException">
        /// Thrown if the function fails after the configured number of retry attempts.
        /// </exception>
        /// <exception cref="OperationCanceledException">
        /// Thrown if the operation is canceled via the cancellation token.
        /// </exception>
        Task<TResult?> ExecuteAsync<TResult>(Func<Task<TResult>> func, CancellationToken cancellationToken = default);
    }

}
