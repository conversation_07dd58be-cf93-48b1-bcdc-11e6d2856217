namespace Infrastructure.Contracts.Retry
{
    /// <summary>
    /// Defines a strategy for retry delays and maximum retry attempts, supporting both synchronous and asynchronous operations.
    /// </summary>
    public interface IRetryStrategy
    {
        /// <summary>
        /// Gets the maximum number of retry attempts allowed.
        /// </summary>
        int MaxRetries { get; }

        /// <summary>
        /// Applies a delay before the next retry attempt in a synchronous context.
        /// The delay duration is typically based on the current attempt number and the chosen retry strategy (e.g., exponential backoff with jitter).
        /// </summary>
        /// <param name="attempt">The current retry attempt number, starting from 0.</param>
        void Delay(int attempt);

        /// <summary>
        /// Asynchronously applies a delay before the next retry attempt.
        /// The delay duration is typically based on the current attempt number and the chosen retry strategy (e.g., exponential backoff with jitter).
        /// </summary>
        /// <param name="attempt">The current retry attempt number, starting from 0.</param>
        /// <param name="cancellationToken">A token to observe while waiting for the delay to complete.</param>
        /// <returns>A task that completes after the calculated delay or when the operation is cancelled.</returns>
        Task DelayAsync(int attempt, CancellationToken cancellationToken);
    }
}
