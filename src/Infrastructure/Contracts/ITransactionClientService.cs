using Infrastructure.Models.Transaction;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Contracts
{
    /// <summary>
    /// Defines methods for managing accounts and transactions in a client system.
    /// </summary>
    /// <remarks>This interface provides asynchronous methods for creating, updating, and deleting accounts, 
    /// as well as initiating and reversing transactions. Each method returns an <see cref="CommandResponse"/> 
    /// indicating the result of the operation.</remarks>
    public interface ITransactionClientService
    {
        // NOSONAR Account related methods
        /// <summary>
        /// Asynchronously creates a new account based on the provided command.
        /// </summary>
        /// <remarks>Ensure that the <paramref name="createAccountCommand"/> contains all required fields 
        /// for account creation. The method performs validation on the input and may return  an error response if the
        /// command is invalid.</remarks>
        /// <param name="createAccountCommand">The command containing the details required to create the account.  This parameter cannot be null.</param>
        /// <returns>A <see cref="CommandResponse"/> indicating the result of the account creation operation. The response
        /// may include status codes and additional information about the operation.</returns>
        Task<CommandResponse> CreateAccountAsync(CreateAccountCommand createAccountCommand);

        /// <summary>
        /// Updates an account with the specified details asynchronously.
        /// </summary>
        /// <remarks>This method performs an asynchronous operation to update account details. Ensure that
        /// the  <paramref name="updateAccountCommand"/> contains valid and complete information before calling this
        /// method.</remarks>
        /// <param name="updateAccountCommand">The command containing the account details to be updated. This parameter cannot be null.</param>
        /// <returns>A <see cref="CommandResponse"/> indicating the result of the update operation.  The response may include
        /// status codes such as <see cref="System.Net.HttpStatusCode.OK"/> for a successful update  or <see
        /// cref="System.Net.HttpStatusCode.BadRequest"/> if the provided details are invalid.</returns>
        Task<CommandResponse> UpdateAccountAsync(UpdateAccountCommand updateAccountCommand);

        /// <summary>
        /// Deletes a user account asynchronously based on the specified command.
        /// </summary>
        /// <remarks>This method performs an asynchronous operation to delete a user account.  Ensure that
        /// the account ID provided in the <paramref name="deleteAccountCommand"/> is valid. The caller should check the
        /// returned <see cref="CommandResponse"/> for the status of the operation.</remarks>
        /// <param name="deleteAccountCommand">The command containing the details required to delete the account, such as the account ID. This parameter
        /// cannot be null.</param>
        /// <returns>A <see cref="CommandResponse"/> indicating the result of the operation.  The response contains the
        /// status code and any additional information about the deletion.</returns>
        Task<CommandResponse> DeleteAccountAsync(DeleteAccountCommand deleteAccountCommand);

        // NOSONAR Transaction related methods
        /// <summary>
        /// Initiates transactions asynchronously based on the provided command.
        /// </summary>
        /// <remarks>This method sends the transaction details encapsulated in the <paramref
        /// name="initiateTransactionCommand"/>  to the appropriate endpoint and returns the server's response. Ensure
        /// that the command contains all  required fields and adheres to the expected format.</remarks>
        /// <param name="initiateTransactionCommand">The command containing the details required to initiate the transaction.  This parameter cannot be null.</param>
        /// <returns>A <see cref="CommandResponse"/> representing the result of the transaction initiation. The response may
        /// include status codes and additional information about the transaction.</returns>
        Task<CommandResponse> InitiateTransactionAsync(InitiateTransactionCommand initiateTransactionCommand);

        /// <summary>
        /// Reverses a financial transaction asynchronously.
        /// </summary>
        /// <remarks>This method performs a reversal of a previously executed transaction.  Ensure that
        /// the provided command contains all required details for the reversal to succeed. The caller should handle any
        /// HTTP response errors or exceptions that may occur during the operation.</remarks>
        /// <param name="reverseTransactionCommand">The command containing the details of the transaction to be reversed.  This parameter cannot be null and
        /// must include valid transaction identifiers.</param>
        /// <returns>A <see cref="CommandResponse"/> indicating the result of the reversal operation.  The response may
        /// include status codes and additional information about the success or failure of the reversal.</returns>
        Task<CommandResponse> ReverseTransactionAsync(ReverseTransactionCommand reverseTransactionCommand);
    }
}