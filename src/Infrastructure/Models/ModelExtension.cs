using Infrastructure.Models.ForgotPassword;
using Selise.Ecap.Entities.PrimaryEntities.Security;

namespace Infrastructure.Models;

public static class ModelExtension
{
    public static UserInfoForForgetPassword AsUserInfoForForgetPassword(this User user)
    {
        return new UserInfoForForgetPassword
        {
            ItemId = user.ItemId,
            Name = user.DisplayName,
            EmployeePin = user.UserName,
            PhoneNumber = user.PhoneNumber,
            Email = user.Email
        };
    }
}