using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Models.Transaction
{
    public class PaymentDetail
    {
        public BuroProductAccountBase Account { get; set; } = default!;
        public BuroMemberSchedule MemberSchedule { get; set; } = default!;
        public EnumProductType AccountType { get; set; }
        public double RealizableAmount { get; set; }
        public double RealizedAmount { get; set; }
        public double? LateFeeAmount { get; set; }
        public double? DueAmount { get; set; }
    }
}
