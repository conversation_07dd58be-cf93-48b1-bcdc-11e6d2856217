namespace Infrastructure.Models.Transaction
{
    public class InitiateTransactionCommand : TransactionBaseCommand
    {
        public List<TransactionCommand> Transactions { get; set; } = new();
        public bool IsIndependentTransaction { get; set; }
    }

    public class TransactionCommand
    {
        public Guid TransactionId { get; set; } = Guid.NewGuid();
        public string AccountHolderNumber { get; set; } = default!;
        public string AccountNumber { get; set; } = default!;
        public double Amount { get; set; }
        public string Currency { get; set; } = "BDT";
        public int TransactionType { get; set; }
        public int AccountType { get; set; }
        public string Reference { get; set; } = default!;
    }
}
