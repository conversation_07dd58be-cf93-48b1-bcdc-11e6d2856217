namespace Infrastructure.Models.ExternalSync.Responses
{
    public class SylviaBaseReponse
    {
        public bool Success { get; set; }
        public string MessageEn { get; set; }
        public string MessageBn { get; set; }
        public int StatusCode { get; set; }
    }

    public class EmployeeData
    {
        public string Pin { get; set; }
        public string EmployeeName { get; set; }
        public string GradeName { get; set; }
        public string DesignationName { get; set; }
        public string WorkstationCode { get; set; }
        public string WorkstationName { get; set; }
        public DateTime? JoiningDate { get; set; }
        public DateTime? ConfirmDate { get; set; }
        public string EmployeeStatus { get; set; }
        public string LiabilitiesName { get; set; }
        public string EmployeeType { get; set; }
    }

    public class DesignationData
    {
        public string DesignationName { get; set; }
        public string DesignationName_Bn { get; set; }
        public string Designation_Short { get; set; }
        public bool IsActive { get; set; }
    }

    public class SylviaOfficeData
    {
        public string OfficeCode { get; set; }
        public string OfficeName { get; set; }
        public string OfficeType { get; set; }
        public string OfficeEmail { get; set; }
        public string OfficeMobileNumber { get; set; }
        public string DivisionOfficeName { get; set; }
        public string ZoneOfficeName { get; set; }
        public string AreaOfficeName { get; set; }
        public string Address { get; set; }
        public string DivisionName { get; set; }
        public string DistrictName { get; set; }
        public string ThanaName { get; set; }
        public DateTime? OpeningDate { get; set; }
        public DateTime? ClosingDate { get; set; }
        public string GpsLocation { get; set; }
        public int NumberOfEmployee { get; set; }
        public bool IsActive { get; set; }
    }

}
