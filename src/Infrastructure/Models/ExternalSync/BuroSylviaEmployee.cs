using MongoDB.Bson.Serialization.Attributes;

namespace Infrastructure.Models.ExternalSync
{
    [BsonIgnoreExtraElements]
    public class BuroSylviaEmployee
    {
        public string ItemId { get; set; }
        public string Pin { get; set; }
        public string EmployeeName { get; set; }
        public string GradeName { get; set; }
        public string DesignationName { get; set; }
        public string WorkstationCode { get; set; }
        public string WorkstationName { get; set; }
        public DateTime? JoiningDate { get; set; }
        public DateTime? ConfirmDate { get; set; }
        public string EmployeeStatus { get; set; }
        public string LiabilitiesName { get; set; }
        public string EmployeeType { get; set; }
        public string OfficeCode { get; set; }
        public string RequestItemId { get; set; }
    }
}
