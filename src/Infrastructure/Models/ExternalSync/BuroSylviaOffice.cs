using MongoDB.Bson.Serialization.Attributes;

namespace Infrastructure.Models.ExternalSync
{
    [BsonIgnoreExtraElements]
    public class BuroSylviaOffice
    {
        public string ItemId { get; set; }
        public string OfficeCode { get; set; }
        public string ParentOfficeCode { get; set; }
        public string OfficeName { get; set; }
        public string OfficeType { get; set; }
        public string OfficeEmail { get; set; }
        public string OfficeMobileNumber { get; set; }
        public string DivisionOfficeName { get; set; }
        public string ZoneOfficeName { get; set; }
        public string AreaOfficeName { get; set; }
        public string Address { get; set; }
        public string DivisionName { get; set; }
        public string DistrictName { get; set; }
        public string ThanaName { get; set; }
        public DateTime? OpeningDate { get; set; }
        public DateTime? ClosingDate { get; set; }
        public string GpsLocation { get; set; }
        public int NumberOfEmployee { get; set; }
        public bool IsActive { get; set; }
        public string RequestItemId { get; set; }
    }
}
