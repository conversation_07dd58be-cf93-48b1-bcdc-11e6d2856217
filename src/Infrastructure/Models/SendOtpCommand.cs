using Infrastructure.Constants;

namespace Infrastructure.Models
{
    public class SendOtpCommand
    {
        public OtpChannel Channel { get; set; } = default!;
        public string Recipient { get; set; } = default!;
        public string UniqueKey { get; set; } = default!;
        public string Purpose { get; set; } = default!;
    }

    public class MatchOtpCommand
    {
        public string OtpToMatch { get; set; } = default!;
        public string UniqueKey { get; set; } = default!;
    }
}
