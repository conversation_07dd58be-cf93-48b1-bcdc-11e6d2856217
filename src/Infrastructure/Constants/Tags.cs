namespace Infrastructure.Constants
{
    public static class Tags
    {
        public const string IsAOffice = "Is-A-Office";
        public const string IsAEmployeeDesignation = "Is-A-EmployeeDesignation";
        public const string IsAEmployee = "Is-A-Employee";
        public const string IsAHoliday = "Is-A-Holiday";
        public const string IsAStandardVoucherConfiguration = "Is-A-StandardVoucherConfiguration";
        public const string IsProductLineVoucherConfiguration = "Is-A-ProductLineVoucherConfiguration";
        public const string IsAVoucher = "Is-A-Voucher";
        public const string IsAWeekend = "Is-A-Weekend";
        public const string IsAGSProductTemplate = "Is-A-GSProductTemplate";
        public const string IsAPhoneNumberChangeApproval = "Is-A-PhoneNumberChangeApproval";
        public const string IsAAddressChangeApproval = "Is-A-AddressChangeApproval";
        public const string IsAConsentFormApproval = "Is-A-ConsentFormApproval";
        public const string IsAContractualAccountApproval = "Is-A-ContractualAccountApproval";
        public const string IsAOutInventoryApproval = "IS-A-OutInventoryApproval";
        public const string IsADisposeInventoryApproval = "IS-A-DisposeInventoryApproval";
        public const string IsATransferInventoryRecipientApproval = "Is-A-TransferInventoryRecipientApproval";
        public const string IsATransferInventorySourceApproval = "Is-A-TransferInventorySourceApproval";
        public const string IsAGSWithdrawalApproval = "Is-A-GSWithdrawalApproval";
        public const string IsALoanApplicationApproval = "Is-A-LoanApplicationAccountApproval";
        public const string IsACollectionFundTransferRevertApproval = "Is-A-CollectionFundTransferRevertApproval";
        public const string IsAChequeBook = "Is-A-ChequeBook";
        public const string Person = "Person";
        public const string IsAFile = "Is-A-File";
        public const string IsAConsentForm = "Is-A-ConsentForm";
        public const string IsAGSChangeApproval = "Is-A-GSChangeApproval";
        public const string IsAChequeRegister = "Is-A-ChequeRegister";
        public const string IsAMembershipFeeSetup = "Is-A-MembershipFeeSetup";
        public const string IsABankBranch = "Is-A-BankBranch";
        public const string IsAIssueChequeRegister = "Is-A-IssueChequeRegister";
        public const string IsAPostRegister = "Is-A-PostRegister";
        public const string IsACommentRegister = "Is-A-CommentRegister";
    }
}
