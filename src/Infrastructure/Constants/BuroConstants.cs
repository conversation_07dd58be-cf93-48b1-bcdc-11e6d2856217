namespace Infrastructure.Constants;

public static class BuroConstants
{
    public const string TenantId = "B14ED887-CBE7-4B8D-AA66-4FA0FAD9E48F";
    public const string DefaultLanguage = "en-US";
    public const string DefaultQueue = "BURO.CommandQueue";
    public const string SurveyCommandQueue = "BURO.Survey.CommandQueue";
    public const string BuroOfficeIdentifier = "BuroOfficeIdentifier";
    public const string BuroEmployeePinIdentifier = "BuroEmployeePinIdentifier";
    public const string BuroExternalSyncRequestIdentifier = "BuroExternalSyncRequestIdentifier";
    public const string PrimaryKey = "_id";
    public const string SuperAdmin = "f19a9fd5-9c00-4cb3-aefe-f745ab3eaadd"; //temp decision
    public const string ForgetPasswordSuffix = "forgetpassword";
    public const string LoginActivitySuffix = "loginactivity";
    public const string LastUpdatedBy = "WinSystem";
    public const string BuroCenterEntity = "BuroCenter";
    public const string BuroMemberIdentifier = "BuroMemberIdentifier";
    public const string ProductCommandQueue = "FinBlocks.Product.CommandQueue";
    public const string BuroEmployeeIDIdentifier = "BuroEmployeeIDIdentifier";
    public const string BuroCenterIDIdentifier = "BuroCenterIDIdentifier";
    public const string BuroLoanApplicationIdentifier = "BuroLoanApplicationIdentifier";
    public const string BuroContractualSavingsApplicationIdentifier = "BuroContractualSavingsApplicationIdentifier";
    public const string BuroContractualSavingsAccountIdentifier = "BuroContractualSavingsAccountIdentifier";
    public const string BuroLoanAccountIdentifier = "BuroLoanAccountIdentifier";
    public const string BuroLoanDisbursementIdentifier = "BuroLoanDisbursementIdentifier";
    public const string BuroGeneralSavingsAccountIdentifier = "BuroGeneralSavingsAccountIdentifier";
    public const string BuroVoucherIdentifier = "BuroVoucherIdentifier";
    public const string BuroMfcibRequestIdentifier = "BuroMfcibRequestIdentifier";
    public const string BuroInventoryVendorIdentifier = "BuroInventoryVendorIdentifier";
    public const string BuroAssetVendorIdentifier = "BuroAssetVendorIdentifier";
    public const string PermissionSuffix = "_Permission";
    public const string DelegatedPermissionSuffix = "_DelegatedPermission";
    public const int MaximumDepthOfOffice = 5;
}
