using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.Enums;
using System.Collections.Immutable;

namespace Infrastructure.Constants
{
    public static class BuroCibConstants
    {
        public const string True = "Y";
        public const string False = "N";
        public const string DateFormat = "ddMMyyyy";
        public const string MFICode = "0288";
        public const string CountryCode = "BD        ";

        private static readonly Dictionary<EnumTransactionMedium, string> transactionMediumMapping = new()
        {
            { EnumTransactionMedium.Cash, "CAS" },
            { EnumTransactionMedium.Cheque, "CHQ" },
            { EnumTransactionMedium.MFS, "MFS" },
            { EnumTransactionMedium.BankDraft, "BAR" },
            { EnumTransactionMedium.DirectTransfer, "DIR" },
            { EnumTransactionMedium.Multiple, "MUL" },
            { EnumTransactionMedium.Others, "OTH" }
        };
        public static readonly ImmutableDictionary<EnumTransactionMedium, string> TransactionMediumMapping = transactionMediumMapping.ToImmutableDictionary();

        private static readonly Dictionary<EnumContractPhase, string> contractPhaseMapping = new()
        {
            { EnumContractPhase.Requested, "RQ" },
            { EnumContractPhase.Renounced, "RN" },
            { EnumContractPhase.Refused, "RF" },
            { EnumContractPhase.Living, "LV" },
            { EnumContractPhase.TerminatedOrFullPaid, "TM" },
            { EnumContractPhase.TerminatedInTransfer, "TT" },
            { EnumContractPhase.TerminatedInAdvanceOrFullPaidInAdvance, "TA" }
        };
        public static readonly ImmutableDictionary<EnumContractPhase, string> ContractPhaseMapping = contractPhaseMapping.ToImmutableDictionary();

        private static readonly Dictionary<EnumProductAccountStatus, string> productAccountStatusMapping = new()
        {
            { EnumProductAccountStatus.Regular, "R" },
            { EnumProductAccountStatus.Watchful, "W" },
            { EnumProductAccountStatus.SubStandard, "SS" },
            { EnumProductAccountStatus.Doubtful, "D" },
            { EnumProductAccountStatus.BadLoan, "BL" },
            { EnumProductAccountStatus.WriteOff, "WO" }
        };
        public static readonly ImmutableDictionary<EnumProductAccountStatus, string> ProductAccountStatusMapping = productAccountStatusMapping.ToImmutableDictionary();

        private static readonly Dictionary<EnumPaymentInterval, string> paymentIntervalMapping = new()
        {
            { EnumPaymentInterval.Weekly, "W" },
            { EnumPaymentInterval.Monthly, "M" }
        };
        public static readonly ImmutableDictionary<EnumPaymentInterval, string> PaymentIntervalMapping = paymentIntervalMapping.ToImmutableDictionary();

        private static readonly Dictionary<EnumLoanType, string> loanTypeMapping = new()
        {
            { EnumLoanType.SingleInstallment, "S" },
            { EnumLoanType.MultipleInstallment, "M" },
            { EnumLoanType.SeasonalSingle, "SS" },
            { EnumLoanType.SeasonalMultiple, "SM" }
        };
        public static readonly ImmutableDictionary<EnumLoanType, string> LoanTypeMapping = loanTypeMapping.ToImmutableDictionary();

        private static readonly Dictionary<EnumGenderOption, string> genderOptionMapping = new()
        {
            { EnumGenderOption.Male, "M" },
            { EnumGenderOption.Female, "F" },
            { EnumGenderOption.Transgender, "T" }
        };
        public static readonly ImmutableDictionary<EnumGenderOption, string> GenderOptionMapping = genderOptionMapping.ToImmutableDictionary();

        private static readonly Dictionary<EnumMfcibDataType, string> mfcibDataTypeMapping = new()
        {
            { EnumMfcibDataType.Header, "H" },
            { EnumMfcibDataType.Person, "P" },
            { EnumMfcibDataType.Contract, "C" }
        };
        public static readonly ImmutableDictionary<EnumMfcibDataType, string> MfcibDataTypeMapping = mfcibDataTypeMapping.ToImmutableDictionary();
    }
}
