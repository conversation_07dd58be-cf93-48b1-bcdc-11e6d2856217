using Infrastructure.Enums;

namespace Infrastructure.Constants
{
    public static class BuroLoanApplicationNotificationKeys
    {
        private const string LoanApplicationBaseNotificationKey = "Loan.Application.{0}.{1}";

        public static string GetNotificationKeyByDesignationCode(string designationCode, EnumLoanActions action)
        {
            var designation = BuroDesignationConstant.DesignationFullForm.GetValueOrDefault(designationCode) ?? string.Empty;
            var actionName = Enum.GetName(typeof(EnumLoanActions), action);
            var notificationKey = string.Format(LoanApplicationBaseNotificationKey, designation, actionName);

            return notificationKey;
        }
    }
}
