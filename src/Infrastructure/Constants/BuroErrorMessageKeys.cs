namespace Infrastructure.Constants;

public static class BuroErrorMessageKeys
{
    public const string RequestBodyInvalid = "REQUEST_BODY_INVALID";
    public const string InvalidProperty = "{0}_INVALID";
    public const string RequiredProperty = "{0}_REQUIRED";
    public const string AlreadyExist = "{0}_ALREADY_EXIST";
    public const string DoesNotExist = "{0}_DOESNOT_EXIST";
    public const string RequiredDataNotFound = "{0}_NOTFOUND";

    public const string InvalidData = "INVALID_DATA";
    public const string InvalidRequest = "INVALID_REQUEST";
    public const string UserInvalid = "USER_INVALID";
    public const string UserIdRequired = "USER_ID_REQUIRED";
    public const string ErrorOccurred = "ERROR_OCCURRED";
    public const string EmailSendingFailed = "EMAIL_SENDING_FAILED";
    public const string SmsSendingFailed = "SMS_SENDING_FAILED";
    public const string OtpExpired = "OTP_EXPIRED";
    public const string OtpNotMatched = "OTP_NOT_MATCHED";
    public const string PasswordResetFailed = "PASSWORD_RESET_FAILED";
    public const string CaptchaRequired = "CAPTCHA_REQUIRED";
    public const string OtpChannelRequired = "OTP_CHANNEL_REQUIRED";
    public const string TransactionTypeNotFound = "TRANSACTION_TYPE_NOT_FOUND";
    public const string OfficeNotFound = "OFFICE_NOT_FOUND";

    // Login Activity
    public const string LoggedInAsUserIdRequired = "LOGGED_IN_AS_USER_ID_REQUIRED";

    //Inventory
    public const string InventoryItemDoesntExists = "INVENTORY_ITEM_DOESNT_EXISTS";
    public const string VendorIdRequired = "VENDOR_ID_REQUIRED";
    public const string VendorItemIdRequired = "VENDOR_ITEM_ID_REQUIRED";
    public const string VendorTypeRequired = "VENDOR_TYPE_REQUIRED";
    public const string VendorDomainRequired = "VENDOR_DOMAIN_REQUIRED";
    public const string VendorItemCategoryRequired = "VENDOR_ITEM_CATEGORY_REQUIRED";
    public const string VendorItemSubCategoryRequired = "VENDOR_ITEM_SUB_CATEGORY_REQUIRED";
    public const string VendorLegalNameRequired = "VENDOR_LEGAL_NAME_REQUIRED";
    public const string VendorTradeLicenseRequired = "VENDOR_TRADE_LICENSE_REQUIRED";
    public const string VendorContactPersonRequired = "VENDOR_CONTACT_PERSON_REQUIRED";
    public const string VendorContactPersonNameRequired = "VENDOR_CONTACT_PERSON_NAME_REQUIRED";
    public const string VendorContactPersonPhoneRequired = "VENDOR_CONTACT_PERSON_PHONE_REQUIRED";
    public const string VendorStatusRequired = "VENDOR_STATUS_REQUIRED";
    public const string EmailRequired = "EMAIL_REQUIRED";
    public const string PhoneNoRequired = "PHONE_NO_REQUIRED";

    public const string InventoryItemItemIdRequired = "INVENTORY_ITEM_ITEM_ID_REQUIRED";
    public const string ItemDefinitionRequired = "ITEM_DEFINITION_REQUIRED";
    public const string InventoryItemNameRequired = "INVENTORY_ITEM_NAME_REQUIRED";
    public const string CategoryItemIdRequired = "CATEGORY_ITEM_ID_REQUIRED";
    public const string CategoryNameRequired = "CATEGORY_NAME_REQUIRED";
    public const string SubCategoryItemIdRequired = "SUB_CATEGORY_ITEM_ID_REQUIRED";
    public const string SubCategoryNameRequired = "SUB_CATEGORY_NAME_REQUIRED";
    public const string ChartOfAccountItemIdRequired = "CHART_OF_ACCOUNT_ITEM_ID_REQUIRED";
    public const string ChartOfAccountRequired = "CHART_OF_ACCOUNT_REQUIRED";
    public const string PriceTypeRequired = "PRICE_TYPE_REQUIRED";
    public const string MinPriceRequired = "MIN_PRICE_REQUIRED";
    public const string MaxPriceRequired = "MAX_PRICE_REQUIRED";
    public const string FixedPriceRequired = "FIXED_PRICE_REQUIRED";
    public const string MinPriceMustBeLessThanMaxPrice = "MIN_PRICE_MUST_BE_LESS_THAN_MAX_PRICE";
    public const string HasSerialNumberRequired = "HAS_SERIAL_NUMBER_REQUIRED";
    public const string RequiresExpiryDateRequired = "REQUIRES_EXPIRY_DATE_REQUIRED";
    public const string InventoryStockBreakdownItemIdRequired = "INVENTORY_STOCK_BREAKDOWN_ITEM_ID_REQUIRED";
    public const string AtLeastOneItemRequired = "ATLEAST_ONE_ITEM_REQUIRED";
    public const string InvalidTransferOfficeId = "INVALID_TRANSFER_OFFICE_ID";

    // InventoryMovement
    public const string OfficeItemIdRequired = "OFFICE_ITEM_ID_REQUIRED";
    public const string OfficeNameRequired = "OFFICE_NAME_REQUIRED";
    public const string InventoryItemIdRequired = "INVENTORY_ITEM_ID_REQUIRED";
    public const string InventoryNameRequired = "INVENTORY_NAME_REQUIRED";
    public const string InventoryItemDefinitionRequired = "INVENTORY_ITEM_DEFINITION_REQUIRED";
    public const string QuantityRequired = "QUANTITY_REQUIRED";
    public const string SerialNumberRequired = "SERIAL_NUMBER_REQUIRED";
    public const string ExpiryDateRequired = "EXPIRY_DATE_REQUIRED";
    public const string PriceRequired = "PRICE_REQUIRED";
    public const string TransactionTypeRequired = "TRANSACTION_TYPE_REQUIRED";
    public const string TransferOfficeItemIdRequired = "TRANSFER_OFFICE_ITEM_ID_REQUIRED";
    public const string TransferOfficeNameRequired = "TRANSFER_OFFICE_NAME_REQUIRED";
    public const string VendorNameRequired = "VENDOR_NAME_REQUIRED";

    // Asset
    public const string VendorGroupRequired = "VENDOR_GROUP_REQUIRED";
    public const string VendorSubGroupRequired = "VENDOR_SUB_GROUP_REQUIRED";
    public const string AssetGroupCannotBeDeleted = "ASSET_GROUP_CANNOT_BE_DELETED";


    // Employee Related Message Keys
    public const string EmployeeAlreadyExists = "EMPLOYEE_ALREADY_EXISTS";
    public const string EmployeeNameRequired = "EMPLOYEE_NAME_REQUIRED";
    public const string EmployeePinRequired = "EMPLOYEE_PIN_REQUIRED";
    public const string FatherNameRequired = "FATHER_NAME_REQUIRED";
    public const string MotherNameRequired = "MOTHER_NAME_REQUIRED";
    public const string NidNoRequired = "NID_NO_REQUIRED";
    public const string OnePhoneRequired = "ATLEAST_ONE_PHONE_REQUIRED";
    public const string PhoneNumberWithCodeRequired = "PHONE_NUMBER_WITH_CODE_REQUIRED";
    public const string WorkPhoneRequired = "WORK_PHONE_REQUIRED";
    public const string PersonalPhoneRequired = "PERSONAL_PHONE_REQUIRED";
    public const string DesignationIdRequired = "DESIGNATION_ID_REQUIRED";
    public const string DesignationTitleRequired = "DESIGNATION_TITLE_REQUIRED";
    public const string DesignationRequired = "DESIGNATION_REQUIRED";
    public const string DesignationCodeRequired = "DESIGNATION_CODE_REQUIRED";
    public const string DesignationInvalid = "DESIGNATION_INVALID";
    public const string DesignationNotFound = "DESIGNATION_NOT_FOUND";
    public const string CountryIdRequired = "COUNTRY_ID_REQUIRED";
    public const string CountryNameRequired = "COUNTRY_NAME_REQUIRED";
    public const string CountryLangKeyRequired = "COUNTRY_LANG_KEY_REQUIRED";
    public const string DistrictIdRequired = "DISTRICT_ID_REQUIRED";
    public const string DistrictNameRequired = "DISTRICT_NAME_REQUIRED";
    public const string DistrictLangKeyRequired = "DISTRICT_LANG_KEY_REQUIRED";
    public const string AddressRequired = "ADDRESS_REQUIRED";
    public const string PostCodeRequired = "POST_CODE_REQUIRED";
    public const string PostOfficeIdRequired = "POST_OFFICE_ID_REQUIRED";
    public const string PostOfficeNameRequired = "POST_OFFICE_NAME_REQUIRED";
    public const string PostOfficeLangKeyRequired = "POST_OFFICE_LANG_KEY_REQUIRED";
    public const string UnionIdRequired = "UNION_ID_REQUIRED";
    public const string UnionNameRequired = "UNION_NAME_REQUIRED";
    public const string UnionLangKeyRequired = "UNION_LANG_KEY_REQUIRED";
    public const string UpazilaIdRequired = "UPAZILA_ID_REQUIRED";
    public const string UpazilaNameRequired = "UPAZILA_NAME_REQUIRED";
    public const string UpazilaLangKeyRequired = "UPAZILA_LANG_KEY_REQUIRED";
    public const string WardIdRequired = "WARD_ID_REQUIRED";
    public const string WardNameRequired = "WARD_NAME_REQUIRED";
    public const string WardLangKeyRequired = "WARD_LANG_KEY_REQUIRED";
    public const string InvalidEmployeeId = "INVALID_EMPLOYEE_ID";
    public const string InvalidNidNo = "INVALID_NID_NO";

    // Employee History
    public const string EmployeeItemIdRequired = "EMPLOYEE_ITEM_ID_REQUIRED";
    public const string TransferHistoryTypeInvalid = "TRANSFER_HISTORY_TYPE_INVALID";
    public const string OfficeIdRequired = "OFFICE_ID_REQUIRED";
    public const string OfficeTitleRequired = "OFFICE_TITLE_REQUIRED";
    public const string MemberCountRequired = "MEMBER_COUNT_REQUIRED";

    // User Related Message Keys

    public const string UserCreationFailed = "USER_CREATION_FAILED";
    public const string UserDoesntExists = "USER_DOESNT_EXISTS";

    // Delegation
    public const string InvalidDelegation = "INVALID_DELEGATION";
    public const string SourceDoesntExists = "SOURCE_DOESNT_EXISTS";
    public const string DelegateeDoesntExists = "DELEGATEE_DOESNT_EXISTS";
    public const string StartDateRequired = "START_DATE_REQUIRED";
    public const string EndDateRequired = "END_DATE_REQUIRED";
    public const string OverlappingDelegationExists = "OVERLAPPING_DELEGATION_EXISTS";
    public const string DelegationIdRequired = "DELEGATION_ID_REQUIRED";


    // Message Keys for CustomProperty entity
    public const string CustomPropertyCategoryInvalid = "CATEGORY_INVALID";
    public const string PropertyValueTypeInvalid = "PROPERTY_VALUE_TYPE_INVALID";
    public const string PropertyNameInvalid = "PROPERTY_NAME_INVALID";
    public const string PropertyNameExists = "PROPERTY_NAME_EXISTS";
    public const string DefaultValueInvalid = "DEFAULT_VALUE_INVALID";

    public const string InvalidSurvey = "BRANCH_CREATION_NEEDS_A_VALID_SURVEY";
    public const string InvalidParentOffice = "INVALID_PARENT_OFFICE";
    public const string InvalidOffice = "INVALID_OFFICE";
    public const string InvalidDivision = "INVALID_DIVISION";
    public const string InvalidDistrict = "INVALID_DISTRICT";
    public const string InvalidUpazila = "INVALID_UPAZILA";
    public const string InvalidUnion = "INVALID_UNION";
    public const string InvalidPostOffice = "INVALID_POSTOFFICE";
    public const string InvalidWard = "INVALID_WARD";
    public const string InvalidCustomProperty = "INVALID_CUSTOMPROPERTY";
    public const string InvalidMemberType = "INVALID_MEMBERTYPE";
    public const string InvalidDate = "INVALID_DATE";

    public const string TemplatDoesNotExist = "TEMPLATE_DOES_NOT_EXIST";
    public const string InterestRateInvalid = "INTEREST_RATE_INVALID";
    public const string InterestRateCalculationNotMatched = "INTEREST_RATE_CALCULATION_DID_NOT_MATCH";

    public const string RoleNotFound = "ROLE_NOTFOUND";
    public const string InvalidRoleName = "INVALID_ROLENAME";

    // Role-Features
    public const string RoleNameRequired = "ROLE_NAME_REQUIRED";
    public const string RoleIdRequired = "ROLE_Id_REQUIRED";
    public const string FeaturesCantBeEmpty = "FEATES_CANT_BE_EMPTY";
    public const string FeatureIdRequired = "FEATURE_ID_REQUIRED";
    public const string FeatureNameRequired = "FEATURE_NAME_REQUIRED";
    public const string FeaturePathRequired = "FEATURE_PATH_REQUIRED";
    public const string ApiPathRequired = "API_PATH_REQUIRED";

    // UserPermission
    public const string UserPermissionDoesntExist = "USER_PERMISSION_DOESNT_EXIST";
    public const string UserPermissionIdRequired = "USER_PERMISSION_ID_REQUIRED";
    public const string FeatureRoleRequired = "FEATURE_ROLE_REQUIRED";

    public const string InvalidRole = "INVALID_ROLE";
    public const string InvalidFeature = "INVALID_FEATURE";
    public const string InvalidFeatureRoleMap = "INVALID_FEATURE_ROLE_MAP";
    public const string RolesRequired = "ROLES_REQUIRED";

    //  Buro Center

    public const string InvalidCenterId = "INVALID_CENTER_ID";

    // Buro Account
    public const string InconsistentShareHolder = "INCONSISTENTSHAREHOLDER";

    //Buro Holiday
    public const string ItemIdRequired = "ITEM_ID_REQUIRED";
    public const string InvalidDateRange = "INVALID_DATE_RANGE";
    public const string HolidayExpired = "HOLIDAY_EXPIRED";
    public const string HolidayEndDateRequired = "HOLIDAY_END_DATE_REQUIRED";
    public const string WeekendEnumListInvalid = "WEEKEND_ENUM_LIST_INVALID";
    public const string InvalidMemberInCenter = "INVALID_MEMBER_IN_CENTER";
    public const string InvalidCenter = "INVALID_CENTER";
    public const string HolidayTypeRequired = "HOLIDAY_TYPE_REQUIRED";
    public const string MemberItemIdRequired = "MEMBERITEM_ID_REQUIRED";
    public const string CenterItemIdRequired = "CENTERITEM_ID_REQUIRED";
    public const string OfficeListRequired = "OFFICE_ID_LIST_REQUIRED";
    public const string CenterListRequired = "CENTER_LIST_REQUIRED";
    public const string BranchRequired = "BRANCH_REQUIRED";

    //Buro Dms

    public const string ArchiveFlagRequired = "ARCHIVE_FLAG_REQUIRED";

    //Buro Task
    public const string InvalidTaskId = "INVALID_TASK_ID";

    //Staffing
    public const string EmployeeNotFound = "EMPLOYEE_NOT_FOUND";
    public const string ExistingEmployeeCantBeMoved = "EXISTING_ASSIGNED_EMPLOYEE_CAN'T_BE_MOVED";

    public const string InvalidIncomeRangeForSamityCriteria = "INVALID_INCOME_RANGE_FOR_SAMITY_CRITERIA";

    //Consent Form
    public const string ConsentFormNotFound = "CONSENT_FORM_NOT_FOUND";

    //Buro Register
    public const string ChequeRegisterNotFound = "CHEQUE_REGISTER_NOT_FOUND";
    public const string ChequeBookNotFound = "CHEQUE_BOOK_NOT_FOUND";
    public const string ChequeLeafNotFound = "CHEQUE_LEAF_NOT_FOUND";
    public const  string PostNotFound = "POST_NOT_FOUND";

    //Buro Voucher Configuration
    public const string TransactionCategoryItemId = "TRANSACTION_CATEGORY_ITEM_ID_REQUIRED";
    public const string VoucherScopeCategory = "VOUCHER_SCOPE_CATEGORY_REQUIRED";
    public const string VoucherGroupItemId = "VOTCHER_GROUP_ITEM_ID_REQUIRED";
    public const string VoucherNotFound = "VOTCHER_NOT_FOUND";
    public const string ManualVoucherNotFound = "MANUAL_VOCHER_NOT_FOUND";
    public const string VoucherExists = "VOTCHER_EXISTS";
    public const string VoucherConfigNotFound = "VOTCHER_CONFIG_NOT_FOUND";
    public const string ProductLineItemIdRequired = "PRODUCTLINE_ITEM_ID_REQUIRED";
    public const string ProductLineItemNotFound = "PRODUCTLINE_ITEM_NOT_FOUND";
    public const string InsufficientBalance = "INSUFFICIENT_BALANCE";
    public const string TransactionCategoryTypeNotFound = "TRANSACTION_CATEGORY_TYPE_NOT_FOUND";
    public const string VoucherCreationType = "VOUCHER_CREATION_TYPE_REQUIRED";
    public const string VoucherTransactionType = "VOUCHER_TRANSACTION_TYPE_REQUIRED";

    //Allowed Sector
    public const string SectorIdRequired = "SECTOR_ID_REQUIRED";
    public const string NameRequired = "NAME_REQUIRED";
    public const string SectorTypeRequired = "SECTOR_TYPE_REQUIRED";
    public const string SectorNotFound = "SECTOR_NOT_FOUND";
    public const string ParentIdRequired = "PARENT_ID_REQUIRED";
    public const string SectorCodeRequired = "SECTOR_CODE_REQUIRED";
    public const string MFCIBCodeCodeRequired = "MFCIB_CODE_REQUIRED";
    public const string SectorStatusRequired = "SECTOR_STATUS_REQUIRED";

    //Asset
    public const string AssetGroupCodeRequired = "ASSET_GROUP_CODE_REQUIRED";
    public const string AssetGroupNameRequired = "ASSET_GROUP_NAME_REQUIRED";
    public const string AssetGroupDoesntExists = "ASSET_GROUP_DOESNT_EXISTS";

    public const string GroupItemIdRequired = "GROUP_ITEM_ID_REQUIRED";
    public const string SubGroupNameRequired = "SUB_GROUP_NAME_REQUIRED";
    public const string SubGroupCodeRequired = "SUB_GROUP_CODE_REQUIRED";
    public const string DepreciationRateNegative = "DEPRECIATION_RATE_NEGATIVE";
    public const string DepreciationRateExceedsLimit = "DEPRECIATION_RATE_EXCEEDS_LIMIT";
    public const string PartialSaleInvalidEnumValue = "PARTIAL_SALE_INVALID_ENUM_VALUE";
    public const string SubGroupItemIdRequired = "SUB_GROUP_ITEM_ID_REQUIRED";
    public const string AssetNameRequired = "ASSET_NAME_REQUIRED";

    //Bank 
    public const string BankNotFound = "BANK_NOT_FOUND";
    public const string BankBranchNotFound = "BANK_BRANCH_NOT_FOUND";


}