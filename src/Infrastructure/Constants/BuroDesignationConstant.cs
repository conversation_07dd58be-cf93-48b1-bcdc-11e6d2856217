using Infrastructure.Enums;
using System.Collections.Immutable;

namespace Infrastructure.Constants
{
    public static class BuroDesignationConstant
    {
        public const string AdminDesignationCode = "NA";
        public const string ProgramOrganizerDesignationCode = "PO";
        public const string BranchAccountantDesignationCode = "BA";
        public const string BranchManagerDesignationCode = "BM";
        public const string AreaManagerDesignationCode = "AM";
        public const string ZoneManagerDesignationCode = "ZM";
        public const string DivisionManagerDesignationCode = "DM";
        public const string HeadQuaterManagerDesignationCode = "HM";

        public const string ProgramOrganizerDesignationFullForm = "ProgramOrganizer";
        public const string BranchAccountantDesignationFullForm = "BranchAccountant";
        public const string BranchManagerDesignationFullForm = "BranchManager";
        public const string AreaManagerDesignationFullForm = "AreaManager";
        public const string ZoneManagerDesignationFullForm = "ZoneManager";
        public const string DivisionManagerDesignationFullForm = "DivisionManager";
        public const string HeadQuaterManagerDesignationFullForm = "HeadOfficeManager";

        private static readonly Dictionary<EnumLevelOfOffice, string> managerDesignationCodeMapping = new()
        {
            { EnumLevelOfOffice.HeadOffice, HeadQuaterManagerDesignationCode},
            { EnumLevelOfOffice.DivisionOffice, DivisionManagerDesignationCode},
            { EnumLevelOfOffice.ZoneOffice, ZoneManagerDesignationCode},
            { EnumLevelOfOffice.AreaOffice, AreaManagerDesignationCode},
            { EnumLevelOfOffice.BranchOffice, BranchManagerDesignationCode},
        };
        public static readonly ImmutableDictionary<EnumLevelOfOffice, string> ManagerDesignationCodeMapping = managerDesignationCodeMapping.ToImmutableDictionary();

        private static readonly Dictionary<string, string> designationFullForm = new()
        {
            { ProgramOrganizerDesignationCode, ProgramOrganizerDesignationFullForm},
            { BranchAccountantDesignationCode, BranchAccountantDesignationFullForm},
            { BranchManagerDesignationCode, BranchManagerDesignationFullForm},
            { AreaManagerDesignationCode, AreaManagerDesignationFullForm},
            { ZoneManagerDesignationCode, ZoneManagerDesignationFullForm},
            { DivisionManagerDesignationCode, DivisionManagerDesignationFullForm},
            { HeadQuaterManagerDesignationCode, HeadQuaterManagerDesignationFullForm}
        };
        public static readonly ImmutableDictionary<string, string> DesignationFullForm = designationFullForm.ToImmutableDictionary();
    }
}
