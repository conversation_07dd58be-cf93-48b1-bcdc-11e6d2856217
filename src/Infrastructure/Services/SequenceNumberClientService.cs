using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Net.Http.Headers;
using System.Text;

namespace Infrastructure.Services
{
    public class SequenceNumberClientService : ISequenceNumberClient
    {
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IServiceClient _serviceClient;
        private readonly IBusinessConfig _businessConfig;
        private readonly AccessTokenProvider _accessTokenProvider;
        private readonly ILogger<SequenceNumberClientService> _logger;

        public SequenceNumberClientService(
            ISecurityContextProvider securityContextProvider,
            IServiceClient serviceClient,
            IBusinessConfig businessConfig,
            AccessTokenProvider accessTokenProvider,
            ILogger<SequenceNumberClientService> logger)
        {
            _securityContextProvider = securityContextProvider ?? throw new ArgumentNullException(nameof(securityContextProvider));
            _serviceClient = serviceClient ?? throw new ArgumentNullException(nameof(serviceClient));
            _businessConfig = businessConfig;
            _accessTokenProvider = accessTokenProvider;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<SequenceNumberQueryResponse> GetSequenceNumber(SequenceNumberQuery sequenceNumberQuery)
        {
            if (sequenceNumberQuery == null)
            {
                throw new ArgumentNullException(nameof(sequenceNumberQuery));
            }

            var securityContext = _securityContextProvider.GetSecurityContext();
            var stsAccessToken = await _accessTokenProvider.CreateFromAccessTokenAsync(securityContext.OauthBearerToken);

            var apiUrl = _businessConfig.GetSequenceNumberServiceBaseUrl();
            var apiVerson = _businessConfig.GetSequenceNumberServiceVersion();

            var sequenceNumberApiUrl = apiUrl + apiVerson + $"/SequenceNumberService/Sequence/Next?Context={sequenceNumberQuery.Context}";

            _logger.LogInformation("Sequence Number Query :: {Context}", sequenceNumberQuery.Context?.ToString());

            try
            {
                var sequenceResponse = await CallSequenceNumberServiceAsHttpPost(sequenceNumberApiUrl, sequenceNumberQuery, stsAccessToken);

                var resultString = await sequenceResponse.Content.ReadAsStringAsync();
                var response = JsonConvert.DeserializeObject<SequenceNumberQueryResponse>(resultString);

                if (sequenceResponse.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Successfully retrieved sequence number. Response: {Response}", resultString);
                }
                else
                {
                    _logger.LogWarning("Failed to retrieve sequence number. Response: {Response}", resultString);
                }

                return response ?? new SequenceNumberQueryResponse();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inside SequenceNumberClient");
                throw new InvalidOperationException("An error occurred during the SequenceNumberClient request.", ex);
            }
        }

        private async Task<HttpResponseMessage> CallSequenceNumberServiceAsHttpPost(string path, SequenceNumberQuery payload, string token)
        {
            var requestMessage = new HttpRequestMessage()
            {
                RequestUri = new Uri(path),
                Method = HttpMethod.Get
            };

            var requestBody = JsonConvert.SerializeObject(payload);

            requestMessage.Headers.Authorization = new AuthenticationHeaderValue("bearer", token);
            requestMessage.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");

            var httpResponse = await _serviceClient.SendToHttpAsync(requestMessage);

            return httpResponse;
        }
    }
}
