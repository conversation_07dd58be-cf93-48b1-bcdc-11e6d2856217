using Infrastructure.Constants;
using Infrastructure.Contracts;
using MongoDB.Bson;
using MongoDB.Driver;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Linq.Expressions;

namespace Infrastructure.Services;

public class BusinessRepository : IBusinessRepository
{
    private readonly IBlocksMongoDbDataContextProvider _ecapMongoDbDataContextProvider;
    private readonly IRepository _repository;

    public BusinessRepository(IBlocksMongoDbDataContextProvider ecapMongoDbDataContextProvider, IRepository repository)
    {
        this._ecapMongoDbDataContextProvider = ecapMongoDbDataContextProvider;
        this._repository = repository;
    }

    private IMongoDatabase GetDataContext()
    {
        return this._ecapMongoDbDataContextProvider.GetTenantDataContext(BuroConstants.TenantId);
    }

    public IMongoCollection<T> GetCollectionByName<T>(string collectionName, MongoCollectionSettings settings = null)
    {
        return this.GetDataContext().GetCollection<T>(collectionName, settings);
    }

    public IMongoCollection<T> GetCollection<T>(MongoCollectionSettings settings = null)
    {
        return this.GetCollectionByName<T>(typeof(T).Name + "s", settings);
    }

    public IFindFluent<T, T> Find<T>(Expression<Func<T, bool>> expression, FindOptions options = null, MongoCollectionSettings settings = null)
    {
        var filter = Builders<T>.Filter.Where(expression);
        return this.GetCollection<T>().Find(filter);
    }

    public IFindFluent<T, T> Find<T>(
        FilterDefinition<T> filter,
        FindOptions options = null,
        MongoCollectionSettings settings = null
    )
    {
        return this.GetCollection<T>(settings).Find(filter, options);
    }

    public Task<UpdateResult> UpdateOneAsync<T>(FilterDefinition<T> filter, UpdateDefinition<T> update,
        UpdateOptions options = null)
    {
        return this.GetCollection<T>().UpdateOneAsync(filter, update, options);
    }

    public Task<UpdateResult> UpdateOneAsync<T>(
        Expression<Func<T, bool>> dataFilters,
        UpdateDefinition<T> update,
        UpdateOptions options = null
    )
    {
        return this.GetCollection<T>().UpdateOneAsync(Builders<T>.Filter.Where(dataFilters), update, options);
    }

    public int GetCountByFilterDefinition<T>(FilterDefinition<T> filterDefinition)
    {
        return (int)this.GetCollection<T>().Find(filterDefinition).CountDocuments();
    }

    public async Task<int> GetCountByFilterDefinitionAsync<T>(FilterDefinition<T> filterDefinition)
    {
        return (int)(await this.GetCollection<T>().Find(filterDefinition).CountDocumentsAsync());
    }

    public Task DeleteAsync<T>(Expression<Func<T, bool>> dataFilters)
    {
        var filter = Builders<T>.Filter.Where(dataFilters);
        return this.GetCollection<T>().DeleteManyAsync(filter);
    }


    public Task<T> GetItemAsync<T>(Expression<Func<T, bool>> dataFilters)
    {
        return this._repository.GetItemAsync(dataFilters);
    }

    public Task<List<T>> GetItemsAsync<T>(Expression<Func<T, bool>> dataFilters)
    {
        return this.GetItemsAsync(Builders<T>.Filter.Where(dataFilters));
    }

    public async Task<List<T>> GetItemsAsync<T>(FilterDefinition<T> filterDefinition, FindOptions<T, T> findOptions = null)
    {
        return await (await this.GetCollection<T>().FindAsync(filterDefinition, findOptions)).ToListAsync();
    }

    public Task<long> CountDocumentsAsync<T>(Expression<Func<T, bool>> dataFilters)
    {
        return this.CountDocumentsAsync(Builders<T>.Filter.Where(dataFilters));
    }

    public Task<long> CountDocumentsAsync<T>(FilterDefinition<T> filterDefinition)
    {
        return this.GetCollection<T>().CountDocumentsAsync(filterDefinition);
    }

    public Task InsertOneAsync<T>(T data)
    {
        return this.GetCollection<T>().InsertOneAsync(data);
    }

    public Task InsertManyAsync<T>(List<T> dataList)
    {
        return this.GetCollection<T>().InsertManyAsync(dataList);
    }

    public Task UpdateManyAsync<T>(FilterDefinition<T> filterDefinition, UpdateDefinition<T> update)
    {
        return this.GetCollection<T>().UpdateManyAsync(filterDefinition, update);
    }

    public Task DeleteOneAsync<T>(Expression<Func<T, bool>> dataFilters)
    {
        return this.GetCollection<T>().DeleteOneAsync(dataFilters);
    }

    public Task<List<BsonDocument>> GetDistinctCountByField<T>(
        FilterDefinition<T> filterDefinition,
        string fieldName,
        string? categoryName)
    {
        return this.GetCollection<T>().Aggregate().Match(filterDefinition).Group(new BsonDocument
            {
                { "_id", "$" + fieldName },
                { "count", new BsonDocument("$sum", 1) },
                { "category", new BsonDocument("$first", "$" + categoryName) }
            }).Sort(new BsonDocument("_id", 1)).ToListAsync();

    }

    public async Task<List<TResult>> FindWithProjectionAsync<T, TResult>(
        Expression<Func<T, bool>>? filterExpression = null,
        Expression<Func<T, TResult>>? projectionExpression = null)
    {
        var collection = GetCollection<T>();

        var filterDefinition = filterExpression == null
            ? Builders<T>.Filter.Empty
            : Builders<T>.Filter.Where(filterExpression);

        var query = collection.Find(filterDefinition);

        if (projectionExpression != null)
        {
            return await query.Project(projectionExpression).ToListAsync();
        }

        var result = await query.ToListAsync();

        return (typeof(T) == typeof(TResult))
            ? result.Cast<TResult>().ToList()
            : throw new InvalidOperationException("A projection is required when Main Entity and Result Entity are of different types");
    }
}