using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Configuration;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services;

public class BusinessConfig : IBusinessConfig
{
    private readonly IConfiguration _configuration;
    private readonly IAppSettings _appSettings;

    public BusinessConfig(IConfiguration configuration, IAppSettings appSettings)
    {
        _configuration = configuration;
        _appSettings = appSettings;
    }

    public string TenantId => BuroConstants.TenantId;
    public string SiteId => "9dae390a-e2b0-4941-9d26-f75f4f7048ab";
    public string SiteName => "BuroBD";
    public string DefaultLanguage => BuroConstants.DefaultLanguage;
    public string DefaultAdminEmail => "<EMAIL>";
    public string DefaultUserId => "e2e03735-058a-4b8c-bfe3-ea09246215b0";

    public string GetUamBaseUrl()
    {
        return _configuration.GetSection("UamBaseUrl").Value;
    }

    public string GetUamVersion()
    {
        return _configuration.GetSection("UamVersion").Value;
    }

    public string GetResetPasswordUrl()
    {
        return $"{GetUamBaseUrl()}/{GetUamVersion()}/UserAccessManagement/SecurityCommand/ResetPassword";
    }
    public string CreateUserUrl()
    {
        return $"{GetUamBaseUrl()}/{GetUamVersion()}/UserAccessManagement/SecurityCommand/CreateUser";
    }

    public string GetPortalOrigin()
    {
        return _configuration.GetSection("Origin").Value;
    }

    public string GetStsTokenUrl()
    {
        return GetStsBaseUrl() + $"StsService/AccessToken/CreateForSystemUserAsync?sk={GetStsKey()}";
    }

    public string GetStsKey()
    {
        return _appSettings.StsSecret;
    }

    public string GetSmsServerUrl()
    {
        return _configuration.GetSection("SmsServerUrl").Value;
    }

    public string GetSmsServerVersion()
    {
        return _configuration.GetSection("SmsServerVersion").Value;
    }

    private string GetStsBaseUrl()
    {
        return _configuration.GetSection("StsBaseUrl").Value;
    }

    public GoogleCaptchaSettings GetCaptchaSettings()
    {
        var section = _configuration.GetSection("Captcha");
        var clientKey = section.GetSection("ClientKey").Value;
        var serverKey = section.GetSection("ServerKey").Value;
        return new GoogleCaptchaSettings
        {
            ClientKey = clientKey,
            ServerKey = serverKey
        };
    }

    public string GetGoogleCaptchaVerificationUrl()
    {
        return this._configuration.GetSection("GoogleCaptchaVerificationUrl").Value;
    }

    public string GetSequenceNumberServiceBaseUrl()
    {
        return this._configuration.GetSection("SequenceNumberServiceBaseUrl").Value;
    }

    public string GetSequenceNumberServiceVersion()
    {
        return this._configuration.GetSection("SequenceNumberServiceVersion").Value;
    }

    public string GetSylviaBaseUrl()
    {
        return this._configuration.GetSection("SylviaBaseUrl").Value;
    }

    public string GetRequestedMfcibFilePath()
    {
        return this._configuration.GetSection("RequestedMfcibFilePath").Value;
    }

    public string GetMfcibExcelTemplatePath()
    {
        return this._configuration.GetSection("MfcibExcelTemplatePath").Value;
    }

    public string GetNumberOfRetries()
    {
        return this._configuration.GetSection("NumberOfRetries").Value;
    }

    public string GetDelaySecondsBetweenRetries()
    {
        return this._configuration.GetSection("DelaySecondsBetweenRetries").Value;
    }

    public string GetMaxDelaySecondsBetweenRetries()
    {
        return this._configuration.GetSection("MaxDelaySecondsBetweenRetries").Value;
    }

    public string GetTransactionServiceSettings()
    {
        return _configuration.GetSection("TransactionServiceBaseUrl").Value;
    }
}