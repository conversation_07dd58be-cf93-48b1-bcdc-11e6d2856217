using Infrastructure.Constants;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Reflection;

namespace Infrastructure.Services
{
    public class ExcelGenerationService : IExcelGenerationService
    {
        private readonly ILogger<ExcelGenerationService> _logger;
        private readonly IRepository _repository;

        public ExcelGenerationService(
            ILogger<ExcelGenerationService> logger,
            IRepository repository)
        {
            _logger = logger;
            _repository = repository;
        }

        public async Task GenerateAsync<T>(List<T> data, string templatePath, string destinationPath, EnumExcelTemplateCategory category)
        {
            _logger.LogInformation("Start Generating Excel");

            var mapping = await GetExcelTemplateMappingAsync(category);
            var templateFilePath = Path.Combine(templatePath, mapping.TemplateFileName);
            var headerToPropertyMappings = mapping.HeaderToPropertyMappings
                .ToDictionary(m => m.HeaderName, m => m.Propertyname);

            using var fileStream = new FileStream(templateFilePath, FileMode.Open, FileAccess.Read);
            var templateWorkbook = new XSSFWorkbook(fileStream);
            var creationHelper = templateWorkbook.GetCreationHelper();
            var sheet = templateWorkbook.GetSheetAt(0);

            var startingRowNumber = 1;
            var headers = ExtractHeaderNamesFromTemplate(sheet, startingRowNumber);

            if (!headers.Any())
            {
                throw new InvalidOperationException("No header defined in the Excel template");
            }

            PopulateDataInExcel(data, headerToPropertyMappings, creationHelper, sheet, startingRowNumber, headers);

            using var outputFileStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write);
            templateWorkbook.Write(outputFileStream);
        }

        private static void PopulateDataInExcel<T>(
            List<T> data,
            Dictionary<string, string> headerToPropertyMappings,
            ICreationHelper creationHelper,
            ISheet sheet,
            int startingRowNumber,
            List<string> headers)
        {
            var rowIndex = startingRowNumber + 1;

            foreach (var item in data)
            {
                var newRow = sheet.CreateRow(rowIndex);
                var properties = typeof(T).GetProperties();

                for (int colIndex = 0; colIndex < headers.Count; colIndex++)
                {
                    var property = GetPropertyInfoAssociatedWithHeader(headerToPropertyMappings, headers, properties, colIndex);

                    CreateNewCellWithValue(creationHelper, item, newRow, colIndex, property);
                }

                rowIndex++;
            }
        }

        private static List<string> ExtractHeaderNamesFromTemplate(ISheet sheet, int startingRowNumber)
        {
            var headerRow = sheet.GetRow(startingRowNumber);
            var headers = new List<string>();

            for (int colIndex = 0; colIndex < headerRow.LastCellNum; colIndex++)
            {
                var cell = headerRow.GetCell(colIndex);
                headers.Add(cell?.ToString()?.Trim() ?? string.Empty);
            }

            return headers;
        }

        private static void CreateNewCellWithValue<T>(ICreationHelper creationHelper, T? item, IRow newRow, int colIndex, PropertyInfo? property)
        {
            if (property == null)
            {
                return;
            }

            var value = property.GetValue(item);
            var cell = newRow.CreateCell(colIndex);

            if (value != null && !IsComplexType(property.PropertyType))
            {
                if (value is int intValue)
                {
                    cell.SetCellValue(intValue);
                }
                else if (value is DateTime dateTimeValue)
                {
                    cell.SetCellValue(dateTimeValue);
                }
                else
                {
                    cell.SetCellValue(creationHelper.CreateRichTextString(value.ToString()));
                }
            }
        }

        private static bool IsComplexType(Type type)
        {
            return !type.IsPrimitive && type != typeof(string) && !type.IsValueType;
        }

        private static PropertyInfo? GetPropertyInfoAssociatedWithHeader(
            Dictionary<string, string> headerToPropertyMappings,
            List<string> headers,
            PropertyInfo[] properties,
            int colIndex)
        {
            var header = headers[colIndex];
            var mappedPropertyName = headerToPropertyMappings.GetValueOrDefault(header);
            var property = !string.IsNullOrWhiteSpace(mappedPropertyName)
                ? Array.Find(properties, p => string.Equals(p.Name, mappedPropertyName, StringComparison.OrdinalIgnoreCase))
                : null;

            return property;
        }

        private async Task<BuroExcelTemplateMapping> GetExcelTemplateMappingAsync(EnumExcelTemplateCategory category)
        {
            return await _repository.GetItemAsync<BuroExcelTemplateMapping>(m => m.Category == category)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroExcelTemplateMapping)));
        }
    }
}
