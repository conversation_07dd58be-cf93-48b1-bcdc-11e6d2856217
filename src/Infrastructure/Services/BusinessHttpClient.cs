using Infrastructure.Contracts;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace Infrastructure.Services;

public class BusinessHttpClient : IBusinessHttpClient
{
    private readonly HttpClient _httpClient = new();

    public async Task<TResponse?> GetDataAsync<TResponse>(string url)
    {
        var httpClient = new HttpClient();
        var response = await httpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();

        var result = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<TResponse>(result);
    }

    public async Task<TResponse?> PostAsJsonAsync<TResponse>(string url, HttpContent httpContent)
    {
        var httpClient = new HttpClient();
        var response = await httpClient.PostAsJsonAsync(url, httpContent);
        response.EnsureSuccessStatusCode();

        var result = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<TResponse>(result);
    }

    public async Task<TResponse?> PostAsJsonAsync<TResponse, TRequest>(string url, TRequest tRequest,
        string accessToken = "")
    {
        var httpClient = new HttpClient();

        if (!string.IsNullOrEmpty(accessToken))
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("bearer", accessToken);

        var response = await httpClient.PostAsJsonAsync(url, tRequest);
        response.EnsureSuccessStatusCode();

        var result = await response.Content.ReadAsStringAsync();
        return JsonConvert.DeserializeObject<TResponse>(result);
    }

    public async Task<HttpResponseHeaders> GetHeaders(string url)
    {
        var httpClient = new HttpClient();
        var response = await httpClient.SendAsync(new HttpRequestMessage(HttpMethod.Head, url));

        return response.Headers;
    }

    public Task<HttpResponseMessage> SendToHttpAsync(HttpRequestMessage request)
    {
        return _httpClient.SendAsync(request);
    }
}