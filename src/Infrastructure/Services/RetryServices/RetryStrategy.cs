using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Retry;

namespace Infrastructure.Services.RetryServices
{
    public class RetryStrategy : IRetryStrategy
    {
        private readonly Random _random;
        private readonly int _maxRetries;
        private readonly int _baseDelayMilliseconds;
        private readonly int _maxDelayMilliseconds;

        public int MaxRetries => _maxRetries;

        public RetryStrategy(IBusinessConfig businessConfig)
        {
            _random = new Random();

            _maxRetries = int.TryParse(businessConfig.GetNumberOfRetries(), out int numberOfRetries)
                ? numberOfRetries
                : RetryExecutorConstants.DefaultNumberOfRetries;

            _baseDelayMilliseconds = int.TryParse(businessConfig.GetDelaySecondsBetweenRetries(), out int delaySecondsBetweenRetries)
                ? delaySecondsBetweenRetries * 1000
                : RetryExecutorConstants.DefaultDelaySecondsBetweenRetries;

            _maxDelayMilliseconds = int.TryParse(businessConfig.GetMaxDelaySecondsBetweenRetries(), out int maxDelaySecondsBetweenRetries)
                ? delaySecondsBetweenRetries * 1000
                : RetryExecutorConstants.DefaultMaxDelaySecondsBetweenRetries;
        }

        public Task DelayAsync(int attempt, CancellationToken cancellationToken)
        {
            var delay = CalculateDelay(attempt);

            return Task.Delay(delay, cancellationToken);
        }

        public void Delay(int attempt)
        {
            var delay = CalculateDelay(attempt);

            Thread.Sleep(delay);
        }

        private int CalculateDelay(int attempt)
        {
            // NOSONAR BaseDelay exponentially increases with each attempt: 1s, 2s, 4s, 8s, etc.
            // NOSONAR Jitter adds a random variation within a range of plus or minus 20% to the delay to prevent retry storms and synchronized traffic spikes.

            var baseDelay = Math.Min(_baseDelayMilliseconds * (1 << attempt), _maxDelayMilliseconds);
            var jitter = _random.Next((int)(baseDelay * 0.8), (int)(baseDelay * 1.2));

            return jitter;
        }
    }
}
