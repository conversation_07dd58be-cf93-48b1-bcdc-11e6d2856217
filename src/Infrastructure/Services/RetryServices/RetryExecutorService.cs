using Infrastructure.Contracts.Retry;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Services.RetryServices
{
    public class RetryExecutorService : IRetryExecutorService
    {
        private readonly ILogger<RetryExecutorService> _logger;
        private readonly IRetryStrategy _retryStrategy;

        public RetryExecutorService(
            ILogger<RetryExecutorService> logger,
            IRetryStrategy retryStrategy)
        {
            _logger = logger;
            _retryStrategy = retryStrategy;
        }

        public void Execute(Action action)
        {
            _logger.LogInformation("Executing action {ActionName}", action.Method.Name);

            RetrySync(() =>
            {
                action();

                return true;
            },
            action.Method.Name);
        }

        public TResult? Execute<TResult>(Func<TResult> func)
        {
            _logger.LogInformation("Executing function {FunctionName}", func.Method.Name);

            return RetrySync(func, func.Method.Name);
        }

        public Task ExecuteAsync(Func<Task> action, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Executing async action {ActionName}", action.Method.Name);

            return RetryAsync(async () =>
            {
                await action();

                return true;
            },
            action.Method.Name,
            cancellationToken);
        }

        public Task<TResult?> ExecuteAsync<TResult>(Func<Task<TResult>> func, CancellationToken cancellationToken = default)
        {
            _logger.LogInformation("Executing async function {FunctionName}", func.Method.Name);

            return RetryAsync(func, func.Method.Name, cancellationToken);
        }

        #region Private
        private TResult? RetrySync<TResult>(Func<TResult> func, string methodName)
        {
            _logger.LogInformation("Retrying function {FunctionName}", methodName);

            Exception? lastException = null;
            var attempt = 0;

            _logger.LogInformation("Starting first execution attempt for method '{MethodName}' with retry strategy (MaxRetries={MaxRetries})", methodName, _retryStrategy.MaxRetries);

            while (attempt <= _retryStrategy.MaxRetries)
            {
                try
                {
                    return func();
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    if (attempt <= _retryStrategy.MaxRetries)
                    {
                        _retryStrategy.Delay(attempt);
                    }

                    attempt++;

                    _logger.LogInformation("Retry attempt {Attempt} for {Name}", attempt, methodName);
                }
            }

            throw new InvalidOperationException($"Error after {attempt} tries of {methodName}", lastException);
        }

        private async Task<TResult?> RetryAsync<TResult>(Func<Task<TResult>> func, string methodName, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Retrying async function {FunctionName}", methodName);

            Exception? lastException = null;
            var attempt = 0;

            _logger.LogInformation("Starting first execution attempt for method '{MethodName}' with retry strategy (MaxRetries={MaxRetries})", methodName, _retryStrategy.MaxRetries);

            while (attempt <= _retryStrategy.MaxRetries)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    return await func();
                }
                catch (Exception ex)
                {
                    lastException = ex;

                    if (attempt <= _retryStrategy.MaxRetries)
                    {
                        await _retryStrategy.DelayAsync(attempt, cancellationToken);
                    }

                    attempt++;

                    _logger.LogInformation("Retry attempt {Attempt} for {Name}", attempt, methodName);
                }
            }

            throw new InvalidOperationException($"Error after {attempt} tries of {methodName}", lastException);
        }
        #endregion
    }
}
