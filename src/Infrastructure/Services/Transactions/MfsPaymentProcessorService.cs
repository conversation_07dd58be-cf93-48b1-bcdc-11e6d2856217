using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class MfsPaymentProcessorService : IPaymentProcessorFactory
    {
        private readonly ILogger<MfsPaymentProcessorService> _logger;
        public EnumTransactionMedium TransactionMedium => EnumTransactionMedium.MFS;

        public MfsPaymentProcessorService(ILogger<MfsPaymentProcessorService> logger)
        {
            _logger = logger;
        }

        public Task ExecutePaymentAsync()
        {
            _logger.LogInformation("Executing MFS payment processing.");
            throw new NotImplementedException();
        }
    }
}
