using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class LoanInstallmentRepaymentService : IAccountTransactionFactory
    {
        public EnumBuroTransactionType TransactionType => EnumBuroTransactionType.LoanInstallmentRepayment;
        private readonly ILogger<LoanInstallmentRepaymentService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;

        public LoanInstallmentRepaymentService(
            ILogger<LoanInstallmentRepaymentService> logger, 
            IFinMongoDbRepository finMongoDbRepository, 
            ISecurityContextProvider securityContextProvider, 
            IInfrastructureSharedService sharedService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
        }

        public async Task ProcessAsync(string productAccountTransactionRequestItemId)
        {
            _logger.LogInformation("Starting Loan Installment repayment transaction processing for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);

            var currentContext = GetCurrentUserId();
            var transactionRequest = await GetProductAccountTransactionAsync(productAccountTransactionRequestItemId);
            var loanAccount = await GetLoanAccountByItemIdAsync(transactionRequest);

            await _sharedService.InitiateProductAccountTransactionAsync(true, transactionRequest);

            await UpdateMemberScheduleAsync(transactionRequest, currentContext.UserId);
            await UpdateLoanAccountAsync(transactionRequest, loanAccount, currentContext.UserId);
            await UpdateProductAccountTransactionAsync(productAccountTransactionRequestItemId, currentContext.UserId);

            await HandleVoucherCreationProcess();
            await SendDepositNotificationAsync(currentContext, productAccountTransactionRequestItemId, transactionRequest);

            _logger.LogInformation("Loan Installment repayment transaction processing completed for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);
        }

        private async Task UpdateMemberScheduleAsync(BuroProductAccountTransactionRequest transactionRequest, string userId)
        {
            ArgumentNullException.ThrowIfNull(transactionRequest.MemberScheduleItemId);

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMemberSchedule.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroMemberSchedule.LastUpdatedBy), userId},
                {nameof(BuroMemberSchedule.PaymentStatus), PaymentStatus.Paid},
                {nameof(BuroMemberSchedule.PaidAmount), transactionRequest.Amount}
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroMemberSchedule>(a => a.ItemId == transactionRequest.MemberScheduleItemId, properties);
        }

        private async Task UpdateProductAccountTransactionAsync(string productAccountTransactionRequestItemId, string userId)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroProductAccountTransactionRequest.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroProductAccountTransactionRequest.LastUpdatedBy), userId},
                {nameof(BuroProductAccountTransactionRequest.Status), EnumProductAccountTransactionStatus.Success},
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroProductAccountTransactionRequest>(a => a.ItemId == productAccountTransactionRequestItemId, properties);
        }

        private async Task UpdateLoanAccountAsync(
            BuroProductAccountTransactionRequest transactionRequest,
            BuroLoanAccount loanAccount,
            string userId)
        {
            var newBalance = loanAccount.Balance - transactionRequest.Amount;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroLoanAccount.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroLoanAccount.LastUpdatedBy), userId},
                {nameof(BuroLoanAccount.Balance), newBalance},
                { nameof(BuroLoanAccount.OverDueAmount), 0 },
                { nameof(BuroLoanAccount.LateFeeAmount), 0 }
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroLoanAccount>(a => a.ItemId == loanAccount.ItemId, properties);
        }

        private async Task HandleVoucherCreationProcess()
        {
            _logger.LogInformation("Starting voucher creation process for Loan Installment repayment transaction.");

            // NOSONAR We need to generate multiple vouchers based on the member and the current branch.
            // NOSONAR This task will be completed once the Voucher module is finished.
            await Task.Delay(1000);
        }

        private SecurityContext GetCurrentUserId()
        {
            return _securityContextProvider.GetSecurityContext();
        }

        private async Task<BuroProductAccountTransactionRequest> GetProductAccountTransactionAsync(string productAccountTransactionRequestItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == productAccountTransactionRequestItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        }

        private async Task<BuroLoanAccount> GetLoanAccountByItemIdAsync(BuroProductAccountTransactionRequest transaction)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroLoanAccount>(
                a => a.ItemId == transaction.AccountItemId && a.MemberItemId == transaction.MemberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroLoanAccount)));
        }

        private async Task SendDepositNotificationAsync(
            SecurityContext securityContext,
            string productAccountTransactionRequestItemId,
            BuroProductAccountTransactionRequest transaction)
        {
            var responseKey = BuroNotificationKeys.LoanAmountDeposited;

            var notificationPayload = new
            {
                TransactionItemId = productAccountTransactionRequestItemId,
                transaction.MemberItemId,
                transaction.MemberName,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                productAccountTransactionRequestItemId,
                securityContext.UserId);
        }
    }
}
