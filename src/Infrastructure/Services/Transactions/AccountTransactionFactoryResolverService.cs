using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class AccountTransactionFactoryResolverService : IAccountTransactionFactoryResolverService
    {
        private readonly ILogger<AccountTransactionFactoryResolverService> _logger;
        private readonly Dictionary<EnumBuroTransactionType, IAccountTransactionFactory> _factories;

        public AccountTransactionFactoryResolverService(
            ILogger<AccountTransactionFactoryResolverService> logger,
            IEnumerable<IAccountTransactionFactory> factories)
        {
            _logger = logger;
            _factories = factories.ToDictionary(f => f.TransactionType, f => f);
        }

        public IAccountTransactionFactory GetFactory(EnumBuroTransactionType transactionType)
        {
            _logger.LogInformation("Resolving factory for transaction type: {TransactionType}", transactionType);

            if (_factories.TryGetValue(transactionType, out var factory))
            {
                return factory;
            }

            throw new ArgumentException($"No factory found for transaction type: {transactionType}");
        }
    }
}
