using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class CSWithdrawalApprovalEventService : ICSWithdrawalApprovalEventService
    {
        private readonly ILogger<CSWithdrawalApprovalEventService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;
        public CSWithdrawalApprovalEventService(
            ILogger<CSWithdrawalApprovalEventService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider,
            IInfrastructureSharedService sharedService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
        }

        public async Task ProcessCSWithdrawalApprovalAsync(CSWithdrawalApprovalEvent command)
        {
            _logger.LogInformation("Processing CS Withdrawal Approval for transaction request Id: {ProductAccountTransactionRequestItemId}", command.ProductAccountTransactionRequestItemId);

            var currentContext = GetCurrentUserId();
            var isTransactionAllowed = command.IsAccepted;

            if (isTransactionAllowed)
            {
                var transactionRequest = await GetAccountTransactionRequestByItemIdAsync(command.ProductAccountTransactionRequestItemId);
                var csAccount = await GetCSAccountByItemIdAsync(transactionRequest);

                isTransactionAllowed = transactionRequest.Amount == csAccount.Balance;

                if (isTransactionAllowed)
                {
                    await _sharedService.InitiateProductAccountTransactionAsync(true, transactionRequest);

                    (double interestRate, double withdrawalAmount, bool isCSMatured) = await GetCalculatedRateForCSWithdrawl(csAccount);
                    await UpdateCSAccount(transactionRequest, csAccount, currentContext.UserId, interestRate, withdrawalAmount, isCSMatured);
                    await HandleVoucherCreationProcess();
                }
            }

            await UpdateProductAccountTransactionAsync(command.ProductAccountTransactionRequestItemId, currentContext.UserId, isTransactionAllowed);
            await SendWithdralNotificationAsync(currentContext, command.ProductAccountTransactionRequestItemId, isTransactionAllowed);

            _logger.LogInformation("CS Withdrawal Approval processed successfully for transaction request Id: {ProductAccountTransactionRequestItemId}", command.ProductAccountTransactionRequestItemId);
        }

        private async Task<(double interestRate, double withdrawalAmount, bool isCSMatured)> GetCalculatedRateForCSWithdrawl(BuroContractualSavingsAccount csAccount)
        {
            double interestRate = -1;
            double withdrawalAmount = -1;
            bool isCSMatured = csAccount.AccountStatus == EnumProductAccountStatus.Matured;

            if (isCSMatured)
            {
                interestRate = csAccount.InterestRate;
                withdrawalAmount = csAccount.Balance;
                return (interestRate, withdrawalAmount, isCSMatured);
            }

            var subscribedProductLine = await _finMongoDbRepository.FindOneAsync<ContractualSavingProductLine>(r => r.ItemId == csAccount.ProductLineItemId);
            if (subscribedProductLine == null)
            {
                throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(ContractualSavingProductLine)));
            }


            // check if has custom rate
            if (subscribedProductLine.EarlyWithdrawalInterestRateDetails.Rate != null)
            {
                interestRate = (double)subscribedProductLine.EarlyWithdrawalInterestRateDetails.Rate;
                withdrawalAmount = interestRate != 0 ? csAccount.Balance * interestRate : csAccount.Balance;
                return (interestRate, withdrawalAmount, isCSMatured);
            }

            // check for nearest lower productline rate
            var relatedProductLineList = await _finMongoDbRepository.FindAsync<ContractualSavingProductLine>(r => r.ProductItemId == subscribedProductLine.ProductItemId);
            var nearestProductLine = FindClosestLowerProductLine(relatedProductLineList, csAccount.ActualTenureDetails.Duration);
            if (nearestProductLine != null)
            {
                interestRate = nearestProductLine.InterestRate;
                withdrawalAmount = interestRate != 0 ? csAccount.Balance * interestRate : csAccount.Balance;
                return (interestRate, withdrawalAmount, isCSMatured);
            }

            // if no minimum year is met, get GS's %
            var relatedGSAccount = await _finMongoDbRepository.FindOneAsync<BuroGeneralSavingsAccount>(r => r.MemberItemId == csAccount.MemberItemId);
            if (relatedGSAccount == null)
            {
                throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroGeneralSavingsAccount)));
            }

            interestRate = relatedGSAccount.InterestRate;
            withdrawalAmount = interestRate != 0 ? csAccount.Balance * interestRate : csAccount.Balance;
            return (interestRate, withdrawalAmount, isCSMatured);
        }

        private static ContractualSavingProductLine FindClosestLowerProductLine(List<ContractualSavingProductLine> productLines, Period inputPeriod)
        {
            int inputDays = inputPeriod.ToDays();

            ContractualSavingProductLine closestProductLine = null;
            int closestTenureDays = int.MinValue;

            foreach (var productLine in productLines)
            {
                foreach (var tenure in productLine.AllowedTenures)
                {
                    int tenureDays = tenure.Duration.ToDays();

                    if (tenureDays <= inputDays && tenureDays > closestTenureDays)
                    {
                        closestTenureDays = tenureDays;
                        closestProductLine = productLine;
                    }
                }
            }

            return closestProductLine;
        }


        private async Task<BuroProductAccountTransactionRequest> GetAccountTransactionRequestByItemIdAsync(string ProductAccountTransactionRequestItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == ProductAccountTransactionRequestItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        }

        private async Task UpdateProductAccountTransactionAsync(string ProductAccountTransactionRequestItemId, string userId, bool isTransactionAllowed)
        {
            var status = isTransactionAllowed ? EnumProductAccountTransactionStatus.Success : EnumProductAccountTransactionStatus.Failed;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroProductAccountTransactionRequest.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroProductAccountTransactionRequest.LastUpdatedBy), userId},
                {nameof(BuroProductAccountTransactionRequest.Status), status},
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroProductAccountTransactionRequest>(a => a.ItemId == ProductAccountTransactionRequestItemId, properties);
        }

        private async Task UpdateCSAccount(
            BuroProductAccountTransactionRequest transaction,
            BuroContractualSavingsAccount csAccount,
            string userId,
            double interestRate,
            double withdrawalAmount,
            bool isCSMatured)
        {
            var newBalance = csAccount.Balance - transaction.Amount;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroContractualSavingsAccount.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroContractualSavingsAccount.LastUpdatedBy), userId},
                {nameof(BuroContractualSavingsAccount.Balance), newBalance},
                {nameof(BuroContractualSavingsAccount.AccountStatus), EnumProductAccountStatus.Completed },
                {nameof(BuroContractualSavingsAccount.IsDisbursed), true },
                {nameof(BuroContractualSavingsAccount.WithdrawlDate), DateTime.UtcNow },
                {nameof(BuroContractualSavingsAccount.WithdrawlPercentrage), interestRate },
                {nameof(BuroContractualSavingsAccount.WithdrawlAmount), withdrawalAmount },
                {nameof(BuroContractualSavingsAccount.IsEarlyWithdrwan), !isCSMatured },
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroGeneralSavingsAccount>(a => a.ItemId == csAccount.ItemId, properties);
        }

        private static async Task HandleVoucherCreationProcess()
        {
            // NOSONAR We need to generate multiple vouchers based on the member and the current branch.
            // NOSONAR This task will be completed once the Voucher module is finished.
            await Task.Delay(1000);
        }

        private async Task SendWithdralNotificationAsync(
            SecurityContext securityContext,
            string productAccountTransactionRequestItemId,
            bool transactionAllowed)
        {
            var responseKey = BuroNotificationKeys.GSAmountWithdrawal;

            var notificationPayload = new
            {
                ProductAccountTransactionRequestItemId = productAccountTransactionRequestItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                IsTransactionCompleted = transactionAllowed
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                productAccountTransactionRequestItemId,
                securityContext.UserId);
        }

        private async Task<BuroContractualSavingsAccount> GetCSAccountByItemIdAsync(BuroProductAccountTransactionRequest transaction)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroContractualSavingsAccount>(
                a => a.ItemId == transaction.AccountItemId
                  && a.MemberItemId == transaction.MemberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroContractualSavingsAccount)));
        }

        private SecurityContext GetCurrentUserId()
        {
            return _securityContextProvider.GetSecurityContext();
        }


    }
}
