using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class TransactionContextFactoryResolverService : ITransactionContextFactoryResolverService
    {
        private readonly ILogger<TransactionContextFactoryResolverService> _logger;
        private readonly Dictionary<EnumProductType, ITransactionContextFactory> _factories;

        public TransactionContextFactoryResolverService(
            ILogger<TransactionContextFactoryResolverService> logger,
            IEnumerable<ITransactionContextFactory> factories)
        {
            _logger = logger;
            _factories = factories.ToDictionary(f => f.ProductType, f => f);
        }

        public ITransactionContextFactory GetFactory(EnumProductType productType)
        {
            _logger.LogInformation("Resolving factory for product type: {ProductType}", productType);

            if (_factories.TryGetValue(productType, out var factory))
            {
                return factory;
            }

            throw new ArgumentException($"No factory found for product type: {productType}");
        }
    }
}
