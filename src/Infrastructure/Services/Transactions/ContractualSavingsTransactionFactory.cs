using Infrastructure.Contracts.Transactions;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class ContractualSavingsTransactionFactory : ITransactionContextFactory
    {
        public EnumProductType ProductType => EnumProductType.ContractualSaving;
        private readonly IAccountTransactionFactoryResolverService _accountTransactionFactoryResolverService;
        private readonly IProductAccountFactoryResolverService _productAccountFactoryResolverService;
        private readonly IPaymentProcessorFactoryResolverService _paymentProcessorFactoryResolverService;

        public ContractualSavingsTransactionFactory(
            IAccountTransactionFactoryResolverService accountTransactionFactoryResolverService,
            IProductAccountFactoryResolverService productAccountFactoryResolverService,
            IPaymentProcessorFactoryResolverService paymentProcessorFactoryResolverService)
        {
            _accountTransactionFactoryResolverService = accountTransactionFactoryResolverService;
            _productAccountFactoryResolverService = productAccountFactoryResolverService;
            _paymentProcessorFactoryResolverService = paymentProcessorFactoryResolverService;
        }

        public IAccountTransactionFactory GetAccountTransactionService(EnumBuroTransactionType transactionType)
        {
            return _accountTransactionFactoryResolverService.GetFactory(transactionType);
        }

        public IProductAccountFactory GetProductAccountService()
        {
            return _productAccountFactoryResolverService.GetFactory(ProductType);
        }

        public IPaymentProcessorFactory GetPaymentProcessorService(EnumTransactionMedium transactionMedium)
        {
            return _paymentProcessorFactoryResolverService.GetFactory(transactionMedium);
        }
    }
}
