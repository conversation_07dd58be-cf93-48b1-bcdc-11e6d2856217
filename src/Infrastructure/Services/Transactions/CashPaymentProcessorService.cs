using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class CashPaymentProcessorService : IPaymentProcessorFactory
    {
        private readonly ILogger<CashPaymentProcessorService> _logger;
        public EnumTransactionMedium TransactionMedium => EnumTransactionMedium.Cash;

        public CashPaymentProcessorService(ILogger<CashPaymentProcessorService> logger)
        {
            _logger = logger;
        }

        public async Task ExecutePaymentAsync()
        {
            _logger.LogInformation("Executing cash payment processing.");

            await Task.Delay(1000);

            _logger.LogInformation("Cash payment processing completed.");
        }
    }
}
