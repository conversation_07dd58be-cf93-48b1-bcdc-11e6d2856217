using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class BankTransferProcessorService : IPaymentProcessorFactory
    {
        private readonly ILogger<BankTransferProcessorService> _logger;
        public EnumTransactionMedium TransactionMedium => EnumTransactionMedium.BankDraft;

        public BankTransferProcessorService(ILogger<BankTransferProcessorService> logger)
        {
            _logger = logger;
        }

        public Task ExecutePaymentAsync()
        {
            _logger.LogInformation("Executing bank transfer payment processing.");
            throw new NotImplementedException();
        }
    }
}
