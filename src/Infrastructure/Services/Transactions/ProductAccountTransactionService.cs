using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Infrastructure.Extensions;
using Infrastructure.Models.Transaction;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class ProductAccountTransactionService : IProductAccountTransactionService
    {
        private readonly ILogger<ProductAccountTransactionService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ITransactionContextFactoryResolverService _transactionContextFactoryResolverService;
        private readonly IServiceClient _serviceClient;

        public ProductAccountTransactionService(
            ILogger<ProductAccountTransactionService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ITransactionContextFactoryResolverService transactionContextFactoryResolverService,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _transactionContextFactoryResolverService = transactionContextFactoryResolverService;
            _serviceClient = serviceClient;
        }

        public async Task ExecuteAsync(string productAccountTransactionItemId)
        {
            _logger.LogInformation("Starting product account transaction execution for transaction: {TransactionItemId}", productAccountTransactionItemId);

            var transactionRequest = await GetTransactionRequestByIdAsync(productAccountTransactionItemId);

            var transactionContextFactory = _transactionContextFactoryResolverService.GetFactory(transactionRequest.ProductType);

            var accountTransactionService = transactionContextFactory.GetAccountTransactionService(transactionRequest.TransactionType);
            var productAccountService = transactionContextFactory.GetProductAccountService();
            var paymentProcessorService = transactionContextFactory.GetPaymentProcessorService(transactionRequest.TransactionMedium);

            await productAccountService.ExecuteTransactionAsync(accountTransactionService, productAccountTransactionItemId);
            await paymentProcessorService.ExecutePaymentAsync();

            // NOSOANR we should not update track the payment here as there can be transactions by system user (which will be added in buro cash in hand)
            SentToTrackAccountPaymentEvent(transactionRequest);

            _logger.LogInformation("Product account transaction execution completed for transaction: {TransactionItemId}", productAccountTransactionItemId);
        }

        private void SentToTrackAccountPaymentEvent(BuroProductAccountTransactionRequest transaction)
        {
            _logger.LogInformation("Sending TrackAccountPaymentEvent for transaction: {TransactionId} Amount: {Amount}", transaction.ItemId, transaction.Amount);

            var trackPayementEvent = new TrackAccountPaymentEvent
            {
                AccountPayments = new List<AccountPayment>
                {
                    new()
                    {
                        MemberItemId = transaction.MemberItemId,
                        AccountItemId = transaction.AccountItemId,
                        PaidAmount = transaction.Amount,
                        ProductType = transaction.ProductType,
                        MemberScheduleItemId = transaction.MemberScheduleItemId
                    }
                }
            };

            _serviceClient.SendToQueue<CommandResponse>(QueueNames.BuroCommandQueue, trackPayementEvent);

            _logger.LogInformation("TrackAccountPaymentEvent sent successfully for transaction: {TransactionId}", transaction.ItemId);
        }

        #region Private
        private async Task<BuroProductAccountTransactionRequest> GetTransactionRequestByIdAsync(string productAccountTransactionItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == productAccountTransactionItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        } 
        #endregion
    }
}
