using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class PaymentProcessorFactoryResolverService : IPaymentProcessorFactoryResolverService
    {
        private readonly ILogger<PaymentProcessorFactoryResolverService> _logger;
        private readonly Dictionary<EnumTransactionMedium, IPaymentProcessorFactory> _factories;

        public PaymentProcessorFactoryResolverService(
            ILogger<PaymentProcessorFactoryResolverService> logger,
            IEnumerable<IPaymentProcessorFactory> factories)
        {
            _logger = logger;
            _factories = factories.ToDictionary(f => f.TransactionMedium, f => f);
        }

        public IPaymentProcessorFactory GetFactory(EnumTransactionMedium transactionMedium)
        {
            _logger.LogInformation("Resolving factory for transaction medium: {TransactionMedium}", transactionMedium);

            if (_factories.TryGetValue(transactionMedium, out var factory))
            {
                return factory;
            }

            throw new ArgumentException($"No factory found for transaction medium: {transactionMedium}");
        }
    }
}
