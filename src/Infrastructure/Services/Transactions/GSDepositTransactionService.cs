using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class GSDepositTransactionService : IAccountTransactionFactory
    {
        public EnumBuroTransactionType TransactionType => EnumBuroTransactionType.GsDeposit;
        private readonly ILogger<GSDepositTransactionService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;

        public GSDepositTransactionService(
            ILogger<GSDepositTransactionService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider,
            IInfrastructureSharedService sharedService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
        }

        public async Task ProcessAsync(string productAccountTransactionRequestItemId)
        {
            _logger.LogInformation("Starting GS deposit transaction processing for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);

            var currentContext = GetCurrentUserId();
            var transactionRequest = await GetProductAccountTransactionAsync(productAccountTransactionRequestItemId);
            var gsAccount = await GetGsAccountByItemIdAsync(transactionRequest);

            await _sharedService.InitiateProductAccountTransactionAsync(true, transactionRequest);

            await UpdateGsAccountAsync(transactionRequest, gsAccount, currentContext.UserId);
            await UpdateProductAccountTransactionAsync(productAccountTransactionRequestItemId, currentContext.UserId);

            await HandleVoucherCreationProcessAsync();
            await SendDepositNotificationAsync(currentContext, productAccountTransactionRequestItemId, transactionRequest);

            _logger.LogInformation("GS deposit transaction processing completed for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);
        }

        private async Task HandleVoucherCreationProcessAsync()
        {
            _logger.LogInformation("Starting voucher creation process for GS deposit transaction.");

            // NOSONAR We need to generate multiple vouchers based on the member and the current branch.
            // NOSONAR This task will be completed once the Voucher module is finished.
            await Task.Delay(1000);
        }

        private async Task<BuroGeneralSavingsAccount> GetGsAccountByItemIdAsync(BuroProductAccountTransactionRequest transaction)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroGeneralSavingsAccount>(
                a => a.ItemId == transaction.AccountItemId && a.MemberItemId == transaction.MemberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroGeneralSavingsAccount)));
        }

        private async Task UpdateProductAccountTransactionAsync(string productAccountTransactionRequestItemId, string userId)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroProductAccountTransactionRequest.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroProductAccountTransactionRequest.LastUpdatedBy), userId},
                {nameof(BuroProductAccountTransactionRequest.Status), EnumProductAccountTransactionStatus.Success},
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroProductAccountTransactionRequest>(a => a.ItemId == productAccountTransactionRequestItemId, properties);
        }

        private async Task UpdateGsAccountAsync(
            BuroProductAccountTransactionRequest transactionRequest,
            BuroGeneralSavingsAccount gsAccount,
            string userId)
        {
            // NOSONAR Need to remove this line instead we need to get the balance from the account and update the value.
            var newBalance = gsAccount.Balance + transactionRequest.Amount;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroGeneralSavingsAccount.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroGeneralSavingsAccount.LastUpdatedBy), userId},
                {nameof(BuroGeneralSavingsAccount.Balance), newBalance}
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroGeneralSavingsAccount>(a => a.ItemId == gsAccount.ItemId, properties);
        }

        private async Task<BuroProductAccountTransactionRequest> GetProductAccountTransactionAsync(string productAccountTransactionRequestItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == productAccountTransactionRequestItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        }

        private async Task SendDepositNotificationAsync(
            SecurityContext securityContext,
            string productAccountTransactionRequestItemId,
            BuroProductAccountTransactionRequest transaction)
        {
            var responseKey = BuroNotificationKeys.GSAmountDeposited;

            var notificationPayload = new
            {
                TransactionItemId = productAccountTransactionRequestItemId,
                transaction.MemberItemId,
                transaction.MemberName,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                productAccountTransactionRequestItemId,
                securityContext.UserId);
        }

        private SecurityContext GetCurrentUserId()
        {
            return _securityContextProvider.GetSecurityContext();
        }
    }
}
