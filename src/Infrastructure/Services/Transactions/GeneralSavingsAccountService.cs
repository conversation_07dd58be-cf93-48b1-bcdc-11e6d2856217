using Infrastructure.Contracts.Transactions;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class GeneralSavingsAccountService : IProductAccountFactory
    {
        public EnumProductType ProductType => EnumProductType.GeneralSaving;

        public async Task ExecuteTransactionAsync(IAccountTransactionFactory transaction, string productAccountTransactionItemId)
        {
            await transaction.ProcessAsync(productAccountTransactionItemId);
        }
    }
}
