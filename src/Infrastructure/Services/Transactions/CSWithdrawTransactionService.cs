using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Infrastructure.Extensions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class CSWithdrawTransactionService : IAccountTransactionFactory
    {
        public EnumBuroTransactionType TransactionType => EnumBuroTransactionType.CsWithdrawalMaturity;
        private readonly ILogger<CSWithdrawTransactionService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;
        private readonly IServiceClient _serviceClient;

        public CSWithdrawTransactionService(
            ILogger<CSWithdrawTransactionService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider,
            IInfrastructureSharedService sharedService,
            IServiceClient serviceClient)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
            _serviceClient = serviceClient;
        }

        public async Task ProcessAsync(string productAccountTransactionRequestItemId)
        {
            _logger.LogInformation("Processing CS withdrawal transaction request for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);

            var currentContext = GetCurrentUserId();
            var transactionRequest = await GetAccountTransactionRequestByItemIdAsync(productAccountTransactionRequestItemId);
            var ShouldGetApproval = transactionRequest.MemberBranchOfficeItemId != transactionRequest.ActionByEmployee.EmployeeOfficeItemId;

            if (ShouldGetApproval)
            {
                var initiator = await GetEmployeeByItemIdAsync(transactionRequest.ActionByEmployee.EmployeeItemId);
                var approver = await GetEmployeeOfAnOfficeByDesignationAsync(transactionRequest.MemberBranchOfficeItemId, BuroDesignationConstant.BranchManagerDesignationCode);

                await SendApprovalAsync(transactionRequest, initiator, approver, currentContext);
            }
            else
            {
                SendToApproveAndAdjustBalance(productAccountTransactionRequestItemId);
            }

            _logger.LogInformation("CS withdrawal transaction request processed successfully for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);

        }

        private async Task<BuroEmployee> GetEmployeeOfAnOfficeByDesignationAsync(string officeItemId, string designationCode)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.CurrentOfficeItemId == officeItemId && e.DesignationCode == designationCode)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroEmployee)));
        }

        private async Task<BuroEmployee> GetEmployeeByItemIdAsync(string employeeItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroEmployee>(e => e.ItemId == employeeItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroEmployee)));
        }

        private void SendToApproveAndAdjustBalance(string productAccountTransactionRequestItemId)
        {
            var approvalEvent = new CSWithdrawalApprovalEvent
            {
                ProductAccountTransactionRequestItemId = productAccountTransactionRequestItemId,
                IsAccepted = true,
            };

            _serviceClient.SendToQueue<bool>(QueueNames.BuroCommandQueue, approvalEvent);
        }

        private async Task SendApprovalAsync(
            BuroProductAccountTransactionRequest transactionRequest,
            BuroEmployee initiator,
            BuroEmployee approver,
            SecurityContext securityContext)
        {
            _logger.LogInformation("Starting SendApprovalAsync");

            var approvalItemId = await CreateApprovalAsync(transactionRequest, initiator, approver, securityContext.UserId);

            var notificationPayload = new
            {
                ProductAccountTransactionRequestItemId = transactionRequest.ItemId,
                transactionRequest.MemberItemId,
                transactionRequest.ProductType,
                transactionRequest.TransactionMedium,
                transactionRequest.TransactionType,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                ApprovalItemId = approvalItemId,
                ApproverName = approver.EmployeeName,
                ApproverPin = approver.EmployeePin,
                ApproverDesignation = approver.DesignationTitle,
            };

            await _sharedService.NotifyUserAsync(
                BuroNotificationKeys.CSAccountWithdrawalBranchManagerInvited,
                notificationPayload,
                transactionRequest.ItemId,
                approver.UserItemId);
        }

        private async Task<string> CreateApprovalAsync(
            BuroProductAccountTransactionRequest transactionRequest,
            BuroEmployee initiator,
            BuroEmployee approver,
            string currentUserId)
        {
            _logger.LogInformation("Creating approval for transaction request Id: {TransactionRequestItemId}", transactionRequest.ItemId);

            var approval = new BuroApproval
            {
                ItemId = Guid.NewGuid().ToString(),
                Tags = new[] { Tags.IsAGSWithdrawalApproval },
                CreatedBy = currentUserId,
                LastUpdatedBy = currentUserId,
                RelatedEntityItemId = transactionRequest.ItemId,
                RelatedEntityName = nameof(BuroProductAccountTransactionRequest),
                RequestedFromPersonItemId = initiator.PersonItemId,
                RequestedFromEmployeeItemId = initiator.ItemId,
                RequestedFromEmployeePIN = initiator.EmployeePin,
                RequestedFromEmployeeName = initiator.EmployeeName,
                ActionedByPersonItemId = approver.PersonItemId,
                ActionedByEmployeeItemId = approver.ItemId,
                ActionedByEmployeePIN = approver.EmployeePin,
                ActionedByEmployeeName = approver.EmployeeName,
                ActionedByEmployeeDesignation = approver.DesignationTitle,
                Status = EnumApprovalStatusType.Pending,
                Category = EnumApprovalCategoryType.GSWithdrawalApproval,
                MetaData = new List<MetaData>
                {
                    new() { Key = "ProductAccountTransactionRequestItemId", Value = transactionRequest.ItemId},
                    new() { Key = nameof(transactionRequest.MemberItemId), Value = transactionRequest.MemberItemId },
                    new() { Key = nameof(transactionRequest.ProductType), Value = Enum.GetName(typeof(EnumProductType), transactionRequest.ProductType)},
                    new() { Key = nameof(transactionRequest.TransactionType), Value = Enum.GetName(typeof(EnumBuroTransactionType), transactionRequest.TransactionType)},
                    new() { Key = nameof(transactionRequest.TransactionMedium), Value = Enum.GetName(typeof(EnumTransactionMedium), transactionRequest.TransactionMedium)}
                },
                IdsAllowedToRead = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToUpdate = new[] { currentUserId, initiator.UserItemId },
                IdsAllowedToDelete = new[] { currentUserId },
                IdsAllowedToWrite = new[] { currentUserId }
            };

            approval.AddEntityBasicInfo();

            await _finMongoDbRepository.InsertOneAsync(approval);

            _logger.LogInformation("Approval created with ItemId: {ApprovalItemId}", approval.ItemId);

            return approval.ItemId;
        }

        private async Task<BuroProductAccountTransactionRequest> GetAccountTransactionRequestByItemIdAsync(string ProductAccountTransactionRequestItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == ProductAccountTransactionRequestItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        }

        private SecurityContext GetCurrentUserId()
        {
            return _securityContextProvider.GetSecurityContext();
        }

    }
}
