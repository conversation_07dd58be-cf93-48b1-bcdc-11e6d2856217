using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Retry;
using Infrastructure.Extensions;
using Infrastructure.Models.Transaction;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Net.Http.Headers;
using System.Text;

namespace Infrastructure.Services.Transactions
{
    public class TransactionClientService : ITransactionClientService
    {
        private readonly IServiceClient _serviceClient;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly AccessTokenProvider _accessTokenProvider;
        private readonly ILogger<TransactionClientService> _logger;
        private readonly IRetryExecutorService _retryExecutorService;
        private readonly string _transactionServiceBaseUrl;

        public TransactionClientService(
            IServiceClient serviceClient,
            IBusinessConfig businessConfig,
            ISecurityContextProvider securityContextProvider,
            AccessTokenProvider accessTokenProvider,
            ILogger<TransactionClientService> logger,
            IRetryExecutorService retryExecutorService)
        {
            _transactionServiceBaseUrl = businessConfig.GetTransactionServiceSettings();
            _serviceClient = serviceClient;
            _securityContextProvider = securityContextProvider;
            _accessTokenProvider = accessTokenProvider;
            _logger = logger;
            _retryExecutorService = retryExecutorService;
        }

        public async Task<CommandResponse> CreateAccountAsync(CreateAccountCommand createAccountCommand)
        {
            return await SendTransactionClientRequestAsync(
                HttpMethod.Post,
                TransactionServiceConstants.TransactionEndpoints.CreateAccount,
                createAccountCommand);
        }

        public async Task<CommandResponse> UpdateAccountAsync(UpdateAccountCommand updateAccountCommand)
        {
            return await SendTransactionClientRequestAsync(
                HttpMethod.Post,
                TransactionServiceConstants.TransactionEndpoints.UpdateAccount,
                updateAccountCommand);
        }

        public async Task<CommandResponse> DeleteAccountAsync(DeleteAccountCommand deleteAccountCommand)
        {
            return await SendTransactionClientRequestAsync(
                HttpMethod.Post,
                TransactionServiceConstants.TransactionEndpoints.DeleteAccount,
                deleteAccountCommand);
        }

        public async Task<CommandResponse> InitiateTransactionAsync(InitiateTransactionCommand initiateTransactionCommand)
        {
            return await SendTransactionClientRequestAsync(
                HttpMethod.Post,
                TransactionServiceConstants.TransactionEndpoints.InitiateTransaction,
                initiateTransactionCommand);
        }

        public async Task<CommandResponse> ReverseTransactionAsync(ReverseTransactionCommand reverseTransactionCommand)
        {
            return await SendTransactionClientRequestAsync(
                HttpMethod.Post,
                TransactionServiceConstants.TransactionEndpoints.ReverseTransaction,
                reverseTransactionCommand);
        }

        #region Private

        private async Task<CommandResponse> SendTransactionClientRequestAsync<T>(HttpMethod method, string endpoint, T payload)
            where T : class
        {
            ArgumentNullException.ThrowIfNull(method);
            ArgumentNullException.ThrowIfNull(endpoint);
            ArgumentNullException.ThrowIfNull(payload);

            var uri = BuildUri(endpoint);
            var token = await GetNewAccessToken();
            var serializedPayload = JsonConvert.SerializeObject(payload);

            _logger.LogInformation("Preparing to send {Method} request to {Endpoint} with payload: {Payload}", method, endpoint, serializedPayload);

            var httpResponse = await _retryExecutorService.ExecuteAsync(ExecuteHttpRequestAsync(method, uri, token, serializedPayload));

            if (httpResponse == null)
            {
                _logger.LogError("Response is null for request to {Url}", uri);

                throw new InvalidOperationException($"Response is null for request to {uri}");
            }

            var response = await ParseCommandResponseAsync(uri, httpResponse);

            return response;
        }

        private Func<Task<HttpResponseMessage>> ExecuteHttpRequestAsync(HttpMethod method, Uri uri, AuthenticationHeaderValue token, string serializedPayload)
        {
            return () =>
            {
                var request = new HttpRequestMessage(method, uri)
                {
                    Content = new StringContent(serializedPayload, Encoding.UTF8, "application/json")
                };

                request.Headers.Authorization = token;

                _logger.LogInformation("Sending {Method} request to {Url} with payload: {Payload}", method, uri, serializedPayload);

                return _serviceClient.SendToHttpAsync(request);
            };
        }

        private async Task<CommandResponse> ParseCommandResponseAsync(Uri uri, HttpResponseMessage httpResponse)
        {
            var responseContent = await httpResponse.Content.ReadAsStringAsync();

            if (httpResponse.IsSuccessStatusCode)
            {
                _logger.LogInformation("Request to {Url} succeeded. Response: {Response}", uri, responseContent);
            }
            else
            {
                _logger.LogError("Request to {Url} failed. Status: {StatusCode}, Response: {Response}", uri, httpResponse.StatusCode, responseContent);
            }

            var response = JsonConvert.DeserializeObject<CommandResponse>(responseContent) ?? new CommandResponse();

            return response;
        }

        private Uri BuildUri(string action)
        {
            var segments = new List<string>
            {
                _transactionServiceBaseUrl,
                TransactionServiceConstants.TransactionServiceName,
                action
            };

            var path = string.Join("/", segments.Where(s => !string.IsNullOrWhiteSpace(s)).Select(s => s.Trim('/')));

            return new Uri(path);
        }

        private async Task<AuthenticationHeaderValue> GetNewAccessToken()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var token = await _accessTokenProvider.CreateFromAccessTokenAsync(securityContext.OauthBearerToken);

            return new AuthenticationHeaderValue(TransactionServiceConstants.TokenSchema, token);
        }

        #endregion
    }
}