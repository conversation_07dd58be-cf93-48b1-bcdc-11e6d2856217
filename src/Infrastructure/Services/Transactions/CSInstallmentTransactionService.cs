using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class CSInstallmentTransactionService : IAccountTransactionFactory
    {
        public EnumBuroTransactionType TransactionType => EnumBuroTransactionType.CsInstallment;
        private readonly ILogger<CSInstallmentTransactionService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;

        public CSInstallmentTransactionService(
            ILogger<CSInstallmentTransactionService> logger, 
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider,
            IInfrastructureSharedService sharedService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
        }

        public async Task ProcessAsync(string productAccountTransactionRequestItemId)
        {
            _logger.LogInformation("Starting CS deposit transaction processing for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);

            var currentContext = GetCurrentUserId();
            var transactionRequest = await GetProductAccountTransactionAsync(productAccountTransactionRequestItemId);
            var csAccount = await GetCsAccountByItemIdAsync(transactionRequest);

            await _sharedService.InitiateProductAccountTransactionAsync(true, transactionRequest);

            await UpdateMemberScheduleAsync(transactionRequest, currentContext.UserId);
            await UpdateCsAccountAsync(transactionRequest, csAccount, currentContext.UserId);
            await UpdateProductAccountTransactionAsync(productAccountTransactionRequestItemId, currentContext.UserId);

            await HandleVoucherCreationProcess();
            await SendDepositNotificationAsync(currentContext, productAccountTransactionRequestItemId, transactionRequest);

            _logger.LogInformation("CS deposit transaction processing completed for transaction request Id: {ProductAccountTransactionRequestItemId}", productAccountTransactionRequestItemId);
        }

        private async Task UpdateMemberScheduleAsync(BuroProductAccountTransactionRequest transactionRequest, string userId)
        {
            var scheduleItemId = await GetScheduleItemIdAsync(transactionRequest);

            ArgumentNullException.ThrowIfNull(scheduleItemId);

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroMemberSchedule.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroMemberSchedule.LastUpdatedBy), userId},
                {nameof(BuroMemberSchedule.PaymentStatus), PaymentStatus.Paid},
                {nameof(BuroMemberSchedule.PaidAmount), transactionRequest.Amount}
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroMemberSchedule>(a => a.ItemId == scheduleItemId, properties);
        }

        private async Task<string?> GetScheduleItemIdAsync(BuroProductAccountTransactionRequest transactionRequest)
        {
            var scheduleItemId = transactionRequest.MemberScheduleItemId;

            var IsScheduleExists = !string.IsNullOrWhiteSpace(scheduleItemId)
                && await _finMongoDbRepository.ExistsAsync<BuroMemberSchedule>(s => s.ItemId == scheduleItemId);

            if (!IsScheduleExists)
            {
                scheduleItemId = (await _finMongoDbRepository.FindWithProjectionAsync<BuroMemberSchedule, Tuple<string, DateTime>>(
                    s => s.MemberItemId == transactionRequest.MemberItemId
                      && s.ProductType == EnumProductType.ContractualSaving
                      && s.PaymentStatus == PaymentStatus.Pending,
                    s => new Tuple<string, DateTime>(s.ItemId, s.ActualPaymentDate)))
                    .OrderBy(s => s.Item2)
                    .Select(s => s.Item1)
                    .FirstOrDefault();
            }

            return scheduleItemId;
        }

        private async Task UpdateProductAccountTransactionAsync(string productAccountTransactionRequestItemId, string userId)
        {
            var properties = new Dictionary<string, object>
            {
                {nameof(BuroProductAccountTransactionRequest.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroProductAccountTransactionRequest.LastUpdatedBy), userId},
                {nameof(BuroProductAccountTransactionRequest.Status), EnumProductAccountTransactionStatus.Success},
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroProductAccountTransactionRequest>(a => a.ItemId == productAccountTransactionRequestItemId, properties);
        }

        private async Task UpdateCsAccountAsync(
            BuroProductAccountTransactionRequest transactionRequest,
            BuroContractualSavingsAccount gsAccount,
            string userId)
        {
            var newBalance = gsAccount.Balance + transactionRequest.Amount;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroContractualSavingsAccount.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroContractualSavingsAccount.LastUpdatedBy), userId},
                {nameof(BuroContractualSavingsAccount.Balance), newBalance}
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroContractualSavingsAccount>(a => a.ItemId == gsAccount.ItemId, properties);
        }

        private async Task HandleVoucherCreationProcess()
        {
            _logger.LogInformation("Starting voucher creation process for CS deposit transaction.");

            // NOSONAR We need to generate multiple vouchers based on the member and the current branch.
            // NOSONAR This task will be completed once the Voucher module is finished.
            await Task.Delay(1000);
        }

        private SecurityContext GetCurrentUserId()
        {
            return _securityContextProvider.GetSecurityContext();
        }

        private async Task<BuroProductAccountTransactionRequest> GetProductAccountTransactionAsync(string productAccountTransactionRequestItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == productAccountTransactionRequestItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        }

        private async Task<BuroContractualSavingsAccount> GetCsAccountByItemIdAsync(BuroProductAccountTransactionRequest transaction)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroContractualSavingsAccount>(
                a => a.ItemId == transaction.AccountItemId && a.MemberItemId == transaction.MemberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroContractualSavingsAccount)));
        }

        private async Task SendDepositNotificationAsync(
            SecurityContext securityContext,
            string productAccountTransactionRequestItemId,
            BuroProductAccountTransactionRequest transaction)
        {
            var responseKey = BuroNotificationKeys.CSAmountDeposited;

            var notificationPayload = new
            {
                TransactionItemId = productAccountTransactionRequestItemId,
                transaction.MemberItemId,
                transaction.MemberName,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                productAccountTransactionRequestItemId,
                securityContext.UserId);
        }
    }
}
