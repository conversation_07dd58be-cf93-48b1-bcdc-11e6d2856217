using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Contracts.Transactions;
using Infrastructure.Events.Transaction;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services.Transactions
{
    public class GSWithdrawalApprovalEventService : IGSWithdrawalApprovalEventService
    {
        private readonly ILogger<GSWithdrawalApprovalEventService> _logger;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly IInfrastructureSharedService _sharedService;

        public GSWithdrawalApprovalEventService(
            ILogger<GSWithdrawalApprovalEventService> logger,
            IFinMongoDbRepository finMongoDbRepository,
            ISecurityContextProvider securityContextProvider,
            IInfrastructureSharedService sharedService)
        {
            _logger = logger;
            _finMongoDbRepository = finMongoDbRepository;
            _securityContextProvider = securityContextProvider;
            _sharedService = sharedService;
        }

        public async Task ProcessGSWithdrawalApprovalAsync(GSWithdrawalApprovalEvent command)
        {
            _logger.LogInformation("Processing GS Withdrawal Approval for transaction request Id: {ProductAccountTransactionRequestItemId}", command.ProductAccountTransactionRequestItemId);

            var currentContext = GetCurrentUserId();
            var isTransactionAllowed = command.IsAccepted;

            if (isTransactionAllowed)
            {
                var transactionRequest = await GetAccountTransactionRequestByItemIdAsync(command.ProductAccountTransactionRequestItemId);
                var gsAccount = await GetGsAccountByItemIdAsync(transactionRequest);

                isTransactionAllowed = transactionRequest.Amount <= gsAccount.Balance;

                if (isTransactionAllowed)
                {
                    await _sharedService.InitiateProductAccountTransactionAsync(true, transactionRequest);

                    await UpdateGsAccount(transactionRequest, gsAccount, currentContext.UserId);
                    await HandleVoucherCreationProcess();
                }
            }

            await UpdateProductAccountTransactionAsync(command.ProductAccountTransactionRequestItemId, currentContext.UserId, isTransactionAllowed);
            await SendWithdralNotificationAsync(currentContext, command.ProductAccountTransactionRequestItemId, isTransactionAllowed);

            _logger.LogInformation("GS Withdrawal Approval processed successfully for transaction request Id: {ProductAccountTransactionRequestItemId}", command.ProductAccountTransactionRequestItemId);
        }

        private async Task<BuroProductAccountTransactionRequest> GetAccountTransactionRequestByItemIdAsync(string ProductAccountTransactionRequestItemId)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroProductAccountTransactionRequest>(t => t.ItemId == ProductAccountTransactionRequestItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroProductAccountTransactionRequest)));
        }

        private async Task UpdateProductAccountTransactionAsync(string ProductAccountTransactionRequestItemId, string userId, bool isTransactionAllowed)
        {
            var status = isTransactionAllowed ? EnumProductAccountTransactionStatus.Success : EnumProductAccountTransactionStatus.Failed;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroProductAccountTransactionRequest.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroProductAccountTransactionRequest.LastUpdatedBy), userId},
                {nameof(BuroProductAccountTransactionRequest.Status), status},
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroProductAccountTransactionRequest>(a => a.ItemId == ProductAccountTransactionRequestItemId, properties);
        }

        private async Task UpdateGsAccount(
            BuroProductAccountTransactionRequest transaction,
            BuroGeneralSavingsAccount gsAccount,
            string userId)
        {
            // NOSONAR Need to remove this line instead we need to get the balance from the account and update the value.
            var newBalance = gsAccount.Balance - transaction.Amount;

            var properties = new Dictionary<string, object>
            {
                {nameof(BuroGeneralSavingsAccount.LastUpdateDate), DateTime.UtcNow},
                {nameof(BuroGeneralSavingsAccount.LastUpdatedBy), userId},
                {nameof(BuroGeneralSavingsAccount.Balance), newBalance}
            };

            await _finMongoDbRepository.UpdateOneAsync<BuroGeneralSavingsAccount>(a => a.ItemId == gsAccount.ItemId, properties);
        }

        private static async Task HandleVoucherCreationProcess()
        {
            // NOSONAR We need to generate multiple vouchers based on the member and the current branch.
            // NOSONAR This task will be completed once the Voucher module is finished.
            await Task.Delay(1000);
        }

        private async Task SendWithdralNotificationAsync(
            SecurityContext securityContext,
            string productAccountTransactionRequestItemId,
            bool transactionAllowed)
        {
            var responseKey = BuroNotificationKeys.GSAmountWithdrawal;

            var notificationPayload = new
            {
                ProductAccountTransactionRequestItemId = productAccountTransactionRequestItemId,
                AssignedAt = DateTime.UtcNow,
                AssignedBy = securityContext.DisplayName,
                IsTransactionCompleted = transactionAllowed
            };

            await _sharedService.NotifyUserAsync(
                responseKey,
                notificationPayload,
                productAccountTransactionRequestItemId,
                securityContext.UserId);
        }

        private async Task<BuroGeneralSavingsAccount> GetGsAccountByItemIdAsync(BuroProductAccountTransactionRequest transaction)
        {
            return await _finMongoDbRepository.FindOneAsync<BuroGeneralSavingsAccount>(
                a => a.ItemId == transaction.AccountItemId
                  && a.MemberItemId == transaction.MemberItemId)
                ?? throw new InvalidOperationException(string.Format(BuroErrorMessageKeys.RequiredDataNotFound, nameof(BuroGeneralSavingsAccount)));
        }

        private SecurityContext GetCurrentUserId()
        {
            return _securityContextProvider.GetSecurityContext();
        }
    }
}
