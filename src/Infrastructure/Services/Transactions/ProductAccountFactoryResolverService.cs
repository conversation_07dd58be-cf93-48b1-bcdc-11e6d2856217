using Infrastructure.Contracts.Transactions;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services.Transactions
{
    public class ProductAccountFactoryResolverService : IProductAccountFactoryResolverService
    {
        private readonly ILogger<ProductAccountFactoryResolverService> _logger;
        private readonly Dictionary<EnumProductType, IProductAccountFactory> _factories;

        public ProductAccountFactoryResolverService(
            ILogger<ProductAccountFactoryResolverService> logger,
            IEnumerable<IProductAccountFactory> factories)
        {
            _logger = logger;
            _factories = factories.ToDictionary(f => f.ProductType, f => f);
        }

        public IProductAccountFactory GetFactory(EnumProductType productType)
        {
            _logger.LogInformation("Resolving factory for product type: {ProductType}", productType);

            if (_factories.TryGetValue(productType, out var factory))
            {
                return factory;
            }

            throw new ArgumentException($"No factory found for product type: {productType}");
        }
    }
}
