using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Infrastructure.Services;

public class GoogleCaptchaVerificationService : IGoogleCaptchaVerificationService
{
    private readonly ILogger<GoogleCaptchaVerificationService> _logger;
    private readonly GoogleCaptchaSettings _captchaSettings;
    private readonly string _googleVerificationUrl;

    public GoogleCaptchaVerificationService(IBusinessConfig configuration,
        ILogger<GoogleCaptchaVerificationService> logger)
    {
        _logger = logger;
        _captchaSettings = configuration.GetCaptchaSettings();
        _googleVerificationUrl = configuration.GetGoogleCaptchaVerificationUrl();
    }
    public async Task<bool> IsCaptchaValid(string token)
    {
        var result = false;
        try
        {
            using var client = new HttpClient();
            var response = await client.PostAsync(
                $"{_googleVerificationUrl}?secret={this._captchaSettings.ServerKey}&response={token}",
                null
            );
            var jsonString = await response.Content.ReadAsStringAsync();
            var captchaVerification = JsonConvert.DeserializeObject<GoogleCaptchaVerificationResponse>(jsonString);
            result = captchaVerification.Success;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Inside GoogleCaptchaVerificationService->IsCaptchaValid Failed to process captcha validation");
        }

        return result;
    }
}