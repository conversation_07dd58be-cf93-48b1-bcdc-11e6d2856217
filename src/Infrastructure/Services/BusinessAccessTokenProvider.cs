using Infrastructure.Constants;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using SeliseBlocks.Genesis.Framework.Infrastructure;
namespace Infrastructure.Services;

public class BusinessAccessTokenProvider : IAccessTokenProvider
{
    private readonly IBusinessConfig _businessConfig;
    private readonly ILogger<BusinessAccessTokenProvider> _logger;
    private readonly AccessTokenProvider _accessTokenProvider;
    public BusinessAccessTokenProvider(ILogger<BusinessAccessTokenProvider> logger,
        IBusinessConfig businessConfig,
        AccessTokenProvider accessTokenProvider)
    {
        _logger = logger;
        _businessConfig = businessConfig;
        _accessTokenProvider = accessTokenProvider;
    }

    public Task<string> GetAdminToken()
    {
        var origin = _businessConfig.GetPortalOrigin();
        var siteId = _businessConfig.SiteId;
        return CreateToken(origin, siteId);
    }


    public Task<string> GetAdminTokenForSite(string origin, string siteId)
    {
        return CreateToken(origin, siteId);
    }

    private async Task<string> CreateToken(string origin, string siteId)
    {
        try
        {
            var adminAuthData = GetAdminAuthDataBySite(origin, siteId);
            string token = await _accessTokenProvider.CreateForUserAsync(adminAuthData);
            return token;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "BusinessAccessTokenProvider>CreateToken");
            throw new InvalidOperationException("An error occurred while creating a token", ex);
        }
    }

    private TokenInfo GetAdminAuthDataBySite(string origin, string siteId)
    {
        return new TokenInfo
        {
            Origin = origin,
            SiteId = siteId,
            SiteName = _businessConfig.SiteName,
            DisplayName = _businessConfig.SiteName,
            UserName = _businessConfig.DefaultAdminEmail,
            Roles = new List<string>
            {
                BuiltInRoles.TenantAdmin,
                UserRoles.Anonymous,
                UserRoles.AppUser,
                UserRoles.Admin
            },
            Language = _businessConfig.DefaultLanguage,
            TenantId = _businessConfig.TenantId,
            UserId = _businessConfig.DefaultUserId,
            LifeSpan = TimeSpan.FromHours(10.0),
            SessionId = Guid.NewGuid().ToString(),
            AccessToken = string.Empty
        };
    }
}
public class TokenDto
{
    public string AccessToken { get; set; }
}