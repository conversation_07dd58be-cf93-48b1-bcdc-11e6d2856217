using Infrastructure.Constants;
using Infrastructure.Contracts;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.StorageService.Commands;
using SeliseBlocks.StorageService.Driver;
using SeliseBlocks.StorageService.Queries;

namespace Infrastructure.Services
{
    public class BuroStorageServiceClient : IBuroStorageServiceClient
    {
        private readonly HttpClient _httpClient;

        private readonly ILogger<BuroStorageServiceClient> _logger;
        private readonly IStorageServiceClient _storageServiceClient;
        private readonly AccessTokenProvider _accessTokenProvider;
        private readonly ISecurityContextProvider _securityContextProvider;

        public BuroStorageServiceClient(
            ILogger<BuroStorageServiceClient> logger,
            IStorageServiceClient storageServiceClient,
            AccessTokenProvider accessTokenProvider,
            ISecurityContextProvider securityContextProvider)
        {
            _httpClient = new HttpClient();

            _logger = logger;
            _storageServiceClient = storageServiceClient;
            _accessTokenProvider = accessTokenProvider;
            _securityContextProvider = securityContextProvider;
        }

        private async Task<string> GetAuthTokenAsync()
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            var accessToken = await _accessTokenProvider.CreateFromAccessTokenAsync(securityContext.OauthBearerToken);

            return accessToken;
        }

        public async Task<string> CompressFileAsync(string fileName, string parentDirectoryId, params string[] fileIds)
        {
            _logger.LogInformation("Inside CompressFileAsync");

            var fileItemId = Guid.NewGuid().ToString();
            var directoryId = GetParentDirectoryId(parentDirectoryId);

            var queryFileIds = string.Join(",", fileIds.Select(f => !string.IsNullOrWhiteSpace(f)));
            var sourceQuery = $"Select <ItemId>from<File>where<ItemId=__in({queryFileIds})>Orderby<>pageNumber=<0>pageSize=<100>";
            var accessToken = await GetAuthTokenAsync();

            var command = new CompressFileCommand
            {
                SourceQuery = sourceQuery,
                OauthBearerToken = accessToken,
                OutputFile = new OutputFile
                {
                    Name = fileName,
                    FileId = fileItemId,
                    Tags = new string[] { Tags.IsAFile },
                    ParentDirectoryID = directoryId,
                    MetaData = default,
                }
            };

            _logger.LogInformation("Make Request for Compress Files. Request: {Request}", JsonConvert.SerializeObject(command));

            var response = await _storageServiceClient.CompressFile(command, accessToken);

            if (response != null && response.Errors.IsValid)
            {
                return fileItemId;
            }

            return string.Empty;
        }

        public async Task<string> UploadFileAsync(string filePath, string parentDirectoryId)
        {
            _logger.LogInformation("Inside UploadFileAsync");

            var fileItemId = Guid.NewGuid().ToString();
            var fileName = Path.GetFileName(filePath);
            var directoryId = GetParentDirectoryId(parentDirectoryId);
            var accessToken = await GetAuthTokenAsync();

            var query = new GetPreSignedUrlForUploadQuery
            {
                ItemId = fileItemId,
                MetaData = string.Empty,
                Name = fileName,
                ParentDirectoryId = directoryId,
                Tags = JsonConvert.SerializeObject(new string[] { Tags.IsAFile })
            };

            _logger.LogInformation("Request for pre signed url for upload. Request: {Request}", JsonConvert.SerializeObject(query));

            var response = await _storageServiceClient.GetPreSignedUrlForUpload(query, accessToken);

            if (response != null && await SendFileAsync(filePath, response.UploadUrl))
            {
                return fileItemId;
            }

            return string.Empty;
        }

        private static string GetParentDirectoryId(string parentDirectoryId)
        {
            return string.IsNullOrWhiteSpace(parentDirectoryId) ? StorageServiceConstants.DefaultParentDirectoryId : parentDirectoryId;
        }

        public async Task<bool> SendFileAsync(string filePath, string uploadUrl)
        {
            _logger.LogInformation("Inside SendFileAsync");

            try
            {
                using var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                using var streamContent = new StreamContent(fileStream);

                _httpClient.DefaultRequestHeaders.Add("x-ms-blob-type", "BlockBlob");

                _logger.LogInformation("Start Sending File. Local file path: {FilePath}", filePath);

                var response = await _httpClient.PutAsync(uploadUrl, streamContent);

                _logger.LogInformation("Response: {Response}", JsonConvert.SerializeObject(response));

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("File uploaded successfully");
                }
                else
                {
                    _logger.LogError("Failed to upload file. Status code: {StatusCode}", response.StatusCode);
                }

                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");

                return false;
            }
        }
    }
}
