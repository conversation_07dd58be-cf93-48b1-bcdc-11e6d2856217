using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.MailService.Driver;
using SeliseBlocks.MailService.Services.Commands;

namespace Infrastructure.Services
{
    public class OtpService : IOtpService
    {
        private readonly ILogger<OtpService> _logger;
        private readonly IBuroKeyStore _buroKeyStore;
        private readonly ISmsServiceClient _smsServiceClient;
        private readonly IMailServiceClient _mailServiceClient;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly AccessTokenProvider _accessTokenProvider;
        private readonly Random _random;

        public OtpService(
            ILogger<OtpService> logger,
            IBuroKeyStore buroKeyStore,
            ISmsServiceClient smsServiceClient,
            IMailServiceClient mailServiceClient,
            ISecurityContextProvider securityContextProvider,
            AccessTokenProvider accessTokenProvider)
        {
            _logger = logger;
            _buroKeyStore = buroKeyStore;
            _smsServiceClient = smsServiceClient;
            _mailServiceClient = mailServiceClient;
            _securityContextProvider = securityContextProvider;
            _accessTokenProvider = accessTokenProvider;
            _random = new Random();
        }

        #region SendOTP
        public async Task<CommandResponse> SendOtpAsync(SendOtpCommand command)
        {
            _logger.LogInformation("Inside SendOtpAsync");

            var response = new CommandResponse();

            try
            {
                var context = _securityContextProvider.GetSecurityContext();
                var uniqueValue = GenerateOtpPayload(context, command);
                await StoreKeyValueAsync(uniqueValue, context.SessionId, command.UniqueKey);

                response = command.Channel switch
                {
                    OtpChannel.Email => await SendOtpViaEmailAsync(uniqueValue, context.TenantId, command.Purpose),
                    OtpChannel.Phone => await SendOtpViaSmsAsync(uniqueValue.Recipient, uniqueValue.Otp),
                    _ => new CommandResponse()
                };

                _logger.LogInformation("Finished SendOtpAsync");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Something happend while sending OTP");

                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            }

            return response;
        }

        private async Task StoreKeyValueAsync(
            OtpPayload uniqueValue,
            string sessionId,
            string uniqueKey,
            int timeOutSecond = BuroOtpConstants.OtpDefaultTimeOutSecond)
        {
            var serializedValue = JsonConvert.SerializeObject(uniqueValue);
            var newSesssionId = GetUniqueSessionId(sessionId, uniqueKey);
            await _buroKeyStore.AddKeyWithExpiryAsync(newSesssionId, serializedValue, timeOutSecond);
        }

        private OtpPayload GenerateOtpPayload(SecurityContext context, SendOtpCommand command)
        {
            var newOtp = _random.Next(100000, 999999).ToString();

            var uniqueValue = new OtpPayload
            {
                UserItemId = context.UserId,
                Name = context.UserName,
                Recipient = command.Recipient,
                Otp = newOtp
            };

            return uniqueValue;
        }

        private static string GetUniqueSessionId(string sessionId, string key)
        {
            return $"{sessionId}_{key}";
        }

        #endregion

        #region MatchOTP
        public async Task<CommandResponse> MatchOtpAsync(MatchOtpCommand command)
        {
            _logger.LogInformation("Inside MatchOtpAsync");

            var response = new CommandResponse();

            try
            {
                var context = _securityContextProvider.GetSecurityContext();
                var sessionId = GetUniqueSessionId(context.SessionId, command.UniqueKey);
                var userInfo = await GetUserInfo(sessionId);

                if (userInfo == null)
                {
                    response.SetError(BuroErrorMessageKeys.UserInvalid, "User is not valid");
                }
                else if (string.IsNullOrWhiteSpace(userInfo.Otp))
                {
                    response.SetError(BuroErrorMessageKeys.OtpExpired, "Otp is expired");
                }
                else if (userInfo.Otp != command.OtpToMatch)
                {
                    response.SetError(BuroErrorMessageKeys.OtpNotMatched, "Otp is not matched");
                }

                _logger.LogInformation("Finished MatchOtpAsync");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Something happend while matching OTP");

                response.SetError(BuroErrorMessageKeys.ErrorOccurred, "Error occurred");
            }

            return response;
        }
        #endregion

        #region Private
        private async Task<CommandResponse> SendOtpViaEmailAsync(OtpPayload userInfo, string tenantId, string purpose)
        {
            _logger.LogInformation("Inside SendOtpViaEmailAsync");

            var newToken = await _accessTokenProvider.CreateForTenantAdminAsync(tenantId);

            var dataContext = new Dictionary<string, string>
            {
                { nameof(userInfo.UserItemId), userInfo.UserItemId },
                { nameof(userInfo.Otp), userInfo.Otp },
                { nameof(userInfo.Name), userInfo.Name }
            };

            var emailCommand = new SendMailToEmailCommand
            {
                Bcc = Array.Empty<string>(),
                Cc = Array.Empty<string>(),
                DataContext = dataContext,
                Language = BuroConstants.DefaultLanguage,
                Purpose = purpose,
                To = new[] { userInfo.Recipient }
            };

            var mailResponse = await _mailServiceClient.EnqueueMail(emailCommand, newToken);

            if (mailResponse.Errors?.IsValid ?? true)
            {
                mailResponse.SetError(BuroErrorMessageKeys.EmailSendingFailed, "Email sending failed");
            }

            _logger.LogInformation("Finished SendOtpViaEmailAsync");

            return mailResponse;
        }

        private async Task<CommandResponse> SendOtpViaSmsAsync(string phoneNumber, string otp)
        {
            _logger.LogInformation("Inside SendOtpViaSmsAsync for {PhoneNumber}", phoneNumber);

            var response = new CommandResponse();

            var smsCommand = new BuroSmsCommand
            {
                PhoneNumber = phoneNumber,
                Message = $"Your OTP for password reset is {otp}",
                Purpose = "ForgetPasswordOtp"
            };

            if (!await _smsServiceClient.SendShortMessage(smsCommand))
            {
                response.SetError(BuroErrorMessageKeys.SmsSendingFailed, "Sms sending failed");
            }

            _logger.LogInformation("Finished SendOtpViaSmsAsync for {PhoneNumber}", phoneNumber);

            return response;
        }

        private async Task<OtpPayload?> GetUserInfo(string key)
        {
            var userInfo = await _buroKeyStore.GetValueAsync<OtpPayload>(key);

            return userInfo;
        }

        #endregion
    }
}
