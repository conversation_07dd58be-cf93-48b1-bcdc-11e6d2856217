using Infrastructure.Enums;

namespace Infrastructure.Services
{
    public static class FileExtensionHelperService
    {
        public static string GetEnumFileExtension(EnumFileExtension fileExtension)
        {
            return fileExtension switch
            {
                EnumFileExtension.Json => ".json",
                EnumFileExtension.Xlsx => ".xlsx",
                EnumFileExtension.Txt => ".txt",
                EnumFileExtension.Jpeg => ".jpeg",
                EnumFileExtension.Png => ".png",
                EnumFileExtension.Pdf => ".pdf",
                EnumFileExtension.Mp3 => ".mp3",
                EnumFileExtension.Mp4 => ".mp4",
                EnumFileExtension.Docx => ".docx",
                _ => default!
            };
        }
    }
}
