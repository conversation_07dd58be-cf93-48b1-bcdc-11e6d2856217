using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Net.Http.Headers;
using System.Text;

namespace Infrastructure.Services
{
    public class NotificationServiceClient : INotificationServiceClient
    {
        private readonly Uri _pushNotificationUri;
        private readonly IServiceClient _serviceClient;
        private readonly ISecurityContextProvider _securityContextProvider;
        private readonly AccessTokenProvider _accessTokenProvider;
        private readonly ILogger<NotificationServiceClient> _logger;

        public NotificationServiceClient(
            IServiceClient serviceClient,
            IConfiguration configuration,
            ISecurityContextProvider securityContextProvider,
            AccessTokenProvider accessTokenProvider,
            ILogger<NotificationServiceClient> logger)
        {
            _serviceClient = serviceClient;
            _securityContextProvider = securityContextProvider;
            _accessTokenProvider = accessTokenProvider;
            _logger = logger;

            _pushNotificationUri = BuildNotificationEndpointUri(configuration["NotifierServerBaseUrl"], configuration["NotifierServerVersion"]);
        }

        private static Uri BuildNotificationEndpointUri(string baseUrl, string version)
        {
            var endpoint = string.IsNullOrWhiteSpace(version)
                ? $"{baseUrl}/api/Notifier/Notify"
                : $"{baseUrl}/{version}/api/Notifier/Notify";

            return new Uri(endpoint);
        }

        public async Task<HttpResponseMessage> NotifyAsync(NotifierPayloadWithResponse notifierPayloadWithResponse, bool skipToSaveOfflineNotification = false)
        {
            var securityContext = _securityContextProvider.GetSecurityContext();
            notifierPayloadWithResponse.SkipToSaveOfflineNotification = skipToSaveOfflineNotification;

            var requestBody = JsonConvert.SerializeObject(notifierPayloadWithResponse);
            var stsAccessToken = await _accessTokenProvider.CreateFromAccessTokenAsync(securityContext.OauthBearerToken);

            using var request = new HttpRequestMessage(HttpMethod.Post, _pushNotificationUri)
            {
                Content = new StringContent(requestBody, Encoding.UTF8, "application/json")
            };
            request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", stsAccessToken);

            _logger.LogInformation("Calling push notification service at {Url}", _pushNotificationUri);

            var response = await _serviceClient.SendToHttpAsync(request);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Notification sent successfully to tenant: {TenantId}, UserIds: {UserIds}, Roles: {Roles}",
                    BuroConstants.TenantId,
                    notifierPayloadWithResponse.UserIds != null ? string.Join(",", notifierPayloadWithResponse.UserIds) : "No Ids specified",
                    notifierPayloadWithResponse.Roles != null ? string.Join(",", notifierPayloadWithResponse.Roles) : "No roles specified");
            }
            else
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to send notification. Status: {StatusCode}, Details: {Error}",
                    response.StatusCode, responseContent);
            }

            _logger.LogInformation($"NotificationServiceClient --> NotifyAsync --> END!");

            return response;
        }
    }
}
