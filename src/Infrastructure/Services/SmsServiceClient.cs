using Infrastructure.Contracts;
using Infrastructure.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Net.Http.Headers;
using System.Text;

namespace Infrastructure.Services;

public class SmsServiceClient : ISmsServiceClient
{
    private readonly IBusinessConfig _businessConfig;
    private readonly ILogger<SmsServiceClient> _logger;
    private readonly IServiceClient _serviceClient;
    private readonly ISecurityContextProvider _securityContextProvider;

    public SmsServiceClient(
        ILogger<SmsServiceClient> logger,
        IBusinessConfig businessConfig,
        IServiceClient serviceClient,
        ISecurityContextProvider securityContextProvider)
    {
        _logger = logger;
        _businessConfig = businessConfig;
        _serviceClient = serviceClient;
        _securityContextProvider = securityContextProvider;
    }

    public async Task<bool> SendShortMessage(BuroSmsCommand command)
    {
        if (string.IsNullOrEmpty(command.PhoneNumber))
        {
            _logger.LogError("Error sending SMS. Reason: PhoneNumber is empty");
            return false;
        }

        if (string.IsNullOrEmpty(command.Purpose))
        {
            _logger.LogError("Error sending SMS. Reason: Purpose is empty");
            return false;
        }

        try
        {
            var payload = JsonConvert.SerializeObject(new SendSmsCommand
            {
                MessageText = command.Message,
                DestinationNumber = command.PhoneNumber
            });
            var shortMessageUri = GetSmsServiceEndPointUri();
            var request = new HttpRequestMessage(HttpMethod.Post, shortMessageUri);
            using (request.Content = new StringContent(payload, Encoding.UTF8, "application/json"))
            {
                var token = _securityContextProvider.GetSecurityContext().OauthBearerToken;
                request.Headers.Authorization = new AuthenticationHeaderValue("bearer", token);
                var response = await _serviceClient.SendToHttpAsync(request);
                if (response.IsSuccessStatusCode) return true;
                var responseJson = await response.Content.ReadAsStringAsync();
                _logger.LogError("Error sending SMS. Reason: {ResponseJson}", responseJson);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Exception in SendShortMessage\n{StackTrace}", e.StackTrace);
        }

        return false;
    }

    private Uri GetSmsServiceEndPointUri()
    {
        var shortMessageUrl =
            $"{_businessConfig.GetSmsServerUrl()}/{_businessConfig.GetSmsServerVersion()}/ShortMessageService/ShortMessageCommand/SendShortMessage";

        return new Uri(shortMessageUrl);
    }
}