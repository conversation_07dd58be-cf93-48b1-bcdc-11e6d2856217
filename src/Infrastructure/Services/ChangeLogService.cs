using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Extensions;
using MongoDB.Bson;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Reflection;

namespace Infrastructure.Services
{
    public class ChangeLogService : IChangeLogService
    {
        private readonly IRepository _repository;
        private readonly ISecurityContextProvider _securityContextProvider;

        public ChangeLogService(
            IRepository repository,
            ISecurityContextProvider securityContextProvider)
        {
            _repository = repository;
            _securityContextProvider = securityContextProvider;
        }

        private string GetLoggedInUserId()
        {
            var currentUser = _securityContextProvider.GetSecurityContext();

            return currentUser.UserId;
        }

        public async Task LogChangeAsync<T>(
            IQueryable<T> oldRecords,
            IDictionary<string, object> updates)
        {
            if (!oldRecords.Any())
            {
                return;
            }

            var changeLogs = new List<BuroChangeLog>();

            foreach (var item in oldRecords)
            {
                var entityId = item.ToBsonDocument().GetElement(BuroConstants.PrimaryKey).Value?.ToString() ?? string.Empty;

                var log = GetChangeLog(item, updates, entityId);

                if (log == null)
                {
                    continue;
                }

                changeLogs.Add(log);
            }

            await _repository.SaveAsync(changeLogs);
        }

        public async Task LogChangeAsync<T>(
            T oldRecord,
            T? newRecords)
        {
            if (object.Equals(oldRecord, default(T))
                || object.Equals(newRecords, default(T)))
            {
                return;
            }

            var updates = typeof(T)
                .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .ToDictionary(
                    prop => prop.Name,
                    prop => prop.GetValue(newRecords) ?? string.Empty
                );

            var entityId = oldRecord.ToBsonDocument().GetElement(BuroConstants.PrimaryKey).Value?.ToString() ?? string.Empty;

            var log = GetChangeLog(oldRecord, updates, entityId);

            if (log == null)
            {
                return;
            }

            await _repository.SaveAsync(log);
        }

        private BuroChangeLog? GetChangeLog<T>(T oldEntity, IDictionary<string, object> updates, string entityId)
        {
            var changes = GetPropertyChanges(oldEntity, updates);

            if (changes.Count == 0) return null;

            var log = new BuroChangeLog
            {
                EntityName = typeof(T).Name,
                EntityItemId = entityId,
                OperationType = EnumDmlOperationType.Update,
                PropertyChanges = changes,
                UserId = GetLoggedInUserId()
            };

            log.AddEntityBasicInfo();

            return log;
        }

        private static List<PropertyChange> GetPropertyChanges<T>(T oldEntity, IDictionary<string, object> updates)
        {
            var changes = new List<PropertyChange>();
            var propertyNamesForUpdate = updates.Keys.ToList();

            var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            properties = properties.Where(p => propertyNamesForUpdate.Contains(p.Name)).ToArray();

            foreach (var property in properties)
            {
                var oldValue = !object.Equals(oldEntity, default(T)) ? property.GetValue(oldEntity) : null;

                if (!updates.TryGetValue(property.Name, out var newValue)
                    || Equals(oldValue, newValue))
                {
                    continue;
                }

                changes.Add(new PropertyChange
                {
                    PropertyName = property.Name,
                    PropertyType = GetDataTypeByName(property.PropertyType.Name),
                    OldValue = oldValue?.ToString() ?? string.Empty,
                    NewValue = newValue?.ToString() ?? string.Empty
                });
            }

            return changes;
        }

        private static EnumDataType GetDataTypeByName(string typeName)
        {
            return typeName.ToUpper() switch
            {
                nameof(EnumDataType.INT) => EnumDataType.INT,
                nameof(EnumDataType.LONG) => EnumDataType.LONG,
                nameof(EnumDataType.DOUBLE) => EnumDataType.DOUBLE,
                nameof(EnumDataType.STRING) => EnumDataType.STRING,
                nameof(EnumDataType.DATETIME) => EnumDataType.DATETIME,
                _ => default
            };
        }
    }
}
