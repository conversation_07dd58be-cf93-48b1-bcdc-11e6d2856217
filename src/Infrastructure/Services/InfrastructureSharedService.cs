using FinMongoDbRepositories;
using Infrastructure.Constants;
using Infrastructure.Contracts;
using Infrastructure.Enums;
using Infrastructure.Extensions;
using Infrastructure.Models;
using Infrastructure.Models.Transaction;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Extensions;
using Newtonsoft.Json;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Services
{
    public class InfrastructureSharedService : IInfrastructureSharedService
    {
        private readonly ILogger<InfrastructureSharedService> _logger;
        private readonly ISequenceNumberClient _sequenceNumberClient;
        private readonly INotificationServiceClient _notificationServiceClient;
        private readonly IFinMongoDbRepository _finMongoDbRepository;
        private readonly ITransactionClientService _transactionClientService;

        public InfrastructureSharedService(
            ILogger<InfrastructureSharedService> logger,
            ISequenceNumberClient sequenceNumberClient,
            INotificationServiceClient notificationServiceClient,
            IFinMongoDbRepository finMongoDbRepository,
            ITransactionClientService transactionClientService)
        {
            _logger = logger;
            _sequenceNumberClient = sequenceNumberClient;
            _notificationServiceClient = notificationServiceClient;
            _finMongoDbRepository = finMongoDbRepository;
            _transactionClientService = transactionClientService;
        }

        public async Task<string> GetSequenceNumberAsync(string context, string prefix)
        {
            var sequence = await _sequenceNumberClient.GetSequenceNumber(new SequenceNumberQuery { Context = context });

            if (sequence == null || sequence.CurrentNumber <= 0)
            {
                _logger.LogWarning("Invalid sequence number received from the sequence number service for context: {Context}", context);

                sequence = new SequenceNumberQueryResponse
                {
                    Context = context,
                    CurrentNumber = DateTime.UtcNow.Ticks
                };
            }

            var sequenceNumber = sequence.CurrentNumber.ToString().PadLeft(6, '0');

            return $"{prefix}{sequenceNumber}";
        }

        public async Task NotifyUserAsync(
            string responseKey,
            object notificationPayload,
            string entityItemId,
            string userId,
            bool skipToSaveOfflineNotification = false)
        {
            _logger.LogInformation("In NotifyUserAsync");

            try
            {
                var payloadWithResponse = MakeNotifierPayloadWithResponse(responseKey, notificationPayload, userId);

                await _notificationServiceClient.NotifyAsync(payloadWithResponse, skipToSaveOfflineNotification);

                _logger.LogInformation("Notification sent for Entity ID: {EntityItemId} to User ID: {UserId}", entityItemId, userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send notification for Entity ID: {EntityItemId} to User ID: {UserId}", entityItemId, userId);
            }
        }

        private NotifierPayloadWithResponse MakeNotifierPayloadWithResponse(
            string ResponseKey,
            object notificationPayload,
            params string[] userId)
        {
            _logger.LogInformation("In MakeNotifierPayloadWithResponse");

            var response = new NotifierPayloadWithResponse
            {
                UserIds = userId.Select(i => Guid.Parse(i)).ToList(),
                NotificationType = userId.Length == 1
                    ? NotificationReceiverTypes.UserSpecificReceiverType
                    : NotificationReceiverTypes.BroadcastReceiverType,
                ResponseKey = ResponseKey,
                DenormalizedPayload = JsonConvert.SerializeObject(notificationPayload)
            };

            return response;
        }

        public string GenerateAbbreviation(string camelCasedStr)
        {
            if (string.IsNullOrWhiteSpace(camelCasedStr))
            {
                return string.Empty;
            }

            var capitalLatters = camelCasedStr.Where(char.IsUpper);

            return capitalLatters.Any() ? string.Concat(capitalLatters) : string.Empty;
        }
        
        public async Task<EmployeeIdentityDto> GetEmployeeInfoAsync(string userId)
        {
            var employeeInfo = await _finMongoDbRepository.FindWithProjectionAsync<BuroEmployee, EmployeeIdentityDto>(
                e => e.UserItemId == userId,
                e => new EmployeeIdentityDto
                {
                    EmployeeName = e.EmployeeName,
                    EmployeePin = e.EmployeePin,
                    DesignationTitle = e.DesignationTitle,
                    CurrentOfficeTitle = e.CurrentOfficeTitle,
                    CurrentOfficeCode = e.CurrentOfficeCode,
                    UserItemId = e.UserItemId,
                    EmployeeItemId = e.ItemId
                });

            return employeeInfo[0];
        }

        public BuroProductAccountTransactionRequest CreateTransactionRequestAsync(
            CreateTransactionRequestDto command,
            BuroEmployee currentEmployee,
            BuroMember member,
            BuroProductAccountBase account)
        {
            _logger.LogInformation("Creating transaction request for transaction ID: {TransactionItemId}", command.TransactionItemId);

            var deposit = new BuroProductAccountTransactionRequest
            {
                ItemId = command.TransactionItemId,
                MemberItemId = member.ItemId,
                MemberSequenceNumber = member.MemberSequenceNumber,
                MemberName = member.MemberName,
                MemberType = member.MemberType,
                MemberNIDNumber = member.NIDNumber,
                MemberBranchOfficeItemId = member.BranchOfficeItemId,
                MemberBranchOfficeName = member.BranchOfficeName,
                Amount = command.Amount,
                TransactionMedium = command.TransactionMedium,
                ProductType = command.ProductType,
                Status = EnumProductAccountTransactionStatus.Pending,
                TransactionType = command.TransactionType,
                AccountItemId = account.ItemId,
                AccountSequenceNumber = account.AccountSequenceNumber,
                MemberScheduleItemId = command.MemberScheduleItemId,
                IsTransactionRequired = command.IsTransactionRequired,
                ActionByEmployee = new BuroActionByEmployeeInformation
                {
                    EmployeeItemId = currentEmployee.ItemId,
                    EmployeePin = currentEmployee.EmployeePin,
                    EmployeeName = currentEmployee.EmployeeName,
                    EmployeeDesginationItemId = currentEmployee.DesignationItemId,
                    EmployeeDesginationTitle = currentEmployee.DesignationTitle,
                    EmployeeOfficeItemId = currentEmployee.CurrentOfficeItemId,
                    EmployeeOfficeName = currentEmployee.CurrentOfficeTitle,
                    EmployeeOfficeCode = currentEmployee.CurrentOfficeCode,
                },
                AdditionalDocumentIds = command.AdditionalDocumentIds
            };

            deposit.AddEntityBasicInfo();

            _logger.LogInformation("Transaction request created successfully for transaction ID: {TransactionItemId}", command.TransactionItemId);

            return deposit;
        }

        public async Task InitiateProductAccountTransactionAsync(bool IsIndependentTransaction = true, params BuroProductAccountTransactionRequest[] transactionRequests)
        {
            var transactionCommands = transactionRequests.Where(t => t.IsTransactionRequired)
                .Select(t => new TransactionCommand
                {
                    TransactionId = Guid.TryParse(t.ItemId, out var result) ? result : Guid.NewGuid(),
                    AccountHolderNumber = t.MemberSequenceNumber,
                    AccountNumber = t.AccountSequenceNumber,
                    Amount = t.Amount,
                    TransactionType = ResolveTransactionTypeConstantForTransactionService(t),
                    AccountType = ResolveAccountTypeConstantForTransactionService(t),
                    Reference = t.TransactionType.GetDisplayName(),
                }).ToList();

            if (!transactionCommands.Any())
            {
                return;
            }

            var response = await _transactionClientService.InitiateTransactionAsync(new InitiateTransactionCommand
            {
                Transactions = transactionCommands,
                IsIndependentTransaction = IsIndependentTransaction,
                MessageCorrelationId = Guid.NewGuid().ToString(),
            });

            if (!response.IsSuccess())
            {
                _logger.LogError("Trnsaction failed for transaction Ids: {TransactionRequestItemIds}", string.Join(", ", transactionRequests.Select(t => t.ItemId)));

                throw new InvalidOperationException($"Transactions failed");
            }
        }

        private static int ResolveAccountTypeConstantForTransactionService(BuroProductAccountTransactionRequest transactionRequest)
        {
            return transactionRequest.ProductType switch
            {
                EnumProductType.GeneralSaving => TransactionServiceConstants.AccountTypeConstants.Savings,
                EnumProductType.ContractualSaving => TransactionServiceConstants.AccountTypeConstants.Current,
                EnumProductType.Loan => TransactionServiceConstants.AccountTypeConstants.Loan,
                _ => default
            };
        }

        private static int ResolveTransactionTypeConstantForTransactionService(BuroProductAccountTransactionRequest transactionRequest)
        {
            return transactionRequest.TransactionType switch
            {
                EnumBuroTransactionType.EnrolmentFormFee or 
                EnumBuroTransactionType.GsDeposit or 
                EnumBuroTransactionType.CsInstallment or 
                EnumBuroTransactionType.LoanInstallmentRepayment => TransactionServiceConstants.TransactionTypeConstants.Deposit,
                EnumBuroTransactionType.GsWithdrawal or 
                EnumBuroTransactionType.CsEarlyWithdrawal or 
                EnumBuroTransactionType.CsWithdrawalMaturity => TransactionServiceConstants.TransactionTypeConstants.Withdrawal,
                _ => default,
            };
        }
    }
}
