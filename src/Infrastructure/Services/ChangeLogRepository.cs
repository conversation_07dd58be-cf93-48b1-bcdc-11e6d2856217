using Infrastructure.Contracts;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Linq.Expressions;

namespace Infrastructure.Services
{
    public class ChangeLogRepository : IChangeLogRepository
    {
        private readonly IRepository _repository;
        private readonly IChangeLogService _changeLogService;

        public ChangeLogRepository(
            IRepository repository,
            IChangeLogService changeLogService)
        {
            _repository = repository;
            _changeLogService = changeLogService;
        }

        public async Task UpdateWithChangeLogAsync<T>(Expression<Func<T, bool>> dataFilters, T data)
        {
            var oldRecord = await _repository.GetItemAsync(dataFilters);

            await _repository.UpdateAsync(dataFilters, data);

            await _changeLogService.LogChangeAsync(oldRecord, data);
        }

        public async Task UpdateWithChangeLogAsync<T>(Expression<Func<T, bool>> dataFilters, IDictionary<string, object> updates)
        {
            var oldRecords = _repository.GetItems(dataFilters);

            await _repository.UpdateAsync(dataFilters, updates);

            await _changeLogService.LogChangeAsync(oldRecords, updates);
        }

        public async Task UpdateManyWithChangeLogAsync<T>(Expression<Func<T, bool>> dataFilters, IDictionary<string, object> updates)
        {
            var oldRecords = _repository.GetItems(dataFilters);

            await _repository.UpdateManyAsync(dataFilters, updates);

            await _changeLogService.LogChangeAsync(oldRecords, updates);
        }
    }
}
