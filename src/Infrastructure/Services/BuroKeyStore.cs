using Infrastructure.Contracts;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;

namespace Infrastructure.Services;

public class BuroKeyStore : IBuroKeyStore
{
    private readonly IKeyStore _keyStore;

    public BuroKeyStore(IKeyStore keyStore)
    {
        _keyStore = keyStore;
    }

    public async Task<T?> GetValueAsync<T>(string key)
    {
        var value = await _keyStore.GetValueAsync(key);
        return !string.IsNullOrEmpty(value) ? JsonConvert.DeserializeObject<T>(value) : default;
    }

    public async Task<string> GetValueAsync(string key)
    {
        var value = await _keyStore.GetValueAsync(key);
        return value;
    }

    public Task<bool> AddKeyWithExpiryAsync(string key, object value, long keyLifeSpanInSecond)
    {
        var serializedValue = JsonConvert.SerializeObject(value);
        return _keyStore.AddKeyWithExprityAsync(key, serializedValue, keyLifeSpanInSecond);
    }

    public Task<bool> AddKeyWithExpiryAsync(string key, string value, long keyLifeSpanInSecond)
    {
        return _keyStore.AddKeyWithExprityAsync(key, value, keyLifeSpanInSecond);
    }

    public Task<bool> AddKeyAsync(string key, string value)
    {
        return _keyStore.AddKeyAsync(key, value);
    }

    public Task<bool> RemoveKeyAsync(string key)
    {
        return _keyStore.RemoveKeyAsync(key);
    }
}