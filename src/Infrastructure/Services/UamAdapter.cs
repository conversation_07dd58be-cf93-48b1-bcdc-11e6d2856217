using Infrastructure.Contracts;
using Infrastructure.Models.ForgotPassword;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using SeliseBlocks.Uam.Commands;
using System.Net.Http.Headers;
using System.Net.Http.Json;

namespace Infrastructure.Services;

public class UamAdapter : IUamAdapter
{
    private readonly IAccessTokenProvider _accessTokenProvider;
    private readonly ISecurityContextProvider _securityContextProvider;
    private readonly IBusinessConfig _businessConfig;
    private readonly ILogger<UamAdapter> _logger;

    public UamAdapter(
        IBusinessConfig businessConfig,
        IAccessTokenProvider accessTokenProvider,
        ISecurityContextProvider securityContextProvider,
        ILogger<UamAdapter> logger)
    {
        _businessConfig = businessConfig;
        _accessTokenProvider = accessTokenProvider;
        _securityContextProvider = securityContextProvider;
        _logger = logger;
    }


    public async Task<bool> ResetPassword(ResetPasswordPayload resetPasswordPayload)
    {
        var token = _securityContextProvider.GetSecurityContext().OauthBearerToken;
        var url = _businessConfig.GetResetPasswordUrl();

        using var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        var response = await httpClient.PostAsJsonAsync(url, resetPasswordPayload);

        return response.IsSuccessStatusCode;
    }

    public async Task<bool> CreateUser(CreateUserCommand userCommand, SecurityContext? securityContext)
    {
        var token = securityContext?.OauthBearerToken;

        if (securityContext == null)
        {
            token = await _accessTokenProvider.GetAdminToken();
        }

        var url = _businessConfig.CreateUserUrl();
        this._logger.LogInformation("UamAdapter->CreateUser-> URL: {Url} TOKEN: {Token}  COMMAND: {Command}", url, token, JsonConvert.SerializeObject(userCommand));

        using var httpClient = new HttpClient();
        httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        var httpResponse = await httpClient.PostAsJsonAsync(url, userCommand);
        httpResponse.EnsureSuccessStatusCode();

        var response = await httpResponse.Content.ReadAsStringAsync();

        this._logger.LogInformation("UamAdapter->CreateUser-> RESPONSE: {Response}", response);

        var commandResponse = JsonConvert.DeserializeObject<CommandResponse>(response);
        return commandResponse != null && commandResponse.Errors.IsValid;
    }



}
