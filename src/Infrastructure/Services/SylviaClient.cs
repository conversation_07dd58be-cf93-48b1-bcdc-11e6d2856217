using Infrastructure.Contracts;
using Infrastructure.Models.ExternalSync.Responses;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;

namespace Infrastructure.Services
{
    public class SylviaClient : ISylviaClient
    {
        private readonly ILogger<SylviaClient> _logger;
        private readonly IBusinessConfig _businessConfig;
        private readonly string _apiBaseUrl;
        private readonly string _sylviaAPIKey;
        public SylviaClient(
            ILogger<SylviaClient> logger,
            IBusinessConfig businessConfig)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _businessConfig = businessConfig ?? throw new ArgumentNullException(nameof(businessConfig));

            _apiBaseUrl = _businessConfig.GetSylviaBaseUrl();
            _sylviaAPIKey = "V2dJdk93%9876GlYa&mlMZ1FhMHdEY87#4554WnVpZew";
        }

        public async Task<SylviaGetDesignationResponse> GetDeisnations()
        {
            _logger.LogInformation("SylviaSyncService->GetDeisnations->START");

            try
            {
                string requestUrl = $"{_apiBaseUrl}/api/External/GetDesignations";
                var httpClient = GetHttpClientWithCredetials();
                HttpResponseMessage response = await httpClient.GetAsync(requestUrl);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("SylviaSyncService->GetDeisnations->Failed to retrieve designations. Status Code: {StatusCode}", response.StatusCode);
                    return new SylviaGetDesignationResponse { Success = false };
                }

                string responseContent = await response.Content.ReadAsStringAsync();
                var designations = JsonSerializer.Deserialize<SylviaGetDesignationResponse>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                _logger.LogInformation("SylviaSyncService->GetDeisnations->Successfully retrieved designations.");
                return designations ?? new SylviaGetDesignationResponse { Success = false };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SylviaSyncService->GetDeisnations->An error occurred while fetching designations.");
                return new SylviaGetDesignationResponse { Success = false };
            }
        }

        public Task<SylviaGetEmployeeDetailResponse> GetEmployeeDetail(string employeePIN)
        {
            throw new NotImplementedException();
        }

        public Task<SylviaGetOfficeDetailsResponse> GetOfficeDetails(string officeCode)
        {
            throw new NotImplementedException();
        }

        public async Task<SylviaGetOfficeEmployeesResponse> GetOfficeEmployees(string officeCode)
        {
            _logger.LogInformation("SylviaSyncService->GetOfficeEmployees->START");

            try
            {
                string requestUrl = $"{_apiBaseUrl}/api/External/GetOfficeEmployees";
                var httpClient = GetHttpClientWithCredetials();
                var requestBody = JsonSerializer.Serialize(new { OfficeCode = officeCode });
                var content = new StringContent(requestBody, Encoding.UTF8, "application/json");
                HttpResponseMessage response = await httpClient.PostAsync(requestUrl, content);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("SylviaSyncService->GetOfficeEmployees->Failed to retrieve Employees. Status Code: {StatusCode}", response.StatusCode);
                    return new SylviaGetOfficeEmployeesResponse { Success = false };
                }

                string responseContent = await response.Content.ReadAsStringAsync();
                var offices = JsonSerializer.Deserialize<SylviaGetOfficeEmployeesResponse>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                _logger.LogInformation("SylviaSyncService->GetOfficeEmployees->Successfully retrieved Employees.");
                return offices ?? new SylviaGetOfficeEmployeesResponse { Success = false };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SylviaSyncService->GetOfficeEmployees->An error occurred while fetching Employees.");
                return new SylviaGetOfficeEmployeesResponse { Success = false };
            }
        }

        public async Task<SylviaGetOfficesResponse> GetOffices(int pageNo, int pageSize)
        {
            _logger.LogInformation("SylviaSyncService->GetOffices->START");

            try
            {
                string requestUrl = $"{_apiBaseUrl}/api/External/GetOffices?pageNumber={pageNo}&pageSize={pageSize}";
                var httpClient = GetHttpClientWithCredetials();
                HttpResponseMessage response = await httpClient.GetAsync(requestUrl);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("SylviaSyncService->GetOffices->Failed to retrieve offices. Status Code: {StatusCode}", response.StatusCode);
                    return new SylviaGetOfficesResponse { Success = false };
                }

                string responseContent = await response.Content.ReadAsStringAsync();
                var offices = JsonSerializer.Deserialize<SylviaGetOfficesResponse>(responseContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                _logger.LogInformation("SylviaSyncService->GetOffices->Successfully retrieved offices.");
                return offices ?? new SylviaGetOfficesResponse { Success = false };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "SylviaSyncService->GetOffices->An error occurred while fetching offices.");
                return new SylviaGetOfficesResponse { Success = false };
            }
        }

        private HttpClient GetHttpClientWithCredetials()
        {
            HttpClient client = new HttpClient();
            client.DefaultRequestHeaders.Add("CF-Key", _sylviaAPIKey);
            return client;
        }
    }
}
