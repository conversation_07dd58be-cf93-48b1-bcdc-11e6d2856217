namespace Infrastructure.Events.Register;

public class ChequeRegisterPostProcessingEvent : BaseEvent
{
    public string BranchItemId { get; set; }
    public string MemberItemId { get; set; }
    public string MemberName { get; set; }
    public string MemberId { get; set; }
    public string ChequeNumber { get; set; }
    public string BankName { get; set; }
    public string? RoutingNumber { get; set; }
    public string? MICRLine { get; set; }
    public string ChequeBeneficiaryName { get; set; }
    public double? Amount { get; set; }
    public string ChequeFileStorageItemId { get; set; }
    public string DepositAccountItemId { get; set; }
    public string DepositAccountCode { get; set; }
    public string DepositAccountNumber { get; set; }
    public bool IsEligibleReturn { get; set; }
}