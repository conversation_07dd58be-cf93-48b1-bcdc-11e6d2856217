using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Events.Account
{
    public class LoanDisbursementPostProcessingEvent
    {
        public string LoanApplicationDisbursementItemId { get; set; } = default!;
        public string LoanApplicationItemId { get; set; } = default!;
        public EnumLoanApplicationDisbursementStatus Status { get; set; }
        public double FinalMicroInsuranceFee { get; set; }
    }
}
