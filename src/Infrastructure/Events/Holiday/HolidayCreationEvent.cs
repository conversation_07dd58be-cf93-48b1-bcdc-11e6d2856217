using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Events.Holiday
{
    public class HolidayCreationEvent
    {
        public DateTime? HolidayStartDate { get; set; }
        public DateTime? HolidayEndDate { get; set; }
        public EnumHolidayType HolidayType { get; set; }
        public string[]? OfficeItemIds { get; set; }
        public string? MemberItemId { get; set; }
        public string[]? CenterItemIds { get; set; }
    }
}
