using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Events.Transaction
{
    public class TrackAccountPaymentEvent
    {
        public List<AccountPayment> AccountPayments { get; set; } = default!;
    }

    public class AccountPayment
    {
        public string MemberItemId { get; set; } = default!;
        public string AccountItemId { get; set; } = default!;
        public string MemberScheduleItemId { get; set; } = default!;
        public double PaidAmount { get; set; }
        public EnumProductType ProductType { get; set; }
    }
}
