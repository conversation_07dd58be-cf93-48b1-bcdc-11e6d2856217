using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;

namespace Infrastructure.Events.EmployeeHistory;

public class CreateEmployeeTransferHistoryEvent : BaseEvent
{
    public string EmployeeItemId { get; set; }
    public string UserItemId { get; set; }
    public string DesignationItemId { get; set; }
    public string DesignationTitle { get; set; }
    public string DesignationCode { get; set; }
    public string OfficeItemId { get; set; }
    public string OfficeTitle { get; set; }
    public string OfficeCode { get; set; }
    public int? MemberCount { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public EnumEmployeeTransferHistoryType TransferHistoryType { get; set; }
}