using System.Reflection;

namespace Infrastructure.Extensions
{
    public static class MapperExtensions
    {
        public static TTarget MapToTarget<TTarget>(this object source)
        {
            if (source == null)
            {
                return default!;
            }

            var target = Activator.CreateInstance<TTarget>();
            var sourceProperties = source.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
            var targetProperties = typeof(TTarget).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var sourceProperty in sourceProperties)
            {
                var targetProperty = Array.Find(targetProperties, p => p.Name == sourceProperty.Name && p.CanWrite);

                try
                {
                    if (targetProperty != null)
                    {
                        var value = sourceProperty.GetValue(source);

                        SetValue(target, targetProperty, value);
                    }
                }
                catch
                {
                    continue;
                }
            }

            return target;
        }

        private static void SetValue<TTarget>(TTarget? target, PropertyInfo targetProperty, object? value)
        {
            if (value == null && targetProperty.PropertyType.IsClass && targetProperty.PropertyType != typeof(string))
            {
                targetProperty.SetValue(target, null);
            }
            else if (value != null && targetProperty.PropertyType.IsInstanceOfType(value))
            {
                targetProperty.SetValue(target, value);
            }
            else if (value != null && targetProperty.PropertyType.IsEnum && Enum.IsDefined(targetProperty.PropertyType, value))
            {
                targetProperty.SetValue(target, Enum.Parse(targetProperty.PropertyType, value.ToString() ?? string.Empty));
            }
            else if (value != null && Nullable.GetUnderlyingType(targetProperty.PropertyType) != null)
            {
                var nullableType = Nullable.GetUnderlyingType(targetProperty.PropertyType);

                if (nullableType != null)
                {
                    targetProperty.SetValue(target, Convert.ChangeType(value, nullableType));
                }
            }
        }
    }
}
