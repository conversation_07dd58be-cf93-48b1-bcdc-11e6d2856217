using System.Text.Json;

namespace Infrastructure.Extensions
{
    public static class DeepCopyExtensions
    {
        /// <summary>
        /// Creates a deep copy of an object using JSON serialization and deserialization.
        /// If the object is null, it returns a new instance of type T.
        /// </summary>
        /// <typeparam name="T">The type of the object to copy.</typeparam>
        /// <param name="obj">The object to be deep copied.</param>
        /// <returns>A new deep-copied instance of the object, or a new instance of type T if the input is null.</returns>
        public static T GetNewCopy<T>(this T obj) where T : class, new()
        {
            if (obj == null)
            {
                return new T();
            }

            var json = JsonSerializer.Serialize(obj);
            var newObj = JsonSerializer.Deserialize<T>(json);

            return newObj ?? new T();
        }
    }
}
