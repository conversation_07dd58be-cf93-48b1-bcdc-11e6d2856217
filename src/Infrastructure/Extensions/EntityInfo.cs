using Infrastructure.Constants;
using SeliseBlocks.Genesis.Framework.PDS.Entity;

namespace Infrastructure.Extensions
{
    public static class EntityInfo
    {
        private static readonly string[] EmptyArray = Array.Empty<string>();
        private static readonly string DefaultLanguage = "en-US";
        public static EntityBase AddEntityBasicInfo(this EntityBase entity)
        {
            if (entity == null) throw new ArgumentNullException(nameof(entity));

            entity.ItemId ??= Guid.NewGuid().ToString();
            entity.TenantId = BuroConstants.TenantId;
            entity.CreateDate = DateTime.UtcNow;
            entity.LastUpdateDate = DateTime.UtcNow;
            entity.Tags ??= EmptyArray;
            entity.IsMarkedToDelete = false;
            entity.Language ??= DefaultLanguage;
            entity.RolesAllowedToRead ??= EmptyArray;
            entity.IdsAllowedToRead ??= EmptyArray;
            entity.RolesAllowedToWrite ??= EmptyArray;
            entity.IdsAllowedToWrite ??= EmptyArray;
            entity.RolesAllowedToUpdate ??= EmptyArray;
            entity.IdsAllowedToUpdate ??= EmptyArray;
            entity.RolesAllowedToDelete ??= EmptyArray;
            entity.IdsAllowedToDelete ??= EmptyArray;

            return entity;
        }
    }
}
