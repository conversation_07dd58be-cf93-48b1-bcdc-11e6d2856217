using Infrastructure.Constants;
using SeliseBlocks.Entities.PrimaryEntities.BURO;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Enums;
using SeliseBlocks.Entities.PrimaryEntities.BURO.Product;
using SeliseBlocks.Genesis.Framework.Infrastructure;
using System.Net;
using System.Text.Json;

namespace Infrastructure.Extensions;

public static class HelperMethod
{
    public static bool IsNullOrEmptyOrWhiteSpace(this string str)
    {
        return string.IsNullOrEmpty(str) || string.IsNullOrWhiteSpace(str) || string.IsNullOrEmpty(str.Trim());
    }

    public static string NormalizeString(this string input)
    {
        if (string.IsNullOrWhiteSpace(input)) return string.Empty;
        return string.Join(" ", input.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries))
            .ToLower();
    }

    public static void ExtendCommandResponse(this CommandResponse response, CommandResponse target)
    {
        response.HttpStatusCode = target.HttpStatusCode;
        response.ExternalError = target.ExternalError;
        foreach (var validationFailure in target.Errors.Errors)
        {
            response.SetError(validationFailure.ErrorCode, validationFailure.ErrorMessage);
        }
    }

    public static bool IsSuccess(this CommandResponse commandResponse)
    {
        // NOSONAR - Need to check why HttpStatusCode is 0 and StatusCode is 0 by default
        return commandResponse is { HttpStatusCode: 0, StatusCode: 0 }
               && !commandResponse.ErrorMessages.Any()
               && string.IsNullOrWhiteSpace(commandResponse.ExternalError);
    }

    public static T? ToObject<T>(this object serializableObject)
    {
        var jsonData = JsonSerializer.Serialize(serializableObject);
        var resultObject = JsonSerializer.Deserialize<T>(jsonData);

        return resultObject;
    }

    public static void TryAddRange<T>(this List<T> destinationCollection, List<T> sourceCollection)
    {
        if (sourceCollection != null && sourceCollection.Any())
        {
            destinationCollection.AddRange(sourceCollection);
        }
    }

    public static string ToStringCibFormate(this bool boolean)
    {
        return boolean ? BuroCibConstants.True : BuroCibConstants.False;
    }

    public static string ToStringCibFormate(this DateTime dateTime)
    {
        return dateTime.ToString(BuroCibConstants.DateFormat);
    }

    public static string ToStringCibFormate(this DateTime? dateTime)
    {
        if (!dateTime.HasValue)
        {
            return string.Empty;
        }

        return dateTime.Value.ToStringCibFormate();
    }

    public static string ToStringCibFormate(this BuroMemberAddress address)
    {
        if (address == null)
        {
            return string.Empty;
        }

        return address.House
            + address.PostOfficeName
            + address.WardName
            + address.UnionName
            + address.UpazilaName
            + address.DistrictName
            + address.DivisionName
            + address.PostCode;
    }

    public static int ToDays(this Period period)
    {
        return period.Unit switch
        {
            EnumTenureUnit.Week => period.Value * 7,
            EnumTenureUnit.Month => period.Value * 30,
            EnumTenureUnit.Year => period.Value * 365,
            _ => default
        };
    }
}