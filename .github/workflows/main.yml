name: Build (main)

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
    types:
      - opened
env:
  RUN_UNIT_TEST: 'false'
  RUN_SONARQUBE: 'true'
  
jobs:
  initialization:
    runs-on: ubuntu-latest
    outputs:
      envvalue1: ${{ steps.setvar.outputs.envvar1 }}
      envvalue2: ${{ steps.setvar.outputs.envvar2 }}
    steps:
      - name: set value
        id: setvar
        run: |
          echo "::set-output name=envvar1::$RUN_UNIT_TEST" 
          echo "::set-output name=envvar2::$RUN_SONARQUBE"
          
  test-job:
    if: ${{ needs.initialization.outputs.envvalue1=='true' }}
    uses: ./.github/workflows/1_test.yml
    needs: [initialization]

  sonarqube-job:
    if: ${{ success() && needs.initialization.outputs.envvalue2=='true' }}
    uses: ./.github/workflows/2_sonar.yml
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN_GLOBAL }}
    needs: [test-job]

  cd-job:
    if: ${{ github.event_name == 'push' && (success() || (needs.initialization.outputs.envvalue1=='false' && needs.initialization.outputs.envvalue2=='false')) }}
    uses: ./.github/workflows/3_web.yml
    with:
      CONTAINER_NAME: "prod-$SERVICE_NAME"
      NAMESPACE: "prod-$REPO_NAME"
      SERVICE_NAME: $SERVICE_NAME
    secrets:
      SELISE_GITHUB_PAT: ${{ secrets.SELISE_GITHUB_PAT }}
      AZURE_CREDENTIALS: ${{ secrets.AZURE_AKS_CREOLYTIX_CREDENTIALS }}
      AZURE_CONTAINER_REGISTRY: ${{ secrets.AZURE_CREOLYTIX_CONTAINER_REGISTRY }}
      ClUSTER_RESOURCE_GROUP: ${{ secrets.ClUSTER_AKS_CREOLYTIX_RESOURCE_GROUP }}
      CLUSTER_NAME: ${{ secrets.AKS_CREOLYTIX_PROD_CLUSTER }}
      ACR_RESOURCE_GROUP: ${{ secrets.ClUSTER_AKS_CREOLYTIX_RESOURCE_GROUP }}
    needs: [sonarqube-job]

  cd-job-win:
    if: ${{ github.event_name == 'push' && (success() || (needs.initialization.outputs.envvalue1=='false' && needs.initialization.outputs.envvalue2=='false')) }}
    uses: ./.github/workflows/4_win.yml
    with:
      CONTAINER_NAME: "prod-$SERVICE_NAME-win"
      NAMESPACE: "prod-$REPO_NAME"
      SERVICE_NAME: $SERVICE_NAME
    secrets:
      SELISE_GITHUB_PAT: ${{ secrets.SELISE_GITHUB_PAT }}
      AZURE_CREDENTIALS: ${{ secrets.AZURE_AKS_CREOLYTIX_CREDENTIALS }}
      AZURE_CONTAINER_REGISTRY: ${{ secrets.AZURE_CREOLYTIX_CONTAINER_REGISTRY }}
      ClUSTER_RESOURCE_GROUP: ${{ secrets.ClUSTER_AKS_CREOLYTIX_RESOURCE_GROUP }}
      CLUSTER_NAME: ${{ secrets.AKS_CREOLYTIX_PROD_CLUSTER }}
      ACR_RESOURCE_GROUP: ${{ secrets.ClUSTER_AKS_CREOLYTIX_RESOURCE_GROUP }}
    needs: [sonarqube-job] 
