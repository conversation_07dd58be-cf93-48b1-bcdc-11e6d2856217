name: <PERSON>-<PERSON><PERSON> and push at ACR

on:
  workflow_call:
    inputs:
      CONTAINER_NAME:
        required: true
        type: string
      NAMESPACE:
        required: true
        type: string
      SERVICE_NAME:
        required: true
        type: string

    secrets:
      SELISE_GITHUB_PAT:
        required: true
      AZURE_CREDENTIALS:
        required: true
      AZURE_CONTAINER_REGISTRY:
        required: true
      ClUSTER_RESOURCE_GROUP:
        required: true
      CLUSTER_NAME:
        required: true
      ACR_RESOURCE_GROUP:
        required: true

env:
  SERVICE_TYPE: "winservice"

jobs:
  deployWinToK8s:
    permissions:
      contents: read
      id-token: write
    runs-on: ubuntu-latest
    steps:
    #Checks out the repository this file is in
    - uses: actions/checkout@v3

    - name: Set Environment Variables
      uses: ./.github/actions/setvars
      with:
        varFilePath: ./.github/variables/vars.env

    # Logs in with your Azure credentials
    - name: Azure login
      uses: azure/login@v1.4.6
      with:
        creds: ${{ secrets.AZURE_CREDENTIALS }}

    - name: Build and push image to ACR
      run: |
        az acr build --image ${{ secrets.AZURE_CONTAINER_REGISTRY }}.azurecr.io/${{ inputs.CONTAINER_NAME }}:${{ github.sha }} --registry ${{ secrets.AZURE_CONTAINER_REGISTRY }} -g ${{ secrets.ACR_RESOURCE_GROUP }} --file ${{ env.WIN_DOCKERFILE }} .

    # pull helm chart repo
    - name: pull helm repo
      uses: actions/checkout@v3
      with:
        repository: SELISEdigitalplatforms/l0-yml-infrastructure-helm
        token: ${{ secrets.SELISE_GITHUB_PAT }}

    # Retrieves your Azure Kubernetes Service cluster's kubeconfig file
    - name: Get K8s context
      uses: azure/aks-set-context@v3
      with:
        resource-group: ${{ secrets.ClUSTER_RESOURCE_GROUP }}
        cluster-name: ${{ secrets.CLUSTER_NAME }}

    - name: Setup Helm Installer
      uses: Azure/setup-helm@v3.5
      with:
        token: ${{ secrets.SELISE_GITHUB_PAT }}      

    - name: Deploy To Kubernetes
      run: |
        helm upgrade --install ${{inputs.CONTAINER_NAME}} ./new-templates/ecap3-${{env.SERVICE_TYPE}}/ --namespace=${{inputs.NAMESPACE}} --values ./${{ secrets.CLUSTER_NAME }}/${{inputs.SERVICE_NAME}}-win.values.yaml --set image.repository=${{ secrets.AZURE_CONTAINER_REGISTRY }}.azurecr.io/${{ inputs.CONTAINER_NAME }} --set image.tag=${{ github.sha }} --set fullnameOverride=${{inputs.CONTAINER_NAME}}

