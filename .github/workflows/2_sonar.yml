name: Analyze Code Quality
on:
  workflow_call:
    secrets:
      SONAR_TOKEN:
        required: true
jobs:
  build:
    name: SonarQube Analysis
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
      with:
        fetch-depth: 0

    - uses: actions/setup-dotnet@v4
      with:
        dotnet-version: '6.0.x'

    - name: Set Environment Variables
      uses: ./.github/actions/setvars
      with:
        varFilePath: ./.github/variables/vars.env

    - name: Set up JDK 17
      uses: actions/setup-java@v1
      with:
        java-version: 1.17

    - name: Cache SonarCloud packages
      uses: actions/cache@v4
      with:
        path: ~\sonar\cache
        key: ${{ runner.os }}-sonar
        restore-keys: ${{ runner.os }}-sonar

    - name: Cache SonarCloud scanner
      id: cache-sonar-scanner
      uses: actions/cache@v4
      with:
        path: .\.sonar\scanner
        key: ${{ runner.os }}-sonar-scanner
        restore-keys: ${{ runner.os }}-sonar-scanner

    - name: Install SonarCloud scanner
      if: steps.cache-sonar-scanner.outputs.cache-hit != 'true'
      shell: pwsh
      run: |
        dotnet tool install --global dotnet-sonarscanner --version 5.5.3

    - name: Install coverlet
      run: "dotnet tool install --global coverlet.console \n#magic___^_^___line\n"
    - name: SonarQube Analysis
      working-directory: ./src
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      shell: pwsh
      run: |
        dotnet-sonarscanner begin /k:"${{ env.SONAR_KEY }}" /o:"${{ env.AUTHOR }}" /d:sonar.login=${{ secrets.SONAR_TOKEN }} /d:sonar.host.url="${{ env.SONARQUBE_HOST }}" /d:sonar.cs.opencover.reportsPaths=coverage.xml
        dotnet restore "./${{ env.SOLUTION_NAME }}" -s https://www.nuget.org/api/v2/ -s https://nuget.selise.biz/nuget
        dotnet build "./${{ env.SOLUTION_NAME }}" --no-incremental
        coverlet ./xUnitTests/bin/Debug/net6.0/xUnitTests.dll --target "dotnet" --targetargs "test ./${{ env.SOLUTION_NAME }} --no-build" -f=opencover -o="coverage.xml"
        dotnet-sonarscanner end /d:sonar.login=${{ secrets.SONAR_TOKEN }}

    # - name: SonarQube Quality Gate check
    #   uses: sonarsource/sonarqube-quality-gate-action@master
    #   # Force to fail step after specific time.
    #   timeout-minutes: 5
    #   with:
    #     scanMetadataReportFile: src/.sonarqube/out/.sonar/report-task.txt
    #   env:
    #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    #     SONAR_HOST_URL: ${{ env.SONARQUBE_HOST }}
        
