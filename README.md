# Microservice: l3-net-buro-business

This repository contains a .NET-based REST API microservice that is part of a larger microservice architecture. It manages business functionalities focused exclusively on data manipulation operations and communicates with other services through HTTP and RabbitMQ. This project is developed using .NET 6.0 and is optimized for deployment in a containerized environment.

## Prerequisites

To set up and run this microservice locally, ensure the following prerequisites are met:

- **.NET SDK**: .NET 6.0 or later
- **VPN Access**: A VPN connection is required for network access to other services within the system. Ensure you have the VPN configured as specified in the system setup documentation.
- **CONFIGS Folder**:
  - On **Windows**: Place the `ECAP_CONFIGS` folder in the `C:\` drive.
  - On **Linux/Mac**:
- **CERTIFICATE Folder**:
  - On **Windows**: Place the `Certificates` folder in the `C:\` drive.
  - On **Linux/Mac**:

## Getting Started

1. **Clone the Repository**

   ```bash
   git clone <repository-url>
   cd <repository-name>
   ```

2. **Setup VPN**  
   Ensure you are connected to the VPN configured for this service. Check with the network administrator if you need details on VPN setup.

3. **Configure Environment Variables and Certificates**
   - Place the `ECAP_CONFIGS` folder in the specified directory based on your OS.
   - Place the `Certificates` folder in the specified directory based on your OS.
   - Verify environment variables are set as per the project documentation for configurations like database connection strings, cache settings, and other dependencies.

4. **Build and Run the Project**
   Open the project in your preferred IDE (such as Visual Studio, JetBrains Rider, or Visual Studio Code) and run the project. Ensure you have configured `.NET 6.0` as the target framework in the IDE settings.

   Alternatively, use the CLI:

   ```bash
   cd <repository-name>/src
   dotnet build WebService
   dotnet run --project WebService
   ```

5. **Access the API**
   Once running, the API will be accessible at the configured base URL (e.g., `http://localhost:25787`). Refer to the project’s API documentation for endpoint details.

## Testing

This project includes a project of unit tests. To run the tests:

```bash
cd <repository-name>/xUnitTests
dotnet test
```

## Additional Notes

- This microservice is optimized for use in a distributed system architecture and follows a CQRS pattern, RabbitMQ for inter-service messaging, and Radish for caching.
