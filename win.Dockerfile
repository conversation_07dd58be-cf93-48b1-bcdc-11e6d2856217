# Build Stage
FROM mcr.microsoft.com/dotnet/sdk:6.0  AS build-env
WORKDIR /src
ARG git_branch

RUN mkdir -p /root/.nuget/NuGet
COPY ./config/NuGetPackageSource.Config /root/.nuget/NuGet/NuGet.Config

COPY /src/ ./

COPY ./src/WinService/*.csproj ./WinService/
RUN dotnet restore ./WinService/

#   Copy everything else and build
COPY ./src/WinService ./WinService/
RUN dotnet build ./WinService/

#   publish
RUN dotnet publish ./WinService/ -o /publish --configuration Release
RUN ls /publish

# Publish Stage
FROM mcr.microsoft.com/dotnet/aspnet:6.0
WORKDIR /app
COPY --from=build-env /publish .
ARG git_branch

ENV ASPNETCORE_ENVIRONMENT=$git_branch

ENTRYPOINT ["dotnet", "WinService.dll"]