# Build Stage
FROM mcr.microsoft.com/dotnet/sdk:6.0  AS build-env
WORKDIR /src
ARG git_branch

RUN mkdir -p /root/.nuget/NuGet
COPY ./config/NuGetPackageSource.Config /root/.nuget/NuGet/NuGet.Config

COPY /src/ ./

#   Copy only .csproj and restore
COPY ./src/WebService/*.csproj ./WebService/
RUN dotnet restore ./WebService/

#   Copy everything else and build
COPY ./src/WebService ./WebService/
RUN dotnet build ./WebService/

#   publish
RUN dotnet publish ./WebService/ -o /publish --configuration Release
RUN ls /publish

# Publish Stage
FROM mcr.microsoft.com/dotnet/aspnet:6.0
ARG git_branch

WORKDIR /app
COPY --from=build-env /publish .
ENV port=80
ARG git_branch

ENV ASPNETCORE_ENVIRONMENT=$git_branch
ENV ASPNETCORE_URLS=http://+:$port

ENTRYPOINT ["dotnet", "WebService.dll"]